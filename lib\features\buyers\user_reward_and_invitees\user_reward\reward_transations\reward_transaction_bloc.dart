import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/reward_transaction_response/reward_transaction_response.dart';
import 'package:swadesic/model/user_rewards_and_invitees_response/user_rewards.dart';
import 'package:swadesic/services/user_rewards_and_invitees_service/user_reward_and_invitees_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
enum RewardTransactionState { Loading, Success, Failed }
class RewardTransactionBloc {
  // region Common Variables
  BuildContext context;
  late RewardTransactionResponse rewardTransactionResponse;

  // endregion


  //region Controller
  final rewardTransactionCtrl = StreamController<RewardTransactionState>.broadcast();
  //endregion

  // region | Constructor |
  RewardTransactionBloc(this.context);
  // endregion

  // region Init
  void init() {
    getTransaction();
  }
// endregion



  //region Get user reward
  Future <void> getTransaction() async {
    rewardTransactionResponse = await UserRewardAndInviteesService().getRewardTransaction();

    //Success
    rewardTransactionCtrl.sink.add(RewardTransactionState.Success);
    return;
    try {
      rewardTransactionResponse = await UserRewardAndInviteesService().getRewardTransaction();

      //Success
      rewardTransactionCtrl.sink.add(RewardTransactionState.Success);


    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted?CommonMethods.toastMessage(error.message!, context):null;
      //Failed
      rewardTransactionCtrl.sink.add(RewardTransactionState.Failed);
      return;
    } catch (error) {
      //Failed
      rewardTransactionCtrl.sink.add(RewardTransactionState.Failed);
    }
  }
  //endregion





//region Dispose
  void dispose(){
    rewardTransactionCtrl.close();
  }
//endregion



}
