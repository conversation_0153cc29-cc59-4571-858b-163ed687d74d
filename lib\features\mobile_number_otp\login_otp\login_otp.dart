import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:swadesic/features/mobile_number_otp/login_otp/login_otp_bloc.dart';
import 'package:swadesic/features/mobile_number_otp/login_otp/resend_count_down/resend_count_down.dart';

import 'package:swadesic/model/login_response/mobile_number_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class LoginOtpScreen extends StatefulWidget {
  // final MobileNumberResponse mobileNumberResponse;
  final String? email;
  final String? googleAccessToken;
  final String phoneNumber;
  final bool isEmailOtp;
  final bool isPhoneOtp;
  final bool isRegisterUser;

  const LoginOtpScreen(
      {super.key,
      this.email,
      required this.phoneNumber,
      this.isEmailOtp = false,
      this.isPhoneOtp = false,
      this.googleAccessToken,
      required this.isRegisterUser});

  @override
  State<LoginOtpScreen> createState() => _LoginOtpScreenState();
}

class _LoginOtpScreenState extends State<LoginOtpScreen> {
  //region Bloc
  late LoginOtpBloc loginOtpBloc;

  //endregion
  //region Init
  @override
  void initState() {
    //Print data
    // print(
    //     "Email is : ${widget.email}, Google access token is : ${widget.googleAccessToken}, Phone number is : ${widget.phoneNumber}, Is email OTP : ${widget.isEmailOtp}, Is phone OTP : ${widget.isPhoneOtp},");
    loginOtpBloc = LoginOtpBloc(context,
        email: widget.email,
        phoneNumber: widget.phoneNumber,
        googleAccessToken: widget.googleAccessToken,
        isEmailOtp: widget.isEmailOtp,
        isPhoneOtp: widget.isPhoneOtp,
        isRegisterUser: widget.isRegisterUser);
    loginOtpBloc.init();
    super.initState();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    loginOtpBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // resizeToAvoidBottomInset: false,

      body: Stack(
        fit: StackFit.expand,
        alignment: Alignment.center,
        children: [
          // Image.asset(
          //   AppImages.onboardingBackground,
          //   fit: BoxFit.fitWidth,
          // ),
          SafeArea(
            child: GestureDetector(
              onTap: () {
                CommonMethods.closeKeyboard(context);
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: kIsWeb ? forWeb() : forApp(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///For web
  //region For web
  Widget forWeb() {
    return SingleChildScrollView(
      child: Column(
        children: [
          //Flag and text
          AppCommonWidgets.flagAndText(
              text: AppStrings.createShopDiscuss, context: context),
          //Email and phone
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Visibility(visible: widget.isPhoneOtp, child: phoneNumberOtp()),
              const SizedBox(height: 30),
              Visibility(visible: widget.isEmailOtp, child: emailOtp()),
            ],
          ),

          SizedBox(
            height: MediaQuery.of(context).size.height / 4,
          ),
          getIn()
        ],
      ),
    );
  }

  //endregion

  ///For app
  //region For app
  Widget forApp() {
    return Stack(
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                //Flag and text
                AppCommonWidgets.flagAndText(
                    text: AppStrings.createShopDiscuss, context: context),
                //Complete verification
                Container(
                    margin: const EdgeInsets.only(bottom: 40),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      AppStrings.completeVerification,
                      style: AppTextStyle.exHeading1(
                          textColor: AppColors.appBlack),
                    )),
                //Email and phone
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Visibility(
                        visible: widget.isPhoneOtp, child: phoneNumberOtp()),
                    const SizedBox(height: 15),
                    Visibility(visible: widget.isEmailOtp, child: emailOtp()),
                  ],
                )
              ],
            ),
          ),
        ),
        //Get in
        Align(alignment: Alignment.bottomCenter, child: getIn())
      ],
    );
  }

  //endregion

  //region Phone OTP
  Widget phoneNumberOtp() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(AppStrings.enterMobileOtp,
            textAlign: TextAlign.start,
            style: AppTextStyle.access0(textColor: AppColors.appBlack)),
        const SizedBox(height: 10),
        // //region Auto fill for app view
        // Visibility(
        //   visible:!kIsWeb,
        //   child: PinFieldAutoFill(
        //     key: Key("login_otp"),
        //     controller: loginOtpBloc.mobileNumberOtpTextCtrl,
        //     decoration: UnderlineDecoration(
        //       lineHeight: 1.3,
        //       colorBuilder: const FixedColorBuilder(AppColors.appBlack), // Customize underline color
        //       textStyle: AppTextStyle.usernameHeading(textColor: AppColors.writingBlack0) // Customize text style
        //     ),
        //     currentCode: loginOtpBloc.mobileNumberOtpTextCtrl.text,
        //     onCodeChanged: (v){
        //       if(v!.length == 6){
        //         CommonMethods.closeKeyboard(context);
        //       }
        //     },
        //     onCodeSubmitted: (value){
        //       loginOtpBloc.mobileNumberOtpTextCtrl.text = value;
        //     },
        //     codeLength: 6, // Specify the length of the PIN or code
        //     cursor: Cursor(color: Colors.orange,width: 1),
        //   ),
        // ),
        // //endregion

        ////////////////////////////////////

        //region Otp filed if web view
        PinCodeTextField(
          controller: loginOtpBloc.mobileNumberOtpTextCtrl,
          length: 6,
          onChanged: (String value) {
            if (value.length == 6) {
              CommonMethods.closeKeyboard(context);
            }
          },
          inputFormatters: <TextInputFormatter>[
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(6),
          ],
          appContext: context,
          autoDismissKeyboard: true,
          animationType: AnimationType.none,
          pinTheme: PinTheme(
            shape: PinCodeFieldShape.box,
            // Use box shape for rounded rectangles
            borderRadius: BorderRadius.circular(10),
            // Rounded corners
            fieldHeight: 60,
            // Set height
            fieldWidth: 50,
            // Set width to be slightly narrower
            activeColor: Colors.transparent,
            // No border when active, matches your design
            inactiveColor: Colors.transparent,
            // No border when inactive
            selectedColor: Colors.transparent,
            // No border when selected
            activeFillColor: AppColors.textFieldFill1,
            // Background color when active
            inactiveFillColor: AppColors.textFieldFill1,
            // Background color when inactive
            selectedFillColor:
                AppColors.textFieldFill1, // Background color when selected
          ),
          cursorColor: AppColors.appBlack,
          backgroundColor: Colors.transparent,
          keyboardType: TextInputType.number,
          enableActiveFill: true,
          // Enables the fill for each box
          textStyle: AppTextStyle.access1(textColor: AppColors.appBlack),
        ),

        // PinCodeTextField(
        //
        //     //auto
        //     controller: loginOtpBloc.mobileNumberOtpTextCtrl,
        //     length: 6,
        //     onChanged: (String value) {
        //       if (value.length == 6) {
        //         CommonMethods.closeKeyboard(context);
        //       }
        //       //mobileNumberOtpBloc.otpTextCtrl.text = value;
        //     },
        //     inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.digitsOnly, LengthLimitingTextInputFormatter(6)],
        //     appContext: context,
        //     autoDismissKeyboard: true,
        //     animationType: AnimationType.none,
        //     pinTheme: PinTheme(
        //       shape: PinCodeFieldShape.underline,
        //       activeColor: AppColors.appBlack,
        //       inactiveColor: AppColors.appBlack,
        //       selectedColor: AppColors.appBlack,
        //       borderWidth: 1,
        //       // fieldHeight: 34,
        //     ),
        //     cursorColor: AppColors.appBlack,
        //     backgroundColor: Colors.transparent,
        //     keyboardType: TextInputType.number,
        //     textStyle: AppTextStyle.usernameHeading(textColor: AppColors.writingBlack0)),

        //endregion
        //Count down
        ResendCountDown(
          isResendMobileOtp: true,
          isFromOnboardingScreen: true,
          onTap: () {
            // For phone OTP
            if (widget.phoneNumber.isNotEmpty) {
              loginOtpBloc.resendOtp(body: {
                "send_otp_to": widget.phoneNumber,
                "is_user_verified": widget.isRegisterUser
              });
            }
          },
        )
      ],
    );
  }

//endregion

  //region Email OTP
  Widget emailOtp() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(AppStrings.enterYourEmailOtp,
            textAlign: TextAlign.start,
            style: AppTextStyle.access0(textColor: AppColors.appBlack)),
        const SizedBox(height: 10),

        //region Auto fill for app view
        // Visibility(
        //   visible:!kIsWeb,
        //   child: PinFieldAutoFill(
        //     key: Key("login_otp"),
        //     controller:  loginOtpBloc.emailOtpTextCtrl,
        //     decoration: UnderlineDecoration(
        //       lineHeight: 1.3,
        //       colorBuilder: const FixedColorBuilder(AppColors.appBlack), // Customize underline color
        //       textStyle:AppTextStyle.usernameHeading(textColor: AppColors.writingBlack0) // Customize text style
        //     ),
        //     currentCode: loginOtpBloc.emailOtpTextCtrl.text,
        //     onCodeChanged: (v){
        //       if(v!.length == 6){
        //         CommonMethods.closeKeyboard(context);
        //       }
        //       // mobileNumberOtpBloc.otpTextCtrl.text = v;
        //       // //print(v);
        //     },
        //     codeLength: 6, // Specify the length of the PIN or code
        //     // onCodeSubmitted: (String code) {
        //     //   // Handle code submission
        //     // },
        //     cursor: Cursor(color: Colors.orange,width: 1),
        //
        //
        //   ),
        // ),
        //endregion

        ////////////////////////////////////

        //region Otp filed if web view
        PinCodeTextField(
          controller: loginOtpBloc.emailOtpTextCtrl,
          length: 6,
          onChanged: (String value) {
            if (value.length == 6) {
              CommonMethods.closeKeyboard(context);
            }
          },
          inputFormatters: <TextInputFormatter>[
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(6),
          ],
          appContext: context,
          autoDismissKeyboard: true,
          animationType: AnimationType.none,
          pinTheme: PinTheme(
            shape: PinCodeFieldShape.box,
            // Use box shape for rounded rectangles
            borderRadius: BorderRadius.circular(10),
            // Rounded corners
            fieldHeight: 60,
            // Set height
            fieldWidth: 50,
            // Set width to be slightly narrower
            activeColor: Colors.transparent,
            // No border when active, matches your design
            inactiveColor: Colors.transparent,
            // No border when inactive
            selectedColor: Colors.transparent,
            // No border when selected
            activeFillColor: AppColors.textFieldFill1,
            // Background color when active
            inactiveFillColor: AppColors.textFieldFill1,
            // Background color when inactive
            selectedFillColor:
                AppColors.textFieldFill1, // Background color when selected
          ),
          cursorColor: AppColors.appBlack,
          backgroundColor: Colors.transparent,
          keyboardType: TextInputType.number,
          enableActiveFill: true,
          // Enables the fill for each box
          textStyle: AppTextStyle.access1(textColor: AppColors.appBlack),
        ),
        // PinCodeTextField(
        //     //auto
        //     controller: loginOtpBloc.emailOtpTextCtrl,
        //     length: 6,
        //     onChanged: (String value) {
        //       if (value.length == 6) {
        //         CommonMethods.closeKeyboard(context);
        //       }
        //     },
        //     inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.digitsOnly, LengthLimitingTextInputFormatter(6)],
        //     appContext: context,
        //     autoDismissKeyboard: true,
        //     animationType: AnimationType.none,
        //     pinTheme: PinTheme(
        //       shape: PinCodeFieldShape.underline,
        //       activeColor: AppColors.appBlack,
        //       inactiveColor: AppColors.appBlack,
        //       selectedColor: AppColors.appBlack,
        //       borderWidth: 1,
        //       // fieldHeight: 34,
        //     ),
        //     cursorColor: AppColors.appBlack,
        //     backgroundColor: Colors.transparent,
        //     keyboardType: TextInputType.number,
        //     textStyle: AppTextStyle.usernameHeading(textColor: AppColors.writingBlack0)),
        //endregion
        //Count down
        ResendCountDown(
            isResendEmailOtp: true,
            isFromOnboardingScreen: true,
            onTap: () {
              // For email OTP
              if (widget.email != null && widget.email!.isNotEmpty) {
                loginOtpBloc.resendOtp(body: {
                  "email": widget.email,
                  "is_user_verified": widget.isRegisterUser
                });
              }
            })
      ],
    );
  }

//endregion

//region Get in
  Widget getIn() {
    return KeyboardVisibilityBuilder(
      builder: (context, isVisible) {
        //If keyboard visible
        if (isVisible) {
          return const SizedBox();
        }
        return Container(
          margin: const EdgeInsets.only(bottom: 30),
          child: SizedBox(
            width: double.infinity,
            child: StreamBuilder<bool>(
              stream: loginOtpBloc.loadingStateCtrl.stream,
              initialData: false,
              builder: (context, snapshot) {
                final bool isLoading = snapshot.data ?? false;
                return CupertinoButton(
                  padding: const EdgeInsets.symmetric(vertical: 13),
                  color: AppColors.brandBlack,
                  borderRadius: BorderRadius.circular(10),
                  onPressed: isLoading ? null : () {
                    Map<String, dynamic> body = {};

                    if (widget.phoneNumber.isNotEmpty) {
                      body["phonenumber"] = widget.phoneNumber;
                    }

                    // Add email to request body if available
                    if (widget.email != null && widget.email!.isNotEmpty) {
                      body["email"] = widget.email;
                      debugPrint(
                          "Added email to verification request: ${widget.email}");
                    }

                    // Add Google access token if available
                    if (widget.googleAccessToken != null &&
                        widget.googleAccessToken!.isNotEmpty) {
                      body["access_token"] = widget.googleAccessToken;
                      debugPrint("Added Google access token to verification request");
                    }

                    // Add email OTP if entered
                    if (loginOtpBloc.emailOtpTextCtrl.text.isNotEmpty) {
                      body["email_otp"] = loginOtpBloc.emailOtpTextCtrl.text;
                      debugPrint(
                          "Added email OTP to verification request: ${loginOtpBloc.emailOtpTextCtrl.text}");
                    }

                    // Add phone OTP if entered
                    if (loginOtpBloc.mobileNumberOtpTextCtrl.text.isNotEmpty) {
                      body["phonenumber_otp"] =
                          loginOtpBloc.mobileNumberOtpTextCtrl.text;
                      debugPrint(
                          "Added phone OTP to verification request: ${loginOtpBloc.mobileNumberOtpTextCtrl.text}");
                    }

                    loginOtpBloc.verifyOtp(body: body);
                  },
                  child: isLoading
                      ? AppCommonWidgets.inlineCircularProgress(size: 24)
                      : Text(
                          (widget.isEmailOtp && widget.isPhoneOtp)
                              ? "Verify OTPs"
                              : "Verify OTP",
                          style: AppTextStyle.access0(textColor: AppColors.appWhite),
                        ),
                );
              },
            ),
          ),
        );
      },
    );
  }
//endregion
}
