
// class SearchScreenCommonWidgets {
//   //region Card and info
//   static Widget searchRgesultCard({
//     required String imageUrl,
//     required String title,
//     required String heading,
//     required String subHeading,
//     required BuildContext context,
//     required String placeHolder,
//
//   }) {
//     return Row(
//       children: [
//         ///Image
//         SizedBox(
//           height: 55,
//           width: 55,
//           child: extendedImage(imageUrl,
//               customPlaceHolder: placeHolder,
//               context, 55, 55,fit: BoxFit.cover),
//         ),
//         horizontalSizedBox(15),
//         ///Detail
//         Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             //Title
//             Container(height: 17,
//             child: appText(title,color:AppColors.appBlack,fontFamily:AppConstants.rRegular,
//             fontWeight: FontWeight.w700,fontSize: 14
//             ),
//             )
//
//           ],
//         )
//
//       ],
//     );
//   }
// //endregion
// }
