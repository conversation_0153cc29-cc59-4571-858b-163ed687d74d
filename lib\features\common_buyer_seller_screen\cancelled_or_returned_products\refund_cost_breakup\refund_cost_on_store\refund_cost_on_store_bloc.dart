import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/refund_amount_calculation_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';



class RefundCostOnStoreBloc {
  // region Common Variables
  BuildContext context;
  final SubOrder subOrder;
  final Order order;
  final List<RefundAmountCalculationDetail> refundAmountCalculationDetailList;


  // endregion

  //region Text Editing Controller

  //endregion


  //region Controller
  final imageCtrl = StreamController<bool>.broadcast();
  //endregion


  // region | Constructor |
  RefundCostOnStoreBloc(this.context, this.subOrder, this.order, this.refundAmountCalculationDetailList,);

  // endregion

  // region Init
  void init() {

  }
// endregion



}
