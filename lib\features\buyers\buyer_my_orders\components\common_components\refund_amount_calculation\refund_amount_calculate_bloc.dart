import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/refund_amount_calculation_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';

enum RefundAmountCalculateState { Loading, Success, Failed, Empty }

class RefundAmountCalculateBloc {
  // region Common Variables
  BuildContext context;
  final List<SubOrder> subOrderList;
  // final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order store;
  late BuyerMyOrderServices buyerMyOrderServices;
  late RefundAmountCalculationResponse refundAmountCalculationResponse;
  bool shoBreakup = false;


  // endregion


  //region Controller
  final refundAmountCalculationStateCtrl = StreamController<RefundAmountCalculateState>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  RefundAmountCalculateBloc(this.context, this.subOrderList, this.store);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    getRefundAmountCalculation();
  }
// endregion


  //region Get refund amount calculation
  getRefundAmountCalculation() async {
    //region Try
    try {

      //Take out sub-order reference
      List<String> subOrderReferenceList = [];
      for(var data in subOrderList){
        subOrderReferenceList.add(data.suborderNumber!);
      }
      //Loading
      refundAmountCalculationStateCtrl.sink.add(RefundAmountCalculateState.Loading);
      // sellerAllOrderCtrl.sink.add(SellerAllOrderState.Loading);
      refundAmountCalculationResponse = await buyerMyOrderServices.refundAmountCalculation(orderNumber: store.orderNumber!,subOrderReferenceList:subOrderReferenceList);
      //Success
      refundAmountCalculationStateCtrl.sink.add(RefundAmountCalculateState.Success);
      // //Filter
      // filtering();

    }
    //endregion
    on ApiErrorResponseMessage {
      refundAmountCalculationStateCtrl.sink.add(RefundAmountCalculateState.Failed);
      // CommonMethods.toastMessage(AppStrings.error, context);
      return;
    } catch (error) {
      refundAmountCalculationStateCtrl.sink.add(RefundAmountCalculateState.Failed);
      // CommonMethods.toastMessage(AppStrings.error, context);
      return;
    }
  }
//endregion


//region On tap show breakup
onTapShowBreakup(){
    //Show/hide
  shoBreakup = !shoBreakup;
  //Refresh success
  refundAmountCalculationStateCtrl.sink.add(RefundAmountCalculateState.Success);

}
//endregion



}
