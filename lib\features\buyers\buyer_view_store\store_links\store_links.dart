import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

import '../../../../model/store_info/store_info.dart';


class StoreLinks extends StatefulWidget {
  final List<Storelinks> storeLinks;
  final BuildContext previousScreenContext;
  // final StoreInfo storeInfo;
  const StoreLinks({Key? key, required this.storeLinks, required this.previousScreenContext}) : super(key: key);

  @override
  State<StoreLinks> createState() => _StoreLinksState();
}

class _StoreLinksState extends State<StoreLinks> {
  @override
  Widget build(BuildContext context) {
    return body();
  }




  //region Body
  Widget body(){
    return linkList();
  }
//endregion


//region Link list
  Widget linkList(){
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      itemCount: widget.storeLinks.length,
        shrinkWrap: true,
        itemBuilder:(context,index){
      return Column(
        children: [
          linkCommon(link: widget.storeLinks[index].storeLink!),
          divider(),
          verticalSizedBox(10)
        ],
      );
    });
  }
  //endregion


//region List of email
  Widget linkCommon({required String link}){
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      alignment: Alignment.centerLeft,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: InkWell(
              onLongPress: (){
                //Copy
                CommonMethods.copyText(context, link);
                //Close bottom sheet
                Navigator.pop(context);
              },
              onTap: (){
                //Close bottom sheet
                Navigator.pop(context);
                //Open web view
                CommonMethods.openAppWebView(webUrl: link, context: widget.previousScreenContext);
              },
              child: Text(link,
              overflow: TextOverflow.ellipsis,
              style:AppTextStyle.access0(textColor: AppColors.appBlack),
              ),
            ),
          ),
          horizontalSizedBox(20),
          SizedBox(
              height: 25,
              width: 25,
              child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: (){
                    Navigator.pop(context);
                    CommonMethods.share(link);
                  },
                  child: SvgPicture.asset(AppImages.externalShareIcon,))),


        ],
      ),
    );
  }
//endregion

}
