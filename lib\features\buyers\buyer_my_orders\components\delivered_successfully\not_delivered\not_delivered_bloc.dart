import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class NotDeliveredBloc {
  // region Common Variables
  BuildContext context;
  bool isProductListDropDownVisible = false;
  final List<SubOrder> subOrderList;
  final Order order;
  final BuyerSubOrderBloc buyerSubOrderBloc;

  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  NotDeliveredBloc(this.context, this.subOrderList, this.order, this.buyerSubOrderBloc);
  // endregion

  // region Init
  void init() {

  }
// endregion


//region On tap Product list Drop down
  void onTapProductList(){
    isProductListDropDownVisible = !isProductListDropDownVisible;
    bottomSheetRefresh.sink.add(true);

  }
//endregion

// region On Tap Speak to seller
  void onTapSpeakToSeller({required SubOrder subOrder}){
    CommonMethods.openBuyerMyOrderDialog(subOrder: subOrder, context: context, heading:"Contact information");
  }
// endregion
}





