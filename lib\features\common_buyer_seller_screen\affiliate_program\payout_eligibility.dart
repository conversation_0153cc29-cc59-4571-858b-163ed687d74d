import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/app_buttons/app_buttons.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class PayOutEligibility extends StatefulWidget {
  @override
  _PayOutEligibilityState createState() => _PayOutEligibilityState();
}

class _PayOutEligibilityState extends State<PayOutEligibility> {

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(20),
      child: ListView(
        shrinkWrap: true,
        // crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Payout eligibility',
            style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            'Payouts are Automatically Sent to Your Connected Primary Account\n\nTo ensure fair rewards and prevent abuse, we’ve set an eligibility requirement for receiving your affiliate payouts:\n',
            style:AppTextStyle.subTitle(textColor: AppColors.appBlack),
            textAlign: TextAlign.left,
          ),

          titleAndSubTitle(title: "• Swadesic Premium Subscription:",subTitle: "Have an active Swadesic Premium subscription during the current payout period (10th to 16th of each month) to receive the previous month’s affiliate earnings."),
          titleAndSubTitle(title: "• Store Activity Alternative:",subTitle: "If you don’t have Swadesic Premium, you’re still eligible if any of your stores has completed 5 qualifying orders (each above ₹500) by the last day of the previous month."),


          Text(
            '\nIf eligibility isn’t met, earnings will reset to zero at the end of each month. But don’t worry—you can continue inviting others and track your potential monthly earnings from their stores as they grow!\n',
            style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
            textAlign: TextAlign.left,
          ),

          AppButtons().button1(
              buttonTextColor: AppColors.appWhite,
              buttonColor: AppColors.appBlack,
              context: context, buttonName: AppStrings.gotIt, onTap: (){
            Navigator.pop(context);
          })
        ],
      ),
    );
  }


  //region Title and sub title
  Widget titleAndSubTitle({required String title,required String subTitle}){
    return Container(
      margin: const EdgeInsets.only(bottom: 20,left: 10),
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: title,
              style: AppTextStyle.access0Strike(textColor: AppColors.appBlack)
            ),
            TextSpan(
              text:
              subTitle,
              style:AppTextStyle.subTitle(textColor: AppColors.appBlack)
            ),

          ],
        ),
      ),
      // child: Column(
      //   mainAxisSize: MainAxisSize.min,
      //   mainAxisAlignment: MainAxisAlignment.center,
      //   crossAxisAlignment: CrossAxisAlignment.start,
      //   children: [
      //     Visibility(
      //       visible: title.isNotEmpty,
      //       child: Text(
      //         title,
      //         style: AppTextStyle.access0Strike(textColor: AppColors.appBlack),
      //         textAlign: TextAlign.left,
      //       ),
      //     ),
      //     Text(
      //       subTitle,
      //       style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
      //       textAlign: TextAlign.left,
      //     )
      //   ],
      // ),
    );
  }
//endregion
}

