import 'product_variant.dart';

class ProductVariantResponse {
  String message;
  List<ProductVariant> productVariants;

  ProductVariantResponse({
    required this.message,
    required this.productVariants,
  });

  ProductVariantResponse.fromJson(Map<String, dynamic> json)
      : message = json['message'] ?? '',
        productVariants = json['product_variants'] != null
            ? List<ProductVariant>.from(
                json['product_variants'].map((x) => ProductVariant.fromJson(x)))
            : [];

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'product_variants': productVariants.map((x) => x.toJson()).toList(),
    };
  }
}

class CreateVariantResponse {
  String message;
  ProductVariant productVariant;

  CreateVariantResponse({
    required this.message,
    required this.productVariant,
  });

  CreateVariantResponse.fromJson(Map<String, dynamic> json)
      : message = json['message'] ?? '',
        productVariant = ProductVariant.fromJson(json['product_variant'] ?? {});

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'product_variant': productVariant.toJson(),
    };
  }
}
