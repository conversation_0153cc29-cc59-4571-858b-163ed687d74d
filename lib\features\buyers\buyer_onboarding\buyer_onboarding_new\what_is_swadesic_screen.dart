import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/role_selection_screen.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/what_is_swadesic_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';

class WhatIsSwadesicScreen extends StatefulWidget {
  final String userReference;
  final Map<String, dynamic> userData;
  final String? icon;

  const WhatIsSwadesicScreen({
    Key? key,
    required this.userReference,
    required this.userData,
    this.icon,
  }) : super(key: key);

  @override
  _WhatIsSwadesicScreenState createState() => _WhatIsSwadesicScreenState();
}

class _WhatIsSwadesicScreenState extends State<WhatIsSwadesicScreen> {
  //region Bloc
  late WhatIsSwadesicBloc whatIsSwadesicBloc;

  //endregion

  //region Init
  @override
  void initState() {
    whatIsSwadesicBloc = WhatIsSwadesicBloc(context);
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: SafeArea(
        child: body(),
      ),
    );
  }

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 40),
            header(),
            const SizedBox(height: 30),
            platformDescription(),
            const SizedBox(height: 30),
            everyPurchaseSupports(),
            const SizedBox(height: 30),
            forStoreOwners(),
            const SizedBox(height: 40),
            swadesicMission(),
            const SizedBox(height: 100),
            areYouIn(),
            const SizedBox(height: 15),
            imInButton(),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header() {
    return Row(
      children: [
        Image.asset(
          AppImages.appIcon,
          height: 37,
          width: 37,
        ),
        const SizedBox(width: 12),
        Text(
          "What is Swadesic?",
          style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
        ),
      ],
    );
  }
  //endregion

  //region Platform Description
  Widget platformDescription() {
    return Text(
      "We are new kind of platform - a place where your shopping actually makes a difference.",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }
  //endregion

  //region Every Purchase Supports
  Widget everyPurchaseSupports() {
    return Text(
      "Every purchase supports India's creators, small businesses, and Bharatiya founders. Explore your interests and get to be part of the growth story of brands you love and Swadeshi Movement.",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }
  //endregion

  //region For Store Owners
  Widget forStoreOwners() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "For Store Owners:",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 8),
        Text(
          "You're not listing products — you're building your brand, your way.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)
              .copyWith(
            fontStyle: FontStyle.italic,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          "With Swadesic, you create your own store, grow your customer community, and tell your story — with full control. And, all at no cost.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 8),
        Text(
          "This is your foundation for long-term success.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
      ],
    );
  }
  //endregion

  //region Swadesic Mission
  Widget swadesicMission() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Swadesic Mission",
          style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 16),
        Text(
          "Swadesic's mission is to accelerate Bharat's Growth Story by empowering Swadeshi businesses through a strong community—and expanding the Swadeshi economy.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
      ],
    );
  }
  //endregion

  //region Are You In
  Widget areYouIn() {
    return Text(
      "Are you in?",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }
  //endregion

  //region I'm In Button
  Widget imInButton() {
    return SizedBox(
      width: double.infinity,
      child: CupertinoButton(
        borderRadius: BorderRadius.circular(11),
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        color: AppColors.appBlack,
        child: Text(
          "I'm in",
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyle.access0(textColor: AppColors.appWhite),
        ),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => RoleSelectionScreen(
                userReference: widget.userReference,
                userData: widget.userData,
                icon: widget.icon,
              ),
            ),
          );
        },
      ),
    );
  }
  //endregion
}
