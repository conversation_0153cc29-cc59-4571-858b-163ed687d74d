import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_history_screen_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/buyer_search_history_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/product_history/product_history_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/buyer_search_response/buyer_search_history_response.dart';
import 'package:swadesic/util/app_images.dart';

class ProductHistory extends StatefulWidget {
  final List<History> productHistoryList;
  final BuyerSearchHistoryBloc buyerSearchHistoryBloc;
  final bool isAllView;



  const ProductHistory({Key? key, required this.productHistoryList, required this.buyerSearchHistoryBloc, required this.isAllView}) : super(key: key);

  @override
  State<ProductHistory> createState() => _ProductHistoryState();
}

class _ProductHistoryState extends State<ProductHistory> {
  //region Bloc
  late ProductHistoryBloc productHistoryBloc;

  //endregion
  //region Init
  @override
  void initState() {
    productHistoryBloc = ProductHistoryBloc(context, widget.productHistoryList,widget.buyerSearchHistoryBloc);
    productHistoryBloc.init();
    // TODO: implement initState
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: productHistoryBloc.refreshCtrl.stream,
        builder: (context, snapshot) {
          ///If there is no product then
          if(widget.productHistoryList.isEmpty){
            return const SizedBox();
          }
          return body();
        }
    );
  }

  //region body
  Widget body() {
    return Column(
      children: [
        // BuyerSearchCommonWidgets.title(data: "Products"),
        Visibility(
          visible: widget.isAllView,
          child: Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 15),
              child: ListView.builder(
                  shrinkWrap: true,
                  // physics: const NeverScrollableScrollPhysics(),
                  itemCount:widget.isAllView?widget.productHistoryList.length:widget.productHistoryList.length>3?productHistoryBloc.itemCount:widget.productHistoryList.length,
                  itemBuilder: (context, index) {
                    return BuyerSearchCommonWidgets.searchedDataCard(
                        customImageContainerType: CustomImageContainerType.product,
                      isProduct: true,
                        placeHolder: AppImages.productPlaceHolder,
                        context: context,
                        heading: "${widget.productHistoryList[index].handle!} ${widget.productHistoryList[index].name!}",
                        imageUrl: widget.productHistoryList[index].icon,
                        // title:widget.productHistoryList[index].handle!,
                        // subTitle: widget.productHistoryList[index].location!,
                        isCrossVisible: true,
                        onPressCross: (){
                          productHistoryBloc.onPressCross(history: widget.productHistoryList[index]);
                        }, onTapCard: (){
                      widget.buyerSearchHistoryBloc.goToSingleProductScreen(productReference: widget.productHistoryList[index].searchItem!);
                    }
                    );
                  }),
            ),
          ),),
        Visibility(
          visible: !widget.isAllView,
          child: Padding(
            padding: const EdgeInsets.only(left: 15),
          child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount:widget.isAllView?widget.productHistoryList.length:widget.productHistoryList.length>3?productHistoryBloc.itemCount:widget.productHistoryList.length,
              itemBuilder: (context, index) {
                return BuyerSearchCommonWidgets.searchedDataCard(
                    customImageContainerType: CustomImageContainerType.product,

                    isProduct: true,
                    placeHolder: AppImages.productPlaceHolder,
                    context: context,
                    heading: "${widget.productHistoryList[index].handle!} ${widget.productHistoryList[index].name!}",
                    imageUrl: widget.productHistoryList[index].icon,
                    // title:widget.productHistoryList[index].handle!,
                    // subTitle: widget.productHistoryList[index].location!,
                    isCrossVisible: true,
                    onPressCross: (){
                      productHistoryBloc.onPressCross(history: widget.productHistoryList[index]);
                    }, onTapCard: (){
                  widget.buyerSearchHistoryBloc.goToSingleProductScreen(productReference: widget.productHistoryList[index].searchItem!);
                }
                );
              }),
        ),),
        InkWell(
          onTap: (){
            productHistoryBloc.onTapViewMore(isIncrease: true);
          },
          child: Visibility(
              visible: widget.isAllView?false:widget.productHistoryList.length > productHistoryBloc.itemCount,

              child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: "View more products",isUnderline: true)),
        ),
      ],
    );
  }
//endregion
}
