import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/user_reward_and_invitees/user_reward/user_reward_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/your_invitees/your_invitees.dart';
import 'package:swadesic/features/widgets/app_left_align_tab_bar/app_left_align_tab_bar.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class UserRewardAndInviteesScreen extends StatefulWidget {
  final int selectedTab;
  const UserRewardAndInviteesScreen({super.key,  this.selectedTab = 0});
  @override
  State<UserRewardAndInviteesScreen> createState() => _UserRewardAndInviteesScreenState();
}

class _UserRewardAndInviteesScreenState extends State<UserRewardAndInviteesScreen>with SingleTickerProviderStateMixin {
  //Tab controller
  late TabController tabController;

  //region Init
  @override
  void initState() {
    tabController = TabController(length:2, vsync: this);
    tabController.animateTo(widget.selectedTab);
    super.initState();
  }
  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      body: YourInvitees(),
    );
  }


  //region Body
Widget body(){
    return TabBarView(
      controller: tabController,
      children: [
        AffiliateProgramScreen(),
        UserRewardScreen(),
        YourInvitees()
      ]
    );
}
//endregion


  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title:AppStrings.myInvitees,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

//endregion


  //region Appbar
  // AppLeftAlignTabBar appBar() {
  //   return AppLeftAlignTabBar(
  //     title: "",
  //     tabController: tabController,
  //     tabs: [
  //       // AppStrings.reward,
  //
  //       AppStrings.affiliateProgram,
  //       AppStrings.myInvitees,
  //     ],
  //   );
  // }
//endregion

}
