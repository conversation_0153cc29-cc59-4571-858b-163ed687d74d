
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_bloc.dart';


class CommentListBloc {
  // region Common Variables
  BuildContext context;
  int initialIndex = 0;
  final int? selectedCommentId;
  final BuyerProductCommentBloc buyerProductCommentBloc;
  bool isCommentBackgroundColorVisible = false;
  late Timer timer;




  // endregion

  //region Controller
  final commentRefreshCtrl = StreamController<bool>.broadcast();

  //endregion


  // region | Constructor |
  CommentListBloc(this.context, this.buyerProductCommentBloc, this.selectedCommentId);

  // endregion

  // region Init
  init() {
    findIndexFromSelectedComment();
  }
// endregion


//region Find the index from selected comment id
findIndexFromSelectedComment(){
    //If selected comment is null
  if(selectedCommentId==null){
    return;
  }
    for(int i = 0; i <buyerProductCommentBloc.filteredComments.length; i++ ){
     //Root comment
      if(selectedCommentId == buyerProductCommentBloc.filteredComments[i].commentid){
        initialIndex=i;
        //Make comment background color
        isCommentBackgroundColorVisible = true;
        //Count down
        countDown();
        //refresh
        commentRefreshCtrl.sink.add(true);
        return;

      }


    }


}
//endregion


  //region Count down
  void countDown(){
    timer = Timer.periodic(
        const Duration(seconds:6), (Timer timer) {
     //Remove comment background color
      isCommentBackgroundColorVisible = false;
      //refresh
      commentRefreshCtrl.sink.add(true);
    });
  }
//endregion

//region Dispose
void dispose(){
  commentRefreshCtrl.close();
  timer.cancel();
}
//endregion

}
