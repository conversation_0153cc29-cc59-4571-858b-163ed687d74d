import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/seller_all_order_response/tracking_detail_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';


class TrackPackageBloc {
  // region Common Variables
  BuildContext context;
  late BuyerMyOrderServices buyerMyOrderServices;
  late TrackingDetailResponse trackingDetailResponse;
  bool isProductListVisible = true;

  // late TrackingDetailResponse trackingDetailResponse;
  final List<SubOrder> subOrderList;

  bool isProductListDropDownVisible = false;


  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  TrackPackageBloc(this.context,this.subOrderList);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    getTrackingDetail(packageNumber: subOrderList.first.packageNumber!);
  }
// endregion

  //region On tap product list
  void onTapProductList() {
    isProductListVisible = !isProductListVisible;
    bottomSheetRefresh.sink.add(true);
  }
//endregion




//
//   //region Get tracking detail
//   getTrackingDetail({required String packageNumber}) async {
//     //region Try
//     try {
//       //sellerAllOrderState.sink.add(SellerAllOrderState.Loading);
//       trackingDetailResponse =  await buyerMyOrderServices.getTrackingDetail(packageNumber: packageNumber);
//
//     }
//     //endregion
//      on ApiErrorResponseMessage catch (error) {
//       CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
//       return;
//
//       var snackBar = SnackBar(content: Text(error.message.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     } catch (error) {
//       //print(error);
//       var snackBar = SnackBar(content: Text(error.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//   }
// //endregion



  //region Get tracking detail
  getTrackingDetail({required String packageNumber}) async {
    //region Try
    try {
      trackingDetailResponse =  await buyerMyOrderServices.getTrackingDetail(packageNumber: packageNumber);

    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }
//endregion

}
