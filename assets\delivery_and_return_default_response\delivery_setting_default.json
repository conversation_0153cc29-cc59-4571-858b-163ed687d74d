{"message": "success", "data": {"deliverysettingid": null, "store_reference": null, "storeid": null, "is_default_settings": true, "productid": null, "product_reference": null, "deliverymethod_whitelabel": false, "deliverymethod_logistics": true, "deliverymethod_self": false, "delivery_locations": "", "selected_location_count": 0, "deliveryfeetype_all_free": true, "deliveryfeetype_standard": false, "deliveryfeetype_distance": false, "deliveryfee_value": null, "deliveryfee_valuetype": "per product", "distance_based_max_deliveryfee": null, "no_deliveryfee_maxvalue": null, "no_deliveryfee_maxvalue_enabled": false, "no_deliveryfee_products": null, "no_deliveryfee_products_enabled": false, "no_deliveryfee_samepincode_enabled": false, "no_deliveryfee_samecity_enablded": false, "delivery_personal_name": null, "default_logistic_partner": null, "delivery_personal_phone": null, "created_by": null, "modified_by": null, "time_to_prepare": null, "time_to_deliver": null, "delivery_settings_version": null, "is_pan_exists": false, "is_gst_exists": false, "trustcenter_location_state": "trustcenter_location_state_value", "fulfillment_options": "", "pickup_location_ids": []}}