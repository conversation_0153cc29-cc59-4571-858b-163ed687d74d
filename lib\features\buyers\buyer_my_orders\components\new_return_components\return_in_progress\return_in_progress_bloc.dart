import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_in_progress/widget/return_pickup_details/return_pickup_details.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_in_progress/widget/return_tracking_details/return_tracking_details.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/seller_all_order_service/seller_all_order_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ReturnInProgressBloc {
  // region Common Variables
  BuildContext context;
  late SellerAllOrderServices sellerAllOrderServices;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  final List<SubOrder> subOrderList;
  List<String> groupNameList = [];


  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  final checkBoxCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  final returnPersonNameCtrl = TextEditingController();
  final returnPersonContactCtrl = TextEditingController();
  final returnTrackingLinkCtrl = TextEditingController();
  final dateCtrl = TextEditingController();
  final additionalReturnNotesCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  ReturnInProgressBloc(this.context, this.buyerSubOrderBloc, this.order, this.subOrderList);

  // endregion

  // region Init
  void init() {
    sellerAllOrderServices = SellerAllOrderServices();
    takeOutDisplayPackageNumbers();
  }

// endregion



  //region Take out Display package number
  void takeOutDisplayPackageNumbers(){
    groupNameList.clear();
    for(var data in subOrderList){
      groupNameList.add(data.displayReturnPackageNumber!);
    }
    ///Clear duplicates
    groupNameList=groupNameList.toSet().toList();
  }
  //endregion

  //region On tap return status (read-only for Swadesic shipping)
  void onTapReturnStatus({required List<SubOrder> groupedSubOrders,}) {
    // For Swadesic shipping, show a read-only view of the return status
    CommonMethods.toastMessage(
      "This return is being handled by Swadesic shipping. Status updates are managed by the logistics team.",
      context,
    );

    // Alternatively, you could show a read-only bottom sheet with status information
    // but for now we'll just show a toast message
  }
  //endregion



  //region On tap return tracking detail
  void onTapReturnTrackingDetail({required List<SubOrder> groupedSubOrders}) async {
    // Check if return is by Swadesic shipping
    bool isSwadesicShipping = !(groupedSubOrders.first.selfReturnByStore ?? false) &&
                             !(groupedSubOrders.first.returnByLogisticPartner ?? false);

    if (isSwadesicShipping) {
      try {
        // For Swadesic shipping, show the pickup details in read-only mode
        if (context.mounted) {
          CommonMethods.appBottomSheet(
            screen: ReturnPickupDetail(
              suborderList: groupedSubOrders,
              buyerSubOrderBloc: buyerSubOrderBloc,
              order: order,
              packageNumber: groupedSubOrders.first.returnPackageNumber!,
            ),
            context: context,
            bottomSheetName: AppStrings.returnTrackingDetail,
          ).then((value) {
            //Unselect all
            CommonMethods.subOrderSelectUnSelectAll(isSelectAll: false, subOrderList: subOrderList);
            //Refresh sub order screen.
            buyerSubOrderBloc.buyerSubOrderStateCtrl.sink.add(BuyerSubOrderState.Success);
          });
        }
      } catch (e) {
        if (context.mounted) {
          CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
        }
      }
    } else {
      // For self return or logistics partner
      CommonMethods.appBottomSheet(
        screen: ReturnTrackingDetail(
          suborderList: groupedSubOrders,
          buyerSubOrderBloc: buyerSubOrderBloc,
          isSeller: true,
          order: order,
        ),
        context: context,
        bottomSheetName: AppStrings.returnTrackingDetail,
      ).then((value) {
        //Unselect all
        CommonMethods.subOrderSelectUnSelectAll(isSelectAll: false, subOrderList: subOrderList);
        //Refresh sub order screen.
        buyerSubOrderBloc.buyerSubOrderStateCtrl.sink.add(BuyerSubOrderState.Success);
      });
    }
  }
  //endregion


//region Remove from return group
  deleteFromReturnGroup({required String subOrderNumber, required String returnPackageNumber, required String groupName}) async {
    try {
      await sellerAllOrderServices.returnRemoveSubOrderFromGroup(subOrderNumbers: [subOrderNumber], packageNumber: returnPackageNumber);
      if (context.mounted) {
        //Message
        CommonMethods.toastMessage(AppStrings.theSubOrderIsRemoved, context);
      }
      //Call the get sub order api
      buyerSubOrderBloc.getSubOrders();
    }
    on ApiErrorResponseMessage {
      //Failed state
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    }
  }
//endregion

  //region Open tap remove from return group
  Future onTapRemoveFromReturnGroup({required SubOrder subOrder}){
    return CommonMethods.appDialogBox(
        context: context,
        widget: SaveOrDiscard(
          firstButtonName: AppStrings.yes,
          secondButtonName: AppStrings.no,
          onTapSave:(value)async{
          deleteFromReturnGroup(
              returnPackageNumber: subOrder.returnPackageNumber!,
              subOrderNumber: subOrder.suborderNumber!,
              groupName: subOrder.displayReturnPackageNumber!
          );
        },
        previousScreenContext: context,
        isMessageVisible: true,
        message: AppStrings.areYouSureWantToUnGroup,
        popPreviousScreen: false,
        )
    );
  }
//endregion

}
