import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/shopping_cart/cart_delivery_address/add_edit_cart_address/add_cart_address_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/cart_delivery_address/cart_delivery_address_bloc.dart';
import 'package:swadesic/model/shopping_cart_address_response/shopping_cart_address_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class AddEditCartAddress extends StatefulWidget {
  final ShoppingCartAddress? shoppingCartAddress;
  final bool isAddAddress;
  final CartDeliveryAddressBloc cartDeliveryAddressBloc;

  const AddEditCartAddress(
      {super.key,
      this.shoppingCartAddress,
      required this.isAddAddress,
      required this.cartDeliveryAddressBloc});

  @override
  State<AddEditCartAddress> createState() => _AddEditCartAddressState();
}

class _AddEditCartAddressState extends State<AddEditCartAddress> {
  //region Bloc
  late AddEditCartAddressBloc addEditCartAddressBloc;

  //endregion

  //region Init
  @override
  void initState() {
    addEditCartAddressBloc = AddEditCartAddressBloc(
        context, widget.cartDeliveryAddressBloc, widget.shoppingCartAddress);
    addEditCartAddressBloc.init();
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: addEditCartAddressBloc.refreshCtrl.stream,
        builder: (context, snapshot) {
          return SingleChildScrollView(
            child: Column(
              children: [addAddressFields(), editAddressFields()],
            ),
          );
        });
  }

//endregion

  ///Add
  //region Add address fields
  Widget addAddressFields() {
    return Visibility(
      visible: widget.isAddAddress,
      child: Container(
        margin: const EdgeInsets.all(15),
        child: Column(
          children: [
            //Add and edit fields
            addAndEditFields(),
            const SizedBox(
              height: 10,
            ),
            //Action buttons
            Row(
              children: [
                Expanded(
                  child: CupertinoButton(
                    onPressed: () {
                      addEditCartAddressBloc.cartDeliveryAddressBloc
                          .addAddressApiCall(
                        firstName:
                            addEditCartAddressBloc.firstNameTextCtrl.text,
                        phoneNumber:
                            addEditCartAddressBloc.phoneNumberTextCtrl.text,
                        address: addEditCartAddressBloc.addressTextCtrl.text,
                        city: addEditCartAddressBloc.cityTextCtrl.text,
                        pinCode: addEditCartAddressBloc.pinCodeTextCtrl.text,
                        state: addEditCartAddressBloc.stateTextCtrl.text,
                      );
                      // addEditCartAddressBloc.addAddressApiCall();
                    },
                    padding: EdgeInsets.zero,
                    child: Container(
                        decoration: const BoxDecoration(
                            color: AppColors.brandBlack,
                            borderRadius:
                                BorderRadius.all(Radius.circular(10))),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 10),
                        child: Center(
                          child: Text(
                            AppStrings.done,
                            style: AppTextStyle.access0(
                                textColor: AppColors.appWhite),
                          ),
                        )),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  //endregion

  ///Edit
  //region Edit address fields
  Widget editAddressFields() {
    return Visibility(
      visible: !widget.isAddAddress,
      child: Container(
        margin: const EdgeInsets.all(15),
        child: Column(
          children: [
            addAndEditFields(),
            const SizedBox(
              height: 10,
            ),
            //Action buttons
            Row(
              children: [
                Expanded(
                  child: CupertinoButton(
                    onPressed: () {
                      addEditCartAddressBloc.cartDeliveryAddressBloc
                          .editAddressApiCall(
                        firstName:
                            addEditCartAddressBloc.firstNameTextCtrl.text,
                        phoneNumber:
                            addEditCartAddressBloc.phoneNumberTextCtrl.text,
                        address: addEditCartAddressBloc.addressTextCtrl.text,
                        city: addEditCartAddressBloc.cityTextCtrl.text,
                        pinCode: addEditCartAddressBloc.pinCodeTextCtrl.text,
                        state: addEditCartAddressBloc.stateTextCtrl.text,
                        addressId: addEditCartAddressBloc
                            .shoppingCartAddress!.useraddressid!,
                      );
                      // addEditCartAddressBloc.addAddressApiCall();
                    },
                    padding: EdgeInsets.zero,
                    child: Container(
                        decoration: const BoxDecoration(
                            color: AppColors.brandBlack,
                            borderRadius:
                                BorderRadius.all(Radius.circular(10))),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 10),
                        child: Center(
                          child: Text(
                            AppStrings.done,
                            style: AppTextStyle.access0(
                                textColor: AppColors.appWhite),
                          ),
                        )),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  //endregion

  //region Add and edit fields
  Widget addAndEditFields() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTextFields.onlyStringWithSpaceTextField(
          context: context,
          textEditingController: addEditCartAddressBloc.firstNameTextCtrl,
          hintText: AppStrings.name,
        ),
        verticalSizedBox(10),
        AppTextFields.mobileNumberTextField(
          context: context,
          textEditingController: addEditCartAddressBloc.phoneNumberTextCtrl,
          hintText: AppStrings.phoneNumber,
        ),
        verticalSizedBox(10),
        //Address
        AppTextFields.allTextField(
          minLines: 5,
          context: context,
          maxEntry: 200,
          maxLines: 10,
          textEditingController: addEditCartAddressBloc.addressTextCtrl,
          hintText: AppStrings.address,
        ),
        verticalSizedBox(10),
        //City and pin code
        Row(
          children: [
            Expanded(
              // child: AppTextFields.onlyStringWithSpaceTextField(
              //   context: context,
              //   textEditingController: addEditCartAddressBloc.cityTextCtrl,
              //   hintText: AppStrings.city,
              // ),
              child: AppCommonWidgets.dropDownOptions(
                  onTap: () {
                    addEditCartAddressBloc.onTapCity();
                  },
                  context: context,
                  hintText: AppStrings.city,
                  value: addEditCartAddressBloc.cityTextCtrl.text),
            ),
            horizontalSizedBox(10),
            Expanded(
                child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppTextFields.onlyNumberTextField(
                  onChanged: (value) {
                    //print(value);
                  },
                  maxEntry: 6,
                  context: context,
                  textEditingController: addEditCartAddressBloc.pinCodeTextCtrl,
                  hintText: AppStrings.pinCode,
                ),
                // Text(AppStrings.noAddressIsSaved,
                //   style: AppTextStyle.smallText(textColor: AppColors.red),
                // )
              ],
            )),
          ],
        ),
        verticalSizedBox(10),
        AppCommonWidgets.dropDownOptions(
            onTap: () {
              addEditCartAddressBloc.onTapState();
            },
            context: context,
            hintText: AppStrings.state,
            value: addEditCartAddressBloc.stateTextCtrl.text)
        // AppTextFields.onlyStringWithSpaceTextField(
        //   context: context,
        //   textEditingController: addEditCartAddressBloc.stateTextCtrl,
        //   hintText: AppStrings.state,
        // ),
      ],
    );
  }
//endregion
}
