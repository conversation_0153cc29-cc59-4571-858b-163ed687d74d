PODS:
  - app_settings (5.1.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    -
    ReachabilitySwift
  - contacts_service (0.2.2):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.4):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.4)
  - DKImagePickerController/PhotoGallery (4.3.4):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.4)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - emoji_picker_flutter (0.0.1):
    - Flutter
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/Analytics (10.18.0):
    - Firebase/Core
  - Firebase/Core (10.18.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.18.0)
  - Firebase/CoreOnly (10.18.0):
    - FirebaseCore (= 10.18.0)
  - Firebase/Messaging (10.18.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.18.0)
  - firebase_analytics (10.7.0):
    - Firebase/Analytics (= 10.18.0)
    - firebase_core
    - Flutter
  - firebase_core (2.24.2):
    - Firebase/CoreOnly (= 10.18.0)
    - Flutter
  - firebase_messaging (14.7.4):
    - Firebase/Messaging (= 10.18.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (10.18.0):
    - FirebaseAnalytics/AdIdSupport (= 10.18.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.18.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.18.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (10.18.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.19.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.19.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.18.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview (0.0.1):
    - Flutter
    - flutter_inappwebview/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - GoogleAppMeasurement (10.18.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.18.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.18.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.18.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.18.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.3.0):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.12.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.12.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.12.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.12.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.12.0)"
  - GoogleUtilities/Reachability (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.12.0):
    - GoogleUtilities/Logger
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker_ios (0.0.1):
    - Flutter
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - open_file (0.0.1):
    - Flutter
  - OrderedSet (5.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - PromisesObjC (2.3.1)
  - ReachabilitySwift (5.0.0)
  - SDWebImage (5.18.6):
    - SDWebImage/Core (= 5.18.6)
  - SDWebImage/Core (5.18.6)
  - SDWebImageWebPCoder (0.14.2):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sms_autofill (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - SwiftyGif (5.4.4)
  - Toast (4.0.0)
  - TOCropViewController (2.6.1)
  - uni_links (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - contacts_service (from `.symlinks/plugins/contacts_service/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - emoji_picker_flutter (from `.symlinks/plugins/emoji_picker_flutter/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_inappwebview (from `.symlinks/plugins/flutter_inappwebview/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - open_file (from `.symlinks/plugins/open_file/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sms_autofill (from `.symlinks/plugins/sms_autofill/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FMDB
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - libwebp
    - Mantle
    - nanopb
    - OrderedSet
    - PromisesObjC
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif
    - Toast
    - TOCropViewController

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  contacts_service:
    :path: ".symlinks/plugins/contacts_service/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  emoji_picker_flutter:
    :path: ".symlinks/plugins/emoji_picker_flutter/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_inappwebview:
    :path: ".symlinks/plugins/flutter_inappwebview/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  open_file:
    :path: ".symlinks/plugins/open_file/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sms_autofill:
    :path: ".symlinks/plugins/sms_autofill/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  app_settings: 017320c6a680cdc94c799949d95b84cb69389ebc
  connectivity_plus: 07c49e96d7fc92bc9920617b83238c4d178b446a
  contacts_service: 849e1f84281804c8bfbec1b4c3eedcb23c5d3eca
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  DKImagePickerController: b512c28220a2b8ac7419f21c491fc8534b7601ac
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  emoji_picker_flutter: df19dac03a2b39ac667dc8d1da939ef3a9e21347
  file_picker: ce3938a0df3cc1ef404671531facef740d03f920
  Firebase: 414ad272f8d02dfbf12662a9d43f4bba9bec2a06
  firebase_analytics: e16a84055b26f7b03c3621dae3d91b528ca47a46
  firebase_core: 0af4a2b24f62071f9bf283691c0ee41556dcb3f5
  firebase_messaging: d22721fcb70a1e1ec228bb039c9e9cf1ec462941
  FirebaseAnalytics: 4d310b35c48eaa4a058ddc04bdca6bdb5dc0fe80
  FirebaseCore: 2322423314d92f946219c8791674d2f3345b598f
  FirebaseCoreInternal: b444828ea7cfd594fca83046b95db98a2be4f290
  FirebaseInstallations: 033d199474164db20c8350736842a94fe717b960
  FirebaseMessaging: 9bc34a98d2e0237e1b121915120d4d48ddcf301e
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_image_compress_common: ec1d45c362c9d30a3f6a0426c297f47c52007e3e
  flutter_inappwebview: 3d32228f1304635e7c028b0d4252937730bbc6cf
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  fluttertoast: 31b00dabfa7fb7bacd9e7dbee580d7a2ff4bf265
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  geocoding_ios: a389ea40f6f548de6e63006a2e31bf66ff80769a
  geolocator_apple: cc556e6844d508c95df1e87e3ea6fa4e58c50401
  GoogleAppMeasurement: 70ce9aa438cff1cfb31ea3e660bcc67734cb716e
  GoogleDataTransport: 57c22343ab29bc686febbf7cbb13bad167c2d8fe
  GoogleUtilities: 0759d1a57ebb953965c2dfe0ba4c82e95ccc2e34
  image_cropper: 60c2789d1f1a78c873235d4319ca0c34a69f2d98
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  open_file: 02eb5cb6b21264bd3a696876f5afbfb7ca4f4b7d
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  package_info_plus: fd030dabf36271f146f1f3beacd48f564b0f17f7
  path_provider_foundation: c68054786f1b4f3343858c1e1d0caaded73f0be9
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  SDWebImage: 3d8caa2430f3d674dbb3e515df4a100a2105af5f
  SDWebImageWebPCoder: 633b813fca24f1de5e076bcd7f720c038b23892b
  share_plus: 056a1e8ac890df3e33cb503afffaf1e9b4fbae68
  shared_preferences_foundation: 986fc17f3d3251412d18b0265f9c64113a8c2472
  sms_autofill: c461043483362c3f1709ee76eaae6eb570b31686
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  SwiftyGif: 93a1cc87bf3a51916001cf8f3d63835fb64c819f
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  uni_links: d97da20c7701486ba192624d99bffaaffcfc298a
  url_launcher_ios: 08a3dfac5fb39e8759aeb0abbd5d9480f30fc8b4
  webview_flutter_wkwebview: b7e70ef1ddded7e69c796c7390ee74180182971f

PODFILE CHECKSUM: 83952432b78be1d782e5ee864084c7be3e597c4a

COCOAPODS: 1.14.3
