import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_history_screen_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/buyer_search_history_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/people_history/people_history.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/product_history/product_history.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/searched_keyword/searcted_keyword.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/store_history/store_history.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Buyer Search history
class BuyerSearchHistory extends StatefulWidget {
  final BuyerSearchBloc buyerSearchBloc;
  const BuyerSearchHistory({Key? key, required this.buyerSearchBloc})
      : super(key: key);

  @override
  State<BuyerSearchHistory> createState() => _BuyerSearchHistoryState();
}
//endregion

class _BuyerSearchHistoryState extends State<BuyerSearchHistory>
    with
        SingleTickerProviderStateMixin,
        AutomaticKeepAliveClientMixin<BuyerSearchHistory> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //region Bloc and tab controller
  late BuyerSearchHistoryBloc buyerSearchHistoryBloc;
  late TabController tabController = TabController(
      length: 4,
      vsync: this,
      initialIndex: BuyerSearchBloc.searchScreenSelectedTab,
      animationDuration: const Duration(milliseconds: 0));
  //endregion

  //region Init
  @override
  void initState() {
    buyerSearchHistoryBloc = BuyerSearchHistoryBloc(context, tabController);
    buyerSearchHistoryBloc.init();
    // TODO: implement initState
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    buyerSearchHistoryBloc.dispose();

    // TODO: implement initState
    super.dispose();
  }
  //endregion

  //region

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 4,
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        body: Column(
          children: [
            filterTab(),
            Expanded(
              child: StreamBuilder<BuyerSearchHistoryState>(
                  stream: buyerSearchHistoryBloc.buyerSearchHistoryCtrl.stream,
                  initialData: BuyerSearchHistoryState.Loading,
                  builder: (context, snapshot) {
                    if (snapshot.data == BuyerSearchHistoryState.Success) {
                      return body();
                    }
                    if (snapshot.data == BuyerSearchHistoryState.Empty) {
                      return noHistory();
                      // return Center(child:AppCommonWidgets.emptyResponseText(emptyMessage:AppStrings.historyCurrentlyNotAvailable),);
                    }
                    if (snapshot.data == BuyerSearchHistoryState.Loading) {
                      return verticalSizedBox(10);
                    }

                    return error();
                  }),
            ),
          ],
        ),
      ),
    );
  }

  //region Body
  Widget body() {
    // return tavView();
    return StreamBuilder<bool>(
        stream: buyerSearchHistoryBloc.tabViewRefreshCtrl.stream,
        builder: (context, snapshot) {
          return TabBarView(
            controller: buyerSearchHistoryBloc
                .tabController, // Assign the TabController to the TabBarView
            children: [
              // allTabView(),
              noHistory(),
              storeTabView(),
              productTabView(),
              peopleTabView(),
            ],
          );
        });
  }

//endregion

  //region Filter Tab
  Widget filterTab() {
    return StreamBuilder<BuyerSearchState>(
      stream: widget.buyerSearchBloc.buyerSearchCtrl.stream,
      builder: (context, snapshot) {
        // Only show filter tab if we're not in initial or history state
        final showFilter = snapshot.data == BuyerSearchState.Success || 
                          snapshot.data == BuyerSearchState.Loading ||
                          snapshot.data == BuyerSearchState.Failed;
                          
        if (!showFilter) {
          return const SizedBox.shrink();
        }
        
        return StreamBuilder<bool>(
          stream: buyerSearchHistoryBloc.tabViewRefreshCtrl.stream,
          builder: (context, snapshot) {
            return Padding(
              padding: const EdgeInsets.only(left: 15, right: 15, bottom: 15),
              child: Row(
                children: [
                  BuyerSearchCommonWidgets.options(
                      context: context,
                      text: AppStrings.posts,
                      onPress: () {
                        buyerSearchHistoryBloc.onSelectOptions(index: 0);
                      },
                      isSelected: BuyerSearchBloc.searchScreenSelectedTab == 0),

                  BuyerSearchCommonWidgets.options(
                      context: context,
                      text: AppStrings.stores,
                      onPress: () {
                        buyerSearchHistoryBloc.onSelectOptions(index: 1);
                      },
                      isSelected: BuyerSearchBloc.searchScreenSelectedTab == 1),

                  BuyerSearchCommonWidgets.options(
                      context: context,
                      text: AppStrings.products,
                      onPress: () {
                        buyerSearchHistoryBloc.onSelectOptions(index: 2);
                      },
                      isSelected: BuyerSearchBloc.searchScreenSelectedTab == 2),

                  BuyerSearchCommonWidgets.options(
                      context: context,
                      text: AppStrings.people,
                      onPress: () {
                        buyerSearchHistoryBloc.onSelectOptions(index: 3);
                      },
                      isSelected: BuyerSearchBloc.searchScreenSelectedTab == 3),
                ],
              ),
            );
          });
      },
    );
  }

//endregion

  //region Tab view
  // Widget tavView(){
  //
  //   if(buyerSearchHistoryBloc.tabController.index==0){
  //     //Error
  //     if(buyerSearchHistoryBloc.buyerSearchHistoryResponse.message==null){
  //       return  error();
  //     }
  //     //Empty all
  //     if(buyerSearchHistoryBloc.buyerSearchHistoryResponse.recentSearches!.isEmpty && buyerSearchHistoryBloc.buyerSearchHistoryResponse.store!.isEmpty &&
  //         buyerSearchHistoryBloc.buyerSearchHistoryResponse.product!.isEmpty && buyerSearchHistoryBloc.buyerSearchHistoryResponse.user!.isEmpty
  //     ){
  //       return noHistory();
  //
  //     }
  //     return  SingleChildScrollView(
  //       child: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         // shrinkWrap: true,
  //         children: [
  //           recentClearAll(),
  //           searchedText(),
  //           store(isAllView: false),
  //           product(isAllView: false),
  //           people(isAllView: false),
  //           verticalSizedBox(50)
  //         ],
  //       ),
  //     );
  //   }
  //   if(buyerSearchHistoryBloc.tabController.index==1){
  //     //Error
  //     if(buyerSearchHistoryBloc.buyerSearchHistoryResponse.message==null){
  //       return  error();
  //
  //     }
  //     //Empty
  //     if(buyerSearchHistoryBloc.buyerSearchHistoryResponse.store!.isEmpty){
  //       return  noHistory();
  //     }
  //     return store(isAllView: true);
  //   }
  //   if(buyerSearchHistoryBloc.tabController.index==2){
  //     //Error
  //     if(buyerSearchHistoryBloc.buyerSearchHistoryResponse.message==null){
  //       return  error();
  //
  //     }
  //     //Error
  //     if(buyerSearchHistoryBloc.buyerSearchHistoryResponse.product!.isEmpty){
  //       return  noHistory();
  //     }
  //
  //
  //     return product(isAllView: true);
  //   }
  //   if(buyerSearchHistoryBloc.tabController.index==3){
  //     //Error
  //     if(buyerSearchHistoryBloc.buyerSearchHistoryResponse.message==null){
  //       return  error();
  //     }
  //     //Empty
  //     if(buyerSearchHistoryBloc.buyerSearchHistoryResponse.user!.isEmpty){
  //       return  noHistory();
  //     }
  //
  //     return people(isAllView: true);
  //   }
  //   return const SizedBox();
  // }
  //endregion

  //region All
  Widget allTabView() {
    //Error
    if (buyerSearchHistoryBloc.buyerSearchHistoryResponse.message == null) {
      return error();
    }
    //Empty all
    if (buyerSearchHistoryBloc
            .buyerSearchHistoryResponse.recentSearches!.isEmpty &&
        buyerSearchHistoryBloc.buyerSearchHistoryResponse.store!.isEmpty &&
        buyerSearchHistoryBloc.buyerSearchHistoryResponse.product!.isEmpty &&
        buyerSearchHistoryBloc.buyerSearchHistoryResponse.user!.isEmpty) {
      return noHistory();
    }
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        // shrinkWrap: true,
        children: [
          recentClearAll(),
          searchedText(),
          store(isAllView: false),
          product(isAllView: false),
          people(isAllView: false),
          verticalSizedBox(50)
        ],
      ),
    );
  }
  //endregion

  //region Store tab view
  Widget storeTabView() {
    //Error
    if (buyerSearchHistoryBloc.buyerSearchHistoryResponse.message == null) {
      return error();
    }
    //Empty
    if (buyerSearchHistoryBloc.buyerSearchHistoryResponse.store!.isEmpty) {
      return noHistory();
    }
    return store(isAllView: true);
  }
  //endregion

  //region Product tab view
  Widget productTabView() {
    //Error
    if (buyerSearchHistoryBloc.buyerSearchHistoryResponse.message == null) {
      return error();
    }
    //Error
    if (buyerSearchHistoryBloc.buyerSearchHistoryResponse.product!.isEmpty) {
      return noHistory();
    }

    return product(isAllView: true);
  }
  //endregion

  //region People tab view
  Widget peopleTabView() {
    //Error
    if (buyerSearchHistoryBloc.buyerSearchHistoryResponse.message == null) {
      return error();
    }
    //Empty
    if (buyerSearchHistoryBloc.buyerSearchHistoryResponse.user!.isEmpty) {
      return noHistory();
    }

    return people(isAllView: true);
  }
  //endregion

//region Recent search and clear all
  Widget recentClearAll() {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.symmetric(horizontal: 15),
      // margin: const EdgeInsets.symmetric(vertical: 10,horizontal: 15),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            AppStrings.recentSearches,
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          const Expanded(child: SizedBox()),
          InkWell(
            onTap: () {
              buyerSearchHistoryBloc.removeAllHistory();
            },
            child: Text(
              "clear all",
              style: AppTextStyle.smallText(textColor: AppColors.brandBlack),
            ),
          ),
        ],
      ),
    );
  }
//endregion

//region Searched text
  Widget searchedText() {
    return SearchedKeyWord(
      searchedKeywordList:
          buyerSearchHistoryBloc.buyerSearchHistoryResponse.recentSearches!,
      buyerSearchHistoryBloc: buyerSearchHistoryBloc,
      buyerSearchBloc: widget.buyerSearchBloc,
    );
  }
//endregion

//region Store
  Widget store({required bool isAllView}) {
    return StoreHistory(
      storeHistoryList:
          buyerSearchHistoryBloc.buyerSearchHistoryResponse.store!,
      buyerSearchHistoryBloc: buyerSearchHistoryBloc,
      isAllView: isAllView,
    );
  }
//endregion

//region Product
  Widget product({required bool isAllView}) {
    return ProductHistory(
      productHistoryList:
          buyerSearchHistoryBloc.buyerSearchHistoryResponse.product!,
      buyerSearchHistoryBloc: buyerSearchHistoryBloc,
      isAllView: isAllView,
    );
  }
//endregion

//region People
  Widget people({required bool isAllView}) {
    return PeopleHistory(
      peopleHistoryList:
          buyerSearchHistoryBloc.buyerSearchHistoryResponse.user!,
      buyerSearchHistoryBloc: buyerSearchHistoryBloc,
      isAllView: isAllView,
    );
  }
//endregion

//region No history
  Widget noHistory() {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(AppImages.noHistoryIcon),
        verticalSizedBox(7.12),
        Text(
          AppStrings.noHistoryYet,
          style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0),
        ),
        verticalSizedBox(4.5),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppStrings.findWhatYouLove,
              style:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
            ),
            horizontalSizedBox(3),
            SvgPicture.asset(AppImages.smileEmoji)
          ],
        ),
      ],
    ));
  }
//endregion

//region Error
  Widget error() {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(AppImages.noHistoryIcon),
        verticalSizedBox(7.12),
        Text(
          AppStrings.yourSearchHistory,
          style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0),
        ),
        verticalSizedBox(4.5),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppStrings.noWorry,
              style:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
            ),
            horizontalSizedBox(3),
            SvgPicture.asset(AppImages.smileEmoji)
          ],
        ),
      ],
    ));
  }
//endregion
}
