import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/needResolution/need_resolution.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/speak_with_seller/speak_with_seller.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/shipping_in_progress/deliver_and_return_person_logistic/delivery_and_return_person_logistic_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/shipping_in_progress/deliver_and_return_person_logistic/delivery_and_return_person_logistic_common_widgets.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Home Screen
class DeliveryAndReturnPersonLogistic extends StatefulWidget {
  final List<SubOrder> suborderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;

  const DeliveryAndReturnPersonLogistic(
      {Key? key,
      required this.suborderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<DeliveryAndReturnPersonLogistic> createState() =>
      _DeliveryAndReturnPersonLogisticState();
}
//endregion

class _DeliveryAndReturnPersonLogisticState
    extends State<DeliveryAndReturnPersonLogistic> {
  //region Build
  late DeliveryAndReturnPersonLogisticBloc deliveryAndReturnPersonLogisticBloc;

  //endregion
  //region Init
  @override
  void initState() {
    deliveryAndReturnPersonLogisticBloc = DeliveryAndReturnPersonLogisticBloc(
        context: context,
        widget.suborderList,
        widget.buyerSubOrderBloc,
        widget.order);
    deliveryAndReturnPersonLogisticBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DeliveryAndReturnPersonLogisticState>(
        stream: deliveryAndReturnPersonLogisticBloc
            .deliveryAndReturnPersonLogisticStateCtrl.stream,
        initialData: DeliveryAndReturnPersonLogisticState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == DeliveryAndReturnPersonLogisticState.Success) {
            return body();
          }
          if (snapshot.data == DeliveryAndReturnPersonLogisticState.Failed) {
            return const SizedBox();
          }
          if (snapshot.data == DeliveryAndReturnPersonLogisticState.Loading) {
            return const SizedBox();
          }
          return const SizedBox();
        });
  }

  //region Body
  Widget body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        moreDetail(),
        verticalSizedBox(20),
        //In case of delay button - standalone
        delayButton(),
        //Delay details - standalone
        delayDetails(),
      ],
    );
  }

//endregion

  //region More detail
  Widget moreDetail() {
    return Column(
      children: [
        // Details title
        Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(10),
          child: Text(
            "Details",
            style: AppTextStyle.access1(textColor: AppColors.appBlack),
          ),
        ),

        // Main details section based on delivery type
        if (deliveryAndReturnPersonLogisticBloc
                .trackingDetailResponse.data?.selfDeliveryByStore ??
            false)
          _buildSelfDeliveryDetails()
        else if (deliveryAndReturnPersonLogisticBloc
                .trackingDetailResponse.data?.deliveryByLogisticPartner ??
            false)
          _buildLogisticPartnerDetails()
        else if (deliveryAndReturnPersonLogisticBloc
                .trackingDetailResponse.data?.deliveryBySwadesic ??
            false)
          _buildSwadesicShippingDetails(),

        // verticalSizedBox(20),

        // Additional notes section
        if (deliveryAndReturnPersonLogisticBloc
                .trackingDetailResponse.data?.additionalNotes?.isNotEmpty ??
            false)
          _buildAdditionalNotesSection(),
      ],
    );
  }

  //endregion

  // Helper method to build detail box container with optional CTA icons
  Widget _buildDetailBox({
    required String title,
    required String value,
    VoidCallback? onCopy,
    VoidCallback? onCall,
    VoidCallback? onLink,
    int? maxLines = 2, // Default to 2 lines, null for unlimited
  }) {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(
        minHeight:
            60, // Minimum height restriction - auto-increases if content needs more
        minWidth: 120, // Minimum width
        // No maxHeight - allows unlimited vertical expansion based on content
      ),
      padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 15),
      margin: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Column C1 - Title and Value
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
                  ),
                  verticalSizedBox(5),
                  Text(
                    value,
                    style: AppTextStyle.access0(textColor: AppColors.appBlack),
                    maxLines: maxLines,
                    overflow: maxLines != null ? TextOverflow.ellipsis : null,
                  ),
                ],
              ),
            ),
            // CTA Icons - only show if any callback is provided
            if (onCopy != null || onCall != null || onLink != null) ...[
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (onCopy != null) ...[
                    InkWell(
                      onTap: onCopy,
                      child: SvgPicture.asset(
                        AppImages.copyIconFilled,
                        width: 30,
                        height: 30,
                        color: AppColors.appBlack,
                      ),
                    ),
                  ],
                  if (onCall != null) ...[
                    horizontalSizedBox(5),
                    InkWell(
                      onTap: onCall,
                      child: SvgPicture.asset(
                        AppImages.callIcon,
                        width: 30,
                        height: 30,
                        color: AppColors.appBlack,
                      ),
                    ),
                  ],
                  if (onLink != null) ...[
                    horizontalSizedBox(5),
                    InkWell(
                      onTap: onLink,
                      child: SvgPicture.asset(
                        AppImages.diagonalArrow,
                        width: 30,
                        height: 30,
                        color: AppColors.appBlack,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Build self delivery details section
  Widget _buildSelfDeliveryDetails() {
    return Column(
      children: [
        // Delivered by and Delivery method row
        IntrinsicHeight(
          child: Row(
            children: [
              Expanded(
                child: _buildDetailBox(
                  title: "Delivered by",
                  value: widget.suborderList.first.estimatedDeliveryDate ?? "",
                ),
              ),
              // horizontalSizedBox(5),
              Expanded(
                child: _buildDetailBox(
                  title: "Delivery method",
                  value: "Store's self delivery",
                ),
              ),
            ],
          ),
        ),

        // verticalSizedBox(6),

        // Delivery personnel name
        _buildDetailBox(
          title: "Delivery personnel name",
          value: deliveryAndReturnPersonLogisticBloc
                  .suborderList.first.deliveryPersonName ??
              "",
        ),

        // verticalSizedBox(6),

        // Phone with call and copy button
        _buildDetailBox(
          title: "Phone",
          value: deliveryAndReturnPersonLogisticBloc
                  .suborderList.first.deliveryPersonContact ??
              "",
          onCopy: () {
            CommonMethods.copyText(
              context,
              deliveryAndReturnPersonLogisticBloc
                      .suborderList.first.deliveryPersonContact ??
                  "",
            );
          },
          onCall: () {
            CommonMethods.openDialPad(
              phoneNumber: deliveryAndReturnPersonLogisticBloc
                      .suborderList.first.deliveryPersonContact ??
                  "",
            );
          },
        ),
      ],
    );
  }

  // Build logistic partner details section
  Widget _buildLogisticPartnerDetails() {
    return Column(
      children: [
        // Delivered by and Delivery method row
        IntrinsicHeight(
          child: Row(
            children: [
              Expanded(
                child: _buildDetailBox(
                  title: "Delivered by",
                  value: widget.suborderList.first.estimatedDeliveryDate ?? "",
                ),
              ),
              horizontalSizedBox(10),
              Expanded(
                child: _buildDetailBox(
                  title: "Delivery method",
                  value: "Store's logistics",
                ),
              ),
            ],
          ),
        ),

        // verticalSizedBox(10),

        // Logistics partner
        _buildDetailBox(
          title: "Logistics partner",
          value: deliveryAndReturnPersonLogisticBloc
                  .trackingDetailResponse.data?.logisticPartner ??
              "",
        ),

        // verticalSizedBox(10),

        // Tracking number with copy button
        _buildDetailBox(
          title: "Tracking number",
          value: deliveryAndReturnPersonLogisticBloc
                  .trackingDetailResponse.data?.trackingNumber ??
              "",
          onCopy: () {
            CommonMethods.copyText(
              context,
              deliveryAndReturnPersonLogisticBloc
                      .trackingDetailResponse.data?.trackingNumber ??
                  "",
            );
          },
        ),

        // verticalSizedBox(10),

        // Tracking link with external link button
        _buildDetailBox(
          title: "Tracking link",
          value: deliveryAndReturnPersonLogisticBloc
                  .trackingDetailResponse.data?.trackingLink ??
              "",
          onLink: () {
            CommonMethods.openAppWebView(
              webUrl: deliveryAndReturnPersonLogisticBloc
                      .trackingDetailResponse.data?.trackingLink ??
                  "",
              context: context,
            );
          },
        ),
      ],
    );
  }

  // Build Swadesic shipping details section (similar to logistic partner)
  Widget _buildSwadesicShippingDetails() {
    return Column(
      children: [
        // Delivered by and Delivery method row
        IntrinsicHeight(
          child: Row(
            children: [
              Expanded(
                child: _buildDetailBox(
                  title: "Delivered by",
                  value: widget.suborderList.first.estimatedDeliveryDate ?? "",
                ),
              ),
              horizontalSizedBox(10),
              Expanded(
                child: _buildDetailBox(
                  title: "Delivery method",
                  value: "Swadesic Shipping",
                ),
              ),
            ],
          ),
        ),

        verticalSizedBox(10),

        // Logistics partner
        _buildDetailBox(
          title: "Logistics partner",
          value: deliveryAndReturnPersonLogisticBloc
                  .trackingDetailResponse.data?.logisticPartner ??
              "",
        ),

        verticalSizedBox(10),

        // Tracking number with copy button
        _buildDetailBox(
          title: "Tracking number",
          value: deliveryAndReturnPersonLogisticBloc
                  .trackingDetailResponse.data?.trackingNumber ??
              "",
          onCopy: () {
            CommonMethods.copyText(
              context,
              deliveryAndReturnPersonLogisticBloc
                      .trackingDetailResponse.data?.trackingNumber ??
                  "",
            );
          },
        ),

        verticalSizedBox(10),

        // Tracking link with external link button
        _buildDetailBox(
          title: "Tracking link",
          value: deliveryAndReturnPersonLogisticBloc
                  .trackingDetailResponse.data?.trackingLink ??
              "",
          onLink: () {
            CommonMethods.openAppWebView(
              webUrl: deliveryAndReturnPersonLogisticBloc
                      .trackingDetailResponse.data?.trackingLink ??
                  "",
              context: context,
            );
          },
        ),
      ],
    );
  }

  // Build additional notes section
  Widget _buildAdditionalNotesSection() {
    return _buildDetailBox(
      title: "Additional notes",
      value: deliveryAndReturnPersonLogisticBloc
              .trackingDetailResponse.data?.additionalNotes ??
          "",
      maxLines: null, // Allow unlimited lines for additional notes
      onCopy: () {
        CommonMethods.copyText(
          context,
          deliveryAndReturnPersonLogisticBloc
                  .trackingDetailResponse.data?.additionalNotes ??
              "",
        );
      },
    );
  }

  ///Self
//region Self delivery
  Widget selfDelivery() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Name
        DeliveryAndReturnPersonLogisticCommonWidgets.detailTitleAndSub(
            titleText: AppStrings.deliveryPersonDetail,
            subTitle: deliveryAndReturnPersonLogisticBloc
                    .suborderList.first.deliveryPersonName ??
                "",
            isCopyButtonVisible: true,
            context: context
            //logisticsPartner
            ),
        verticalSizedBox(13),

        //Call
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                onLongPress: () {
                  CommonMethods.copyText(
                      context,
                      deliveryAndReturnPersonLogisticBloc
                          .suborderList.first.deliveryPersonContact!);
                },
                child: Text(
                  deliveryAndReturnPersonLogisticBloc
                          .suborderList.first.deliveryPersonContact ??
                      "",
                  style: AppTextStyle.heading2Medium(
                    textColor: AppColors.appBlack,
                  ),
                ),
              ),
              AppCommonWidgets.copyCallButton(
                  text: AppStrings.call,
                  onTap: () {
                    CommonMethods.openDialPad(
                        phoneNumber: deliveryAndReturnPersonLogisticBloc
                            .suborderList.first.deliveryPersonContact!);
                  }),
            ],
          ),
        )
      ],
    );
  }

//endregion
  ///Logistic
//region Logistic
  Widget logistic() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Tracking service name
        DeliveryAndReturnPersonLogisticCommonWidgets.detailTitleAndSub(
          isCopySubTitle: true,
          titleText: AppStrings.trackingNumberAndDetail,
          subTitle: deliveryAndReturnPersonLogisticBloc
                  .trackingDetailResponse.data!.logisticPartner ??
              "",
          context: context,
          //logisticsPartner
        ),
        verticalSizedBox(13),

        //Tracking number
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                deliveryAndReturnPersonLogisticBloc
                        .trackingDetailResponse.data!.trackingNumber ??
                    "",
                style: AppTextStyle.heading2Medium(
                  textColor: AppColors.appBlack,
                ),
              ),
              AppCommonWidgets.copyCallButton(
                  text: AppStrings.copy,
                  onTap: () {
                    CommonMethods.copyText(
                        context,
                        deliveryAndReturnPersonLogisticBloc
                            .trackingDetailResponse.data!.trackingNumber!);
                  }),
            ],
          ),
        ),
        verticalSizedBox(15),

        //Tracking link
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: InkWell(
                  onLongPress: () {
                    CommonMethods.copyText(
                        context,
                        deliveryAndReturnPersonLogisticBloc
                            .trackingDetailResponse.data!.trackingLink!);
                  },
                  child: Text(
                    deliveryAndReturnPersonLogisticBloc
                            .trackingDetailResponse.data!.trackingLink ??
                        "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.heading2Medium(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ),
              ),
              horizontalSizedBox(10),
              AppCommonWidgets.copyCallButton(
                  text: AppStrings.openLink,
                  onTap: () {
                    CommonMethods.openAppWebView(
                        webUrl: deliveryAndReturnPersonLogisticBloc
                            .trackingDetailResponse.data!.trackingLink!,
                        context: context);
                  }),
            ],
          ),
        )
      ],
    );
  }

//endregion

  ///Swadesic Shipping
//region Swadesic Shipping
  Widget swadesicShipping() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Tracking service name
        DeliveryAndReturnPersonLogisticCommonWidgets.detailTitleAndSub(
          isCopySubTitle: true,
          titleText: AppStrings.trackingNumberAndDetail,
          subTitle: deliveryAndReturnPersonLogisticBloc
                  .trackingDetailResponse.data!.logisticPartner ??
              "",
          context: context,
          //logisticsPartner
        ),

        //Tracking number
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                deliveryAndReturnPersonLogisticBloc
                        .trackingDetailResponse.data!.trackingNumber ??
                    "",
                style: AppTextStyle.heading2Medium(
                  textColor: AppColors.appBlack,
                ),
              ),
              AppCommonWidgets.copyCallButton(
                  text: AppStrings.copy,
                  onTap: () {
                    CommonMethods.copyText(
                        context,
                        deliveryAndReturnPersonLogisticBloc
                            .trackingDetailResponse.data!.trackingNumber!);
                  }),
            ],
          ),
        ),
        verticalSizedBox(15),

        //Tracking link
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: InkWell(
                  onLongPress: () {
                    CommonMethods.copyText(
                        context,
                        deliveryAndReturnPersonLogisticBloc
                            .trackingDetailResponse.data!.trackingLink!);
                  },
                  child: Text(
                    deliveryAndReturnPersonLogisticBloc
                            .trackingDetailResponse.data!.trackingLink ??
                        "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.heading2Medium(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ),
              ),
              horizontalSizedBox(10),
              AppCommonWidgets.copyCallButton(
                  text: AppStrings.openLink,
                  onTap: () {
                    CommonMethods.openAppWebView(
                        webUrl: deliveryAndReturnPersonLogisticBloc
                            .trackingDetailResponse.data!.trackingLink!,
                        context: context);
                  }),
            ],
          ),
        )
      ],
    );
  }

//endregion

//region In case of any delay
  Widget delayButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        children: [
          Visibility(
            visible: !deliveryAndReturnPersonLogisticBloc.isDelayDetailVisible,
            child: Expanded(
              child: AppCommonWidgets.activeButton(
                  buttonName: AppStrings.inCaseOfDelay,
                  onTap: () {
                    deliveryAndReturnPersonLogisticBloc.onTapDelayButton();
                  }),
            ),
            // child: InkWell(
            //   onTap: () {
            //     deliveryAndReturnPersonLogisticBloc.onTapDelayButton();
            //   },
            //   child: Container(
            //     margin: const EdgeInsets.symmetric(horizontal: 10),
            //     alignment: Alignment.center,
            //     width: double.infinity,
            //     decoration: BoxDecoration(borderRadius: BorderRadius.circular(100), color: AppColors.brandGreen),
            //     padding: const EdgeInsets.symmetric(vertical: 15),
            //     child: Text(
            //       AppStrings.inCaseOfDelay,
            //       style: AppTextStyle.heading3Medium(textColor: AppColors.appWhite),
            //     ),
            //   ),
            // ),
          ),
        ],
      ),
    );
  }

//endregion

//region Widget delay details
  Widget delayDetails() {
    return Visibility(
      visible: deliveryAndReturnPersonLogisticBloc.isDelayDetailVisible,
      child: Column(
        children: [
          ///Speak with seller
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: SpeakWithSeller(
              buyerSubOrderBloc:
                  deliveryAndReturnPersonLogisticBloc.buyerSubOrderBloc,
              order: deliveryAndReturnPersonLogisticBloc.order,
              subOrderList: deliveryAndReturnPersonLogisticBloc.suborderList,
              title: AppStrings.getAnUpdate,
              subTitle: AppStrings.possibleReasonForDelay,
            ),
          ),

          ///Need resolution
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: NeedResolution(
              order: deliveryAndReturnPersonLogisticBloc.order,
              buyerSubOrderBloc:
                  deliveryAndReturnPersonLogisticBloc.buyerSubOrderBloc,
              escalationReason: AppStrings.delayInDelivery,
              subOrderList: deliveryAndReturnPersonLogisticBloc.suborderList,
              packageReference: deliveryAndReturnPersonLogisticBloc
                  .suborderList.first.packageNumber,
              title: AppStrings.ifYouFeelAfter,
              subTitle: AppStrings.additionalNotesOnThis,
              buttonText: AppStrings.needResolutionDelayInDelivery,
            ),
          )
        ],
      ),
    );
  }
//endregion

// //region Possible reason for delay
// Widget possibleRea
// //endregion
}
