import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class BuyOptionsBottomSheet extends StatelessWidget {
  final VoidCallback onTapBuyOnSwadesic;
  final VoidCallback onTapPickupFromStore;

  const BuyOptionsBottomSheet({
    Key? key,
    required this.onTapBuyOnSwadesic,
    required this.onTapPickupFromStore,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      decoration: const BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Buy on Swadesic Option
          ListTile(
            horizontalTitleGap: 0,
            onTap: () {
              if(CommonMethods().isStaticUser()){
                CommonMethods().goToSignUpFlow();
                return ;
              }
              Navigator.pop(context);
              onTapBuyOnSwadesic();
            },
            leading: Image.asset(
              AppImages.appIcon,
              height: 31,
              width: 31,
            ),
            title: Text(
              'Buy on Swadesic',
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
          divider(),
          // Pickup from Store Option
          ListTile(
            horizontalTitleGap: 0,
            onTap: () {
              Navigator.pop(context);
              onTapPickupFromStore();
            },
            leading: SvgPicture.asset(
              AppImages.pickUpLocationIcon,
              height: 28,
              width: 28,
            ),
            title: Text(
              'Pickup from Store',
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

  Widget divider() {
    return const Divider(
      height: 1,
      thickness: 1,
      color: AppColors.borderColor0,
    );
  }
}
