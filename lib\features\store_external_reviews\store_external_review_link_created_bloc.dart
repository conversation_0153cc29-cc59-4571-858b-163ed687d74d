import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

class StoreExternalReviewLinkCreatedBloc {
  // region Common Variables
  BuildContext context;
  String token;
  String expiresAt;
  String userIdentifier;
  String storeReference;
  String storeHandle;
  String reviewLink = '';

  // endregion

  // region | Constructor |
  StoreExternalReviewLinkCreatedBloc(
    this.context,
    this.token,
    this.expiresAt,
    this.userIdentifier,
    this.storeReference,
    this.storeHandle,
  ) {
    _generateReviewLinkSafely();
  }
  // endregion

  // region Generate review link safely
  void _generateReviewLinkSafely() {
    // Create a simple store external review link without using Provider
    // This avoids the issue with accessing Provider during widget disposal
    String storeHandleData = "$storeHandle/e-review-request/";
    String data = "?t=$token&sr=$storeReference&ur=$userIdentifier";
    var encodedData = CommonMethods.encodeBase32(data);
    reviewLink = "${AppConstants.domainName}$storeHandleData$encodedData";

    // Skip Firebase deep link creation to avoid potential issues
    // We'll use only the base32 encoded link
  }
  // endregion

  // region On tap copy link
  void onTapCopyLink() {
    CommonMethods.copyText(context, reviewLink);
  }
  // endregion

  // region Get formatted expiry date
  String getFormattedExpiryDate() {
    try {
      // Parse the ISO 8601 date string
      DateTime expiryDate = DateTime.parse(expiresAt);
      
      // Format it to a more readable format
      String formattedDate = DateFormat('MMM dd, yyyy \'at\' hh:mm a').format(expiryDate);
      
      return 'Expires on $formattedDate';
    } catch (e) {
      // If parsing fails, return a default message
      return 'Link expires soon';
    }
  }
  // endregion

  // region On tap share
  void onTapShare() {
    // This method can be used for additional sharing functionality if needed
    // For now, the main sharing is handled in the screen
  }
  // endregion
}
