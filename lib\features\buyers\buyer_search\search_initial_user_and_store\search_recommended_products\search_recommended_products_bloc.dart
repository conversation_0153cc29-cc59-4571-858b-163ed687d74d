import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_recommended_products/search_recommended_products_pagination.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_product_screen.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/common_methods.dart';
enum SearchRecommendedProductsState { Loading, Success, Failed, Empty }

class SearchRecommendedProductsBloc{
  //region Common variable
  late BuildContext context;
  List<Product> recommendedProductsList = [];
  late SearchRecommendedProductsPagination searchRecommendedProductsPagination;
  //endregion

//region Text Editing Controller
//endregion

  //region Controller
  final searchRecommendedProductsCtrl = StreamController<SearchRecommendedProductsState>.broadcast();
  final ScrollController scrollController  = ScrollController() ;
  //endregion
  //region Constructor
  SearchRecommendedProductsBloc(this.context,);
  //endregion
//region Init
  void init(){
    searchRecommendedProductsPagination = SearchRecommendedProductsPagination(context,this);
    getSearchRecommendedProducts();
  }
//endregion


  //region Get search recommended products
  Future<void> getSearchRecommendedProducts() async {
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    //region Try
    try {
      //Make pagination loading state
      searchRecommendedProductsPagination.searchRecommendedProductsPaginationStateCtrl.sink.add(SearchRecommendedProductsPaginationState.Loading);
      //Api call
      recommendedProductsList = await RecommendedStoreAndUserServices().getRecommendedProducts(limit: 20, offset: 0,context: context);
      //Add recommended products to data model
      productDataModel.addProductIntoList(products: recommendedProductsList);
      //Success
      searchRecommendedProductsCtrl.sink.add(SearchRecommendedProductsState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted ? CommonMethods.toastMessage(error.message!, context) : null;
      //Failed
      searchRecommendedProductsCtrl.sink.add(SearchRecommendedProductsState.Failed);
    } catch (error) {
      //Failed
      searchRecommendedProductsCtrl.sink.add(SearchRecommendedProductsState.Failed);
      // return context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
    }
  }
//endregion


  //region Go to Product View Screen
  void goToViewProductScreen({required Product product,required int index}) {
    var screen = BuyerViewProductScreen(
      openingFrom: SearchScreenEnum.RECOMMENDED_PRODUCTS,
      index: index,
      productList: recommendedProductsList,
      searchedText: "Recommended products",
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
    });
  }
  //endregion


//region Dispose
  void dispose(){
    searchRecommendedProductsCtrl.close();
  }
//endregion


}