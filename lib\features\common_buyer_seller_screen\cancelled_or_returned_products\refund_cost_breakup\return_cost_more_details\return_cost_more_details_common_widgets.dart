import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class ReturnCostMoreDetailsCommonWidgets{



  //region Info
  static Widget info({required String title,required String value}){
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(title,

              maxLines: 2,
              style: AppTextStyle.settingText(textColor: AppColors.writingBlack),),
          ),
          horizontalSizedBox(20),
          Expanded(
            child: Text(value,
              textAlign: TextAlign.right,
              maxLines: 2,
              style: AppTextStyle.settingText(textColor: AppColors.writingBlack),),
          ),


        ],
      ),
    );
  }
//endregion

}