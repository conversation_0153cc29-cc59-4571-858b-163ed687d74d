import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_add_image/buyer_add_image_screen.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/model/product_comment_response/add_parent_comment_response.dart';
import 'package:swadesic/model/product_comment_response/product_all_comment_response.dart';
import 'package:swadesic/services/product_comment_services/product_comment_services.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum EditReviewState { Loading, Success, Failed }

class EditReviewBloc {
  // region Common Variables
  BuildContext context;
  bool isEditEnable = false;
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  late ProductCommentServices productCommentServices;
  late ProductAllCommentResponse productAllCommentResponse;

  ///Add Parent comment Response
  late AddParentCommentResponse addParentCommentResponse;

  ///Upload service
  late var uploadFileService = UploadFileService();

  // endregion

  //region Controller
  final editReviewStateCtrl = StreamController<EditReviewState>.broadcast();

  //endregion

  //region Text Controller
  final TextEditingController commentTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  EditReviewBloc(this.context, this.subOrderList, this.buyerSubOrderBloc, this.order);

  // endregion

  // region Init
  void init() {
    productCommentServices = ProductCommentServices();
  }
// endregion


  //region On tap update
  void onTapUpdate(){
    //Change the flag to true
    isEditEnable = true;
    //Add comment into text field
    commentTextCtrl.text = subOrderList.first.replyAndComments!.comments!;
    //Refresh screen
    editReviewStateCtrl.sink.add(EditReviewState.Success);
  }
  //endregion


  //region OnTap save
  void onTapSave({required SubOrder subOrder})async{
    //Change the flag to true
    isEditEnable = false;
    //Clear text field
    // commentTextCtrl.clear();
    await addReview(subOrder: subOrder);
    //Refresh screen
    // editReviewStateCtrl.sink.add(EditReviewState.Success);
  }
  //endregion





  //region OnTap cancel
  void onTapCancel(){
    //Change the flag to true
    isEditEnable = false;
    //Clear text field
    // commentTextCtrl.clear();
    //Refresh screen
    editReviewStateCtrl.sink.add(EditReviewState.Success);
  }
  //endregion


  //region Go to Buyer Image Preview Screen
  void goToBuyerProductImageScreen({required List<CommentReviewImage> productImage,required int index}){
    List<String> imageUrls = [];
    for(var data in productImage){
      imageUrls.add(data.reviewImage!);
    }

    var screen =  BuyerImagePreviewScreen(productImage: imageUrls,imageIndex:index ,);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
//endregion



//region Go to Buyer Add Image Screen
  void goToBuyerAddImageScreen({required SubOrder subOrder}) {
    var screen = const BuyerAddImageScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route).then((value) {
      //Add all XFiles to list of file in suborders
      for (var data in AppConstants.multipleSelectedImage) {
        //print(data.path);
        subOrder.reviewImages.add(File(data.path));
      }
      //print("Review images leangth are ${subOrder.reviewImages.length}");
      //Clear global images
      AppConstants.multipleSelectedImage.clear();
      //Refresh screen
      editReviewStateCtrl.sink.add(EditReviewState.Success);
    });
  }

//endregion

//region Remove suborder image
  void removeReviewImage({required SubOrder subOrder, required int imageIndex}) {
    //Remove image where the index is same
    subOrder.reviewImages.removeAt(imageIndex);
    //Refresh
    editReviewStateCtrl.sink.add(EditReviewState.Success);

    //If review image is empty
  }

//endregion

  ///1
//region Add review
  Future<void> addReview({required SubOrder subOrder}) async {
    try {
      //Loading
      editReviewStateCtrl.sink.add(EditReviewState.Loading);
      ///Add review comment
      addParentCommentResponse = await productCommentServices.addParentComment(
          subOrder.productReference!,commentTextCtrl.text, "review");

      ///Add star ratting
      await productCommentServices.productRating(subOrder.replyAndComments!.review!.round(), addParentCommentResponse.data!.commentid!);

      ///Upload image only if images are selected
      if (subOrder.reviewImages.isNotEmpty) {
        await addCommentImage(commentId: addParentCommentResponse.data!.commentid!, suborder: subOrder);
      }
      ///Get all comments
      await getComments();
      //Loading
      editReviewStateCtrl.sink.add(EditReviewState.Success);

    } on ApiErrorResponseMessage {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      //print(error);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

//endregion
///2
//region Add Comment Image
  Future<void>addCommentImage({required int commentId, required SubOrder suborder}) async {
    try {
      for (int i = 0; i < suborder.reviewImages.length; i++) {
        //print("File name with extn ${suborder.reviewImages[i].path.split('/').last}");

        await uploadFileService.addCommentImage(
            filePath: suborder.reviewImages[i].path,
            url: AppConstants.addCommentImage,
            fileNameWithExtension: suborder.reviewImages[i].path.split('/').last,
            commentId: commentId);
        //print(i);
      }
      //Clear all selected images in sub order
      suborder.reviewImages.clear();
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }
//endregion

///3
  //region Get comment
  Future<void> getComments() async{
    //region Try
    try {
      productAllCommentResponse = await productCommentServices.getProductComment(productRef:subOrderList.first.productReference!,userReference: AppConstants.appData.userReference! );
      //Check where comment id is same and add the object
      subOrderList.first.replyAndComments = productAllCommentResponse.data!.firstWhere((element) => element.commentid==addParentCommentResponse.data!.commentid!);

      //Message
      CommonMethods.toastMessage(AppStrings.yourFeedbackIsGreatly, context);

      //print("ram");
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

//endregion



//region Delete review image
  deleteCommentImage({required CommentReviewImage commentReviewImage})async{
    try{

      //Api call to delete image
      productCommentServices.deleteCommentImage(commentReviewImage.reviewImageId!);
      //Delete local image
      subOrderList.first.replyAndComments!.commentReviewImage!.removeWhere((element) => element.reviewImageId == commentReviewImage.reviewImageId);
      //Refresh ui
      editReviewStateCtrl.sink.add(EditReviewState.Success);

      //Message
      // CommonMethods.toastMessage(AppStrings., context);


    }
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
    catch(error){
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
//endregion
}
