import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_confirmed/start_return/start_return.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/seller_all_order_service/seller_all_order_service.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class ReturnConfirmedBloc {
  // region Common Variables
  BuildContext context;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  late SellerAllOrderServices sellerAllOrderServices;
  late BuyerMyOrderServices buyerMyOrderServices;
  final List<SubOrder> suborderList;
  bool isActionDone = false;
  // endregion


  //region Controller
  final bottomSheetRefreshCtrl = StreamController<bool>.broadcast();
  final checkBoxCtrl = StreamController<bool>.broadcast();
  // final screenRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller
  final deliveryPersonNameCtrl = TextEditingController();
  final deliveryPersonContactCtrl = TextEditingController();
  final trackingLinkCtrl = TextEditingController();
  final dateCtrl = TextEditingController();
  final cancelReasonTextCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  ReturnConfirmedBloc(this.context, this.buyerSubOrderBloc, this.suborderList, this.order) {
    sellerAllOrderServices = SellerAllOrderServices();
  }
  // endregion

  // region Init
  void init() {
    sellerAllOrderServices = SellerAllOrderServices();
    buyerMyOrderServices = BuyerMyOrderServices();
  }
// endregion

  //region Cancel return request
  Future<void> cancelReturnRequest({required SubOrder subOrder}) async {
    //region Try
    try {
      // Get current context before async gap
      final currentContext = context;

      await buyerMyOrderServices.cancelReturnRequest(subOrderNumbers: [subOrder.suborderNumber!]);

      // Check if context is still valid
      if (!currentContext.mounted) return;

      //Message
      CommonMethods.toastMessage("Return cancelled", currentContext);

      //Get sub orders
      buyerSubOrderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage {
      final currentContext = context;
      if (!currentContext.mounted) return;
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      return;
    } catch (error) {
      final currentContext = context;
      if (!currentContext.mounted) return;
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      return;
    }
  }
  //endregion
}
