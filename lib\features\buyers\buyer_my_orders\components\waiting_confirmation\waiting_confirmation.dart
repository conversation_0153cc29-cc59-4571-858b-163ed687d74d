import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/waiting_confirmation/waiting_confirmation_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class WaitingForConfirmation extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const WaitingForConfirmation({Key? key, required this.subOrderList, required this.buyerSubOrderBloc, required this.order}) : super(key: key);

  @override
  State<WaitingForConfirmation> createState() => _WaitingForConfirmationState();
}

class _WaitingForConfirmationState extends State<WaitingForConfirmation> {

  // region Bloc
  late WaitingForConfirmationBloc waitingForConfirmationBloc;

  // endregion

  // region Init
  @override
  void initState() {
    waitingForConfirmationBloc = WaitingForConfirmationBloc(context,widget.buyerSubOrderBloc,widget.order,widget.subOrderList);
    waitingForConfirmationBloc.init();
    super.initState();
  }

  // endregion


  //region Dis update
  @override
  void didUpdateWidget(covariant WaitingForConfirmation oldWidget) {
    waitingForConfirmationBloc = WaitingForConfirmationBloc(context,widget.buyerSubOrderBloc,widget.order,widget.subOrderList);
    waitingForConfirmationBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion


  //region Build

  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion


  //region Body
  Widget body(){
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: const BoxDecoration(
          color: AppColors.appWhite,
          border: Border(
              bottom: BorderSide(
                  color: AppColors.lightStroke
              )
          )
      ),
      child: ExpandablePanel(
        //region Theme
        theme: const ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(),
        //endregion
        collapsed: const SizedBox(height: 1),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // cancel(),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: waitingForConfirmationBloc.subOrderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        productInfoCard(context: context, subOrder: waitingForConfirmationBloc.subOrderList[index]),
                        //Cancel
                        Row(
                          children: [
                            AppCommonWidgets.subOrderButton(
                                buttonName: AppStrings.cancel,
                                onTap: () {
                                  // //Mark only selected
                                  waitingForConfirmationBloc.subOrderList[index].isSelected = true;
                                  // //Open bottom sheet
                                  waitingForConfirmationBloc.onTapCancel();
                                  // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                                },
                                horizontalPadding: 25),
                          ],
                        ),
                        verticalSizedBox(10),
                        //Divider
                        Visibility(
                          visible: waitingForConfirmationBloc.subOrderList.length-1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }
  //endregion


  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.loading,
      componentName: AppStrings.waitingForConfirmation,
      suborderList: waitingForConfirmationBloc.subOrderList,
      additionalWidgets:const SizedBox()
      ///Todo un-comment
      // Container(
      //   // height: 20,
      //   alignment: Alignment.centerLeft,
      //   child: Row(
      //     mainAxisSize: MainAxisSize.min,
      //     mainAxisAlignment: MainAxisAlignment.start,
      //     crossAxisAlignment: CrossAxisAlignment.center,
      //     children: [
      //       Text("35:50 mins",
      //         style: AppTextStyle.heading1Bold(textColor: AppColors.writingColor2),
      //       ),
      //       horizontalSizedBox(10),
      //       Expanded(
      //         child: Text(AppStrings.autoCancelAndFull,
      //           maxLines: 2,
      //           style: AppTextStyle.normalTextBold(textColor: AppColors.brandGreen),
      //         ),
      //       ),
      //     ],
      //   ),
      // ),
    );
  }

  //endregion

  //region Cancel
  Widget cancel() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: AppCommonWidgets.inActiveButton(
                buttonName: AppStrings.cancel,
                onTap: () {
                  waitingForConfirmationBloc.onTapCancel();
                  // CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.subOrderList);
                  // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                })),
      ],
    );
  }

//endregion





}
