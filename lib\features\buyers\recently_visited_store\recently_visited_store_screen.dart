import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/recently_visited_store/recently_visited_store_bloc.dart';
import 'package:swadesic/features/providers/store_info_provider/store_info_provider.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Visited store
class RecentlyVisitedStoreScreen extends StatefulWidget {
  const RecentlyVisitedStoreScreen({Key? key}) : super(key: key);

  @override
  _RecentlyVisitedStoreScreenState createState() =>
      _RecentlyVisitedStoreScreenState();
}
// endregion

class _RecentlyVisitedStoreScreenState
    extends State<RecentlyVisitedStoreScreen> {
  //Width
  double width = 0.0;
  // region Bloc
  late RecentVisitedStoreBloc recentVisitedStoreBloc;

  // endregion

  // region Init
  @override
  void initState() {
    //print(context);
    recentVisitedStoreBloc = RecentVisitedStoreBloc(context);
    recentVisitedStoreBloc.init();
    super.initState();
  }

  // endregion

  @override
  void dispose() {
    //print("disposed");
    super.dispose();
  }

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        appBar: appBar(),
        body: SafeArea(child: Center(child: body())),
        // body: SafeArea(child: Center(child: visitedStores())),
      ),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isTitleVisible: true,
        isCustomTitle: false,
        title: AppStrings.recentlyVisited,
        isDefaultMenuVisible: true,
        isCartVisible: false,
        isMembershipVisible: true,
        onTapDrawer: () {
          // buyerViewStoreBloc.goToSellerAccountScreen();
        });
  }
  //endregion

  //region AppBar
  // AppBar appBar(){
  //   return buyerAppBar(leadingIcon: SvgPicture.asset(AppImages.backButton,fit: BoxFit.cover,), context: context,titleText: AppStrings.visitedStores,drawerIconEnable: true,
  //
  //   );
  // }

  //endregion

  // region Body
  Widget body() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        width = constraints.maxWidth;
        return RefreshIndicator(
          color: AppColors.brandBlack,
          onRefresh: () async {
            recentVisitedStoreBloc.init();
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 15,
            ),
            child: Column(
              children: [
                searchBar(),
                verticalSizedBox(20),
                followedStores(),
                //exploreStores(),
              ],
            ),
          ),
        );
      },
    );
  }

// endregion

//region Followed Stores
  Widget followedStores() {
    return Expanded(
      child: StreamBuilder<RecentlyVisitedStoreState>(
          stream: recentVisitedStoreBloc.visitedStoreCtrl.stream,
          initialData: RecentlyVisitedStoreState.Loading,
          builder: (context, snapshot) {
            if (snapshot.data == RecentlyVisitedStoreState.Loading) {
              return Center(child: AppCommonWidgets.appCircularProgress());
              // return const Center(child: CircularProgressIndicator(),);
            }
            if (snapshot.data == RecentlyVisitedStoreState.SearchEmpy) {
              return Container(
                alignment: Alignment.center,
                height: MediaQuery.of(context).size.width,
                child: const NoResult(
                    message: AppStrings.noMatchingResultInRecentlyVisited),
              );
              return AppCommonWidgets.emptyResponseText(
                  emptyMessage: AppStrings.noMatchingResultInRecentlyVisited);
              // return const Center(child: CircularProgressIndicator(),);
            }

            if (snapshot.data == RecentlyVisitedStoreState.Empty) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // emptyImage(),
                  verticalSizedBox(60),
                  youAreNotFollowing(),
                  verticalSizedBox(150),
                  //exploreStores()
                ],
              );
            }
            if (snapshot.data == RecentlyVisitedStoreState.Success) {
              var data = recentVisitedStoreBloc.storeList;
              return GridView.builder(
                shrinkWrap: true,
                //itemCount: buyerHomeBloc.recentlyVisitedItemCount,
                itemCount: data.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,

                  /// childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.5),
                  mainAxisSpacing: 20,

                  /// crossAxisSpacing: 15,
                  // childAspectRatio: 0.7
                  crossAxisSpacing: 15,

                  mainAxisExtent: width / 5 +
                      CommonMethods.textHeight(
                        textStyle: AppTextStyle.subTitle(
                            textColor: AppColors.appBlack),
                        context: context,
                      ) +
                      6,
                ),
                // physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return AppCommonWidgets.storeCardGreed(
                    width: width,
                    context: context,
                    onTap: () {
                      recentVisitedStoreBloc.goToBuyerViewStore(
                          selectedStore: data[index]);
                    },
                    storeInfo: data[index],
                  );
                },
              );
            }
            return Center(child: AppCommonWidgets.errorWidget(onTap: () {
              recentVisitedStoreBloc.init();
            }));
          }),
    );
  }
//endregion

  //region Searchbar
  Widget searchBar() {
    return AppSearchField(
      textEditingController: recentVisitedStoreBloc.searchTextCtrl,
      onChangeText: (value) {
        recentVisitedStoreBloc.onChangeSearchField();
      },
      onTapSuffix: () {
        recentVisitedStoreBloc.onChangeSearchField();
      },
      hintText: AppStrings.searchVisitedStore,
    );
  }
  //endregion

//region Empty image
  Widget emptyImage() {
    return Center(
        child: SvgPicture.asset(
      AppImages.storesYouFollow,
      fit: BoxFit.cover,
    ));
  }
//endregion

//region You Are not Following
  Widget youAreNotFollowing() {
    return Text(
      AppStrings.youAreNotVisited,
      textAlign: TextAlign.center,
      style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
    );
    // return Text(
    //   AppStrings.youAreNotVisited,
    //   textAlign: TextAlign.center,
    //   style: const TextStyle(
    //       fontWeight: FontWeight.w600,
    //       fontFamily: "LatoBold",
    //       fontSize: 16,
    //       color: AppColors.writingColor2),
    // );
  }
//endregion

//region Explore stores
  Widget exploreStores() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        //recentVisitedStoreBloc.goToStoreYouFollow();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: 15,
        ),
        decoration: BoxDecoration(
          color: AppColors.brandBlack,
          borderRadius: const BorderRadius.all(Radius.circular(100)),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 1),
              blurRadius: 5,
              color: AppColors.appBlack.withOpacity(0.2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            AppStrings.exploreStores,
            style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w700,
                fontFamily: "LatoSemiBold",
                color: AppColors.appWhite),
          ),
        ),
      ),
    );
  }
//endregion

  Widget visitedStores() {
    return Consumer<StoreInfoProvider>(
      builder: (context, storeData, _) {
        switch (storeData.visitedState) {
          case AppState.Success:
            final visitedStores = storeData.getFilteredVisitedStores();
            return ListView.builder(
              itemCount: visitedStores.length,
              itemBuilder: (context, index) {
                // final store = storeData.seenStores[index];
                return ListTile(
                  title: Text(visitedStores[index].storeName!),
                  trailing: IconButton(
                    icon: Icon(visitedStores[index].isLike
                        ? Icons.favorite
                        : Icons.favorite_border),
                    onPressed: () =>
                        storeData.toggleLike(storeInfo: visitedStores[index]),
                  ),
                );
              },
            );
          case AppState.Failed:
            return Center(child: Text('Failed to fetch seen stores'));
          default:
            return Center(child: CircularProgressIndicator());
        }
      },
    );
  }
}
