import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/refund_amount_calculation/refund_amount_calculate_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/refund_amount_calculation/refund_amount_calculation_common_widgets.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class RefundAmountCalculation extends StatefulWidget {
  final List<SubOrder> subOrderList;
  // final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  final Color backgroundColor;


  const RefundAmountCalculation({
    Key? key,
    required this.subOrderList,
    required this.order, required this.backgroundColor,
  }) : super(key: key);

  @override
  State<RefundAmountCalculation> createState() => _RefundAmountCalculationState();
}

class _RefundAmountCalculationState extends State<RefundAmountCalculation> {
  // region Bloc
  late RefundAmountCalculateBloc refundAmountCalculateBloc;

  // endregion

  // region Init
  @override
  void initState() {
    refundAmountCalculateBloc = RefundAmountCalculateBloc(context, widget.subOrderList, widget.order);
    refundAmountCalculateBloc.init();
    super.initState();
  }

  // endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return detail();
  }

//endregion

  //region Detail
  Widget detail() {
    return StreamBuilder<RefundAmountCalculateState>(
      stream: refundAmountCalculateBloc.refundAmountCalculationStateCtrl.stream,
      initialData: RefundAmountCalculateState.Loading,
      builder: (context, snapshot) {
        //Loading
        if(snapshot.data == RefundAmountCalculateState.Loading){
          return Center(child: AppCommonWidgets.appCircularProgress());
        }
        //Success
        if(snapshot.data == RefundAmountCalculateState.Success){
          return refundAmountCalculation();
        }
        return AppCommonWidgets.errorMessage(error: AppStrings.refundAmountCalculationIsNotAvailable);


      }
    );
  }

  //endregion

  //region Refund amount calculation
  Widget refundAmountCalculation() {
    return Container(
        color: widget.backgroundColor,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            //Title
            refundAmountTitle(),

            //Product and refund details
            productAndRefundDetails(),
          ],
        ));
  }

  //endregion

  //region Refund amount title
  Widget refundAmountTitle() {
    return Container(
      color: AppColors.textFieldFill1,
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            AppStrings.refundAmountCalculation,
            style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
          ),
          InkWell(
              onTap: () {
                refundAmountCalculateBloc.onTapShowBreakup();
              },
              child: Container(
                  margin: const EdgeInsets.only(right: 10),
                  child: Text(
                    refundAmountCalculateBloc.shoBreakup?AppStrings.hideBreakup:AppStrings.showBreakup,
                    style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
                  )))
        ],
      ),
    );
  }

  //endregion

//region Product and refund details
  Widget productAndRefundDetails() {
    return Container(
      margin: const EdgeInsets.only(top: 15,left: 17,right: 17,bottom: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //Product detail
          ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
              itemCount: refundAmountCalculateBloc.refundAmountCalculationResponse.productDetails!.length,
              itemBuilder: (context, index) {
              //Last index
              // if(index == (refundAmountCalculateBloc.refundAmountCalculationResponse.productDetails!.length-1)){
              //   return RefundAmountCalculationCommonWidgets.title(
              //       refundAmountCalculationDetail: refundAmountCalculateBloc.refundAmountCalculationResponse.productDetails!.last,
              //     price: Text("${refundAmountCalculateBloc.refundAmountCalculationResponse.productDetails![index].orderBreakupItemValue!}",style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack),),
              //
              //   );
              // }
              //Normal
                return RefundAmountCalculationCommonWidgets.subTitle(
                  refundAmountCalculationDetail: refundAmountCalculateBloc.refundAmountCalculationResponse.productDetails![index],
                  breakupPriceVisible: refundAmountCalculateBloc.shoBreakup,
                );
              }),
          //Divider
          Container(
            margin: const EdgeInsets.symmetric(vertical: 11,horizontal: 50),
            padding: const EdgeInsets.symmetric(vertical:10,horizontal: 10),
            child: const Divider(color: AppColors.lightStroke,height: 1),
          ),

          //Refund detail
          ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: refundAmountCalculateBloc.refundAmountCalculationResponse.refundDetails!.length,
              itemBuilder: (context, index) {
                //Last index
                // if(index == (refundAmountCalculateBloc.refundAmountCalculationResponse.refundDetails!.length-1)){
                //   return RefundAmountCalculationCommonWidgets.title(
                //       refundAmountCalculationDetail: refundAmountCalculateBloc.refundAmountCalculationResponse.refundDetails!.last,
                //     price: Text("${refundAmountCalculateBloc.refundAmountCalculationResponse.refundDetails![index].orderBreakupItemValue!}",style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),),
                //   );
                // }
                //Normal
                return RefundAmountCalculationCommonWidgets.subTitle(
                  refundAmountCalculationDetail: refundAmountCalculateBloc.refundAmountCalculationResponse.refundDetails![index],
                  breakupPriceVisible: refundAmountCalculateBloc.shoBreakup,
                );
              }),

        ],
      ),
    );
  }
//endregion
}
