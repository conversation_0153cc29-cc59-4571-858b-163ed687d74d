import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_details/buyer_my_order_details_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/order_history/order_history_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/order_invoice/order_invoice_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';


enum BuyerSubOrderState { Loading, Success, Failed, Empty }

class BuyerSubOrderBloc {
  // region Common Methods
  BuildContext context;
  final String  orderNumber;
  late BuyerMyOrderServices buyerMyOrderServices;

  late GetOrderResponse getSubOrdersResponse;


  ///Components
  List<SubOrder> waitingForConfirmation = [];
  List<SubOrder> confirmedNotYetShipped = [];
  List<SubOrder> scheduledForShipping = [];
  List<SubOrder> shippingInProgress = [];
  List<SubOrder> returns = [];
  List<SubOrder> delivered = [];
  List<SubOrder> paymentFailed = [];
  List<SubOrder> paymentPending = [];

  ///Cancelled
  List<SubOrder> cancelledByYouBeforeShipping = [];
  List<SubOrder> cancelledByYouAfterShipping = [];
  List<SubOrder> notConfirmedAndCancelled = [];
  List<SubOrder> sellerCancelledConfirmedOrder = [];
  List<SubOrder> cancelledByYouAfterSwadesicShipping = [];

  ///Returns
  List<SubOrder> returnRequested = [];
  List<SubOrder> returnConfirmed = [];
  List<SubOrder> returnInProgress = [];
  List<SubOrder> returnCompleted = [];
  List<SubOrder> refundOnHold = [];



  // endregion
  //region Controller
  final buyerSubOrderStateCtrl = StreamController<BuyerSubOrderState>.broadcast();
  final isInvoiceAvailableCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  BuyerSubOrderBloc(this.context, this.orderNumber);

  // endregion

  // region Init
  init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    getSubOrders();
  }
  // endregion


  //region Get Sub orders
  getSubOrders() async {
    //region Try
    try {
      //Loading
      // buyerSubOrderStateCtrl.sink.add(BuyerSubOrderState.Loading);
      // sellerAllOrderCtrl.sink.add(SellerAllOrderState.Loading);
      getSubOrdersResponse = await buyerMyOrderServices.getBuyerMyOrder(orderNumber: orderNumber);
      //Success
      buyerSubOrderStateCtrl.sink.add(BuyerSubOrderState.Success);
      //Filter
      filtering();
      //Invoice button active check
      invoiceButtonActiveCheck();

    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      buyerSubOrderStateCtrl.sink.add(BuyerSubOrderState.Failed);
      CommonMethods.toastMessage(error.message!, context);
    } catch (error) {
      buyerSubOrderStateCtrl.sink.add(BuyerSubOrderState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
//endregion


  ///Filtering
  //region Filtering
  void filtering(){
    ///Payment failed
    paymentFailed = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.paymentFailedStatus;
    }).toList();
    ///Payment pending
    paymentPending = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.paymentPendingStatus;
    }).toList();


    ///Waiting for confirmation
    waitingForConfirmation = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      // return element.suborderStatus == AppConstants.subOrderPaymentSuccessStatus || element.suborderStatus == AppConstants.subOrderPaymentPending;
      return element.suborderStatus == AppConstants.waitingForConfirmation;
    }).toList();

    ///Confirmed not yet shipped
    confirmedNotYetShipped = getSubOrdersResponse.orderList!.first.subOrderList!.where((element){
      return element.suborderStatus == AppConstants.subOrderConfirmedStatus;
    }).toList();

    ///Scheduled for shipping
    scheduledForShipping = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.subOrderScheduledForShippingStatus && element.packageNumber != null;
    }).toList();
    ///Shipping in progress
    shippingInProgress = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.deliveryInProgressStatus && element.packageNumber != null;
    }).toList();
    ///Delivered
    delivered = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.orderDeliveredStatus;
    }).toList();
    ///Returns
    returns = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.returnRequestedStatus
      || element.suborderStatus == AppConstants.returnRequestConfirmed
      || element.suborderStatus == AppConstants.refundHold
      || element.suborderStatus == AppConstants.returnedToSeller;

    }).toList();

    ///Cancelled by you before shipping

    /*
    Cancelled by you before Shipping :
    1. Sub order status should be "ORDER_CANCELLED_BY_BUYER"
    2. Sub order package number should be null.
     */
    cancelledByYouBeforeShipping = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.orderCanceledByBuyerStatus &&
      element.packageNumber == null;
    }).toList();
    ///Cancelled by you after shipping

    /*
    Cancelled by you after Shipping :
    1. Sub order status should be "ORDER_CANCELLED_BY_BUYER"
    2. Sub order package number should not be null.
     */
    cancelledByYouAfterShipping = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.orderCanceledByBuyerStatus &&
          element.packageNumber != null;
    }).toList();

       ///Cancelled by you after swadesic shipping

    /*
    Cancelled by you after swadesic Shipping :
    1. Sub order status should be "CANCELLED_IN_TRANSIT"
    2. Sub order package number should not be null.
     */
    cancelledByYouAfterSwadesicShipping = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.orderCancelledInTransit &&
          element.packageNumber != null;
    }).toList();

    ///Return requested
    returnRequested = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.returnRequestedStatus;
    }).toList();

    ///Return Confirmed
    returnConfirmed = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.returnRequestConfirmed;
    }).toList();

    ///Return in progress
    returnInProgress = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.returnInProgress;
    }).toList();

    ///Return completed
    returnCompleted = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.returnedToSeller;
    }).toList();

    ///Refund hold
    refundOnHold = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.refundHold;
    }).toList();


    ///Not Confirmed and cancelled
    /*
    There is 2 condition should be satisfy
    1. Sub order status should be "ORDER_AUTO_CANCELLED"
    2. Sub order should be "ORDER_CANCELLED_BY_SELLER" and Confirmed date should be null
     */
    notConfirmedAndCancelled = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.orderAutoCancelled
      ||(element.suborderStatus == AppConstants.orderCanceledBySellerStatus && element.confirmationDate == null);
    }).toList();
    ///Seller cancelled confirmed order
    sellerCancelledConfirmedOrder = getSubOrdersResponse.orderList!.first.subOrderList!.where((element) {
      return element.suborderStatus == AppConstants.orderCanceledBySellerStatus &&
          element.confirmationDate != null;
    }).toList();


  }
  //endregion


  ///On tap i=order detail
//region On tap order detail
  customerAndOrderDetailsBottomSheet(){

    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topRight: Radius.circular(20),topLeft: Radius.circular(20)),
        ),
        builder: (context) {
          return FractionallySizedBox(
            heightFactor: 0.8,
            child:Column(
              children:  [
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 20),
                  child: Text(
                    "Order details",
                    style: TextStyle(
                        fontFamily: "LatoSemibold",
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: AppColors.writingBlack),
                  ),
                ),
                Expanded(child:BuyerMyOrderDetailsScreen(orderNumber:orderNumber, order: getSubOrdersResponse.orderList!.first ,)),
              ],
            ),
          );
        });



  }
//endregion




  //region On tap order history
  goToOrderHistory(){
    var screen =  OrderHistoryScreen(subOrderList:getSubOrdersResponse.orderList!.first.subOrderList!,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region On tap report
  void onTapReport(){
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => const AlertDialog(
        content:IssueSuggestionDialog(),
      ),
    );
  }
  //endregion


  // region Go to invoice
  void goToInvoice(){
    var screen =  OrderInvoiceScreen(orderNumber:orderNumber,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion




  //region Invoice button active
  // Only if all the sub orders are closed
  // Closed: ORDER_AUTO_CANCELLED,ORDER_CANCELLED_BY_BUYER,ORDER_CANCELLED_BY_SELLER,
  // ORDER_DELIVERED ,RETURNED_TO_SELLER, REFUNDED,

  void invoiceButtonActiveCheck(){

    List<String> closedSubOrderStatus = [
      AppConstants.orderAutoCancelled,
      AppConstants.orderCanceledByBuyerStatus,
      AppConstants.orderCanceledBySellerStatus,
      AppConstants.orderDeliveredStatus,
      AppConstants.returnedToSeller,
      AppConstants.refunded,
    ];

    //Sub order loop

    for(var subOrder in getSubOrdersResponse.orderList!.first.subOrderList!){
      //If suborder status does not contain any of the closed status then button should be inactive
      if (!closedSubOrderStatus.contains(subOrder.suborderStatus!)) {
        isInvoiceAvailableCtrl.sink.add(false);
      }
      else{
        isInvoiceAvailableCtrl.sink.add(true);
      }

    }






  }

  //endregion

//region Dispose
  void dispose(){
    buyerSubOrderStateCtrl.close();
  }
//endregion


}
