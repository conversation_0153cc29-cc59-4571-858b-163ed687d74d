import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/seller_all_order_response/return_tracking_detail_ressponse.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum ReturnPickupDetailState { Loading, Success, Failed, Empty }

class ReturnPickupDetailsBloc {
  // region Common Variables
  final BuildContext context;
  final String packageNumber;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;

  late BuyerMyOrderServices buyerMyOrderServices;
  late ReturnTrackingDetailResponse returnTrackingDetailResponse;
  bool isSelfReturn = true;
  bool? isUrlValid;

  // Data storage for display only
  String returnPersonName = "";
  String returnPersonContact = "";
  String logisticPartner = "";
  String trackingLink = "";
  String trackingNumber = "";
  String groupName = "";
  String additionalNotes = "";

  // endregion

  //region Controller
  final returnPickupDetailCtrl = StreamController<ReturnPickupDetailState>.broadcast();
  //endregion

  // region | Constructor |
  ReturnPickupDetailsBloc(this.context, this.packageNumber, this.buyerSubOrderBloc, this.order);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    getReturnTrackingDetail(packageNumber: packageNumber);
  }
  // endregion

  //region Get return tracking detail
  Future<void> getReturnTrackingDetail({required String packageNumber}) async {
    //region Try
    try {
      returnPickupDetailCtrl.sink.add(ReturnPickupDetailState.Loading);
      returnTrackingDetailResponse = await buyerMyOrderServices.getReturnTrackingDetail(packageNumber: packageNumber);
      returnPickupDetailCtrl.sink.add(ReturnPickupDetailState.Success);
      //Add all data to variables
      addAllDataToField();
    }
    //endregion
    on ApiErrorResponseMessage {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    }
  }
  //endregion

  //region Add all data to the field
  void addAllDataToField() {
    //Group name
    groupName = returnTrackingDetailResponse.data!.displayReturnPackageNumber ?? "";

    //Self return
    if (returnTrackingDetailResponse.data!.selfReturnByStore ?? false) {
      isSelfReturn = true;
      returnPersonName = returnTrackingDetailResponse.data!.returnPersonName ?? "";
      returnPersonContact = returnTrackingDetailResponse.data!.returnPersonContact?.replaceAll("+91", "") ?? "";
    }

    //Logistic
    if (returnTrackingDetailResponse.data!.returnByLogisticPartner ?? false) {
      isSelfReturn = false;
      logisticPartner = returnTrackingDetailResponse.data!.returnPickupLogisticPartner ?? "";
      trackingNumber = returnTrackingDetailResponse.data!.returnTrackingNumber ?? "";
      trackingLink = returnTrackingDetailResponse.data!.returnTrackingLink ?? "";
    }

    //Notes
    additionalNotes = returnTrackingDetailResponse.data!.additionalReturnNotes ?? "";
  }
  //endregion

  //region Dispose
  void dispose() {
    returnPickupDetailCtrl.close();
  }
  //endregion
}
