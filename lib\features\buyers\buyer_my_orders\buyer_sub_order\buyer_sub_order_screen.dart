import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/buyer_returns/buyer_returns.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/cancelled_by_you_after_shipping/cancelled_by_you_after_shipping.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/cancelled_by_you_before_shipping/cancelled_by_you_before_shipping.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/cancelled_by_you_after_swadesic_shipping/cancelled_by_you_after_swadesic_shipping.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/confirmed_not_yet_shipped/confirmed_not_yet_shipped.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/delivered_successfully.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/not_confirmmed_and_cancelled/not_confirmmed_and_cancelled.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/payment_failed/payment_failed.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/payment_pending/payment_pending.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/seller_cancelled_confirmed_order/seller_cancelled_confirmed_order.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/scheduled_for_shipping/scheduled_for_shipping.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/shipping_in_progress/shipping_in_progress_screen.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/waiting_confirmation/waiting_confirmation.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_requested/return_requested_screen.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_confirmed/return_confirmed_screen.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_in_progress/return_in_progress_screen.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/return_completed/return_completed_screen.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/new_return_components/refund_on_hold/refund_on_hold_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/buyer_and_seller_order_card/buyer_and_seller_order_card.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class BuyerSubOrderScreen extends StatefulWidget {
  final String orderNumber;
  const BuyerSubOrderScreen({Key? key, required this.orderNumber})
      : super(key: key);

  @override
  State<BuyerSubOrderScreen> createState() => _BuyerSubOrderScreenState();
}

class _BuyerSubOrderScreenState extends State<BuyerSubOrderScreen> {
  //region Bloc
  late BuyerSubOrderBloc buyerSubOrderBloc;
  //endregion
  //region Init
  @override
  void initState() {
    buyerSubOrderBloc = BuyerSubOrderBloc(context, widget.orderNumber);
    buyerSubOrderBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    buyerSubOrderBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  //region Build
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,
      body: StreamBuilder<BuyerSubOrderState>(
          initialData: BuyerSubOrderState.Loading,
          stream: buyerSubOrderBloc.buyerSubOrderStateCtrl.stream,
          builder: (context, snapshot) {
            if (snapshot.data == BuyerSubOrderState.Success) {
              return body();
            }
            if (snapshot.data == BuyerSubOrderState.Loading) {
              return AppCommonWidgets.appCircularProgress();
            }
            return AppCommonWidgets.errorWidget(onTap: () {
              buyerSubOrderBloc.getSubOrders();
            });
          }),
    );
  }
  //endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: widget.orderNumber,
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
        isCustomMenuVisible: true,
        customMenuButton: myMenuButton());
  }

//endregion

  //region Menu button
  Widget myMenuButton() {
    return StreamBuilder<bool>(
        stream: buyerSubOrderBloc.isInvoiceAvailableCtrl.stream,
        initialData: false,
        builder: (context, snapshot) {
          return PopupMenuButton(
            // shadowColor: Colors.transparent,
            // add icon, by default "3 dot" icon
            // icon: Icon(Icons.book)
            padding: EdgeInsets.zero,
            icon: SvgPicture.asset(AppImages.drawerIcon),
            itemBuilder: (context) {
              return [
                ///Order history
                PopupMenuItem<int>(
                  height: 0,
                  value: 0,
                  onTap: () async {
                    await Future.delayed(Duration.zero);

                    buyerSubOrderBloc.goToOrderHistory();
                  },
                  padding: EdgeInsets.zero,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        width: 150,
                        padding: const EdgeInsets.all(10),
                        child: AppCommonWidgets.menuText(
                            text: AppStrings.orderHistory),
                      ),
                      divider()
                    ],
                  ),
                ),

                ///Invoice
                PopupMenuItem<int>(
                  height: 0,
                  value: 0,
                  onTap: () async {
                    await Future.delayed(Duration.zero);

                    snapshot.data!
                        ? buyerSubOrderBloc.goToInvoice()
                        : CommonMethods.toastMessage(
                            "The invoice will not be available until all orders are closed.",
                            context,
                            toastShowTimer: 5);
                  },
                  padding: EdgeInsets.zero,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        width: 150,
                        padding: const EdgeInsets.all(10),
                        child: AppCommonWidgets.menuText(
                            text:
                                "${AppStrings.viewInvoice}${snapshot.data! ? "" : " (Not available yet)"}"),
                      ),
                      divider()
                    ],
                  ),
                ),

                ///Guides
                PopupMenuItem<int>(
                  height: 0,
                  value: 0,
                  onTap: () async {
                    await Future.delayed(Duration.zero);
                  },
                  padding: EdgeInsets.zero,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        width: 150,
                        padding: const EdgeInsets.all(10),
                        child:
                            AppCommonWidgets.menuText(text: AppStrings.guides),
                      ),
                      divider()
                    ],
                  ),
                ),

                ///Report
                PopupMenuItem<int>(
                  height: 0,
                  value: 0,
                  onTap: () {
                    buyerSubOrderBloc.onTapReport();
                  },
                  padding: EdgeInsets.zero,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        width: 150,
                        padding: const EdgeInsets.all(10),
                        child:
                            AppCommonWidgets.menuText(text: AppStrings.report),
                      ),
                      // divider()
                    ],
                  ),
                ),
              ];
            },
          );
        });
  }
  //endregion

//region Body
  Widget body() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        await buyerSubOrderBloc.getSubOrders();
      },
      child: ListView(
        // shrinkWrap: true,
        // mainAxisSize: MainAxisSize.max,
        // mainAxisAlignment: MainAxisAlignment.center,
        // crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          order(),

          ///Payment failed
          paymentFailed(),

          ///Payment pending
          paymentPending(),

          ///Waiting for confirmation
          waitingForConfirmation(),

          ///Confirmed not yet shipped
          confirmedNotYetShipped(),

          ///Shipping in progress
          shippingInProgress(),

          ///Scheduled for shipping
          scheduledForShipping(),

          ///Delivered
          orderDelivered(),

          ///Cancel
          ///Cancelled by you before shipping
          cancelledByYouBeforeShipping(),

          ///Cancelled by you after shipping
          cancelledByYouAfterShipping(),

          ///Cancelled by you after swadesic shipping
          cancelledByYouAfterSwadesicShipping(),

          ///Not confirm and cancelled
          notConfirmedAndCancelled(),

          ///Seller cancelled confirmed order
          sellerCancelledConfirmedOrder(),

          ///returns
          // returns(),
          ///Bottom space
          // AppCommonWidgets.bottomListSpace(context: context),
          ///return Requested
          returnRequested(),

          ///return Confirmed
          returnConfirmed(),

          ///return in progress
          returnInProgress(),

          ///return completed
          returnCompleted(),

          /// refund on hold
          refundOnHold(),
        ],
      ),
    );
  }
//endregion

//region Order
  Widget order() {
    return BuyerAndSellerOrderCard(
      order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
      isArrowVisible: false,
      buyerSellerOrderScreenContext: context,
      isSeller: false,
      storeAndProfilePlaceHolder: AppImages.storePlaceHolder,
    );
  }
//endregion

  ///Waiting for conformation
//region Waiting for conformation
  Widget waitingForConfirmation() {
    return Visibility(
        visible: buyerSubOrderBloc.waitingForConfirmation.isNotEmpty,
        child: WaitingForConfirmation(
          order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
          subOrderList: buyerSubOrderBloc.waitingForConfirmation,
          buyerSubOrderBloc: buyerSubOrderBloc,
        ));
  }

//endregion

  ///Confirmed not yet shipped
//region Confirmed not yet shipped
  Widget confirmedNotYetShipped() {
    return Visibility(
        visible: buyerSubOrderBloc.confirmedNotYetShipped.isNotEmpty,
        child: ConfirmedNotYetShipped(
          order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
          subOrderList: buyerSubOrderBloc.confirmedNotYetShipped,
          buyerSubOrderBloc: buyerSubOrderBloc,
        ));
  }

//endregion

  ///Scheduled for shipping
//region Scheduled for shipping
  Widget scheduledForShipping() {
    return Visibility(
      visible: buyerSubOrderBloc.scheduledForShipping.isNotEmpty,
      child: ScheduledForShipping(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.scheduledForShipping,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

  ///Shipping in progress
//region Shipping in progress
  Widget shippingInProgress() {
    return Visibility(
      visible: buyerSubOrderBloc.shippingInProgress.isNotEmpty,
      child: ShippingInProgressScreen(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        suborderList: buyerSubOrderBloc.shippingInProgress,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

  ///Order delivered
//region Order delivered
  Widget orderDelivered() {
    return Visibility(
      visible: buyerSubOrderBloc.delivered.isNotEmpty,
      child: DeliveredSuccessfully(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.delivered,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

  ///Cancelled
//region Cancelled by you before shipping
  Widget cancelledByYouBeforeShipping() {
    return Visibility(
      visible: buyerSubOrderBloc.cancelledByYouBeforeShipping.isNotEmpty,
      child: CancelledByYouBeforeShippingScreen(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.cancelledByYouBeforeShipping,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

  //region Cancelled by you after shipping
  Widget cancelledByYouAfterShipping() {
    return Visibility(
      visible: buyerSubOrderBloc.cancelledByYouAfterShipping.isNotEmpty,
      child: CancelledByYouAfterShippingScreen(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.cancelledByYouAfterShipping,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

  //region Cancelled by you after swadesic shipping
  Widget cancelledByYouAfterSwadesicShipping() {
    return Visibility(
      visible: buyerSubOrderBloc.cancelledByYouAfterSwadesicShipping.isNotEmpty,
      child: CancelledByYouAfterSwadesicShippingScreen(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.cancelledByYouAfterSwadesicShipping,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

//region Not Confirmed and cancelled
  Widget notConfirmedAndCancelled() {
    return Visibility(
      visible: buyerSubOrderBloc.notConfirmedAndCancelled.isNotEmpty,
      child: NotConfirmedAndCancelled(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.notConfirmedAndCancelled,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

//region Seller cancelled confirmed order
  Widget sellerCancelledConfirmedOrder() {
    return Visibility(
      visible: buyerSubOrderBloc.sellerCancelledConfirmedOrder.isNotEmpty,
      child: SellerCancelledConfirmedOrder(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.sellerCancelledConfirmedOrder,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

  //region returns
  Widget returns() {
    return Visibility(
      visible: buyerSubOrderBloc.returns.isNotEmpty,
      child: BuyerReturns(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.returns,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }

//endregion
// region Payment return
  Widget paymentFailed() {
    return Visibility(
      visible: buyerSubOrderBloc.paymentFailed.isNotEmpty,
      child: PaymentFailed(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.paymentFailed,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

// region Payment payment
  Widget paymentPending() {
    return Visibility(
      visible: buyerSubOrderBloc.paymentPending.isNotEmpty,
      child: PaymentPending(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        subOrderList: buyerSubOrderBloc.paymentPending,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
//endregion

//region Return requested
  Widget returnRequested() {
    return Visibility(
      visible: buyerSubOrderBloc.returnRequested.isNotEmpty,
      child: ReturnRequestedScreen(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        suborderList: buyerSubOrderBloc.returnRequested,
        buyerSuborderBloc: buyerSubOrderBloc,
      ),
    );
  }

  //region Return in progress
  Widget returnInProgress() {
    return Visibility(
      visible: buyerSubOrderBloc.returnInProgress.isNotEmpty,
      child: ReturnInProgressScreen(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        suborderList: buyerSubOrderBloc.returnInProgress,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }

  //region Return in progress
  Widget returnConfirmed() {
    return Visibility(
      visible: buyerSubOrderBloc.returnConfirmed.isNotEmpty,
      child: ReturnConfirmedScreen(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        suborderList: buyerSubOrderBloc.returnConfirmed,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }

  //region Return completed
  Widget returnCompleted() {
    return Visibility(
      visible: buyerSubOrderBloc.returnCompleted.isNotEmpty,
      child: ReturnCompletedScreen(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        suborderList: buyerSubOrderBloc.returnCompleted,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }

  // refund on hold
  Widget refundOnHold() {
    return Visibility(
      visible: buyerSubOrderBloc.refundOnHold.isNotEmpty,
      child: RefundOnHoldScreen(
        order: buyerSubOrderBloc.getSubOrdersResponse.orderList!.first,
        suborderList: buyerSubOrderBloc.refundOnHold,
        buyerSubOrderBloc: buyerSubOrderBloc,
      ),
    );
  }
}
