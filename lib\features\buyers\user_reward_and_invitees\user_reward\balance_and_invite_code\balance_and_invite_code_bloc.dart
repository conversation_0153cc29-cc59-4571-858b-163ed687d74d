import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/user_rewards_and_invitees_response/user_rewards.dart';
import 'package:swadesic/services/user_rewards_and_invitees_service/user_reward_and_invitees_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
enum BalanceAndInviteCodeState { Loading, Success, Failed }
class BalanceAndInviteCodeBloc {
  // region Common Variables
  BuildContext context;
  late UserRewards userRewards;

  // endregion


  //region Controller
  final balanceAndInviteCodeCtrl = StreamController<BalanceAndInviteCodeState>.broadcast();
  //endregion

  // region | Constructor |
  BalanceAndInviteCodeBloc(this.context);
  // endregion

  // region Init
  void init() {
    getUserReward();
  }
// endregion



  //region Get user reward
  Future <void> getUserReward() async {
    try {
      userRewards = await UserRewardAndInviteesService().getRewards();

      //Success
      balanceAndInviteCodeCtrl.sink.add(BalanceAndInviteCodeState.Success);


    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted?CommonMethods.toastMessage(error.message!, context):null;
      //Failed
      balanceAndInviteCodeCtrl.sink.add(BalanceAndInviteCodeState.Failed);
      return;
    } catch (error) {
      //Failed
      balanceAndInviteCodeCtrl.sink.add(BalanceAndInviteCodeState.Failed);
    }
  }
  //endregion


//region Dispose
  void dispose(){
    balanceAndInviteCodeCtrl.close();
  }
//endregion



}
