import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_history_screen_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/buyer_search_history_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/searched_keyword/searched_keyword_bloc.dart';
import 'package:swadesic/model/buyer_search_response/buyer_search_history_response.dart';
import 'package:swadesic/util/common_methods.dart';

class SearchedKeyWord extends StatefulWidget {
  final List<History> searchedKeywordList;
  final BuyerSearchHistoryBloc buyerSearchHistoryBloc;
  final BuyerSearchBloc buyerSearchBloc;

  const SearchedKeyWord({Key? key,  required this.searchedKeywordList, required this.buyerSearchHistoryBloc, required this.buyerSearchBloc}) : super(key: key);

  @override
  State<SearchedKeyWord> createState() => _SearchedKeyWordState();
}

class _SearchedKeyWordState extends State<SearchedKeyWord> {

  //region Bloc
  late SearchedKeyWordBloc searchedKeyWordBloc;
  //endregion
  //region Init
  @override
  void initState() {
    searchedKeyWordBloc = SearchedKeyWordBloc(context,widget.buyerSearchHistoryBloc);
    searchedKeyWordBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    searchedKeyWordBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }


  //region body
Widget body(){
    return StreamBuilder<bool>(
        stream: searchedKeyWordBloc.refreshCtrl.stream,
      builder: (context, snapshot) {
        ///If there is no product then
        if(widget.searchedKeywordList.isEmpty){
          return const SizedBox();
        }
        return Column(
          children: [
            ListView.builder(
              shrinkWrap: true,
                itemCount:widget.searchedKeywordList.length>3?searchedKeyWordBloc.itemCount:widget.searchedKeywordList.length,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder:(context,index){
              return BuyerSearchCommonWidgets.keyWords(history: widget.searchedKeywordList[index], onPressCross:(){
                searchedKeyWordBloc.onPressCross(history: widget.searchedKeywordList[index], searchedKeywordList:widget.searchedKeywordList);
              }, onTapKeyWord: (){
                BuyerSearchBloc.searchTextEditingCtrl.clear();
                BuyerSearchBloc.searchTextEditingCtrl.text = widget.searchedKeywordList[index].searchInputText!;
                //Call on change method
                widget.buyerSearchBloc.onChangeTextField();
                //Close keyboard
                CommonMethods.closeKeyboard(context);

              });
                }),
            InkWell(
                onTap: (){
                  searchedKeyWordBloc.onTapViewMore(isIncrease: true, searchedKeywordList: widget.searchedKeywordList);
                },
                child: Visibility(
                    visible: widget.searchedKeywordList.length > searchedKeyWordBloc.itemCount,
                    child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: "View more",isUnderline: true))),
          ],
        );
      }
    );
}
//endregion
}
