class ProductOption {
  String optionName;
  List<String> optionValues;

  ProductOption({
    required this.optionName,
    required this.optionValues,
  });

  ProductOption.fromJson(Map<String, dynamic> json)
      : optionName = json['option_name'] ?? '',
        optionValues = List<String>.from(json['option_values'] ?? []);

  Map<String, dynamic> toJson() {
    return {
      'option_name': optionName,
      'option_values': optionValues,
    };
  }

  ProductOption copyWith({
    String? optionName,
    List<String>? optionValues,
  }) {
    return ProductOption(
      optionName: optionName ?? this.optionName,
      optionValues: optionValues ?? this.optionValues,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductOption &&
        other.optionName == optionName &&
        _listEquals(other.optionValues, optionValues);
  }

  @override
  int get hashCode => optionName.hashCode ^ optionValues.hashCode;

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    if (identical(a, b)) return true;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}
