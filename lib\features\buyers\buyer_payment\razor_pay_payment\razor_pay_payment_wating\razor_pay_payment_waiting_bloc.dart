import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_payment/buyer_payment_status/buyer_payment_status_screen.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_payment_options_responses/payment_status_check.dart';
import 'package:swadesic/services/buyer_payment_services/buyer_payment_services.dart';
import 'package:swadesic/services/buyer_payment_services/upi_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:webview_flutter/webview_flutter.dart';

class RazorPayPaymentWaitingBloc {
  // region Common Methods
  BuildContext context;
  late PaymentStatusCheckResponse paymentStatusCheckResponse;
  String paymentStatus = "PAYMENT_PENDING";
  final String razorPayPaymentId;
  final String razorPayOrderId;
  final String razorPaySignature;
  final String transactionId;
  final String amount;
  final String orderNumber;

//TXN_FAILURE
  //PENDING

  // endregion
  //region Controller
  final screenRefreshCtrl = StreamController<bool>.broadcast();
  late WebViewController webViewController;

  //endregion

  // region | Constructor |
  RazorPayPaymentWaitingBloc(this.context,
      this.transactionId,
      this.amount,
      this.orderNumber,
      this.razorPayPaymentId,
      this.razorPayOrderId,
      this.razorPaySignature,);

  // endregion

  // region Init
  void init() async {
    // await Future.delayed(const Duration(seconds: 3));

    checkStatus();
  }

  // endregion

  //region Call upi status every 2 second
  void checkStatus() async {
    //Success
    if (paymentStatus == 'PAYMENT_SUCCESS') {
      // screenRefreshCtrl.sink.add(true);
      goToPaymentStatusScreen(paymentStatus: paymentStatus);
      //Clear shopping cart data model quantity
      ShoppingCartQuantityDataModel shoppingCartQuantityDataModel = Provider.of<ShoppingCartQuantityDataModel>(context, listen: false);
      shoppingCartQuantityDataModel.clearDara();
      return;
    }
    //Cancelled
    else if (paymentStatus != 'PAYMENT_SUCCESS' && paymentStatus != 'PAYMENT_PENDING') {
      // screenRefreshCtrl.sink.add(false);
      goToPaymentStatusScreen(paymentStatus: "PAYMENT_CANCELLED");
      return;
    } else {
      await Future.delayed(const Duration(milliseconds: 100));
      checkUpiPaymentStatus();
    }
  }

  //endregion

  //region Check upi payment status
  checkUpiPaymentStatus() async {
    // paymentStatusCheckResponse = await BuyerPaymentServices().getPaymentStatus(
    //   transactionId: transactionId,
    //   razorPayOrderId: razorPayOrderId,
    //   razorPayPaymentId: razorPayPaymentId,
    //   razorPaySignature: razorPaySignature,
    //   amount: amount.replaceAll("₹", ""),
    //   orderId: orderNumber,
    // );
    // //print(paymentStatusCheckResponse);
    // return;
    //region Try
    try {
      paymentStatusCheckResponse = await BuyerPaymentServices().getPaymentStatus(
        transactionId: transactionId,
        razorPayOrderId: razorPayOrderId,
        razorPayPaymentId: razorPayPaymentId,
        razorPaySignature: razorPaySignature,
        amount: amount.replaceAll("₹", ""),
        orderId: orderNumber,
      );
      //print(paymentStatusCheckResponse.message);
      // paymentStatusCheckResponse = await buyerPaymentServices.paymentStatusCheck();
      paymentStatus = paymentStatusCheckResponse.data!.body!.resultInfo!.resultStatus!;
      // //print(paymentStatus);
      checkStatus();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted?CommonMethods.toastMessage(error.message!, context):null;
    } catch (error) {
      //print(error);
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }

  //endregion

  //region  Html web
  String bankWeb() {
    return AppConstants.htmlForm;
  }

  //endregion

//region Go to Buyer payment Status Screen
  void goToPaymentStatusScreen({required String paymentStatus}) {
    var screen = BuyerPaymentStatus(
      paymentStatus: paymentStatus,
      paymentStatusCheckResponse: paymentStatusCheckResponse,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route).then((value) {
    //
    // });
    Navigator.pushReplacement(context, route).then((value) {});
  }

//endregion

//region Dispose
  void dispose() {
    screenRefreshCtrl.close();
  }
//endregion
}
