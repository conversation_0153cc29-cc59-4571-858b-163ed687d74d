import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/needResolution/need_resolution.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/speak_with_seller/speak_with_seller.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/not_delivered/not_delivered_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class NotDelivered extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final Order order;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final dynamic escalationBody;
  final bool isFromGroupHead ;



  const NotDelivered({Key? key, required this.subOrderList, required this.order,
    required this.buyerSubOrderBloc, this.escalationBody,  this.isFromGroupHead = true

  }) : super(key: key);

  @override
  State<NotDelivered> createState() => _NotDeliveredState();
}

class _NotDeliveredState extends State<NotDelivered> {
  // region Bloc
  late NotDeliveredBloc notDeliveredBloc;

  // endregion

  // region Init
  @override
  void initState() {
    //print("Is from group header level ? ${widget.isFromGroupHead}");
    notDeliveredBloc = NotDeliveredBloc(context,widget.subOrderList,widget.order,widget.buyerSubOrderBloc,);
    notDeliveredBloc.init();
    super.initState();
  }

  // endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }


  //region Body
Widget body(){
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Column(
          children: [
            SpeakWithSeller(subOrderList: notDeliveredBloc.subOrderList,
              buyerSubOrderBloc: notDeliveredBloc.buyerSubOrderBloc,
              order: notDeliveredBloc.order,
              title:AppStrings.getAnUpdate,
              subTitle: AppStrings.possibleReason,

            ),
            NeedResolution(subOrderList: notDeliveredBloc.subOrderList,
            buyerSubOrderBloc: notDeliveredBloc.buyerSubOrderBloc,
              order: notDeliveredBloc.order, escalationReason: AppStrings.buyerDidNotReceivedEscalationReason,
              packageReference:widget.isFromGroupHead?notDeliveredBloc.subOrderList.first.packageNumber:null, title:AppStrings.ifYouFeelAfter, subTitle:AppStrings.additionalNotesOnThis, buttonText:AppStrings.needResolutionMarkNotDelivered ,
            ),

          ],
        ),
      ),
    );
}
//endregion




}
