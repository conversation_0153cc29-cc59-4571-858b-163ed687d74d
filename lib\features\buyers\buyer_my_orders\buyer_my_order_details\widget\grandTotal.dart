import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_order_details/order_detail_common_widgets.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_order_details/seller_all_order_details_bloc.dart';
import 'package:swadesic/model/seller_all_order_response/seller_order_details.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class GrandTotal extends StatefulWidget {
  // final SellerAllOrderDetailBloc sellerAllOrderDetailBloc;
  final StreamController<bool> grandTotalCtrl;
  final SellerOrdersDetailsResponse sellerOrdersDetailsResponse;
  const GrandTotal({Key? key, required this.grandTotalCtrl, required this.sellerOrdersDetailsResponse}) : super(key: key);

  @override
  State<GrandTotal> createState() => _GrandTotalState();
}

class _GrandTotalState extends State<GrandTotal> {
  @override
  Widget build(BuildContext context) {
    return grandTotal();
  }


  //region Grand total
  Widget grandTotal() {
    return  StreamBuilder<bool>(
        stream: widget.grandTotalCtrl.stream,
        initialData: true,
        builder: (context, snapshot) {
          var isGrandTotalVisible = snapshot.data;
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //Header
              InkWell(
                onTap: (){
                  widget.grandTotalCtrl.sink.add(!snapshot.data!);
                  // widget.sellerAllOrderDetailBloc.onTapGrandTotal();
                },
                child: Container(
                  color: AppColors.lightWhite3,
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      //Grand total
                      Text(AppStrings.totalPaid,style: AppTextStyle.heading1Medium(textColor: AppColors.appBlack),),
                      // appText(AppStrings.grandTotal,color: AppColors.writingColor2,fontWeight: FontWeight.w600,fontFamily:AppConstants.rRegular,fontSize: 16 ),
                      Expanded(child: horizontalSizedBox(10)),
                      snapshot.data!?const RotatedBox(quarterTurns: 2,child:Icon(Icons.keyboard_arrow_down,weight: 2),):
                      const Icon(Icons.keyboard_arrow_down,weight: 2)
                    ],
                  ),

                ),
              ),
              //Grand total list
              grandTotalList(snapshot: snapshot),
            ],
          );
        }
    );
  }
//endregion



//region Grand total list
  Widget grandTotalList({required AsyncSnapshot<bool>  snapshot}) {
    return Container(
      margin: const EdgeInsets.only(top: 13,left: 17.5,right: 17.5,bottom: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //Product detail
          ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount:widget.sellerOrdersDetailsResponse.grandTotals!.length,
              itemBuilder: (context, index) {
                //Last index
                if(index == (widget.sellerOrdersDetailsResponse.grandTotals!.length-1)){
                  return OrderDetailCommonWidgets.title(
                    priceDetails: widget.sellerOrdersDetailsResponse.grandTotals!.last,
                    price: Text("${widget.sellerOrdersDetailsResponse.grandTotals![index].orderBreakupItemValue!.toString()}",
                      style:


                      widget.sellerOrdersDetailsResponse.grandTotals![index].orderBreakupItemText!.contains('Grand total')?
                        AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
                          :
                      AppTextStyle.settingText(textColor: AppColors.appBlack)

                      ,),

                  );
                }
                //Normal
                return OrderDetailCommonWidgets.subTitle(
                  priceDetails: widget.sellerOrdersDetailsResponse.grandTotals![index],
                  breakupPriceVisible:snapshot.data!,
                  // breakupPriceVisible:true,
                );
              }),


        ],
      ),
    );
  }
//endregion


  
}
