import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_screen.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class AffiliateProgramWithAppBar extends StatefulWidget {
  const AffiliateProgramWithAppBar({super.key});

  @override
  State<AffiliateProgramWithAppBar> createState() => _AffiliateProgramWithAppBarState();
}

class _AffiliateProgramWithAppBarState extends State<AffiliateProgramWithAppBar> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      body: AffiliateProgramScreen(),

    );
  }



  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title:AppStrings.affiliateProgram ,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

//endregion
}
