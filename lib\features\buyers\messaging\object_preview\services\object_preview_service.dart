import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/features/buyers/messaging/object_preview/models/object_preview_models.dart';
import 'dart:developer' as developer;

/// Service class for fetching object previews
class ObjectPreviewService {
  static const String _tag = 'ObjectPreviewService';

  /// Fetches entity preview (User or Store) by reference
  Future<EntityPreview?> getEntityPreview(String reference) async {
    try {
      developer.log(
        '[ENTER] getEntityPreview(): Fetching entity preview for reference: $reference',
        name: _tag
      );

      final query = '''
      query GetEntityPreview {
        getEntityPreview(reference: "$reference") {
          reference
          entityType
          handle
          name
          icon
        }
      }
      ''';

      final response = await _executeGraphQLQuery(query);

      if (response != null && 
          response.containsKey('data') && 
          response['data'] != null && 
          response['data']['getEntityPreview'] != null) {
        
        final entityData = response['data']['getEntityPreview'];
        final entity = EntityPreview.fromJson(entityData);
        
        developer.log(
          '[EXIT] getEntityPreview(): Successfully fetched entity preview: $entity',
          name: _tag
        );
        
        return entity;
      }

      developer.log(
        '[EXIT] getEntityPreview(): No entity preview found for reference: $reference',
        name: _tag
      );
      return null;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] getEntityPreview(): Failed to fetch entity preview: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Fetches post preview by reference
  Future<PostPreview?> getPostPreview(String reference) async {
    try {
      developer.log(
        '[ENTER] getPostPreview(): Fetching post preview for reference: $reference',
        name: _tag
      );

      final query = '''
      query GetPostPreview {
        getPostPreview(reference: "$reference") {
          contentType
          postReference
          postText
          postImages {
            mediaId
            mediaType
            mediaPath
            order
          }
          createdBy {
            reference
            entityType
            handle
            name
            icon
          }
          isDeleted
        }
      }
      ''';

      final response = await _executeGraphQLQuery(query);

      if (response != null && 
          response.containsKey('data') && 
          response['data'] != null && 
          response['data']['getPostPreview'] != null) {
        
        final postData = response['data']['getPostPreview'];
        final post = PostPreview.fromJson(postData);
        
        developer.log(
          '[EXIT] getPostPreview(): Successfully fetched post preview',
          name: _tag
        );
        
        return post;
      }

      developer.log(
        '[EXIT] getPostPreview(): No post preview found for reference: $reference',
        name: _tag
      );
      return null;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] getPostPreview(): Failed to fetch post preview: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Fetches product preview by reference
  Future<ProductPreview?> getProductPreview(String reference) async {
    try {
      developer.log(
        '[ENTER] getProductPreview(): Fetching product preview for reference: $reference',
        name: _tag
      );

      final query = '''
      query GetProductPreview {
        getProductPreview(reference: "$reference") {
          productid
          productReference
          productName
          brandName
          productImages {
            mediaId
            mediaType
            mediaPath
            order
          }
          createdBy {
            reference
            entityType
            handle
            name
            icon
          }
        }
      }
      ''';

      final response = await _executeGraphQLQuery(query);

      if (response != null && 
          response.containsKey('data') && 
          response['data'] != null && 
          response['data']['getProductPreview'] != null) {
        
        final productData = response['data']['getProductPreview'];
        final product = ProductPreview.fromJson(productData);
        
        developer.log(
          '[EXIT] getProductPreview(): Successfully fetched product preview',
          name: _tag
        );
        
        return product;
      }

      developer.log(
        '[EXIT] getProductPreview(): No product preview found for reference: $reference',
        name: _tag
      );
      return null;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] getProductPreview(): Failed to fetch product preview: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Fetches order preview by reference
  Future<OrderPreview?> getOrderPreview(String reference) async {
    try {
      developer.log(
        '[ENTER] getOrderPreview(): Fetching order preview for reference: $reference',
        name: _tag
      );

      final query = '''
      query GetOrderPreview {
        getOrderPreview(reference: "$reference") {
          orderNumber
          suborderNumber
          userReference
          userIcon
          userName
          storeReference
          storeIcon
          storeName
          productPreviewImage
        }
      }
      ''';

      final response = await _executeGraphQLQuery(query);

      if (response != null && 
          response.containsKey('data') && 
          response['data'] != null && 
          response['data']['getOrderPreview'] != null) {
        
        final orderData = response['data']['getOrderPreview'];
        final order = OrderPreview.fromJson(orderData);
        
        developer.log(
          '[EXIT] getOrderPreview(): Successfully fetched order preview',
          name: _tag
        );
        
        return order;
      }

      developer.log(
        '[EXIT] getOrderPreview(): No order preview found for reference: $reference',
        name: _tag
      );
      return null;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] getOrderPreview(): Failed to fetch order preview: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Determines the type of object from its reference and fetches the appropriate preview
  Future<dynamic> getObjectPreview(String reference) async {
    try {
      developer.log(
        '[ENTER] getObjectPreview(): Determining object type for reference: $reference',
        name: _tag
      );

      // Determine object type based on reference prefix
      if (reference.startsWith('U')) {
        // User reference
        return await getEntityPreview(reference);
      } else if (reference.startsWith('S')) {
        // Store reference
        return await getEntityPreview(reference);
      } else if (reference.startsWith('PO')) {
        // Post reference
        return await getPostPreview(reference);
      } else if (reference.startsWith('P')) {
        // Product reference
        return await getProductPreview(reference);
      } else if (reference.startsWith('O')) {
        // Order reference
        return await getOrderPreview(reference);
      }

      developer.log(
        '[EXIT] getObjectPreview(): Unknown object type for reference: $reference',
        name: _tag
      );
      return null;
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] getObjectPreview(): Failed to determine object type: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Executes a GraphQL query and returns the response
  Future<Map<String, dynamic>?> _executeGraphQLQuery(String query) async {
    try {
      final response = await http.post(
        Uri.parse(AppConstants.graphQlUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${AppConstants.appData.accessToken}',
        },
        body: jsonEncode({
          'query': query.replaceAll('\n', ''),
        }),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        developer.log(
          '[ERROR] _executeGraphQLQuery(): GraphQL request failed with status: ${response.statusCode}, body: ${response.body}',
          name: _tag
        );
        return null;
      }
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] _executeGraphQLQuery(): Failed to execute GraphQL query: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }
}
