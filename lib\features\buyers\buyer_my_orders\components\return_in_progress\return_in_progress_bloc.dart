import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/return_in_progress/tracking_return_status/track_return_status.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class ReturnInProgressBloc {
  // region Common Variables
  BuildContext context;
  final BuyerMyOrdersBloc buyerMyOrdersBloc;
  late BuyerMyOrderServices buyerMyOrderServices;
  final Order store;

  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  ReturnInProgressBloc(this.context, this.store, this.buyerMyOrdersBloc);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();

  }
// endregion



// region On tap Tracking detail
  Future onTapCancel(List<SubOrder> suborderList,BuyerMyOrdersBloc buyerMyOrdersBloc)async{

    // return CommonMethods.sellerAndBuyerOrderBottomSheet(
    //   subOrderList: suborderList,
    //   context: context,
    //   screen:BuyerCancellingOrderScreen(subOrderList: suborderList, buyerMyOrdersBloc: buyerMyOrdersBloc, store: store,),
    //   title: AppStrings.cancellingTheOrder,
    //   subTitle: "",
    //   // buyerMyOrdersBloc: buyerMyOrdersBloc
    // ).then((value) {
    // });

  }
// endregion






// region On tap Tracking detail
  Future onTapTracking(List<SubOrder> suborderList,BuyerMyOrdersBloc buyerMyOrdersBloc){
    return CommonMethods.sellerAndBuyerOrderBottomSheet(
      subOrderList: "",
      context: context,
      screen:TrackReturnStatus(subOrderList: suborderList,),
      title: "Track the return package",
      subTitle: "",
    ).then((value){
    });

  }
// endregion



}
