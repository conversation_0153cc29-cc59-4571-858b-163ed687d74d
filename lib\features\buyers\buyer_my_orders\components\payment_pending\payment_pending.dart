import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/payment_pending/payment_pending_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class PaymentPending extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const PaymentPending({Key? key, required this.subOrderList, required this.buyerSubOrderBloc, required this.order}) : super(key: key);

  @override
  State<PaymentPending> createState() => _PaymentPendingState();
}

class _PaymentPendingState extends State<PaymentPending> {

  // region Bloc
  late PaymentPendingBloc paymentPendingBloc;

  // endregion

  // region Init
  @override
  void initState() {
    paymentPendingBloc = PaymentPendingBloc(context,widget.order,widget.buyerSubOrderBloc,widget.subOrderList);
    paymentPendingBloc.init();
    super.initState();
  }

  // endregion


  //region Dis update
  @override
  void didUpdateWidget(covariant PaymentPending oldWidget) {
    paymentPendingBloc = PaymentPendingBloc(context,widget.order,widget.buyerSubOrderBloc,widget.subOrderList);
    paymentPendingBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion


  //region Build

  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion


  //region Body
  Widget body(){
    // return Text("hehe");
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: const BoxDecoration(
          color: AppColors.appWhite,
          border: Border(
              bottom: BorderSide(
                  color: AppColors.lightStroke
              )
          )
      ),
      child: ExpandablePanel(
        //region Theme
        theme: const ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(),
        //endregion
        collapsed: const SizedBox(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: paymentPendingBloc.subOrderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        //Sub order filter

                        subOrderFilter(subOrder:paymentPendingBloc.subOrderList[index] ),
                        //Divider
                        Visibility(
                          visible: paymentPendingBloc.subOrderList.length-1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }
  //endregion


  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.thumbUpIcon,
      componentName: "Payment pending",
      suborderList: paymentPendingBloc.subOrderList,
      isEstimateDeliveryShow: false,
      isBuyerSideDeliveredOnShow: false,
      isSellerSideDelivered: false, additionalWidgets: Text(AppStrings.storeWillReceiveTheOrderWhenThePayment,style: AppTextStyle.normalTextBold(textColor: AppColors.appBlack),),
    );
  }

  //endregion


  //region Sub order filter
  Widget subOrderFilter({required SubOrder subOrder}){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

        verticalSizedBox(10),

        productInfoCard(
            context: context, subOrder:subOrder,
            isCancelledOnVisible: false,

            isYouWillReceiveVisible:false
        ),
        verticalSizedBox(10)
      ],
    );
  }
  //endregion







}
