import 'dart:async';
import 'package:flutter/material.dart';

class SelectRolesBloc {
  // region Common Methods
  BuildContext context;
  final Function(List<String>) onChangeData;

  List<Map<String, dynamic>> roleList = [
    {
      'title': "As a Buyer",
      "detail":
          "by supporting stores, exploring & purchasing products I love, engaging with the community.",
      "role": "BUYER",
      "isSelected": false,
      "isExpanded": false
    },
    {
      'title': "As a Seller",
      "detail":
          "by setting up a store, listing & selling products, engaging buyers & grow as a brand.",
      "role": "SELLER",
      "isSelected": false,
      "isExpanded": false
    },
    {
      'title': "As a Content Creator",
      "detail":
          "By sharing stories, creating reviews, and promoting Swadeshi products through engaging content.",
      "role": "CONTENT_CREATOR",
      "isSelected": false,
      "isExpanded": false
    },
    {
      'title': "As a Investor",
      "detail":
          "By funding Swadeshi startups, encouraging innovation, and helping businesses succeed.",
      "role": "INVESTOR",
      "isSelected": false,
      "isExpanded": false
    },
    {
      'title': "As a Volunteer",
      "detail":
          "By helping communities, organizing events, and promoting the Swadeshi mission.",
      "role": "VOLUNTEER",
      "isSelected": false,
      "isExpanded": false
    },
    {
      'title': "As a Partner/Collaborator",
      "detail":
          "By accelerating the Swadeshi mission with strategic partnerships or using businesses or organizations.",
      "role": "COLLABORATOR",
      "isSelected": false,
      "isExpanded": false
    }
  ];

  // endregion

  //region Controller
  final selectRoleCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  SelectRolesBloc(this.context, this.onChangeData);
  // endregion

  // region Init
  void init() {}
  // endregion

//region On select Role
  void onSelectRole({required Map<String, dynamic> selectedRole}) {
    for (var data in roleList) {
      // If selected data is not selected then select
      if (data['role'] == selectedRole['role'] && !selectedRole['isSelected']) {
        // Mark as selected
        data['isSelected'] = true;
      }
      // If selected data is already selected then unselect
      else if (data['role'] == selectedRole['role'] &&
          selectedRole['isSelected']) {
        // Mark as unselected
        data['isSelected'] = false;
      }
    }

    // Update UI
    selectRoleCtrl.sink.add(true);

    // Send back selected roles
    List<String> selectedRoles = roleList
        .where((element) => element['isSelected'])
        .map((e) => e['role'] as String) // Explicit cast to String
        .toList();
    onChangeData(selectedRoles);
  }
//endregion

//region Toggle Expanded State
  void toggleExpandedState({required Map<String, dynamic> role}) {
    for (var data in roleList) {
      if (data['role'] == role['role']) {
        // Toggle expanded state for the selected role
        data['isExpanded'] = !data['isExpanded'];
      }
    }

    // Update UI
    selectRoleCtrl.sink.add(true);
  }
//endregion

//region Dispose
  void dispose() {
    selectRoleCtrl.close();
  }
//endregion
}
