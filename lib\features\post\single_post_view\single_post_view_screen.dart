import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:readmore/readmore.dart';
import 'package:swadesic/features/common_buyer_seller_screen/adding_post_progress/adding_post_progress.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/post_screen_bloc.dart';
import 'package:swadesic/features/post/single_post_view/comment_field/comment_field.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/no_comment/no_comment.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/features/widgets/post_widgets/comment_card.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/app_link_services/page_url_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';

//region Post screen
class SinglePostViewScreen extends StatefulWidget {
  final String postReference;
  final bool isFromProductScreen;
  final bool? isRepostDetailVisible;

  const SinglePostViewScreen(
      {super.key,
      required this.postReference,
      this.isFromProductScreen = false,
      this.isRepostDetailVisible = false});

  @override
  State<SinglePostViewScreen> createState() => _SinglePostViewScreenState();
}
//endregion

class _SinglePostViewScreenState extends State<SinglePostViewScreen>
    with AutomaticKeepAliveClientMixin<SinglePostViewScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //Post screen
  late SinglePostViewBloc singlePostViewBloc;

  //region Init
  @override
  void initState() {
    singlePostViewBloc = SinglePostViewBloc(
        context, widget.postReference, widget.isFromProductScreen);
    singlePostViewBloc.init();
    super.initState();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    singlePostViewBloc.dispose();
    super.dispose();
  }

  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    super.build(
        context); // Call super.build to maintain AutomaticKeepAliveClientMixin

    // Set page URL for web app navigation using the same URL as sharing
    String postShareUrl = AppLinkCreateService()
        .createPostLink(postReference: widget.postReference);

    // Extract path from full URL for web navigation
    String urlPath = postShareUrl;
    try {
      Uri uri = Uri.parse(postShareUrl);
      urlPath = '${uri.path}${uri.query.isNotEmpty ? '?${uri.query}' : ''}';
    } catch (e) {
      // Use full URL if parsing fails
    }

    PageUrlService.setPageUrlAfterBuild(
        urlPath, widget.postReference.startsWith("PO") ? 'Post' : 'Comment');

    return Scaffold(
      appBar: appBar(),
      body: GestureDetector(
          onTap: () {
            CommonMethods.closeKeyboard(context);
          },
          child: body()),
    );
  }

  //endregion

  //region Body
  Widget body() {
    return Container(
        child:
            widget.isFromProductScreen ? onlyComments() : singlePostOrComment()
        // widget.isFromProductScreen?postList():onlyComments(),
        // child:rootComment(),
        );
  }

//endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: widget.postReference.startsWith("PO")
            ? AppStrings.postTitle
            : widget.isFromProductScreen
                ? AppStrings.productComment
                : AppStrings.comment,
        isCartVisible: false,
        isMembershipVisible: false,
        isDefaultMenuVisible: false);
  }

  //endregion

//region Single post or comment
  Widget singlePostOrComment() {
// // Get reference to the PostDataModel using Provider
//     var postDataModel = Provider.of<PostDataModel>(context, listen: true);
//
    return Consumer<PostDataModel>(
      builder: (BuildContext context, PostDataModel data, Widget? child) {
        List<PostDetail> singlePost;
        singlePost = data.allPostDetailList
            .where((element) =>
                element.postOrCommentReference == widget.postReference)
            .toList();

        //print("We got the single post ${singlePost.length}");
        return Column(
          children: [
            Expanded(
              child: StreamBuilder<SinglePostViewState>(
                  stream: singlePostViewBloc.singlePostStateCtrl.stream,
                  initialData: SinglePostViewState.Loading,
                  builder: (context, snapshot) {
                    //Success
                    if (snapshot.data == SinglePostViewState.Success) {
                      //If no single post
                      if (singlePost.isEmpty) {
                        return const NoResult(
                          message: AppStrings.looksLikeThereAreNoPost,
                        );
                        // return Center(child: Text(AppStrings.noPostFound,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)));
                      }
                      return RefreshIndicator(
                        color: AppColors.brandBlack,
                        onRefresh: () async {
                          await singlePostViewBloc.getSinglePostOrComment();
                        },
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          controller: singlePostViewBloc.scrollController,
                          padding: const EdgeInsets.only(bottom: 50),
                          child: Column(
                            children: [
                              //Product info
                              productInfoInComment(),
                              //Commented on
                              commentedOn(commentOrPost: singlePost.first),
                              PostCard(
                                postDetail: singlePost.first,
                                isFromSinglePost: true,
                                isCustomTitleVisible:
                                    widget.isRepostDetailVisible,
                                customTitle: singlePostViewBloc
                                    .singlePostOrComment.contentHeaderText,
                                imageSize: 300,
                                isFullView: true,
                                onTapDelete: () {
                                  singlePostViewBloc.confirmDelete(
                                      postDetail: singlePost.first);
                                },
                                onTapDrawer: () {
                                  singlePostViewBloc.onTapDrawer(
                                      postDetail: singlePost.first);
                                },
                                onTapEdit: () {
                                  singlePostViewBloc.goToEditPost(
                                      postDetail: singlePost.first);
                                },
                                onTapHeart: () {
                                  singlePostViewBloc.onTapHeart(
                                      postDetail: singlePost.first);
                                },
                                onTapShare: () {
                                  singlePostViewBloc.onTapShare(
                                      postDetail: singlePost.first);
                                },
                                onTapProfileImage: () {
                                  singlePostViewBloc.onTapUserOrStoreIcon(
                                      reference: singlePost.first.createdBy!
                                          .userOrStoreReference!);
                                },
                                onTapPost: () {},
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              //Divider
                              const Divider(
                                  color: AppColors.borderColor1, height: 0.5),
                              //Root comment
                              rootComment(),
                              // paginationProgress()
                            ],
                          ),
                        ),
                      );
                    }
                    //Failed
                    if (snapshot.data == SinglePostViewState.Failed) {
                      return AppCommonWidgets.errorWidget(
                          errorMessage: AppStrings.commonErrorMessage,
                          onTap: () {
                            singlePostViewBloc.getSinglePostOrComment();
                          });
                    }
                    //Empty
                    if (snapshot.data == SinglePostViewState.Empty) {
                      return const NoResult(
                        message: AppStrings.looksLikeThereAreNoPost,
                      );
                    }
                    //Loading
                    if (snapshot.data == SinglePostViewState.Loading) {
                      return Container(
                          alignment: Alignment.center,
                          height: MediaQuery.of(context).size.height,
                          child: AppCommonWidgets.appCircularProgress());
                    }
                    return const SizedBox();
                  }),
            ),
            //Comment field
            commentField()
          ],
        );
      },
    );
  }

//endregion

//region Root comments
  Widget rootComment() {
    return Consumer<PostDataModel>(
      builder: (BuildContext context, PostDataModel data, Widget? child) {
        List<PostDetail> rootCommentList = [];
        //Collect the comments where parent comment id is equal to the  postReference
        // rootCommentList = data.allPostDetailList.where((element) => element.mainParentId == widget.postReference).toSet().toList();

        //If Level 1
        // if(widget.postReference.startsWith("PO") && !widget.isFromProductScreen ){
        //   rootCommentList = data.allPostDetailList.where((element) => element.mainParentId == widget.postReference).toSet().toList();
        // }
        // //If If level 2
        // if(widget.postReference.startsWith("CO") && !widget.isFromProductScreen ){
        //   rootCommentList = data.allPostDetailList.where((element) => element.parentCommentId == widget.postReference).toSet().toList();
        // }
        // else{
        //   rootCommentList = data.allPostDetailList.where((element) => element.mainParentId == widget.postReference).toSet().toList();
        // }
        //
        // rootCommentList = data.allPostDetailList.where((element) => element.postOrCommentReference == singlePostViewBloc.commentList.map((e) => e.postOrCommentReference)).toSet().toList();

        var commentReferencesSet = singlePostViewBloc.commentList
            .map((e) => e.postOrCommentReference)
            .toSet();
        rootCommentList = data.allPostDetailList
            .where((element) =>
                commentReferencesSet.contains(element.postOrCommentReference))
            .toList();

        // Sort from newest to oldest (reverse chronological order)
        rootCommentList
            .sort((a, b) => b.createdDate!.compareTo(a.createdDate!));

        //print("Total comment list is ${rootCommentList.length}");
        return StreamBuilder<CommentsState>(
            stream: singlePostViewBloc.commentStateCtrl.stream,
            initialData: CommentsState.Loading,
            builder: (context, snapshot) {
              //Success
              if (snapshot.data == CommentsState.Success) {
                //If no single post
                if (rootCommentList.isEmpty) {
                  return const NoComment();
                }
                return ListView.builder(
                    itemCount: rootCommentList.length + 1,
                    // controller: singlePostViewBloc.scrollController,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      if (index < rootCommentList.length) {
                        return CommentCard(
                          postDetail: rootCommentList[index],
                          onTapDelete: () {
                            singlePostViewBloc.confirmDelete(
                                postDetail: rootCommentList[index]);
                          },
                          onTapDrawer: () {
                            singlePostViewBloc.onTapDrawer(
                                postDetail: rootCommentList[index]);
                          },
                          onTapEdit: () {
                            singlePostViewBloc.goToEditPost(
                                postDetail: rootCommentList[index]);
                          },
                          onTapHeart: () {
                            singlePostViewBloc.onTapHeart(
                                postDetail: rootCommentList[index]);
                          },
                          onTapReport: () {},
                          onTapShare: () {
                            singlePostViewBloc.onTapShare(
                                postDetail: rootCommentList[index]);
                          },
                          onTapProfileImage: () {
                            singlePostViewBloc.onTapUserOrStoreIcon(
                                reference: rootCommentList[index]
                                    .createdBy!
                                    .userOrStoreReference!);
                          },
                          onTapPost: () {
                            singlePostViewBloc.goToSinglePostView(
                                postReference: rootCommentList[index]
                                    .postOrCommentReference!);
                          },
                          onTapReply: () {
                            //Take out child comment reference also take out handle
                            singlePostViewBloc.replyCommentOrPostDetail = {
                              "reference":
                                  rootCommentList[index].postOrCommentReference,
                              "handle": rootCommentList[index].createdBy!.handle
                            };
                            singlePostViewBloc.commentFieldsBloc.onTapReply(
                                replyCommentOrPostDetail: singlePostViewBloc
                                    .replyCommentOrPostDetail);

                            // singlePostViewBloc.onTapReply(childComment: rootCommentList[index]);

                            // singlePostViewBloc.goToSinglePostView(postReference: rootCommentList[index].postOrCommentReference!);
                          },
                          singlePostViewBloc: singlePostViewBloc,
                        );
                      } else {
                        return paginationProgress();

                        // paginationProgress();
                      }
                    });
              }
              //Failed
              if (snapshot.data == CommentsState.Failed) {
                return AppCommonWidgets.errorWidget(
                    errorMessage: AppStrings.commonErrorMessage,
                    onTap: () {
                      singlePostViewBloc.init();
                    });
              }
              //Empty
              if (snapshot.data == CommentsState.Empty) {
                return const NoComment();
              }
              //Loading
              if (snapshot.data == CommentsState.Loading) {
                return AppCommonWidgets.appCircularProgress();
              }
              return const SizedBox();
            });
      },
    );
  }

//endregion

  ///From product screen

//region Only comments
  Widget onlyComments() {
    return Column(
      children: [
        //Product info
        productInfoInComment(),
        Expanded(
          child: Consumer<PostDataModel>(
            builder: (BuildContext context, PostDataModel data, Widget? child) {
              List<PostDetail> rootCommentList = [];
              //Collect the comments where parent comment id is equal to the  postReference
              // rootCommentList = data.allPostDetailList.where((element) => element.mainParentId == widget.postReference).toSet().toList();

              //If Level 1
              if (widget.postReference.startsWith("PO") &&
                  !widget.isFromProductScreen) {
                rootCommentList = data.allPostDetailList
                    .where((element) =>
                        element.mainParentId == widget.postReference)
                    .toSet()
                    .toList();
              }
              //If If level 2
              if (widget.postReference.startsWith("CO") &&
                  !widget.isFromProductScreen) {
                rootCommentList = data.allPostDetailList
                    .where((element) =>
                        element.parentCommentId == widget.postReference)
                    .toSet()
                    .toList();
              } else {
                rootCommentList = data.allPostDetailList
                    .where((element) =>
                        element.mainParentId == widget.postReference)
                    .toSet()
                    .toList();
              }

              // rootCommentList = data.allPostDetailList.where((element) => element.postOrCommentReference == singlePostViewBloc.commentList.map((e) => e.postOrCommentReference)).toSet().toList();

              var commentReferencesSet = singlePostViewBloc.commentList
                  .map((e) => e.postOrCommentReference)
                  .toSet();
              rootCommentList = data.allPostDetailList
                  .where((element) => commentReferencesSet
                      .contains(element.postOrCommentReference))
                  .toList();

              // Sort from newest to oldest (reverse chronological order)
              rootCommentList
                  .sort((a, b) => b.createdDate!.compareTo(a.createdDate!));

              //print("Total comment list is ${rootCommentList.length}");
              return StreamBuilder<CommentsState>(
                  stream: singlePostViewBloc.commentStateCtrl.stream,
                  initialData: CommentsState.Loading,
                  builder: (context, snapshot) {
                    //Success
                    if (snapshot.data == CommentsState.Success) {
                      //If no single post
                      if (rootCommentList.isEmpty) {
                        return const NoResult(
                            message: AppStrings.noCommentFound);
                      }
                      return RefreshIndicator(
                        color: AppColors.brandBlack,
                        onRefresh: () async {
                          await singlePostViewBloc.getCommentList();
                        },
                        child: ListView.builder(
                            itemCount: rootCommentList.length + 1,
                            controller: singlePostViewBloc.scrollController,
                            shrinkWrap: true,
                            physics: const AlwaysScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              if (index < rootCommentList.length) {
                                return CommentCard(
                                  onTapReply: () {
                                    //Take out child comment reference also take out handle
                                    singlePostViewBloc
                                        .replyCommentOrPostDetail = {
                                      "reference": rootCommentList[index]
                                          .postOrCommentReference,
                                      "handle": rootCommentList[index]
                                          .createdBy!
                                          .handle
                                    };
                                    singlePostViewBloc.commentFieldsBloc
                                        .onTapReply(
                                            replyCommentOrPostDetail:
                                                singlePostViewBloc
                                                    .replyCommentOrPostDetail);

                                    // singlePostViewBloc.onTapReply(childComment: rootCommentList[index]);
                                  },
                                  postDetail: rootCommentList[index],
                                  onTapDelete: () {
                                    singlePostViewBloc.confirmDelete(
                                        postDetail: rootCommentList[index]);
                                  },
                                  onTapDrawer: () {
                                    singlePostViewBloc.onTapDrawer(
                                        postDetail: rootCommentList[index]);
                                  },
                                  onTapEdit: () {
                                    singlePostViewBloc.goToEditPost(
                                        postDetail: rootCommentList[index]);
                                  },
                                  onTapHeart: () {
                                    singlePostViewBloc.onTapHeart(
                                        postDetail: rootCommentList[index]);
                                  },
                                  onTapReport: () {},
                                  onTapShare: () {
                                    singlePostViewBloc.onTapShare(
                                        postDetail: rootCommentList[index]);
                                  },
                                  onTapProfileImage: () {
                                    singlePostViewBloc.onTapUserOrStoreIcon(
                                        reference: rootCommentList[index]
                                            .createdBy!
                                            .userOrStoreReference!);
                                  },
                                  onTapPost: () {
                                    singlePostViewBloc.goToSinglePostView(
                                        postReference: rootCommentList[index]
                                            .postOrCommentReference!);
                                  },
                                  singlePostViewBloc: singlePostViewBloc,
                                );
                              } else {
                                return paginationProgress();
                              }
                            }),
                      );
                    }
                    //Failed
                    if (snapshot.data == CommentsState.Failed) {
                      return AppCommonWidgets.errorWidget(
                          errorMessage: AppStrings.commonErrorMessage,
                          onTap: () {
                            singlePostViewBloc.init();
                          });
                    }
                    //Empty
                    if (snapshot.data == CommentsState.Empty) {
                      return const NoResult(message: AppStrings.noCommentFound);
                    }
                    //Loading
                    if (snapshot.data == CommentsState.Loading) {
                      return AppCommonWidgets.appCircularProgress();
                    }
                    return const SizedBox();
                  });
            },
          ),
        ),
        //Comment field
        commentField()
      ],
    );
  }

//endregion

  ///Pagination
//region Pagination progress
  Widget paginationProgress() {
    return ValueListenableBuilder<CommentPaginationState>(
      valueListenable: singlePostViewBloc.commentPaginationStateCtrl,
      builder: (context, data, _) {
        //Success
        if (data == CommentPaginationState.Success) {
          return const Text("");
        }
        //End
        if (data == CommentPaginationState.End) {
          return const Text("");
        }
        //Loading
        if (data == CommentPaginationState.Loading) {
          return AppCommonWidgets.appCircularProgress(
              isPaginationProgress: true);
        }
        return const SizedBox();
      },
    );
  }

//endregion

  ///Commented on
  //region Commented on
  Widget commentedOn({required PostDetail commentOrPost}) {
    return Visibility(
      //Level should not be 0 and should not from product comment
      visible: commentOrPost.level != null && commentOrPost.level != 0 && !widget.isFromProductScreen,
      child: InkWell(
        onTap: () {
          singlePostViewBloc.goToSinglePostView(
              postReference: commentOrPost.level == 1
                  ? commentOrPost.mainParentId!
                  : commentOrPost.parentCommentId!);
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              SvgPicture.asset(
                AppImages.goToMainPOst,
                height: 24,
                width: 24,
              ),
              const SizedBox(
                height: 3,
              ),
              Flexible(
                child: RichText(
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  text: TextSpan(
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack1),
                    children: <TextSpan>[
                      TextSpan(
                        text: commentOrPost.commentType == "REVIEW"
                            ? "review on "
                            : "commented on ",
                        style: AppTextStyle.smallText(
                            textColor: AppColors.writingBlack1),
                      ),
                      TextSpan(
                        text: '${commentOrPost.parentHandle}',
                        style: AppTextStyle.smallText(
                            textColor: AppColors.brandBlack),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            OnTapTag(context, commentOrPost.parentHandle!);
                          },
                      ),
                      //Identify previous one is comment or post
                      //L1 Comment
                      if (commentOrPost.level == 1)
                        TextSpan(
                          text:
                              '\'s ${commentOrPost.mainParentId!.startsWith("CO") ? "comment" : commentOrPost.mainParentId!.startsWith("PO") ? "post" : "product"}',
                          style: AppTextStyle.smallTextRegular(
                              textColor: AppColors.writingBlack0),
                        ),

                      if (commentOrPost.level == 2)
                        TextSpan(
                          text:
                              '\'s ${commentOrPost.parentCommentId!.startsWith("CO") ? "comment" : commentOrPost.parentCommentId!.startsWith("PO") ? "post" : "product"}',
                          style: AppTextStyle.smallTextRegular(
                              textColor: AppColors.writingBlack0),
                        )
                    ],
                  ),
                ),
              ),
              // Expanded(
              //   child: ReadMoreText(
              //     "@sushant_1hjhjhjhjhjhjhjhjhjhjhjhjhjjhj",
              //     trimMode: TrimMode.Line,
              //     trimLines: 3,
              //     colorClickableText: Colors.pink,
              //     style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              //     lessStyle: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
              //     moreStyle: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
              //     trimLength: 5,
              //     trimCollapsedText: AppStrings.more,
              //     trimExpandedText: " ${AppStrings.less}",
              //     textAlign: TextAlign.start,
              //     annotations: [
              //       // Annotation(
              //       //   regExp: RegExp(r'#([a-zA-Z0-9_]+)'),
              //       //   spanBuilder: ({required String text, TextStyle? textStyle}) => TextSpan(
              //       //     text: text,
              //       //     style: textStyle?.copyWith(color: Colors.blue),
              //       //   ),
              //       // ),
              //       //User name or handle
              //       Annotation(
              //         regExp: RegExp(r'@([a-zA-Z0-9_]+)'),
              //         spanBuilder: ({required String text, TextStyle? textStyle}) => TextSpan(
              //           text: text,
              //           style: textStyle?.copyWith(color: AppColors.brandGreen),
              //           recognizer: TapGestureRecognizer()..onTap = () {
              //             // Extract the username from the tapped text
              //             final userName = text.substring(1);
              //             OnTapTag(context,userName);
              //             //print(userName); // Print the username
              //           },
              //         ),
              //       ),
              //     ],
              //
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  //endregion

  //region Product info in comment
  Widget productInfoInComment() {
    return StreamBuilder<CommentsState>(
        stream: singlePostViewBloc.commentedProductCtrl.stream,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data == CommentsState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          if (snapshot.data == CommentsState.Success) {
            return InkWell(
              onTap: () {
                singlePostViewBloc.onTapProduct(
                    reference: singlePostViewBloc.product.productReference!);
              },
              child: Column(
                children: [
                  Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 5),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Expanded(
                            child: RichText(
                              textAlign: TextAlign.start,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              text: TextSpan(
                                style: AppTextStyle.contentHeading0(
                                    textColor: AppColors.writingBlack0),
                                children: <TextSpan>[
                                  TextSpan(
                                    text:
                                        "${singlePostViewBloc.product.brandName!} ",
                                    style: AppTextStyle.contentHeading0(
                                        textColor: AppColors.writingBlack0),
                                  ),
                                  TextSpan(
                                    text:
                                        singlePostViewBloc.product.productName!,
                                    style: AppTextStyle.contentText0(
                                        textColor: AppColors.writingBlack0),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          CustomImageContainer(
                            height: 35,
                            width: 35,
                            imageUrl:
                                singlePostViewBloc.product.prodImages!.isEmpty
                                    ? null
                                    : singlePostViewBloc
                                        .product.prodImages!.first.productImage,
                            imageType: CustomImageContainerType.product,
                            showShadow: false,
                          ),
                        ],
                      )),
                  //Divider
                  const Divider(color: AppColors.borderColor1, height: 0.5),
                ],
              ),
            );
          }
          return const SizedBox();
        });
  }

  //endregion

  ///Comment field
  //region Comment field
  Widget commentField() {
    return StreamBuilder<CommentsState>(
        stream: singlePostViewBloc.commentStateCtrl.stream,
        builder: (context, snapshot) {
          if (snapshot.data == CommentsState.Success) {
            return CommentField(
              singlePostViewBloc: singlePostViewBloc,
            );
          }
          return const SizedBox();
        });
  }
//endregion
}
