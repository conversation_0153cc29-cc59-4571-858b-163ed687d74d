import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_details/buyer_my_order_details_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_order_customer_details/seller_all_order_customer_details_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_order_details/seller_all_order_details_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';



class BuyerAndSellerOrderCardBloc {
  // region Common Methods
  BuildContext context;
  final Order order;
  final bool isSeller;
  String buyerOrderText = "";
  String sellerOrderText = "";



  // endregion
  //region Controller
  final cardRefreshCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  BuyerAndSellerOrderCardBloc(this.context, this.order, this.isSeller);

  // endregion

  // region Init
  init() {
    sellerCalculateWaitingOpenAndClose();
    buyerCalculateWaitingOpenAndClose();
  }
  // endregion


  ///Seller order text
  //region Seller Calculate waiting, open and closed
  void sellerCalculateWaitingOpenAndClose(){
    int waitingOrdersCount = 0;
    int openOrdersCount = 0;
    int closeOrdersCount = 0;

    //region Waiting waiting
    /*
      "WAITING_FOR_CONFIRMATION",
      "PAYMENT_SUCCESS",
      "RETURN_REQUESTED"
     */
    waitingOrdersCount = order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.subOrderPaymentSuccessStatus ||
        element.suborderStatus == AppConstants.waitingForConfirmation||
        element.suborderStatus == AppConstants.returnRequestedStatus
    ).toList().length;
    //endregion
    //region Open orders
    /*
    ORDER_CONFIRMED,
    DELIVERY_IN_PROGRESS,
    RETURN_FAILED,
    RETURN_IN_PROGRESS,
    RETURN_CONFIRMED,
    REFUND_HOLD
    ORDER_CANCELLED_BY_BUYER && package_number != null.(Buyer cancelled after shipping)
     */
    openOrdersCount =  order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.subOrderConfirmedStatus ||
        element.suborderStatus == AppConstants.subOrderScheduledForShippingStatus||
        element.suborderStatus == AppConstants.deliveryInProgressStatus||
        element.suborderStatus == AppConstants.returnFailed||
        element.suborderStatus == AppConstants.returnInProgress||
        element.suborderStatus == AppConstants.returnRequestConfirmed||
        element.suborderStatus == AppConstants.refundHold||
        (element.suborderStatus == AppConstants.orderCanceledByBuyerStatus && element.packageNumber !=null)
    ).toList().length;

    // Count CANCELLED_IN_TRANSIT separately as they will be shown as partial/full cancellations
    //endregion

    //region Closed order
    /*
    ORDER_DELIVERED,
    ORDER_CANCELLED_BY_SELLER,
    ORDER_AUTO_CANCELLED,
    RETURNED_TO_SELLER,
    ORDER_CANCELLED_BY_BUYER && package_number == null.(Buyer cancelled before shipping)
     */
    closeOrdersCount = order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.orderDeliveredStatus ||
        element.suborderStatus == AppConstants.orderCanceledBySellerStatus||
        element.suborderStatus == AppConstants.orderAutoCancelled||
        element.suborderStatus == AppConstants.returnedToSeller||
        (element.suborderStatus == AppConstants.orderCanceledByBuyerStatus && element.packageNumber ==null)
    ).toList().length;
    //endregion

    // Count partial and full cancellations
    int partialCancelCount = 0;
    int fullCancelCount = 0;

    // Group suborders by package number
    Map<String, List<String>> packageSuborderStatuses = {};

    // Collect all package numbers and their suborder statuses
    for (var suborder in order.subOrderList!) {
      if (suborder.displayPackageNumber != null) {
        String packageKey = suborder.displayPackageNumber!;

        if (!packageSuborderStatuses.containsKey(packageKey)) {
          packageSuborderStatuses[packageKey] = [];
        }

        packageSuborderStatuses[packageKey]!.add(suborder.suborderStatus ?? '');
      }
    }

    // Check each package for partial or full cancellation
    packageSuborderStatuses.forEach((packageNumber, statuses) {
      bool hasNonCancelledSuborders = statuses.any((status) =>
        status != AppConstants.orderCancelledInTransit);

      if (statuses.contains(AppConstants.orderCancelledInTransit)) {
        if (hasNonCancelledSuborders) {
          partialCancelCount++;
        } else {
          fullCancelCount++;
        }
      }
    });

    // Count cancelled in transit suborders
    int cancelledInTransitCount = order.subOrderList!.where((element) =>
        element.suborderStatus == AppConstants.orderCancelledInTransit
    ).toList().length;

    //Make all data to a single string
    sellerOrderText = "${waitingOrdersCount>0? "$waitingOrdersCount waiting,":""} ${openOrdersCount>0? "$openOrdersCount open,":""} ${cancelledInTransitCount>0? "$cancelledInTransitCount cancelled in transit,":""} ${partialCancelCount>0? "$partialCancelCount packages partial cancel,":""} ${fullCancelCount>0? "$fullCancelCount packages full cancel,":""} ${closeOrdersCount>0? "$closeOrdersCount closed,":""}" ;
    //Remove extra commas and remove last commas
    sellerOrderText = "Suborders: ${CommonMethods.removeExtraCommas(sellerOrderText)}";
    //Refresh
    cardRefreshCtrl.sink.add(true);
  }
  //endregion



  ///Buyer order text
  //region Buyer Calculate waiting, open and closed
  void buyerCalculateWaitingOpenAndClose(){
    int waitingOrdersCount = 0;
    int cancelledOrdersCount = 0;
    int confirmedOrdersCount = 0;
    int shippedOrdersCount = 0;
    int deliveredOrdersCount = 0;
    int returningOrdersCount = 0;
    int returnedOrdersCount = 0;
    int refundHold = 0;
    int paymentFailedCount = 0;
    int paymentPendingCount = 0;


    //region Waiting
    /*
      WAITING_FOR_CONFIRMATION
     */
    waitingOrdersCount = order.subOrderList!.where((element) =>
        element.suborderStatus == AppConstants.waitingForConfirmation
    ).toList().length;
//endregion

    //region Payment failed
    //PAYMENT_FAILED
    paymentFailedCount = order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.paymentFailedStatus
    ).toList().length;
    //endregion

    //region Payment pending
    //PAYMENT_PENDING
    paymentPendingCount = order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.paymentPendingStatus
    ).toList().length;
    //endregion





    //region Cancelled orders
    /*
    ORDER_CANCELLED_BY_SELLER,
    ORDER_CANCELLED_BY_BUYER,
    ORDER_AUTO_CANCELLED
     */

    cancelledOrdersCount =  order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.orderCanceledBySellerStatus ||
        element.suborderStatus == AppConstants.orderCanceledByBuyerStatus ||
        element.suborderStatus == AppConstants.orderAutoCancelled
    ).toList().length;

    // Count CANCELLED_IN_TRANSIT separately as they will be shown as partial/full cancellations
    //endregion

    //region Confirmed orders
    /*
   ORDER_CONFIRMED
     */

    confirmedOrdersCount =  order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.subOrderConfirmedStatus||
    element.suborderStatus == AppConstants.subOrderScheduledForShippingStatus
    ).toList().length;
    //endregion

    //region Shipped orders
    /*
   DELIVERY_IN_PROGRESS
     */

    shippedOrdersCount =  order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.deliveryInProgressStatus
    ).toList().length;
    //endregion

    //region Delivered orders
    /*
   ORDER_DELIVERED
     */

    deliveredOrdersCount =  order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.orderDeliveredStatus
    ).toList().length;
    //endregion

    //region Returning orders
    /*
    RETURN_IN_PROGRESS,
    RETURN_REQUESTED,
    RETURN_CONFIRMED,
     */

    returningOrdersCount =  order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.returnInProgress ||
        element.suborderStatus == AppConstants.returnRequestedStatus||
        element.suborderStatus == AppConstants.returnRequestConfirmed

    ).toList().length;
    //endregion

    //region Returned orders
    /*
   RETURNED_TO_SELLER
     */

    returnedOrdersCount =  order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.returnedToSeller
    ).toList().length;
    //endregion

    //region Refund hold
    //REFUND_HOLD
    refundHold =  order.subOrderList!.where((element) =>
    element.suborderStatus == AppConstants.refundHold
    ).toList().length;
    //endregion

    // Count partial and full cancellations
    int partialCancelCount = 0;
    int fullCancelCount = 0;

    // Group suborders by package number
    Map<String, List<String>> packageSuborderStatuses = {};

    // Collect all package numbers and their suborder statuses
    for (var suborder in order.subOrderList!) {
      if (suborder.displayPackageNumber != null) {
        String packageKey = suborder.displayPackageNumber!;

        if (!packageSuborderStatuses.containsKey(packageKey)) {
          packageSuborderStatuses[packageKey] = [];
        }

        packageSuborderStatuses[packageKey]!.add(suborder.suborderStatus ?? '');
      }
    }

    // Check each package for partial or full cancellation
    packageSuborderStatuses.forEach((packageNumber, statuses) {
      bool hasNonCancelledSuborders = statuses.any((status) =>
        status != AppConstants.orderCancelledInTransit);

      if (statuses.contains(AppConstants.orderCancelledInTransit)) {
        if (hasNonCancelledSuborders) {
          partialCancelCount++;
        } else {
          fullCancelCount++;
        }
      }
    });

    // Count cancelled in transit suborders
    int cancelledInTransitCount = order.subOrderList!.where((element) =>
        element.suborderStatus == AppConstants.orderCancelledInTransit
    ).toList().length;

    //Make all data to a single string
    buyerOrderText = ""
        "${waitingOrdersCount>0? "$waitingOrdersCount waiting confirmation,":""} "
        "${cancelledOrdersCount>0? "$cancelledOrdersCount cancelled,":""} "
        "${cancelledInTransitCount>0? "$cancelledInTransitCount cancelled in transit,":""} "
        "${partialCancelCount>0? "$partialCancelCount packages partial cancel,":""} "
        "${fullCancelCount>0? "$fullCancelCount packages full cancel,":""} "
        "${confirmedOrdersCount>0? "$confirmedOrdersCount confirmed,":""} "
        "${shippedOrdersCount>0? "$shippedOrdersCount shipped,":""} "
        "${deliveredOrdersCount>0? "$deliveredOrdersCount delivered,":""} "
        "${returningOrdersCount>0? "$returningOrdersCount returning,":""} "
        "${paymentFailedCount>0? "$paymentFailedCount payment failed,":""} "
        "${paymentPendingCount>0? "$paymentPendingCount payment pending,":""} "
        "${refundHold>0? "$refundHold refund hold,":""} "
        "${returnedOrdersCount>0? "$returnedOrdersCount returned,":""}" ;
    //Remove extra commas and remove last commas
    buyerOrderText = CommonMethods.removeExtraCommas(buyerOrderText);
    //Refresh
    cardRefreshCtrl.sink.add(true);
  }
  //endregion



  //region Items
  String calculateItems(){
    int value = 0;
    for(var data in order.subOrderList!){
      value = data.productQuantity! + value;
    }
    if(value>1){
      return "$value items";
    }
    else{
      return "$value item";

    }
  }
  //endregion



  //region Item and items
  //endregion





//region On tap image
  void onTapImage(){
    StatefulWidget screen;
    //If is seller is true then push to the user screen
    if(isSeller){
       screen = UserProfileScreen(userReference:order.userReference!);
    }
    else{
       screen =  BuyerViewStoreScreen(storeReference: order.storeReference!);
    }
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion






  //region On tap Customer detail
  onTapCustomer(){
    CommonMethods.appBottomSheet(screen: SellerAllOrderCustomerDetailsScreen(order: order,), context: context, bottomSheetName: AppStrings.customerDetail);



  }
//endregion

  //region On tap Order details
  onTapOrderDetail(){
    //If seller
    if(isSeller){
      return     CommonMethods.appBottomSheet(screen:SellerAllOrderDetailsScreen(orderNumber:order.orderNumber!,), context: context, bottomSheetName: AppStrings.orderDetail);

    }
    else{
      return     CommonMethods.appBottomSheet(screen:BuyerMyOrderDetailsScreen(orderNumber:order.orderNumber!, order: order,), context: context, bottomSheetName: AppStrings.orderDetail);

    }



  }
//endregion


//region Text width
  double textWidth(String text) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: const TextStyle(fontSize: 16.0)),
      textDirection: TextDirection.ltr,
      maxLines: 1,
      ellipsis: '',
    )..layout();

    return textPainter.width;
  }
//endregion

//region Dispose
void dispose(){
  cardRefreshCtrl.close();
}
//endregion


}
