import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/refund_amount_calculation_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/services/refund_cost_breakup_services/refund_cost_breakup_services.dart';


enum RefundAmountBreakupState { Loading, Success, Failed, Empty }

class RefundCostBreakupBloc {
  // region Common Variables
  BuildContext context;
  final SubOrder subOrder;
  final Order order;
  final bool isSellerView;
  bool shoBreakup = false;

  ///Return const service and model
  late RefundCostBreakupServices refundCostBreakupServices;
  late RefundAmountCalculationResponse sellerRefundAmountCalculationResponse = RefundAmountCalculationResponse();

  // endregion

  //region Text Editing Controller

  //endregion


  //region Controller
  final refundAmountCalculationStateCtrl = StreamController<RefundAmountBreakupState>.broadcast();
  //endregion


  // region | Constructor |
  RefundCostBreakupBloc(this.context, this.subOrder, this.order, this.isSellerView,);

  // endregion

  // region Init
  void init() {

    refundCostBreakupServices = RefundCostBreakupServices();
    getRefundAmountCalculation();
  }
// endregion


  //region Get refund amount calculation
  getRefundAmountCalculation() async {
    //If seller view then call this else return
    if(!isSellerView){
      return;
    }
    //region Try
    try {

      //Loading
      refundAmountCalculationStateCtrl.sink.add(RefundAmountBreakupState.Loading);
      // sellerAllOrderCtrl.sink.add(SellerAllOrderState.Loading);
      sellerRefundAmountCalculationResponse = await refundCostBreakupServices.refundAmountCalculationSellerView(orderNumber: order.orderNumber!,subOrderReferenceList:[subOrder.suborderNumber!]);
      //print(sellerRefundAmountCalculationResponse);
      //Success
      refundAmountCalculationStateCtrl.sink.add(RefundAmountBreakupState.Success);
      // //Filter
      // filtering();

    }
    //endregion
    on ApiErrorResponseMessage {
      refundAmountCalculationStateCtrl.sink.add(RefundAmountBreakupState.Failed);
      // CommonMethods.toastMessage(AppStrings.error, context);
      return;
    } catch (error) {
      refundAmountCalculationStateCtrl.sink.add(RefundAmountBreakupState.Failed);
      // CommonMethods.toastMessage(AppStrings.error, context);
      return;
    }
  }
//endregion

//region On tap show breakup
  onTapShowBreakup(){
    //Show/hide
    shoBreakup = !shoBreakup;
    //Refresh success
    refundAmountCalculationStateCtrl.sink.add(RefundAmountBreakupState.Success);

  }
//endregion





}
