import 'package:flutter/foundation.dart';

/// Model class for entity previews (Users, Stores)
class EntityPreview {
  final String reference;
  final String entityType;
  final String handle;
  final String name;
  final String? icon;

  EntityPreview({
    required this.reference,
    required this.entityType,
    required this.handle,
    required this.name,
    this.icon,
  });

  factory EntityPreview.fromJson(Map<String, dynamic> json) {
    return EntityPreview(
      reference: json['reference'] ?? '',
      entityType: json['entityType'] ?? '',
      handle: json['handle'] ?? '',
      name: json['name'] ?? '',
      icon: json['icon'],
    );
  }

  @override
  String toString() {
    return 'EntityPreview{reference: $reference, entityType: $entityType, handle: $handle, name: $name, icon: $icon}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is EntityPreview &&
      other.reference == reference &&
      other.entityType == entityType &&
      other.handle == handle &&
      other.name == name &&
      other.icon == icon;
  }

  @override
  int get hashCode {
    return reference.hashCode ^
      entityType.hashCode ^
      handle.hashCode ^
      name.hashCode ^
      icon.hashCode;
  }
}
