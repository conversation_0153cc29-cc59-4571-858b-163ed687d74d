import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/support/feedback_item/feedback_item_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/support/get_all_feedback_response.dart';
import 'package:swadesic/services/add_feedback_responses/add_feedback_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum TicketsToMeState { Loading, Success, Failed }

class TicketsToMeBloc {
  // region Common Variables
  late BuildContext context;
  final String entityReference;
  final AddSupportServices addSupportServices = AddSupportServices();

  // Controllers
  final ticketsToMeCtrl = StreamController<TicketsToMeState>.broadcast();
  final TextEditingController searchFieldTextCtrl = TextEditingController();

  // Data
  late GetAllFeedbackResponse getAllTicketsResponse;
  List<FeedbackDetail> rootTicketsList = [];
  List<FeedbackDetail> finalFilteredTicketsList = [];

  // endregion

  // region Constructor
  TicketsToMeBloc(this.context, this.entityReference);
  // endregion

  // region Init
  Future<void> init() async {
    await getAllTicketsToMe();
  }
  // endregion

  // region Get all tickets to me (RECEIVED)
  Future<void> getAllTicketsToMe() async {
    try {
      // Loading state
      ticketsToMeCtrl.sink.add(TicketsToMeState.Loading);
      
      // API call to get tickets sent TO this entity (RECEIVED)
      getAllTicketsResponse = await addSupportServices.getTicketsByType(
        type: 'RECEIVED',
        entityReference: entityReference,
      );
      
      // Calculate day difference
      calculateDayDifference();
      
      // Clear and populate root list
      rootTicketsList.clear();
      rootTicketsList.addAll(getAllTicketsResponse.feedbackDetailList ?? []);
      
      // Apply search filter
      applyFilter();
      
      // Success state
      ticketsToMeCtrl.sink.add(TicketsToMeState.Success);
    } on ApiErrorResponseMessage {
      ticketsToMeCtrl.sink.add(TicketsToMeState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    } catch (error) {
      ticketsToMeCtrl.sink.add(TicketsToMeState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
  // endregion

  // region Calculate day difference
  void calculateDayDifference() {
    for (var element in getAllTicketsResponse.feedbackDetailList ?? []) {
      if (element.date != null) {
        element.dayDifference = int.parse(CommonMethods.dateTimeAmPm(date: element.date!)[0]);
      }
    }
  }
  // endregion

  // region Apply filter
  void applyFilter() {
    finalFilteredTicketsList.clear();
    finalFilteredTicketsList.addAll(rootTicketsList);
  }
  // endregion

  // region On search
  void onSearch() {
    //If field is empty then store Default data
    if (searchFieldTextCtrl.text.isEmpty) {
      //Clear searched tickets
      finalFilteredTicketsList.clear();
      //Add all data to finalFilteredTicketsList
      finalFilteredTicketsList.addAll(rootTicketsList);
      //Success state
      ticketsToMeCtrl.sink.add(TicketsToMeState.Success);
    }
    //Clear searched tickets
    finalFilteredTicketsList.clear();
    //Search the text field data and filter
    for (var data in rootTicketsList) {
      if (data.brief!.toLowerCase().contains(searchFieldTextCtrl.text.toLowerCase()) ||
          data.details!.toLowerCase().contains(searchFieldTextCtrl.text.toLowerCase()) ||
          data.name!.toLowerCase().contains(searchFieldTextCtrl.text.toLowerCase()) ||
          data.feedbackId!.toString().contains(searchFieldTextCtrl.text.toLowerCase())) {
        finalFilteredTicketsList.add(data);
      }
    }
    //Success state
    ticketsToMeCtrl.sink.add(TicketsToMeState.Success);
  }
  // endregion

  // region Go to ticket detail
  void goToTicketDetail({required FeedbackDetail ticketDetail, required int ticketId}) {
    var screen = FeedbackItemScreen(
      feedbackId: ticketId,
      isAdmin: AppConstants.adminUserReference.contains(AppConstants.appData.userReference!),
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // Refresh screen
      ticketsToMeCtrl.sink.add(TicketsToMeState.Success);
    });
  }
  // endregion

  // region On tap upvote
  void onTapUpVote({required FeedbackDetail rootFeedback}) {
    // Handle upvote logic if needed
    addVoteApiCall(rootFeedback: rootFeedback);
  }
  // endregion

  // region Add vote API
  Future<void> addVoteApiCall({required FeedbackDetail rootFeedback}) async {
    try {
      await addSupportServices.addVote(feedbackId: rootFeedback.feedbackId!);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
  // endregion

  // region Dispose
  void dispose() {
    ticketsToMeCtrl.close();
    searchFieldTextCtrl.dispose();
  }
  // endregion
}
