import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_on_boarding/buyer_on_boarding_bloc.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_on_boarding/select_roles/selecte_roles.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/initial_onboarding_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/features/common_buyer_seller_screen/store_handle_and_user_name/store_handle_and_user_name.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:url_launcher/url_launcher.dart';

class BuyerOnBoardingScreen extends StatefulWidget {
  final String userReference;
  final String? icon;

  const BuyerOnBoardingScreen({
    Key? key,
    required this.userReference,
    this.icon,
  }) : super(key: key);

  @override
  _BuyerOnBoardingScreenState createState() => _BuyerOnBoardingScreenState();
}

class _BuyerOnBoardingScreenState extends State<BuyerOnBoardingScreen> {
  //region Bloc
  late BuyerOnBoardingBloc buyerOnBoardingBloc;

  //endregion

  //region Init
  @override
  void initState() {
    buyerOnBoardingBloc =
        BuyerOnBoardingBloc(context, widget.userReference, widget.icon);
    buyerOnBoardingBloc.init();
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
          backgroundColor: AppColors.appWhite, body: SafeArea(child: body())),
    );
  }

  //region Body
  Widget body() {
    return Stack(
      fit: StackFit.expand,
      alignment: Alignment.center,
      children: [
        Image.asset(
          AppImages.onboardingBackground,
          fit: BoxFit.fitWidth,
        ),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: userDetailForm()),
      ],
    );
  }

//endregion

  //region User detail form
  Widget userDetailForm() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          flagAndText(),
          nameTextField(),
          verticalSizedBox(40),
          userName(),
          verticalSizedBox(40),
          gender(),
          verticalSizedBox(40),
          city(),
          const SizedBox(height: 40),
          SelectRoles(onChangeData: (value) {
            buyerOnBoardingBloc.selectedRoles = value;
            // //print(buyerOnBoardingBloc.selectedRoles);
          }),
          const SizedBox(height: 40),
          referralCode(),
          SizedBox(
            height: MediaQuery.of(context).size.height / 15,
          ),
          letsMakeIn(),
          const SizedBox(
            height: 10,
          ),
          termsAndPolicy(),
          AppCommonWidgets.bottomListSpace(context: context),
        ],
      ),
    );
  }

  //endregion

  //region Flag and text
  Widget flagAndText() {
    return Padding(
      padding: const EdgeInsets.only(top: 38, bottom: 50),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            AppImages.splashLogo,
            height: 82,
            width: 82,
          ),
          const SizedBox(height: 40),
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.completeOnBoarding,
              textAlign: TextAlign.left,
              style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Name and Text Field
  Widget nameTextField() {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AppTitleAndOptions(
            title: AppStrings.name,
            option: AppTextFields.onlyStringWithSpaceTextField(
              context: context,
              textEditingController: buyerOnBoardingBloc.nameTextCtrl,
              hintText: AppStrings.firstName,
              // fillColor: AppColors.tertiaryGreen
            ),
          ),
          // Text(AppStrings.name, style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack)),
          // verticalSizedBox(15),
          // colorFilledTextField(
          //   contentPaddingHorizontal: 11,
          //   contentPaddingVertical: 11,
          //   context: context,
          //   textFieldCtrl: buyerOnBoardingBloc.nameTextCtrl,
          //   hintText: AppStrings.firstName,
          //   textFieldMaxLine: 1,
          //   keyboardType: TextInputType.text,
          //   textInputAction: TextInputAction.done,
          // )
        ]);
  }

  //endregion

  //region User name
  StoreHandleAndUserName userName() {
    return StoreHandleAndUserName(
      onSearch: (data) {
        buyerOnBoardingBloc.isExistUser = data!;
        //print("On change data is ${buyerOnBoardingBloc.isExistUser}");
        // editUserProfileBloc.isChanged=true;
        // editUserProfileBloc.isUserNameAvailable = data==null?null:!data;
        // //print(data);
      },
      handleUserNameTextCtrl: buyerOnBoardingBloc.userNameTextCtrl,
      screenContext: context,
      olderData: buyerOnBoardingBloc.userNameTextCtrl.text,
      isUserNameCheck: true,
      title: "${AppStrings.userName} (${AppStrings.lowerCaseOnlyAllowed})",
      onChange: (data) {},
    );
  }

  //endregion

  //region Gender
  Widget gender() {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AppTitleAndOptions(
            title: "Gender",
            titleOption: AppToolTip(
                message: AppStrings.thisHelpUsPersonalize,
                toolTipWidget: Text(
                  AppStrings.why,
                  style: AppTextStyle.contentText0(
                      textColor: AppColors.writingBlack1),
                )),
            option: StreamBuilder<String>(
                stream: buyerOnBoardingBloc.genderCtrl.stream,
                initialData: buyerOnBoardingBloc.gender,
                builder: (context, snapshot) {
                  return Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            buyerOnBoardingBloc.onSelectGender("Female");
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                                color: snapshot.data == "Female"
                                    ? AppColors.brandBlack
                                    : AppColors.appWhite,
                                borderRadius: const BorderRadius.all(
                                    Radius.circular(100)),
                                border: Border.all(
                                    color: snapshot.data == "Female"
                                        ? AppColors.brandBlack
                                        : AppColors.darkStroke)),
                            child: Center(
                                child: Text(
                              "Female",
                              style: AppTextStyle.contentText0(
                                  textColor: snapshot.data == "Female"
                                      ? AppColors.appWhite
                                      : AppColors.appBlack),
                            )),
                          ),
                        ),
                      ),
                      horizontalSizedBox(15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            buyerOnBoardingBloc.onSelectGender("Male");
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                                color: snapshot.data == "Male"
                                    ? AppColors.brandBlack
                                    : AppColors.appWhite,
                                borderRadius: const BorderRadius.all(
                                    Radius.circular(100)),
                                border: Border.all(
                                    color: snapshot.data == "Male"
                                        ? AppColors.brandBlack
                                        : AppColors.darkStroke)),
                            child: Center(
                                child: Text(
                              "Male",
                              style: AppTextStyle.contentText0(
                                  textColor: snapshot.data == "Male"
                                      ? AppColors.appWhite
                                      : AppColors.appBlack),
                            )),
                          ),
                        ),
                      ),
                      horizontalSizedBox(15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            buyerOnBoardingBloc.onSelectGender("Others");
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                                borderRadius: const BorderRadius.all(
                                    Radius.circular(100)),
                                color: snapshot.data == "Others"
                                    ? AppColors.brandBlack
                                    : AppColors.appWhite,
                                border: Border.all(
                                    color: snapshot.data == "Others"
                                        ? AppColors.brandBlack
                                        : AppColors.darkStroke)),
                            child: Center(
                                child: Text(
                              "Others",
                              style: AppTextStyle.contentText0(
                                  textColor: snapshot.data == "Others"
                                      ? AppColors.appWhite
                                      : AppColors.appBlack),
                            )),
                          ),
                        ),
                      )
                    ],
                  );
                }),
          ),

          // Row(
          //   crossAxisAlignment: CrossAxisAlignment.center,
          //   mainAxisAlignment: MainAxisAlignment.center,
          //   mainAxisSize: MainAxisSize.min,
          //   children: [
          //     Text(
          //       "Gender",
          //       style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
          //     ),
          //     Expanded(child: horizontalSizedBox(10)),
          //
          //     ///Todo
          //     const Text(
          //       "why?",
          //       style: TextStyle(
          //           fontStyle: FontStyle.italic,
          //           fontWeight: FontWeight.w600,
          //           fontSize: 15,
          //           decoration: TextDecoration.underline,
          //           color: AppColors.writingColor3,
          //           fontFamily: "LatoRegular"),
          //     ),
          //   ],
          // ),
          // verticalSizedBox(15),
          // StreamBuilder<String>(
          //     stream: buyerOnBoardingBloc.genderCtrl.stream,
          //     initialData: buyerOnBoardingBloc.gender,
          //     builder: (context, snapshot) {
          //       return Row(
          //         children: [
          //           Expanded(
          //             child: InkWell(
          //               onTap: () {
          //                 buyerOnBoardingBloc.onSelectGender("Female");
          //               },
          //               child: Container(
          //                 padding: const EdgeInsets.symmetric(vertical: 10),
          //                 decoration: BoxDecoration(
          //                     color: snapshot.data == "Female" ? AppColors.brandGreen : AppColors.white,
          //                     borderRadius: const BorderRadius.all(Radius.circular(20)),
          //                     border: Border.all(color: snapshot.data == "Female" ? AppColors.brandGreen : AppColors.darkStroke)),
          //                 child: Center(
          //                     child: Text(
          //                   "Female",
          //                   style: AppTextStyle.heading4SemiBold(textColor: snapshot.data == "Female" ? AppColors.white : AppColors.appBlack),
          //                 )),
          //               ),
          //             ),
          //           ),
          //           horizontalSizedBox(15),
          //           Expanded(
          //             child: InkWell(
          //               onTap: () {
          //                 buyerOnBoardingBloc.onSelectGender("Male");
          //               },
          //               child: Container(
          //                 padding: const EdgeInsets.symmetric(vertical: 10),
          //                 decoration: BoxDecoration(
          //                     color: snapshot.data == "Male" ? AppColors.brandGreen : AppColors.white,
          //                     borderRadius: const BorderRadius.all(Radius.circular(20)),
          //                     border: Border.all(color: snapshot.data == "Male" ? AppColors.brandGreen : AppColors.darkStroke)),
          //                 child: Center(
          //                     child: Text(
          //                   "Male",
          //                   style: AppTextStyle.heading4SemiBold(textColor: snapshot.data == "Male" ? AppColors.white : AppColors.appBlack),
          //                 )),
          //               ),
          //             ),
          //           ),
          //           horizontalSizedBox(15),
          //           Expanded(
          //             child: InkWell(
          //               onTap: () {
          //                 buyerOnBoardingBloc.onSelectGender("Others");
          //               },
          //               child: Container(
          //                 padding: const EdgeInsets.symmetric(vertical: 10),
          //                 decoration: BoxDecoration(
          //                     borderRadius: const BorderRadius.all(Radius.circular(20)),
          //                     color: snapshot.data == "Others" ? AppColors.brandGreen : AppColors.white,
          //                     border: Border.all(color: snapshot.data == "Others" ? AppColors.brandGreen : AppColors.darkStroke)),
          //                 child: Center(
          //                     child: Text(
          //                   "Others",
          //                   style: AppTextStyle.heading4SemiBold(textColor: snapshot.data == "Others" ? AppColors.white : AppColors.appBlack),
          //                 )),
          //               ),
          //             ),
          //           )
          //         ],
          //       );
          //     })
        ]);
  }

  //endregion

  //region City
  Widget city() {
    return StreamBuilder<bool>(
        stream: buyerOnBoardingBloc.refreshCtrl.stream,
        builder: (context, snapshot) {
          return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                AppTitleAndOptions(
                  title: AppStrings.city,
                  option: AppCommonWidgets.dropDownOptions(
                      onTap: () {
                        buyerOnBoardingBloc.onTapCity();
                      },
                      context: context,
                      hintText: AppStrings.city,
                      value: buyerOnBoardingBloc.cityTextCtrl.text),
                ),
              ]);
        });
  }

  //endregion

  //region Referral code
  Widget referralCode() {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          AppTitleAndOptions(
            title: AppStrings.inviteCode,
            option: Column(
              children: [
                Text(
                  AppStrings.byJoiningViaAnInvite,
                  textAlign: TextAlign.left,
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                ),
                const SizedBox(
                  height: 15,
                ),
                AppTextFields.allTextField(
                  context: context,
                  maxEntry: 10,
                  textEditingController:
                      buyerOnBoardingBloc.referralCodeTextCtrl,
                  hintText: AppStrings.inviteCode,
                  // fillColor: AppColors.tertiaryGreen
                ),
              ],
            ),
          ),
        ]);
  }

  //endregion

  //region Send OTP and Get In
  // Widget sendGetIn() {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 40),
  //     child: SizedBox(
  //       height: 51,
  //       width: double.infinity,
  //       child: CupertinoButton(
  //           borderRadius: BorderRadius.circular(120),
  //           padding: EdgeInsets.zero,
  //           color: AppColors.brandGreen,
  //           child: Text(
  //             AppStrings.makeInBharat,
  //             style: const TextStyle(fontFamily: "LatoBold", fontSize: 15, color: AppColors.appWhite, fontWeight: FontWeight.w700),
  //           ),
  //           onPressed: () {
  //             buyerOnBoardingBloc.createUserProfileApiCall();
  //           }),
  //     ),
  //   );
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 80),
  //     child: InkWell(
  //         onTap: () {
  //           buyerOnBoardingBloc.createUserProfileApiCall();
  //         },
  //         child: Container(
  //           padding: const EdgeInsets.symmetric(vertical: 20),
  //           width: double.infinity,
  //           decoration: const BoxDecoration(color: AppColors.brandGreen, borderRadius: BorderRadius.all(Radius.circular(100))),
  //           child: Center(
  //             child: Text(
  //               AppStrings.makeInBharat,
  //               style: const TextStyle(fontFamily: "LatoBold", fontSize: 15, color: AppColors.appWhite, fontWeight: FontWeight.w700),
  //             ),
  //           ),
  //         )),
  //   );
  // }
//endregion

//region Lets make in
  Widget letsMakeIn() {
    return KeyboardVisibilityBuilder(
      builder: (context, isKeyBoardOpen) {
        //If open
        if (isKeyBoardOpen) {
          return const SizedBox();
        }
        return Container(
          width: double.infinity,
          child: CupertinoButton(
              key: const Key("get_and_verify_otp"),
              borderRadius: BorderRadius.circular(120),
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 15),
              color: AppColors.brandBlack,
              child: Text(
                "Join the Swadeshi Movement 2.0",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.exButton(textColor: AppColors.appWhite),
              ),
              onPressed: () {
                // Navigate to the new onboarding flow
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => InitialOnboardingScreen(
                      userReference: widget.userReference,
                      icon: widget.icon,
                    ),
                  ),
                );
              }),
        );
      },
    );
  }
//endregion

//region Terms and policy
  Widget termsAndPolicy() {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        text: 'By joining Swadesic, you agree to our ',
        style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
        children: <TextSpan>[
          TextSpan(
            text: 'Terms of Use',
            style: AppTextStyle.smallText(textColor: AppColors.brandBlack),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                //If web
                if (kIsWeb) {
                  await launchUrl(Uri.parse(
                      AppConstants.appCurrentEnvironment == Environment.dev
                          ? AppConstants.devTermsWebsite
                          : AppConstants.termsWebsite));
                } else {
                  var screen = AppWebView(
                      url: AppConstants.appCurrentEnvironment == Environment.dev
                          ? AppConstants.devTermsWebsite
                          : AppConstants.termsWebsite);
                  var route = CupertinoPageRoute(builder: (context) => screen);
                  Navigator.push(context, route);
                }
              },
          ),
          TextSpan(
            text: ' and ',
            style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
          ),
          TextSpan(
            text: 'Privacy Policy.',
            style: AppTextStyle.smallText(textColor: AppColors.brandBlack),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                //If web
                if (kIsWeb) {
                  await launchUrl(Uri.parse(
                      AppConstants.appCurrentEnvironment == Environment.dev
                          ? AppConstants.devPrivacyPolityWebsite
                          : AppConstants.privacyPolityWebsite));
                } else {
                  var screen = AppWebView(
                      url: AppConstants.appCurrentEnvironment == Environment.dev
                          ? AppConstants.devPrivacyPolityWebsite
                          : AppConstants.privacyPolityWebsite);
                  var route = CupertinoPageRoute(builder: (context) => screen);
                  Navigator.push(context, route);
                }
              },
          ),
        ],
      ),
    );
  }
//endregion
}
