import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/available_to/available_to.dart';
import 'package:swadesic/features/buyers/shopping_cart/change_it_here/change_it_here.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/selected_delivery_pincode/selected_delivery_pin_code.dart';

// import 'package:hashtagable/widgets/hashtag_text_field.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_common_widgets.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_price/shopping_cart_price.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_field_style.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Buyer Store Screen
class SecureCheckoutScreen extends StatefulWidget {
  final ShoppingCartBloc shoppingCartBloc;
  const SecureCheckoutScreen({Key? key, required this.shoppingCartBloc})
      : super(key: key);

  //ShoppingCartScreen({Key? key}) : super(key: key);

  //#region Region - createState
  @override
  _SecureCheckoutScreenState createState() => _SecureCheckoutScreenState();
}

// endregion
class _SecureCheckoutScreenState extends State<SecureCheckoutScreen> {
  // region Bloc
  late SecureCheckoutBloc secureCheckoutBloc;

  // endregion

  // region Init
  @override
  void initState() {
    secureCheckoutBloc = SecureCheckoutBloc(context, widget.shoppingCartBloc);
    secureCheckoutBloc.init();
    super.initState();
  }
  // endregion

  //
  // @override
  // void didChangeDependencies() {
  //   //print("change");
  //
  //   // TODO: implement didChangeDependencies
  //   super.didChangeDependencies();
  // }
  //
  // @override
  // void didUpdateWidget(covariant ShoppingCartScreen oldWidget) {
  //   //print("change");
  //   // TODO: implement didUpdateWidget
  //   super.didUpdateWidget(oldWidget);
  // }

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
        },
        child: Scaffold(
          backgroundColor: AppColors.appWhite,
          appBar: appBar(),
          resizeToAvoidBottomInset: true,
          body: SafeArea(
              // child: Container()
              child: body()),
        ));
  }

  // endregion
//
  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.shoppingCart,
        isCartVisible: false,
        isMembershipVisible: false);
  }

// endregion

  // region Body
  Widget body() {
    return StreamBuilder<SecureCheckoutState>(
        stream: secureCheckoutBloc.secureCheckOutStateCtrl.stream,
        initialData: SecureCheckoutState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == SecureCheckoutState.Loading) {
            return Center(
              child: AppCommonWidgets.appCircularProgress(),
            );
          }
          if (snapshot.data == SecureCheckoutState.Success) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SelectedDeliveryPinCode(
                      shoppingCartBloc: secureCheckoutBloc.shoppingCartBloc,
                      secureCheckoutBloc: secureCheckoutBloc),
                  verticalSizedBox(10),
                  deliveryAddressTitle(),
                  deliveryAddress(),
                  addViewMoreAddress(),
                  verticalSizedBox(20),
                  secureCheckout()
                ],
              ),
            );
          }
          return AppCommonWidgets.errorWidget(onTap: () {
            secureCheckoutBloc.init();
          });
        });
  }

  // endregion

//region Delivery Address Details title
  Widget deliveryAddressTitle() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 17),
      child: Text(
        AppStrings.deliveryAddress,
        style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
      ),
    );
  }

//endregion

  //region Selected Delivery Address
  Widget deliveryAddress() {
    //If no address is selected then return container else show the selected address
    return secureCheckoutBloc.selectedAddress.name == null
        ? Container()
        : Padding(
            padding: const EdgeInsets.only(top: 17, left: 17, right: 17),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,

              ///If user pin code is not same as selected address pin code . Then show a warning message with the selected address.
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                  decoration: BoxDecoration(
                      color: AppColors.textFieldFill1,
                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                      border: Border.all(color: AppColors.textFieldFill1)),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        //shoppingCartBloc.selectedAddressModel.name!??
                        secureCheckoutBloc.selectedAddress.name!,
                        maxLines: 1,
                        style:
                            AppTextStyle.access0(textColor: AppColors.appBlack),
                        // style: const TextStyle(fontFamily: "LatoSemiBold", fontWeight: FontWeight.w600, fontSize: 14, color: AppColors.appBlack),
                      ),
                      verticalSizedBox(10),
                      Text(
                        "${secureCheckoutBloc.selectedAddress.address},${secureCheckoutBloc.selectedAddress.city},${secureCheckoutBloc.selectedAddress.state},${secureCheckoutBloc.selectedAddress.pincode}",
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack),
                      ),
                      verticalSizedBox(20),
                      InkWell(
                        onTap: () {
                          secureCheckoutBloc.goToCartAddress();
                          // shoppingCartBloc.goToCartAddress();
                          // shoppingCartBloc.onTapAddViewAddress();
                        },
                        child: Text(
                          "change",
                          style: AppTextStyle.access0(
                              textColor: AppColors.appBlack),
                        ),
                      )
                    ],
                  ),
                ),

                /* Make it visible only if
      //1. Selected address pin code is not equal to the user pincode
      //2. User pin code should be in address list
       */
                Visibility(
                  visible:
                      BuyerHomeBloc.userDetailsResponse.userDetail != null &&
                          secureCheckoutBloc.selectedAddress.pincode !=
                              BuyerHomeBloc
                                  .userDetailsResponse.userDetail?.pincode &&
                          secureCheckoutBloc
                              .shoppingCartAddressResponse.addressList!
                              .any((e) =>
                                  e.pincode ==
                                  BuyerHomeBloc
                                      .userDetailsResponse.userDetail?.pincode),
                  child: Text(
                    "${AppStrings.pleaseSelectAddressOf} ${BuyerHomeBloc.userDetailsResponse.userDetail?.pincode ?? ''}",
                    style: AppTextStyle.smallText(textColor: AppColors.red),
                  ),
                ),
              ],
            ),
          );
  }

  //endregion

//region Add and  View More address
  Widget addViewMoreAddress() {
    return secureCheckoutBloc.selectedAddress.useraddressid == null
        ? Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 17,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    secureCheckoutBloc.goToCartAddress();
                    // shoppingCartBloc.onTapAddViewAddress();
                  },
                  child: Container(
                    margin: const EdgeInsets.only(top: 17),
                    alignment: Alignment.center,
                    width: double.infinity,
                    decoration: BoxDecoration(
                        color: AppColors.appWhite,
                        borderRadius: BorderRadius.circular(100),
                        border:
                            Border.all(color: AppColors.appBlack, width: 1.2)),
                    padding: const EdgeInsets.symmetric(
                        vertical: 13, horizontal: 20),
                    child: Text(AppStrings.viewSelectAndAddAddress,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyle.access0(
                            textColor: AppColors.appBlack)),
                  ),
                ),
              ],
            ),
          )
        : Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //Delivery notes
              addDeliveryNotes(),

              Container(
                margin: const EdgeInsets.only(bottom: 40, left: 17, right: 17),
                child: Text(
                  "Delivery note will be sent to all stores in the cart",
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack),
                ),
              ),

              Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 17,
                  ),
                  child: Text(
                    AppStrings.contactDetails,
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack),
                  )),

              ChangeItHere(
                secureCheckoutBloc: secureCheckoutBloc,
              )
            ],
          );
  }

//endregion

//region Add Delivery Note

  Widget addDeliveryNotes() {
    //If delivery notes are empty and text field visibility is false
    if (secureCheckoutBloc
            .shoppingCartBloc.cartDetailsResponse.deliveryNotes.isEmpty &&
        !secureCheckoutBloc.shoppingCartBloc.cartDetailsResponse
            .isDeliveryNotesTextFieldVisible) {
      return CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          secureCheckoutBloc.shoppingCartBloc.cartDetailsResponse
              .isDeliveryNotesTextFieldVisible = true;
          secureCheckoutBloc.secureCheckOutStateCtrl.sink
              .add(SecureCheckoutState.Success);
          //Refresh screen
          // secureCheckoutBloc.shoppingCartBloc.shoppingCartCtrl.sink.add(ShoppingCartState.Success);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(7),
              color: AppColors.textFieldFill1),
          margin: const EdgeInsets.symmetric(horizontal: 17, vertical: 11),
          child: Text(
            AppStrings.addDeliveryNote,
            style: AppTextStyle.access0(textColor: AppColors.appBlack),
          ),
        ),
      );
    }
    //If field visible is true
    if (secureCheckoutBloc
        .shoppingCartBloc.cartDetailsResponse.isDeliveryNotesTextFieldVisible) {
      final TextEditingController notesTextCtrl = TextEditingController(
          text: secureCheckoutBloc
              .shoppingCartBloc.cartDetailsResponse.deliveryNotes);
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 17, vertical: 11),
        child: Stack(
          children: [
            TextFormField(
              minLines: 5,
              maxLines: 5,
              controller: notesTextCtrl,
              // autofocus: true,
              style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack),
              keyboardType: TextInputType.text,
              textCapitalization: TextCapitalization.sentences,
              decoration: InputDecoration(
                  isDense: true,
                  hintStyle:
                      AppTextStyle.hintText(textColor: AppColors.writingBlack1),
                  fillColor: AppColors
                      .textFieldFill1, // Specify the desired internal color
                  filled: true,
                  hintText: AppStrings.addDeliveryNote,
                  contentPadding: const EdgeInsets.symmetric(
                      vertical: 10.0, horizontal: 16.0),
                  border: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  focusedBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  enabledBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  disabledBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  focusedErrorBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1),
                  errorBorder: AppTextFieldStyle.filledColored(
                      fieldColors: AppColors.textFieldFill1)),
            ),
            Positioned(
                bottom: 12,
                right: 11,
                child: InkWell(
                  onTap: () {
                    //If field is null
                    if (notesTextCtrl.text.isEmpty) {
                      //Mark field visible to false
                      secureCheckoutBloc.shoppingCartBloc.cartDetailsResponse
                          .isDeliveryNotesTextFieldVisible = false;
                      //Refresh screen
                      // secureCheckoutBloc.shoppingCartBloc.shoppingCartCtrl.sink.add(ShoppingCartState.Success);
                    }
                    //Add text to the seller notes.
                    secureCheckoutBloc.shoppingCartBloc.cartDetailsResponse
                        .deliveryNotes = notesTextCtrl.text;
                    //Mark field visible to false
                    secureCheckoutBloc.shoppingCartBloc.cartDetailsResponse
                        .isDeliveryNotesTextFieldVisible = false;
                    //Save notes to cache memory
                    secureCheckoutBloc.shoppingCartBloc
                        .saveMessageToSharePref();
                    //Refresh screen
                    // secureCheckoutBloc.shoppingCartBloc.shoppingCartCtrl.sink.add(ShoppingCartState.Success);

                    //print(secureCheckoutBloc.shoppingCartBloc.cartDetailsResponse.deliveryNotes);
                    secureCheckoutBloc.secureCheckOutStateCtrl.sink
                        .add(SecureCheckoutState.Success);
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                        color: AppColors.textFieldFill1,
                        borderRadius: BorderRadius.circular(8),
                        border:
                            Border.all(color: AppColors.appBlack, width: 1)),
                    child: Text(
                      AppStrings.add,
                      style: AppTextStyle.button2Bold(
                          textColor: AppColors.appBlack),
                    ),
                  ),
                ))
          ],
        ),
      );
    }
    //If seller notes is not empty and field visible is false
    if (secureCheckoutBloc
            .shoppingCartBloc.cartDetailsResponse.deliveryNotes.isNotEmpty &&
        !secureCheckoutBloc.shoppingCartBloc.cartDetailsResponse
            .isDeliveryNotesTextFieldVisible) {
      return InkWell(
        onTap: () {
          //
          secureCheckoutBloc.shoppingCartBloc.cartDetailsResponse
              .isDeliveryNotesTextFieldVisible = true;

          //Refresh screen
          secureCheckoutBloc.secureCheckOutStateCtrl.sink
              .add(SecureCheckoutState.Success);
        },
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.symmetric(horizontal: 17, vertical: 11),
          decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.circular(10)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppStrings.deliveryNotes,
                style: AppTextStyle.access0(textColor: AppColors.appBlack),
              ),
              verticalSizedBox(10),
              Text(
                secureCheckoutBloc
                    .shoppingCartBloc.cartDetailsResponse.deliveryNotes,
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
            ],
          ),
        ),
      );
    }

    return Container();
  }
//endregion

//region Secure checkout
  Widget secureCheckout() {
    return StreamBuilder<bool>(
        stream: secureCheckoutBloc.startSecureCheckOutCtrl.stream,
        initialData: false,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data!) {
            return AppCommonWidgets.appCircularProgress();
          }
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: CupertinoButton(
                borderRadius: const BorderRadius.all(Radius.circular(50)),
                color: AppColors.brandBlack,
                padding: const EdgeInsets.symmetric(vertical: 17.5),
                child: SizedBox(
                  width: double.infinity,
                  child: Center(
                    child: Text(
                      AppStrings.secureCheckout,
                      style:
                          AppTextStyle.access1(textColor: AppColors.appWhite),
                    ),
                  ),
                ),
                onPressed: () {
                  secureCheckoutBloc.createOrderAndInitiatePayment();
                  // shoppingCartBloc.sellerNote();
                  // shoppingCartBloc.orderCreate();
                }),
          );
        });
  }

//endregion
}
