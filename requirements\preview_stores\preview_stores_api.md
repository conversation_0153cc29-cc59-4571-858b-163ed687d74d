# Preview Store API Documentation

## Endpoints

### 1. Create Preview Store
**Endpoint:** `POST store/previewstore/`

Creates a new preview store for a user.

#### Request
```http
POST /api/stores/previewstore/
Content-Type: multipart/form-data
Authorization: Bearer <your_access_token>

{
    "user_reference": "user_123",
    "preview_storehandle": "my-preview-store",
    "preview_store_name": "My Preview Store",
    "preview_store_icon": <file_upload>
}
```

#### Request Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_reference | string | Yes | Reference ID of the user creating the store |
| preview_storehandle | string | Yes | Unique handle/username for the store (alphanumeric, dashes, underscores) |
| preview_store_name | string | Yes | Display name of the store (max 40 chars) |
| preview_store_icon | file | No | Store icon image file (will be compressed to 60-70KB) |

#### Response (Success - 200 OK)
```json
{
    "message": "success",
    "data": {
        "preview_store_id": 1,
        "user_reference": "user_123",
        "user_id": 1,
        "preview_store_reference": "S9991620000000",
        "preview_store_name": "My Preview Store",
        "preview_storehandle": "my-preview-store",
        "preview_store_icon": "preview_store_icons/icon_123.jpg",
        "created_at": "2025-07-16T10:01:23.123456Z",
        "updated_at": "2025-07-16T10:01:23.123456Z"
    }
}
```

#### Response (Error - 400 Bad Request)
```json
{
    "message": "error",
    "error": "Store with this preview storehandle already exists."
}
```

### 2. Get User's Preview Stores
**Endpoint:** `GET store/previewstore/`

Retrieves all preview stores for a user.

#### Request
```http
GET /api/stores/previewstore/?user_id=1
# OR
GET /api/stores/previewstore/?user_reference=user_123
```

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | integer | Either this or user_reference | ID of the user |
| user_reference | string | Either this or user_id | Reference ID of the user |

#### Response (Success - 200 OK)
```json
{
    "message": "success",
    "data": [
        {
            "preview_store_id": 1,
            "user_reference": "user_123",
            "user_id": 1,
            "preview_store_reference": "S9991620000000",
            "preview_store_name": "My Preview Store",
            "preview_storehandle": "my-preview-store",
            "preview_store_icon": "preview_store_icons/icon_123.jpg",
            "created_at": "2025-07-16T10:01:23.123456Z",
            "updated_at": "2025-07-16T10:01:23.123456Z"
        },
        {
            "preview_store_id": 2,
            "user_reference": "user_123",
            "user_id": 1,
            "preview_store_reference": "S9991620000001",
            "preview_store_name": "Another Store",
            "preview_storehandle": "another-store",
            "preview_store_icon": "preview_store_icons/icon_124.jpg",
            "created_at": "2025-07-16T11:30:45.678901Z",
            "updated_at": "2025-07-16T11:30:45.678901Z"
        }
    ]
}
```

#### Response (Error - 400 Bad Request)
```json
{
    "message": "error",
    "error": "User not found"
}
```
## Notes
1. The `preview_store_reference` is automatically generated in the format `S999` followed by a timestamp.
2. Image uploads are automatically compressed to maintain a size between 60-70KB.
3. All endpoints require authentication.
4. The `preview_storehandle` must be unique across all stores.
5. The store name is limited to 40 characters.

## Error Handling
The API returns appropriate HTTP status codes along with error messages in the response body. Common status codes include:

- 200 OK: Request successful
- 400 Bad Request: Invalid input or validation error
- 401 Unauthorized: Authentication required
- 403 Forbidden: Insufficient permissions
- 500 Internal Server Error: Server error
