import 'package:swadesic/services/app_link_services/app_link_create_service.dart';

import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_filter_sort/search_filter/search_screen_filter.dart';
import 'package:swadesic/features/buyers/buyer_search/search_post_store_product_and_people/search_post_store_product_and_people_pagination.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_all_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_access_bottom_sheet/share_access_bottom_sheet.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/search_result_count_data_model/search_result_count_data_model.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/buyer_search_response/search_response.dart';
import 'package:swadesic/model/buyer_search_response/search_result_count_response.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart'
    as store_list_response;
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/filters/search_screen_filter_model/search_screen_filter_model.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/buyer_search_services/buyer_search_services.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_filter_bottom_sheet/app_filter_single_bottom_sheet.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum SearchPostStoreProductAndPeopleState { Loading, Success, Failed, Empty }

enum SearchPostStoreProductAndPeoplePaginationState { Loading, Success, End }

class SearchPostStoreProductAndPeopleBloc {
  // region Common Variables
  BuildContext context;
  late ScrollController scrollController = ScrollController();
  List<PostDetail> postList = [];
  List<Product> productList = [];
  List<StoreInfo> storeList = [];
  List<User> userList = [];
  final EntityType entityType;
  int offset = 0;
  final int limit;
  late SearchPostStoreProductAndPeoplePagination
      searchPostStoreProductAndPeoplePagination;
  final BuyerSearchBloc buyerSearchBloc;

  bool isSearchedProductListView = false; // Always use grid view
  Timer? timer;

  // endregion

  //region Controller

  final searchResultCtrl =
      StreamController<SearchPostStoreProductAndPeopleState>.broadcast();
  // final TextEditingController searchTextCtrl;

  // region | Constructor |
  SearchPostStoreProductAndPeopleBloc(
      this.context, this.entityType, this.limit, this.buyerSearchBloc);

  // endregion

  // region Init
  void init() async {
    //Pagination initialize
    searchPostStoreProductAndPeoplePagination =
        SearchPostStoreProductAndPeoplePagination(context, this);
    getSearchResultApiCall();
    getSearchResultCount();

    BuyerSearchBloc.searchTextEditingCtrl.addListener(() async {
      // await Future.delayed(Duration(seconds: 1));
      //print("text is changing in search post and people screen");
      context.mounted ? onChangeTextField() : null;
    });
  }

// endregion

  //region On Text Change
  void onChangeTextField() {
    if (timer?.isActive ?? false) {
      timer!.cancel();
    }
    timer = Timer(const Duration(seconds: 1), () {
      getSearchResultApiCall();
      getSearchResultCount();
    });
  }

//endregion

  //region Get Search Result
  Future<void> getSearchResultApiCall() async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //Get reference to the LoggedInUserInfoDataModel
    late LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    // Get reference to the Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    //Reset limit and offset to default
    offset = 0;

    try {
      //Pagination loading broadcast
      searchPostStoreProductAndPeoplePagination.paginationStateCtrl.sink
          .add(SearchResultPaginationState.Loading);
      late Timer progressTimer;
      bool showProgress = false;
      // Start the progress timer
      progressTimer = Timer(const Duration(milliseconds: 200), () {
        // If the API call is still in progress after 200 milliseconds, show progress
        if (!showProgress) {
          searchResultCtrl.sink
              .add(SearchPostStoreProductAndPeopleState.Loading);
        }
      });

      ///Post
      if (EntityType.POST == entityType) {
        //Clear
        // postList.clear();
        postList = (await BuyerSearchServices().getSearchResult(
          entityType: entityType,
          limit: limit,
          offset: offset,
          pinCode: loggedInUserInfoDataModel.userDetail!.pincode ?? "110068",
          query: BuyerSearchBloc.searchTextEditingCtrl.text,
        ))['posts']
            .map<PostDetail>((post) => PostDetail.fromLeanJson(post))
            .toList();

        //Empty
        if (postList.isEmpty) {
          return searchResultCtrl.sink
              .add(SearchPostStoreProductAndPeopleState.Empty);
        }
        // //Add post in post list
        postDataModel.addPostIntoList(postList: postList);
      }

      ///Store
      if (EntityType.STORE == entityType) {
        //Clear
        storeList.clear();
        storeList = (await BuyerSearchServices().getSearchResult(
          entityType: entityType,
          limit: limit,
          offset: offset,
          pinCode: loggedInUserInfoDataModel.userDetail!.pincode ?? "110068",
          query: BuyerSearchBloc.searchTextEditingCtrl.text,
        ))['store']
            .map<StoreInfo>((store) => StoreInfo.fromJson(store))
            .toList();
        //Empty
        if (storeList.isEmpty) {
          return searchResultCtrl.sink
              .add(SearchPostStoreProductAndPeopleState.Empty);
        }
      }

      ///Product
      if (EntityType.PRODUCT == entityType) {
        //Clear
        productList.clear();
        productList = (await BuyerSearchServices().getSearchResult(
          entityType: entityType,
          limit: limit,
          offset: offset,
          pinCode: loggedInUserInfoDataModel.userDetail!.pincode ?? "110068",
          query: BuyerSearchBloc.searchTextEditingCtrl.text,
        ))['product']
            .map<Product>((product) => Product.fromJson(product))
            .toList();

        //Add all products in product data model
        productDataModel.addProductIntoList(products: productList);

        //Empty
        if (productList.isEmpty) {
          return searchResultCtrl.sink
              .add(SearchPostStoreProductAndPeopleState.Empty);
        }
      }

      ///User
      if (EntityType.USER == entityType) {
        //Clear
        userList.clear();
        userList = (await BuyerSearchServices().getSearchResult(
          entityType: entityType,
          limit: limit,
          offset: offset,
          pinCode: loggedInUserInfoDataModel.userDetail!.pincode ?? "110068",
          query: BuyerSearchBloc.searchTextEditingCtrl.text,
        ))['user']
            .map<User>((user) => User.fromJson(user))
            .toList();

        //Empty
        if (userList.isEmpty) {
          return searchResultCtrl.sink
              .add(SearchPostStoreProductAndPeopleState.Empty);
        }
      }

      // await Future.delayed(Duration(seconds: 3));
      searchResultCtrl.sink.add(SearchPostStoreProductAndPeopleState.Success);

      //
      // // Stop the progress timer
      // progressTimer.cancel();
      // //Progress false
      // searchProgressState.sink.add(false);
      // //Update the pin code used to srearch
      // pinCodeUsedToSearch = pinCode;
      // //Clear filtered product list
      // filteredProductList.clear();
      // //Add product list in filtered product list
      // filteredProductList.addAll(searchResponse.product!);
      // //Add keyword to history
      // buyerSearchServices.addKeyWordHistory(query: searchTextEditingCtrl.text);
      // buyerSearchCtrl.sink.add(BuyerSearchState.Success);
      // // historyOrSearch = 'search';
      // // AppConstants.buyerSearchFilter = AppConstants.buyerSearchFilter;
      // onChangeOptionCtrl.sink.add(true);
      // screenRefreshCtrl.sink.add(true);
      //
      // ///If field is empty then show the history data
      // // if (searchTextEditingCtrl.text == "" || searchTextEditingCtrl.text.length < 3) {
      // //   //print("Cleared");
      // //   buyerSearchCtrl.sink.add(BuyerSearchState.History);
      // //   return;
      // // }
      //
      // ///Item count check
      // itemCountCheck();
      //
      // ///Apply filter
      // applyFilter();
    } on ApiErrorResponseMessage catch (e) {
      // context.mounted?CommonMethods.toastMessage(e.message.toString(), context):null;
      searchResultCtrl.sink.add(SearchPostStoreProductAndPeopleState.Failed);
      return;
    } catch (error) {
      searchResultCtrl.sink.add(SearchPostStoreProductAndPeopleState.Failed);
      return;
    }
  }
  //endregion

  //region Get search result count
  Future<void> getSearchResultCount() async {
    //Get reference to the SearchResultCountDataModel
    late SearchResultCountDataModel searchResultCountDataModel =
        Provider.of<SearchResultCountDataModel>(context, listen: false);
    try {
      ///Get search result count
      SearchResultCountResponse searchedCountData =
          await BuyerSearchServices().getSearchResultCount(
        query: BuyerSearchBloc.searchTextEditingCtrl.text,
      );
      //Add searched count data to SearchResultCountDataModel
      searchResultCountDataModel.saveData(data: searchedCountData);
      //Success
      searchResultCtrl.sink.add(SearchPostStoreProductAndPeopleState.Success);
    } on ApiErrorResponseMessage catch (e) {
      // context.mounted?CommonMethods.toastMessage(e.message.toString(), context):null;
      // searchResultCtrl.sink.add(SearchPostStoreProductAndPeopleState.Failed);
    } catch (error) {
      // searchResultCtrl.sink.add(SearchPostStoreProductAndPeopleState.Failed);
    }
  }
  //endregion

  //region Add data as per the entity
  void addDataAsPerTheEntityType() {
    //Post
    if (EntityType.POST == entityType) {}
  }

  //endregion

  //region On tap drawer
  void onTapDrawer({required PostDetail postDetail}) async {
    List<Map<String, dynamic>> accessOptions = [];
    if (postDetail.createdBy!.userOrStoreReference ==
        (AppConstants.appData.isUserView!
            ? AppConstants.appData.userReference!
            : AppConstants.appData.storeReference!)) {
      accessOptions = [
        //Copy
        {
          'title': AppStrings.copyPostLink,
          'onTap': () {
            Navigator.pop(context);
            CommonMethods.copyText(
                context,
                AppLinkCreateService().createPostLink(
                    postReference: postDetail.postOrCommentReference!));
          },
        },
        //Edit
        {
          'title': AppStrings.editPost.toLowerCase(),
          'onTap': () {
            Navigator.pop(context);
            goToEditPost(postDetail: postDetail);
          },
        },
        //Delete post
        {
          'title': AppStrings.deletePost,
          'onTap': () {
            Navigator.pop(context);
            confirmDelete(postDetail: postDetail);
          },
        },
        // Add more options if needed
      ];
    } else {
      accessOptions = [
        {
          'title': AppStrings.reportThePost,
          'onTap': () {
            Navigator.pop(context);
            // Navigator.pop(context);
            var screen = ReportScreen(
              reference: postDetail.postOrCommentReference!,
              isProduct: true,
            );
            var route = MaterialPageRoute(builder: (context) => screen);
            Navigator.push(context, route);

//endregion
          },
        },
      ];
    }

    CommonMethods.accessBottomSheet(
      screen: ShareAccessBottomSheet(accessOptions: accessOptions),
      context: context,
    );
  }

  //endregion

  //region Confirm delete
  Future confirmDelete({required PostDetail postDetail}) {
    return CommonMethods.appDialogBox(
        context: context,
        widget: OkayAndCancelDialogScreen(
          onTapSecondButton: () {
            deletePost(postDetail: postDetail);
          },
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.areYouSureWantsToDelete,
          firstButtonName: "Cancel",
          secondButtonName: "Delete",
        ));
  }

//endregion

  //region On Tap Share
  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: AppLinkCreateService()
            .createPostLink(postReference: postDetail.postOrCommentReference!),
        imageLink: postDetail.images!.isEmpty
            ? null
            : postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        entityType: EntityType.POST,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.handle,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,
      ),
      context: context,
    );
    //
    // showModalBottomSheet(
    //     context: context,
    //     isScrollControlled: true,
    //     enableDrag: true,
    //     backgroundColor: AppColors.appWhite,
    //     shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(20), topLeft: Radius.circular(20))),
    //     builder: (context) {
    //       return SingleChildScrollView(
    //           padding: EdgeInsets.zero,
    //           child: Container(
    //             child: Column(
    //               children: [
    //                 Container(
    //                   margin: const EdgeInsets.symmetric(vertical: 20),
    //                   child: Column(
    //                     children: [
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                       verticalSizedBox(10),
    //                       SizedBox(
    //                         width: 40,
    //                         child: divider(),
    //                       ),
    //                     ],
    //                   ),
    //                 ),
    //                 BuyerProductShareScreen(url: link, imageLink:imageUrl,),
    //               ],
    //             ),
    //           ));
    //     }).then((value) {
    //   // if (value == null) return;
    //   // supportFilterModel = value;
    //   // applyFilter();
    // });
  }

  //endregion

  //region Go to single post view
  void goToSinglePostView({required String postReference}) {
    var screen = SinglePostViewScreen(
      postReference: postReference,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

//endregion

  //region Go to edit post
  void goToEditPost({required PostDetail postDetail}) {
    Widget screen = EditPostScreen(
      postDetail: postDetail,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }

  //endregion

  //region Delete post api call
  Future<void> deletePost({required PostDetail postDetail}) async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //region Try
    try {
      //Api call
      UploadFileService()
          .deletePost(postReference: postDetail.postOrCommentReference!);
      //Remove local data
      //Remove post detail from the userOrStoreFeeds
      // postDataModel.userOrStoreFeedsList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Remove post detail from the allPostDetailList
      postDataModel.allPostDetailList.removeWhere((element) =>
          element.postOrCommentReference == postDetail.postOrCommentReference);
      //Refresh ui
      postDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

//endregion

  //region On tap heart
  Future<void> onTapHeart({required PostDetail postDetail}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      //Update liked count and is liked
      //Toggle the like status
      postDetail.likeStatus = !postDetail.likeStatus!;
      if (postDetail.likeStatus!) {
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      } else {
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      //Refresh ui
      postDataModel.updateUi();
      //Api call
      await PostService().likePost(
          postReference: postDetail.postOrCommentReference!,
          likeStatus: postDetail.likeStatus!);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      //Failed
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;
      return;
    }
  }
//endregion

  //region On tap image
  void onTapImage({required List<String> imageList, required int index}) {
    Widget screen = BuyerImagePreviewScreen(
      productImage: imageList,
      imageIndex: index,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }

  //endregion

  //region On tap user or store icon
  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference &&
        AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference &&
        AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion

  //region Go to View Store Screen
  goToViewStoreScreen({required StoreInfo selectedStore}) {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewStores! != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }

    ///Add searched item
    BuyerSearchServices().addSearchedItem(
      searchedText: BuyerSearchBloc.searchTextEditingCtrl.text,
      searchedItem: selectedStore.storeReference!,
      searchType: entityType.name,
    );
    // addSearchedItemApi(searchedItem:selectedStore.storeReference!, searchType: "STORE");
    var screen =
        BuyerViewStoreScreen(storeReference: selectedStore.storeReference!);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

//endregion

  //region Go to View Product Screen
  void goToViewProductScreen({required Product product, required int index}) {
    ///Add searched item
    BuyerSearchServices().addSearchedItem(
      searchedText: BuyerSearchBloc.searchTextEditingCtrl.text,
      searchedItem: product.productReference!,
      searchType: entityType.name,
    );
    var screen = BuyerViewProductScreen(
        openingFrom: SearchScreenEnum.SEARCH_RESULT,
        productList: productList,
        index: index,
        searchedText: BuyerSearchBloc.searchTextEditingCtrl.text);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region Go to View All Product
  goToViewAllProducts() {
    // No longer toggles view - always uses grid view
    // Just navigate to view all products screen if needed

    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewProduct! != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }
    // var screen = BuyerViewAllProductScreen(
    //   searchKeyword: BuyerSearchBloc.searchTextEditingCtrl.text,
    //   productList: productList,
    // );
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route).then((value) {});
  }
//endregion

  //region Go to Profile screen
  goToUserProfileScreen({required String userReference}) {
    ///Add searched item
    BuyerSearchServices().addSearchedItem(
      searchedText: BuyerSearchBloc.searchTextEditingCtrl.text,
      searchedItem: userReference,
      searchType: entityType.name,
    );
    var screen = UserProfileScreen(
      userReference: userReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

//endregion

//region Dispose
  void dispose() {
    //Get reference to the SearchResultCountDataModel
    late SearchResultCountDataModel searchResultCountDataModel =
        Provider.of<SearchResultCountDataModel>(context, listen: false);
    searchResultCountDataModel.clearData();
    imageCache.clear();
    searchResultCtrl.close();
  }
//endregion
}
