import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/how_refund_amount_calculated/how_refund_amount_calculated.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class CancelledByYouAfterShippingBloc {
  // region Common Variables
  BuildContext context;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  late BuyerMyOrderServices buyerMyOrderServices;
  late GetOrderResponse buyerMyOrderResponse;
  final Order store;
  final List<SubOrder> subOrderList;


  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  CancelledByYouAfterShippingBloc(this.context, this.buyerSubOrderBloc, this.store, this.subOrderList);

  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
  }

// endregion

// region On how refund calculated
  Future onTapHowRefundCalculated() {
    return CommonMethods.appBottomSheet(
        screen: HowRefundAmountCalculated(
      subOrderList: subOrderList,
      store: store, buyerSubOrderBloc:buyerSubOrderBloc,
    ), context: context, bottomSheetName: AppStrings.refundAmountCalculation).then((value) {
      //Unselect all
      CommonMethods.subOrderSelectUnSelectAll(isSelectAll: false, subOrderList: subOrderList);
    });
  }
// endregion
}
