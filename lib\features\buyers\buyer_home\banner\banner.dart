import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_home/banner/banner_bloc.dart';
import 'package:swadesic/model/banner_response/banner_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

//region Banner screen
class BannerScreen extends StatefulWidget {
  final bool isFromFeed;
  const BannerScreen({Key? key, required this.isFromFeed}) : super(key: key);

  @override
  State<BannerScreen> createState() => _BannerScreenState();
}
//endregion

class _BannerScreenState extends State<BannerScreen>with AutomaticKeepAliveClientMixin<BannerScreen> {

  //Keep alive
  @override
  bool get wantKeepAlive => true;
  //Width
  double width = 0.0;
  //region Bloc
  late BannerBloc bannerBloc;
  //endregion

  //region Init
  @override
  void initState() {
    bannerBloc = BannerBloc(context,widget.isFromFeed);
    bannerBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    bannerBloc.dispose();
    super.dispose();
  }

  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    // return AppConstants.appData.isUserView! && !widget.isFromFeed?homeBanner():
    // feedBanner():
    //
    //Home banner
    if(!widget.isFromFeed){
      return homeBanner();
    }
    //Else feed banner
    else{
      return feedBanner();
    }
  }
  //endregion

//region Home banner
Widget homeBanner(){
    return Container(
      color: AppColors.appWhite,
      alignment: Alignment.topCenter,
      height: CommonMethods.calculateWebWidth(context: context) * 0.7 ,
      child: StreamBuilder<List<BannerInfo>>(
          stream: bannerBloc.bannerCtrl.stream,
          initialData:const [],
          builder: (context, snapshot) {
            //Not empty
            if (snapshot.data!.isNotEmpty) {
              return Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  PageView.builder(
                      allowImplicitScrolling: true,
                      onPageChanged: (index) {
                        bannerBloc.onChangeSlider(index);
                      },
                      itemCount: snapshot.data!.length,
                      controller: bannerBloc.imageSliderPageCtrl,
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: (){
                            bannerBloc.onTapBanner(bannerInfo: snapshot.data![index]);
                          },
                          child: extendedImage(snapshot.data![index].imageUrl!, context, 500, 500,
                              fit: BoxFit.fitWidth, customPlaceHolder: AppImages.bannerPlaceHolder
                            // fit: BoxFit.cover,
                          ),
                        );
                      }),
                  Visibility(
                    visible:snapshot.data!.length != 1,
                    child: Container(
                      margin: const EdgeInsets.symmetric(vertical: 10),
                      height: 6,
                      child: ListView.builder(
                        // physics: const NeverScrollableScrollPhysics(),
                          itemCount: snapshot.data!.length,
                          scrollDirection: Axis.horizontal,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.only(right: 5),
                              child: SvgPicture.asset(
                                AppImages.dot,
                                height: 5.29,
                                width: 5.29,
                                color: bannerBloc.currentPage == index ? AppColors.darkGray : AppColors.darkStroke,
                              ),
                            );
                          }),
                    ),
                  ),
                ],
              );
            }
            //Empty
            if (snapshot.data!.isEmpty) {
              return SvgPicture.asset(
                AppImages.bannerPlaceHolder,
                fit: BoxFit.fitWidth,
              );
            }
            return SvgPicture.asset(
              AppImages.bannerPlaceHolder,
              fit: BoxFit.fitWidth,
            );
          }),
    );
}
//endregion


//region Feed banner
  Widget feedBanner(){
    return  ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: StreamBuilder<List<BannerInfo>>(
          stream: bannerBloc.bannerCtrl.stream,
          initialData: [],
          builder: (context, snapshot) {
            //Not empty
            if (snapshot.data!.isNotEmpty) {
              return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 5,vertical: 10),
                  width: double.infinity,
                  height: CommonMethods.calculateWebWidth(context: context) * 0.16,
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      PageView.builder(
                          allowImplicitScrolling: true,
                          onPageChanged: (index) {
                            bannerBloc.onChangeSlider(index);
                          },
                          itemCount: snapshot.data!.length,
                          controller: bannerBloc.imageSliderPageCtrl,
                          itemBuilder: (context, index) {
                            return InkWell(
                              onTap: (){
                                bannerBloc.onTapBanner(bannerInfo: snapshot.data![index]);
                              },
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(CommonMethods.calculateWebWidth(context: context) * 0.02),
                                child: CachedNetworkImage(
                                  imageUrl: "${AppConstants.baseUrl}${snapshot.data![index].imageUrl}",
                                  width: double.infinity,
                                  height: CommonMethods.calculateWebWidth(context: context) * 0.16,
                                  fit: BoxFit.fitWidth,
                                  placeholder: (context, url) => Image.asset(
                                    AppImages.feedPlaceHolder,
                                    fit: BoxFit.fitWidth,
                                  ),
                                  errorWidget: (context, url, error) => Image.asset(
                                    AppImages.feedPlaceHolder,
                                    fit: BoxFit.fitWidth,
                                  ),
                                ),
                              ),
                            );
                          }),
                      Visibility(
                        visible: snapshot.data!.length != 1,
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 5),
                          height: 6,
                          child: ListView.builder(
                            // physics: const NeverScrollableScrollPhysics(),
                              itemCount: snapshot.data!.length,
                              scrollDirection: Axis.horizontal,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(right: 5),
                                  child: SvgPicture.asset(
                                    AppImages.dot,
                                    height: 5.29,
                                    width: 5.29,
                                    color: bannerBloc.currentPage == index ? AppColors.darkGray : AppColors.darkStroke,
                                  ),
                                );
                              }),
                        ),
                      ),
                    ],
                  ));
            }
            return const SizedBox();
          }
      ),
    );
  }
//endregion
}
