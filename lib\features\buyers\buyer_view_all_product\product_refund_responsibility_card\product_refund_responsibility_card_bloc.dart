import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';



class ProductRefundResponsibilityCardBloc {
  // region Common Variables
  BuildContext context;


  // endregion

  //region Controller
  final dropDownRefreshCtrl = StreamController<bool>.broadcast();
  //endregion

  //region Text Editing Controller
  TextEditingController searchTextEditingCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  ProductRefundResponsibilityCardBloc(this.context);  // endregion

  // region Init
  void init() {

  }
// endregion


//region On tap drop down
void onTapDropDown({required bool value}) {
  dropDownRefreshCtrl.add(!value);
}
//endregion


}
