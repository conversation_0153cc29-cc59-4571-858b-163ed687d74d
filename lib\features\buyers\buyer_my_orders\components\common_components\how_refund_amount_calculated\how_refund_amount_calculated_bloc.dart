import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class HowRefundAmountCalculatedBloc {
  // region Common Variables
  BuildContext context;
  bool isProductListDropDownVisible = true;
  // bool isTotalOrderVisible = false;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  late BuyerMyOrderServices buyerMyOrderServices;
  late GetOrderResponse buyerMyOrderResponse;
  final List<SubOrder> subOrderList;
  final Order order;

  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller
  final cancelReasonTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  HowRefundAmountCalculatedBloc(this.context, this.buyerSubOrderBloc, this.order, this.subOrderList);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
  }
// endregion


//region On tap Product list Drop down
  void onTapProductList(){
    isProductListDropDownVisible = !isProductListDropDownVisible;
    bottomSheetRefresh.sink.add(true);

  }
//endregion



//region Select suborder
  void onSelectSubOrder(SubOrder subOrder) {
    subOrder.isSelected = !subOrder.isSelected;

    bottomSheetRefresh.sink.add(true);
  }

//endregion




  //region Proceed to cancel
  proceedToCancel({required List<SubOrder> subOrderList}) async {

    //If reason is empty then return
    if(cancelReasonTextCtrl.text.isEmpty){
      return CommonMethods.toastMessage(AppStrings.cancelReasonCanNot, context);
    }
    //Check no sub order is selected
    if(CommonMethods.sellerSelectedSubOrderNumberList(subOrderList).isEmpty){
      return CommonMethods.toastMessage(AppStrings.pleaseSelectOrders, context);
    }


    //region Try
    try {
      await buyerMyOrderServices.cancelOrder(CommonMethods.sellerSelectedSubOrderNumberList(subOrderList),cancelReasonTextCtrl.text);
      //Close bottom sheet
      Navigator.pop(context);
      //Message
      CommonMethods.toastMessage("${CommonMethods.sellerSelectedSubOrderNumberList(subOrderList).length==1?"${CommonMethods.sellerSelectedSubOrderNumberList(subOrderList).length} ${AppStrings.order}":"${CommonMethods.sellerSelectedSubOrderNumberList(subOrderList).length} ${AppStrings.orders}"} ${AppStrings.cancelled}", context);

      //Get sub orders
      buyerSubOrderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }
//endregion


}
