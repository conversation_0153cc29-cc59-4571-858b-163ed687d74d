import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:provider/provider.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/delivery_fee_method/delivery_fee_method.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/pickup_location/add_pickup_location_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/prepare_package_delivery/prepare_package_delivery.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/select_delivery_method/select_delivery_method.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/self_or_logistics/self_or_logistics.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivery_setting_bloc.dart';
import 'package:swadesic/model/pickup_location/pickup_location.dart';
import 'package:swadesic/model/seller_delivery_setting_response/seller_delivery_setting_response.dart';
import 'package:swadesic/model/seller_return_warranty_response/address_response.dart'
    as address;
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../util/app_images.dart';

// region Seller Store Delivery Setting
class SellerStoreDeliverySettingScreen extends StatefulWidget {
  final String storeRef;
  final int? productId;
  final String? productReference;
  final bool isFromAddProduct;
  final bool isFromEditProduct;
  final bool isFromStore;
  final SellerDeliveryStoreResponse? deliverySettingsDataFromAddProduct;

  const SellerStoreDeliverySettingScreen(
      {Key? key,
      required this.storeRef,
      this.productId,
      this.productReference,
      required this.isFromAddProduct,
      required this.isFromEditProduct,
      required this.isFromStore,
      this.deliverySettingsDataFromAddProduct})
      : super(key: key);

  @override
  _SellerStoreDeliverySettingScreenState createState() =>
      _SellerStoreDeliverySettingScreenState();
}
// endregion

class _SellerStoreDeliverySettingScreenState
    extends State<SellerStoreDeliverySettingScreen> {
  late SellerStoreDeliverySettingBloc sellerStoreDeliverySettingBloc;
  bool isDeliveryEnabled = false;
  bool isPickupEnabled = false;

  @override
  void initState() {
    super.initState();
    sellerStoreDeliverySettingBloc = SellerStoreDeliverySettingBloc(
      context,
      widget.storeRef,
      widget.productId,
      widget.productReference,
      widget.isFromAddProduct,
      widget.deliverySettingsDataFromAddProduct ??
          SellerDeliveryStoreResponse(),
    );

    // Initialize bloc
    WidgetsBinding.instance.addPostFrameCallback((_) {
      //print("Initializing bloc");
      sellerStoreDeliverySettingBloc.loadPickupLocations();
      sellerStoreDeliverySettingBloc.init().then((_) {
        // Set toggle states based on fulfillment options
        setState(() {
          // Check fulfillment options from product or store level settings
          final deliverySettings =
              sellerStoreDeliverySettingBloc.getCurrentDeliverySettings();

          if (deliverySettings != null) {
            final fulfillmentOptions =
                deliverySettings.fulfillmentOptions ?? '';

            // Set delivery toggle
            isDeliveryEnabled = fulfillmentOptions == 'DELIVERY' ||
                fulfillmentOptions == 'DELIVERY_AND_IN_STORE_PICKUP';

            // Set in-store pickup toggle
            isPickupEnabled = fulfillmentOptions == 'IN_STORE_PICKUP' ||
                fulfillmentOptions == 'DELIVERY_AND_IN_STORE_PICKUP';
          }
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Provider<SellerStoreDeliverySettingBloc>(
      create: (_) => sellerStoreDeliverySettingBloc,
      child: GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
        },
        child: Scaffold(
          floatingActionButton: saveButton(),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
          backgroundColor: AppColors.appWhite,
          appBar: appBar(),
          body: SafeArea(
            child: StreamBuilder<DeliverySettingState>(
              stream: sellerStoreDeliverySettingBloc.deliverySettingCtrl.stream,
              builder: (context, snapshot) {
                if (snapshot.data == DeliverySettingState.Success) {
                  return SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            "Sellers have the flexibility to sell anywhere they want in their own options.",
                            style: AppTextStyle.subTitle(
                                textColor: AppColors.brandBlack),
                          ),
                        ),
                        // Add product level title when coming from add/edit product
                        widget.isFromAddProduct || widget.isFromEditProduct
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0),
                                child: editProductLeverTitle(),
                              )
                            : const SizedBox(),
                        const SizedBox(height: 10),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Text(
                            "Fulfillment Options",
                            style: AppTextStyle.sectionHeading(
                                textColor: AppColors.appBlack),
                          ),
                        ),
                        const SizedBox(height: 14),
                        _buildFulfillmentOption(
                          title: "Delivery (Ship to Customer)",
                          description:
                              "Buyers place an order on Swadesic and pay online. You ship the products to them.",
                          isEnabled: isDeliveryEnabled,
                          onChanged: (value) {
                            setState(() {
                              isDeliveryEnabled = value;

                              // Update fulfillment options in the bloc
                              final currentSettings =
                                  sellerStoreDeliverySettingBloc
                                      .getCurrentDeliverySettings();

                              if (currentSettings != null) {
                                // Update fulfillment options based on toggle
                                currentSettings.fulfillmentOptions = value
                                    ? (isPickupEnabled
                                        ? 'DELIVERY_AND_IN_STORE_PICKUP'
                                        : 'DELIVERY')
                                    : (isPickupEnabled
                                        ? 'IN_STORE_PICKUP'
                                        : '');
                              }

                              // Trigger settings change
                              sellerStoreDeliverySettingBloc.onChangeSetting();
                            });
                          },
                        ),
                        _buildFulfillmentOption(
                          title: "In-Store Pickup (Buy at store)",
                          description:
                              "Buyers see your store locations and visit to purchase in person.",
                          isEnabled: isPickupEnabled,
                          onChanged: (value) {
                            setState(() {
                              isPickupEnabled = value;

                              // Update fulfillment options in the bloc
                              final currentSettings =
                                  sellerStoreDeliverySettingBloc
                                      .getCurrentDeliverySettings();

                              if (currentSettings != null) {
                                // Update fulfillment options based on toggle
                                currentSettings.fulfillmentOptions = value
                                    ? (isDeliveryEnabled
                                        ? 'DELIVERY_AND_IN_STORE_PICKUP'
                                        : 'IN_STORE_PICKUP')
                                    : (isDeliveryEnabled ? 'DELIVERY' : '');
                              }

                              // Trigger settings change
                              sellerStoreDeliverySettingBloc.onChangeSetting();
                            });
                          },
                        ),
                        if (isDeliveryEnabled) ...[
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 8),
                                deliveryPreferences(),
                              ],
                            ),
                          ),
                        ],
                        if (isPickupEnabled) ...[
                          const SizedBox(height: 16),
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Pickup Preferences",
                                  style: AppTextStyle.sectionHeading(
                                      textColor: AppColors.appBlack),
                                ),
                                const SizedBox(height: 8),
                                pickupPreferences(),
                              ],
                            ),
                          ),
                        ],
                        const SizedBox(height: 80),
                      ],
                    ),
                  );
                }
                if (snapshot.data == DeliverySettingState.Loading) {
                  return Center(
                    child: AppCommonWidgets.appCircularProgress(),
                  );
                }
                if (snapshot.data == DeliverySettingState.Failed) {
                  return Center(
                    child: AppCommonWidgets.errorMessage(
                        error: AppStrings.commonErrorMessage),
                  );
                }
                return const SizedBox();
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFulfillmentOption({
    required String title,
    required String description,
    required bool isEnabled,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.darkStroke),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment:
            CrossAxisAlignment.center, // Center items vertically
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min, // Take minimum vertical space
              children: [
                Text(
                  title,
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack),
                ),
                const SizedBox(height: 8),
                Text(
                  description,
                  style: AppTextStyle.contentText0(
                          textColor: AppColors.writingColor3)
                      .copyWith(
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Transform.scale(
            scale: 1.0,
            child: CupertinoSwitch(
              value: isEnabled,
              onChanged: onChanged,
              activeColor: AppColors.brandBlack,
            ),
          ),
        ],
      ),
    );
  }

  Widget pickupPreferences() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Store locations for Pickup",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 8),
        Text(
          "Select one or more locations that will be available for customer pickup",
          style: AppTextStyle.contentText0(textColor: AppColors.writingColor3)
              .copyWith(
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 16),
        ValueListenableBuilder<List<address.Data>>(
          valueListenable:
              sellerStoreDeliverySettingBloc.pickupLocationsNotifier,
          builder: (context, locations, child) {
            return Column(
              children: [
                if (locations.isNotEmpty) ...[
                  ...locations
                      .map((location) => _buildPickupLocationCard(location))
                      .toList(),
                  const SizedBox(height: 16),
                ],
                _buildAddPickupLocationButton(),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildPickupLocationCard(address.Data location) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: AppColors.borderColor1),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        child: Slidable(
          key: ValueKey(location.addressid),
          endActionPane: ActionPane(
            motion: const DrawerMotion(),
            children: [
              Theme(
                data: Theme.of(context).copyWith(
                  textTheme: Theme.of(context).textTheme.copyWith(
                    labelLarge: AppTextStyle.smallText(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ),
                child: SlidableAction(
                  onPressed: (_) {
                    sellerStoreDeliverySettingBloc.editPickupLocation(location);
                  },
                  backgroundColor: AppColors.textFieldFill1,
                  foregroundColor: AppColors.appBlack,
                  icon: Icons.edit,
                  label: 'Edit',
                ),
              ),
              Theme(
                data: Theme.of(context).copyWith(
                  textTheme: Theme.of(context).textTheme.copyWith(
                    labelLarge: AppTextStyle.smallText(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ),
                child: SlidableAction(
                  onPressed: (_) {
                    sellerStoreDeliverySettingBloc.deletePickupLocation(location);
                  },
                  backgroundColor: AppColors.red.withOpacity(0.05),
                  foregroundColor: AppColors.red,
                  icon: Icons.delete,
                label: 'Delete',
              ),
            ),
            ],
          ),
          child: InkWell(
            onTap: () {
              final index = sellerStoreDeliverySettingBloc.pickupLocations
                  .indexOf(location);
              sellerStoreDeliverySettingBloc.togglePickupLocationStatus(
                  index, !(location.isPickupLocation ?? false));
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Checkbox(
                    value: location.isPickupLocation == true,
                    activeColor: AppColors.brandBlack,
                    onChanged: (value) {
                      // Call the toggle method in the bloc
                      sellerStoreDeliverySettingBloc.togglePickupLocationStatus(
                          sellerStoreDeliverySettingBloc.pickupLocations
                              .indexOf(location),
                          value ?? false);
                    },
                  ),
                  horizontalSizedBox(15),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          location.name ?? '',
                          maxLines: 1,
                          style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack),
                        ),
                        verticalSizedBox(5),
                        Text(
                          "${location.address}, ${location.city}, ${location.state}, ${location.pincode}",
                          style: AppTextStyle.contentText0(
                              textColor: AppColors.appBlack),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAddPickupLocationButton() {
    return Container(
      alignment: Alignment.centerLeft,
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        minSize: 0,
        onPressed: () {
          sellerStoreDeliverySettingBloc.addPickupLocation();
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: AppColors.textFieldFill1,
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                border: Border.all(color: AppColors.lightStroke),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    AppImages.plus,
                    color: AppColors.appBlack,
                    fit: BoxFit.fill,
                    height: 14,
                  ),
                  horizontalSizedBox(15),
                  Text(
                    "Add a new Pickup location",
                    style: AppTextStyle.access0(textColor: AppColors.appBlack),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget deliveryPreferences() {
    return Column(
      children: [
        storeLevelTitle(),
        SelectDeliveryMethod(
            sellerStoreDeliverySettingBloc: sellerStoreDeliverySettingBloc),
        preparePackageDelivery(),
        SelfOrLogistics(
            sellerStoreDeliverySettingBloc: sellerStoreDeliverySettingBloc),
        servicedLocation(),
        DeliveryFeeMethod(
            sellerStoreDeliverySettingBloc: sellerStoreDeliverySettingBloc),
        // AppCommonWidgets.bottomListSpace(context: context)
      ],
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: AppStrings.deliverySettings,
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isTextButtonVisible: false,
    );
  }

  //endregion

  //region Save button
  Widget saveButton() {
    return StreamBuilder<DeliverySettingState>(
        stream: sellerStoreDeliverySettingBloc.deliverySettingCtrl.stream,
        initialData: DeliverySettingState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == DeliverySettingState.Success) {
            return Align(
              alignment: Alignment.bottomCenter,
              child: FloatingActionButton.extended(
                  backgroundColor: AppColors.appWhite,
                  extendedPadding: EdgeInsets.zero,
                  isExtended: true,
                  elevation: 0,
                  tooltip: AppStrings.saveChanges,
                  label: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      ///Check box
                      /*
                      Make this check box visible only if
                      1. If store level setting is not there. Means store level setting id has to be null/0
                      2. Not from store level view
                       */
                      Visibility(
                        visible: SellerStoreDeliverySettingBloc
                                    .storeLevelDeliverySettingResponse
                                    .deliverySettingData!
                                    .deliverysettingid ==
                                0 &&
                            !widget.isFromStore,
                        child: Container(
                          margin: const EdgeInsets.only(right: 10),
                          child: appRadioCheckBox(
                              isActive: sellerStoreDeliverySettingBloc
                                  .saveAsStoreDefault,
                              text: AppStrings.saveAsStoreDefault,
                              isExpand: false,
                              onTap: () {
                                //If already selected then mark as false
                                if (sellerStoreDeliverySettingBloc
                                    .saveAsStoreDefault) {
                                  //Save as store default to false
                                  sellerStoreDeliverySettingBloc
                                      .saveAsStoreDefault = false;
                                  //Success
                                  sellerStoreDeliverySettingBloc
                                      .deliverySettingCtrl.sink
                                      .add(DeliverySettingState.Success);
                                  return;
                                }
                                //Else open dialog and show disclaimer
                                sellerStoreDeliverySettingBloc
                                    .opTapSaveStoreDefault();
                              }),
                        ),
                      ),

                      ///Save change button
                      Row(
                        children: [
                          CupertinoButton(
                            color: AppColors.brandBlack,
                            borderRadius: BorderRadius.circular(100),
                            onPressed: () {
                              sellerStoreDeliverySettingBloc
                                  .addDeliveryStoreSettings(
                                isDeliveryEnabled: isDeliveryEnabled,
                                isPickupEnabled: isPickupEnabled,
                              );
                            },
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 10),
                            child: Text(
                              AppStrings.saveChanges,
                              style: AppTextStyle.access0(
                                  textColor: AppColors.appWhite),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  onPressed: null),
            );
          }
          return const SizedBox();
        });
  }

  //endregion

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            storeLevelTitle(),
            SelectDeliveryMethod(
                sellerStoreDeliverySettingBloc: sellerStoreDeliverySettingBloc),
            preparePackageDelivery(),
            SelfOrLogistics(
                sellerStoreDeliverySettingBloc: sellerStoreDeliverySettingBloc),
            servicedLocation(),
            DeliveryFeeMethod(
                sellerStoreDeliverySettingBloc: sellerStoreDeliverySettingBloc),
            AppCommonWidgets.bottomListSpace(context: context)
          ],
        ),
      ),
    );
  }

// endregion

  //region Store level title
  Widget storeLevelTitle() {
    return Visibility(
        visible: widget.isFromStore && !widget.isFromAddProduct,
        child: Container(
            margin: const EdgeInsets.only(bottom: 20),
            child: const SizedBox()));
  }

//endregion

  Widget editProductLeverTitle() {
    return StreamBuilder<bool>(
        stream: sellerStoreDeliverySettingBloc.isSameSettingCtrl.stream,
        builder: (context, snapshot) {
          // Check if product level settings exist
          final productLevelSettings = SellerStoreDeliverySettingBloc
              .productLevelDeliverySettings.deliverySettingData;
          final storeLevelSettings = SellerStoreDeliverySettingBloc
              .storeLevelDeliverySettingResponse.deliverySettingData;

          // Early return conditions
          if (widget.isFromAddProduct &&
              storeLevelSettings!.deliverysettingid! == 0 &&
              productLevelSettings!.deliverysettingid! == 0) {
            return const SizedBox();
          }

          // Determine if settings are the same
          final isSameSettings = sellerStoreDeliverySettingBloc.isSameSetting();

          // Determine visibility and content
          bool showFetchedFromStoreText = false;
          bool showCustomizedText = false;

          // Check if store-level settings actually exist
          bool storeSettingsExist = storeLevelSettings != null &&
              storeLevelSettings.deliverysettingid != 0;

          // Case 1: No product level settings exist or settings are the same (and store settings exist)
          if ((productLevelSettings!.deliverysettingid == 0 ||
                  isSameSettings) &&
              storeSettingsExist) {
            showFetchedFromStoreText = true;
          }
          // Case 2: Product level settings exist and differ from store level (and store settings exist)
          else if (storeSettingsExist &&
              productLevelSettings.deliverysettingid != 0 &&
              !isSameSettings) {
            showCustomizedText = true;
          }
          // Case 3: No store settings exist but product settings exist - show customized text (without reset button)
          else if (!storeSettingsExist &&
              productLevelSettings.deliverysettingid != 0) {
            showCustomizedText = true;
          }
          // Case 4: No store settings and no product settings - show nothing
          else if (!storeSettingsExist &&
              productLevelSettings.deliverysettingid == 0) {
            showFetchedFromStoreText = false;
            showCustomizedText = false;
          }

          // Force show customized text if settings are different and store settings exist
          if (storeSettingsExist &&
              productLevelSettings.fulfillmentOptions !=
                  storeLevelSettings.fulfillmentOptions) {
            showFetchedFromStoreText = false;
            showCustomizedText = true;
          }

          return Visibility(
              visible: widget.isFromEditProduct || widget.isFromAddProduct,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show "Preferences fetched from store" text
                  if (showFetchedFromStoreText)
                    Text(
                      AppStrings.beloPreferencesAreFetchedFromStore,
                      style:
                          AppTextStyle.subTitle(textColor: AppColors.appBlack),
                    ),

                  // Show vertical spacing if text is shown
                  if (showFetchedFromStoreText) verticalSizedBox(10),

                  // Show "Preferences customized" text and reset button (only if store settings exist)
                  if (showCustomizedText) ...[
                    Text(
                      "The preferences are customized to this product",
                      style:
                          AppTextStyle.subTitle(textColor: AppColors.appBlack),
                    ),
                    // Only show reset button if store settings exist to reset to
                    if (storeSettingsExist) ...[
                      Padding(
                        padding: const EdgeInsets.only(bottom: 20, top: 10),
                        child: AppCommonWidgets.borderButton(
                            buttonName: AppStrings.resetToDefaultStoreSettings,
                            onTap: () async {
                              await sellerStoreDeliverySettingBloc.onTapReset();

                              // Reload pickup locations
                              sellerStoreDeliverySettingBloc
                                  .loadPickupLocations();

                              // Update toggle states based on reset settings
                              setState(() {
                                final deliverySettings =
                                    sellerStoreDeliverySettingBloc
                                        .getCurrentDeliverySettings();

                                if (deliverySettings != null) {
                                  final fulfillmentOptions =
                                      deliverySettings.fulfillmentOptions ?? '';

                                  // Set delivery toggle
                                  isDeliveryEnabled =
                                      fulfillmentOptions == 'DELIVERY' ||
                                          fulfillmentOptions ==
                                              'DELIVERY_AND_IN_STORE_PICKUP';

                                  // Set in-store pickup toggle
                                  isPickupEnabled =
                                      fulfillmentOptions == 'IN_STORE_PICKUP' ||
                                          fulfillmentOptions ==
                                              'DELIVERY_AND_IN_STORE_PICKUP';
                                }
                              });

                              // Trigger settings change
                              sellerStoreDeliverySettingBloc.onChangeSetting();
                            }),
                      ),
                    ] else ...[
                      // Just add some spacing when no reset button is shown
                      verticalSizedBox(10),
                    ],
                  ],
                ],
              ));
        });
  }

  //region Edit level title
//endregion

  //region Add level intro
  Widget addLevelIntro() {
    return Column(
      children: [
        ///From add product and settings are same as store level
        /*
        Make this visible only if
        1. Make this visible only if comes from add product screen,
        2. Store level setting is not empty and store level setting id is not null
        3. resetVisible flag has to be true.
         */
        Visibility(
            visible: sellerStoreDeliverySettingBloc.isFromAddProduct &&
                SellerStoreDeliverySettingBloc.storeLevelDeliverySettingResponse
                        .deliverySettingData!.deliverysettingid !=
                    0 &&
                !sellerStoreDeliverySettingBloc.resetVisible,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppStrings.beloPreferencesAreFetchedFromStore,
                  style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
                ),
                verticalSizedBox(10)
              ],
            )),

        ///From Add product and settings are not same as store level
        /*
        Make this visible only if
        1. Make this visible only if comes from add product screen,
        2. Store level setting is not empty
        3. resetVisible flag has to be true.
         */
        Visibility(
            visible: sellerStoreDeliverySettingBloc.isFromAddProduct &&
                SellerStoreDeliverySettingBloc.storeLevelDeliverySettingResponse
                        .deliverySettingData!.deliverysettingid !=
                    0 &&
                sellerStoreDeliverySettingBloc.resetVisible,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppStrings.youCanCustomizeDelivery,
                  style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
                ),
                verticalSizedBox(10),
                AppCommonWidgets.borderButton(
                    buttonName: AppStrings.resetToDefaultStoreSettings,
                    onTap: () {
                      sellerStoreDeliverySettingBloc.onTapReset();

                      // Reload pickup locations
                      sellerStoreDeliverySettingBloc.loadPickupLocations();

                      // Update toggle states based on reset settings
                      setState(() {
                        final deliverySettings = sellerStoreDeliverySettingBloc
                            .getCurrentDeliverySettings();

                        if (deliverySettings != null) {
                          final fulfillmentOptions =
                              deliverySettings.fulfillmentOptions ?? '';

                          // Set delivery toggle
                          isDeliveryEnabled =
                              fulfillmentOptions == 'DELIVERY' ||
                                  fulfillmentOptions ==
                                      'DELIVERY_AND_IN_STORE_PICKUP';

                          // Set in-store pickup toggle
                          isPickupEnabled =
                              fulfillmentOptions == 'IN_STORE_PICKUP' ||
                                  fulfillmentOptions ==
                                      'DELIVERY_AND_IN_STORE_PICKUP';
                        }
                      });

                      // Trigger settings change
                      sellerStoreDeliverySettingBloc.onChangeSetting();
                    }),
                verticalSizedBox(20)
              ],
            )),
      ],
    );
  }
  //endregion

  //region Prepare package and delivery
  Widget preparePackageDelivery() {
    return PreparePackageDelivery(
      sellerStoreDeliverySettingBloc: sellerStoreDeliverySettingBloc,
    );
  }

  //endregion

  //region Serviced Location
  Widget servicedLocation() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: AppStrings.deliveryServiceLocation,
          option: Column(
            children: [
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  //If trust center is not completed then show a message that you have to completed the trust center
                  if (!sellerStoreDeliverySettingBloc.isTrustCenterActive()) {
                    context.mounted
                        ? CommonMethods.toastMessage(
                            AppStrings.trustCenterSetupIncompleteMessage,
                            context)
                        : null;
                    return;
                  }
                  //Else open delivery location list
                  else {
                    sellerStoreDeliverySettingBloc.onTapSelectLocation();
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  alignment: Alignment.centerLeft,
                  height: 43,
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: AppColors.textFieldFill1,
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                  ),
                  child: StreamBuilder<bool>(
                      stream: sellerStoreDeliverySettingBloc.pinCodeCtrl.stream,
                      builder: (context, snapshot) {
                        return SellerStoreDeliverySettingBloc
                                .productLevelDeliverySettings
                                .deliverySettingData!
                                .deliveryLocations!
                                .isEmpty
                            ? Text("0 locations selected",
                                style: AppTextStyle.contentText0(
                                    textColor: AppColors.brandBlack))
                            : Text(
                                "${SellerStoreDeliverySettingBloc.productLevelDeliverySettings.deliverySettingData!.selectedLocationCount!} locations selected",
                                style: AppTextStyle.contentText0(
                                    textColor: AppColors.brandBlack));
                      }),
                ),
              ),

              //If trust center is not completed
              Visibility(
                  visible:
                      !sellerStoreDeliverySettingBloc.isTrustCenterActive(),
                  child: RichText(
                    textScaler: MediaQuery.textScalerOf(context),
                    text: TextSpan(
                      text: "${AppStrings.trustCenterSetupIncompleteMessage} ",
                      style: AppTextStyle.smallText(textColor: AppColors.red),
                      children: <TextSpan>[
                        TextSpan(
                          text: 'Go to Trust Center',
                          style: AppTextStyle.smallText(
                              textColor: AppColors.red, isUnderline: true),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              sellerStoreDeliverySettingBloc.goToTrustCenter();
                            },
                        ),
                      ],
                    ),
                  )),
            ],
          ),
        ),
        //
        //
        // Text(AppStrings.deliveryServiceLocation,
        //   style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
        // ),
        // verticalSizedBox(13),
        //
        // verticalSizedBox(13),
        // CupertinoButton(
        //   padding: EdgeInsets.zero,
        //   onPressed: (){
        //    sellerStoreDeliverySettingBloc.onTapSelectLocation();
        //   },
        //   child: Container(
        //     padding: const EdgeInsets.symmetric(horizontal: 15),
        //     alignment: Alignment.centerLeft,
        //     height: 43,
        //     width: double.infinity,
        //     decoration: const BoxDecoration(
        //       color: AppColors.lightestGrey,
        //       borderRadius: BorderRadius.all(Radius.circular(10)),
        //     ),
        //     child: StreamBuilder<bool>(
        //         stream: sellerStoreDeliverySettingBloc.pinCodeCtrl.stream,
        //         builder: (context, snapshot) {
        //
        //           return SellerStoreDeliverySettingBloc.deliverySettingResponse.deliverySettingData!.deliveryLocations!.isEmpty
        //               ?  Text("0 locations selected",
        //                   style: AppTextStyle.heading2Medium(textColor: AppColors.brandGreen))
        //               : Text("${SellerStoreDeliverySettingBloc.deliverySettingResponse.deliverySettingData!.selectedLocationCount!} locations selected",
        //                   style: AppTextStyle.heading2Medium(textColor: AppColors.brandGreen));
        //         }),
        //   ),
        // ),
        verticalSizedBox(23)
      ],
    );
  }

//endregion
}
