import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/app_buttons/app_buttons.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class WelcomeTo extends StatefulWidget {
  @override
  _WelcomeToState createState() => _WelcomeToState();
}

class _WelcomeToState extends State<WelcomeTo> {

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(20),
      child: ListView(
        // crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Welcome to Swadesic!!',
            style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            'You’ve joined a community supporting Swadeshi products and small businesses.',
            style:AppTextStyle.subTitle(textColor: AppColors.appBlack),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            '(This card appears only the first time)',
            style: AppTextStyle.smallText(textColor: AppColors.writingBlack1).copyWith(fontSize: 10),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Container(
              alignment: Alignment.centerLeft,
              child: Text("Get Started & Make an Impact",style: AppTextStyle.subTitle(textColor: AppColors.appBlack).copyWith(fontFamily: AppConstants.rBold),)),
          const SizedBox(height: 20),
          titleAndSubTitle(title: "Discover & Support",subTitle: "Explore unique Swadeshi products and follow your favorite stores."),
          titleAndSubTitle(title: "Leave a Review",subTitle: "Review Swadeshi products, even from other platforms, to help others find great alternatives!"),
          titleAndSubTitle(title: "Create a Post",subTitle: "Share your thoughts or shout out deserved small brands . We’ll reach to them & offer them exclusive rewards!"),
          titleAndSubTitle(title: "Invite Friends & Stores",subTitle: "Expand the Swadeshi movement and earn rewards yourself for every new sign-up (click on Swadesic icon at the top)."),
          titleAndSubTitle(title: "",subTitle: "Set up your store for free and build a loyal customer base!"),
          AppButtons().button1(
              buttonTextColor: AppColors.appWhite,
              buttonColor: AppColors.appBlack,
              context: context, buttonName: AppStrings.gotIt, onTap: (){
            Navigator.pop(context);
          })
        ],
      ),
    );
  }


  //region Title and sub title
Widget titleAndSubTitle({required String title,required String subTitle}){
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      alignment: Alignment.centerLeft,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: title.isNotEmpty,
            child: Text(
              title,
              style: AppTextStyle.access0Strike(textColor: AppColors.appBlack),
              textAlign: TextAlign.left,
            ),
          ),
          Text(
            subTitle,
            style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
            textAlign: TextAlign.left,
          )
        ],
      ),
    );
}
//endregion
}

