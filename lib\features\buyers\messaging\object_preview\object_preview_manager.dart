import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/messaging/object_preview/models/object_preview_models.dart';
import 'package:swadesic/features/buyers/messaging/object_preview/services/object_preview_service.dart';
import 'package:swadesic/features/buyers/messaging/object_preview/widgets/object_preview_widgets.dart';
import 'dart:developer' as developer;

/// Manager class for handling object previews in messages
class ObjectPreviewManager {
  static const String _tag = 'ObjectPreviewManager';
  
  final ObjectPreviewService _previewService = ObjectPreviewService();
  
  // Cache for previews to avoid redundant API calls
  final Map<String, dynamic> _previewCache = {};

  /// Gets the appropriate preview widget for an object reference
  Future<Widget?> getObjectPreviewWidget(String reference, bool isMe) async {
    try {
      developer.log(
        '[ENTER] getObjectPreviewWidget(): Getting preview for reference: $reference',
        name: _tag
      );

      // Check cache first
      if (_previewCache.containsKey(reference)) {
        developer.log(
          '[INFO] getObjectPreviewWidget(): Using cached preview for reference: $reference',
          name: _tag
        );
        return _buildPreviewWidget(_previewCache[reference], isMe);
      }

      // Fetch preview data
      final preview = await _previewService.getObjectPreview(reference);
      
      if (preview == null) {
        developer.log(
          '[EXIT] getObjectPreviewWidget(): No preview found for reference: $reference',
          name: _tag
        );
        return null;
      }

      // Cache the preview
      _previewCache[reference] = preview;
      
      developer.log(
        '[EXIT] getObjectPreviewWidget(): Successfully got preview for reference: $reference',
        name: _tag
      );
      
      return _buildPreviewWidget(preview, isMe);
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] getObjectPreviewWidget(): Failed to get preview: ${e.toString()}',
        name: _tag,
        stackTrace: stackTrace
      );
      return null;
    }
  }

  /// Builds the appropriate widget based on the preview type
  Widget _buildPreviewWidget(dynamic preview, bool isMe) {
    if (preview is EntityPreview) {
      return EntityPreviewWidget(entity: preview, isMe: isMe);
    } else if (preview is PostPreview) {
      return PostPreviewWidget(post: preview, isMe: isMe);
    } else if (preview is ProductPreview) {
      return ProductPreviewWidget(product: preview, isMe: isMe);
    } else if (preview is OrderPreview) {
      return OrderPreviewWidget(order: preview, isMe: isMe);
    } else {
      throw Exception('Unknown preview type: ${preview.runtimeType}');
    }
  }

  /// Clears the preview cache
  void clearCache() {
    _previewCache.clear();
  }
}
