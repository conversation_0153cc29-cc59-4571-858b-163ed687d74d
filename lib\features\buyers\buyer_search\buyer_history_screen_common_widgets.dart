import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/buyer_search_response/buyer_search_history_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class BuyerSearchCommonWidgets {
//region Options
  static Widget options(
      {required text,
      required onPress,
      required bool isSelected,
      int count = 0,
      required BuildContext context}) {
    return Flexible(
      child: InkWell(
        onTap: () {
          onPress();
        },
        child: Container(
            constraints: BoxConstraints(
                minWidth:
                    CommonMethods.calculateWebWidth(context: context) / 4 - 10),
            margin: const EdgeInsets.only(right: 5),
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 5),
            decoration: BoxDecoration(
                color:
                    isSelected ? AppColors.brandBlack : AppColors.lightestGrey2,
                borderRadius: BorderRadius.circular(9)),
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  //Text
                  Text(
                    text,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: AppTextStyle.contentText0(
                            textColor: isSelected
                                ? AppColors.appWhite
                                : AppColors.appBlack)
                        .copyWith(height: 1),
                  ),
                  //Count
                  if (count > 0)
                    Padding(
                      padding: const EdgeInsets.only(left: 5),
                      child: Text(
                        "$count",
                        textAlign: TextAlign.center,
                        style: AppTextStyle.smallText(
                                textColor: isSelected
                                    ? AppColors.appWhite
                                    : AppColors.appBlack)
                            .copyWith(height: 1),
                      ),
                    )
                ],
              ),
            )),
      ),
    );
  }

//endregion

//region Searched keyword
  static Widget keyWords(
      {required History history,
      required onPressCross,
      required onTapKeyWord}) {
    return Container(
      margin: const EdgeInsets.only(left: 15),
      // padding: const EdgeInsets.symmetric(vertical: 7.5),
      child: InkWell(
        onTap: () {
          onTapKeyWord();
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(AppImages.searchedTextIcon),
            horizontalSizedBox(7),
            Expanded(
              child: Text(
                history.searchInputText!,
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
            ),
            Theme(
              data: ThemeData(unselectedWidgetColor: AppColors.lightStroke),
              child: InkWell(
                onTap: () {
                  onPressCross();
                },
                child: Container(
                    padding: const EdgeInsets.all(15),
                    child: SvgPicture.asset(
                      AppImages.close,
                      color: AppColors.darkGray,
                      fit: BoxFit.cover,
                    )),
              ),
            ),
          ],
        ),
      ),
    );
  }

//endregion

//region View more, Clear all and view all
  static Widget viewMoreClearAllViewAll(
      {required String data, required bool isUnderline}) {
    return Container(
      alignment: Alignment.centerLeft,
      // margin: const EdgeInsets.symmetric(horizontal: 15,vertical: 10),
      // padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
      padding: const EdgeInsets.only(left: 30, top: 10, bottom: 10),
      child: Text(
        data,
        style: AppTextStyle.access0(
            textColor: AppColors.brandBlack, isUnderline: isUnderline),
      ),
    );
  }

//endregion

//region Title
  static Widget title({required String data}) {
    return Container(
      alignment: Alignment.centerLeft,
      margin: const EdgeInsets.only(left: 15, right: 15, bottom: 10),
      padding: const EdgeInsets.all(10),
      child: Text(
        data,
        style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
      ),
    );
  }

//endregion

//region Searched data card
  static Widget searchedDataCard(
      {String? heading,
      String? title,
      String? subTitle,
      Widget? verifiedWidget,
      required String? imageUrl,
      required BuildContext context,
      required onTapCard,
      required String placeHolder,
      onTapCross,
      bool isCrossVisible = false,
      onPressCross,
      bool isStore = false,
      bool isUser = false,
      bool isProduct = false,
      required CustomImageContainerType customImageContainerType}) {
    return InkWell(
      onTap: () {
        onTapCard();
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 10),
        // height: 50,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CustomImageContainer(
              width: 54,
              height: 54,
              imageUrl: imageUrl,
              imageType: customImageContainerType,
            ),
            // ClipRRect(
            //   borderRadius:  BorderRadius.all(Radius.circular(isStore?19:isUser?100:isProduct?9:0)),
            //   child: SizedBox(
            //       height: 46,
            //       width: 46,
            //       child: extendedImage(imageUrl, context,
            //           customPlaceHolder: placeHolder,
            //           100, 100, cache: true, fit: BoxFit.cover)
            //           ),
            // ),
            //Detail
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 10),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    heading == null
                        ? const SizedBox()
                        : Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                heading,
                                overflow: TextOverflow.visible,
                                maxLines: 1,
                                style: AppTextStyle.contentHeading0(
                                    textColor: AppColors.appBlack),
                              ),
                              verifiedWidget ?? const SizedBox()
                            ],
                          ),
                    title == null
                        ? const SizedBox()
                        : Text(
                            title,
                            maxLines: 1,
                            overflow: TextOverflow.visible,
                            style: AppTextStyle.smallTextRegular(
                                textColor: AppColors.writingBlack1),
                          ),
                    subTitle == null || subTitle.isEmpty
                        ? const SizedBox()
                        : Text(
                            subTitle,
                            maxLines: 1,
                            overflow: TextOverflow.visible,
                            style: AppTextStyle.smallTextRegular(
                                textColor: AppColors.writingBlack1),
                          ),
                  ],
                ),
              ),
            ),
            //Cross
            isCrossVisible
                ? Theme(
                    data:
                        ThemeData(unselectedWidgetColor: AppColors.lightStroke),
                    child: InkWell(
                      onTap: () {
                        onPressCross();
                      },
                      child: Container(
                          padding: const EdgeInsets.all(15),
                          child: SvgPicture.asset(
                            AppImages.close,
                            color: AppColors.darkGray,
                            fit: BoxFit.cover,
                          )),
                    ),
                  )
                : const SizedBox(),
          ],
        ),
      ),
    );
  }
//endregion
}
