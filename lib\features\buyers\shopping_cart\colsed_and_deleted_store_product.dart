import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/shopping_cart/closed_store_dialog/closed_store_dialog.dart';
import 'package:swadesic/features/buyers/shopping_cart/deleted_product_dialog/deleted_product_dialog.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class ClosedAndDeletedStoreProduct {
  late ShoppingCartBloc shoppingCartBloc;
  List<CartStore> closedStoreList = [];
  List<CartProduct> deletedCartProductList = [];

  ClosedAndDeletedStoreProduct({required this.shoppingCartBloc, required this.closedStoreList, required this.deletedCartProductList}) {
    closedStoreList.isNotEmpty?closedStoreDialog():deletedProductDialog();
  }

//region Closed store dialog
  Future closedStoreDialog() {
    return CommonMethods.appDialogBox(
      widget: ClosedStoreDialog(closedStoreList:closedStoreList, shoppingCartBloc: shoppingCartBloc,),
      context: shoppingCartBloc.context,
    ).then((value) {
      //If value is not null and deleted cart product is not empty then open deleted product dialog
      if(value != null && deletedCartProductList.isNotEmpty){
        deletedProductDialog();
      }
    });
  }
//endregion

  //region Deleted product dialog
  Future deletedProductDialog() {
    return CommonMethods.appDialogBox(
      widget: DeletedProductDialog(deletedCartProductList: deletedCartProductList,shoppingCartBloc: shoppingCartBloc),
      context: shoppingCartBloc.context,
    );
  }

//endregion




// //region Remove product from cart and get cart
//   void removeProductsFromCartAndGet()async {
//     await shoppingCartBloc.removeMultipleCartItemsApi(storeIdList: deletedStoreIdList);
//   }
// //endregion
}
