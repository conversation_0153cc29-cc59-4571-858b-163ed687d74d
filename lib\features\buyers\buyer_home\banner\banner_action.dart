import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/supported_stores/supported_stores_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_store_options/create_store_options.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/faq/faq_navigation.dart';
import 'package:swadesic/features/post/add_post/add_post_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/seller/add_product/add_product_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_store_warranty_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivey_setting_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_profile/seller_store_profile_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/id_verification/id_verification_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_screen.dart';
import 'package:swadesic/features/user_profile/edit_user_profile/edit_user_profile_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/banner_response/banner_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class BannerAction {
  final BuildContext context;
  final BannerInfo bannerInfo;

  //region Constructor
  BannerAction(this.context, this.bannerInfo) {
    view();
  }

  //endregion

//region View
  void view() {
    //If static user then return else edit user profile
    if (CommonMethods().isStaticUser()) {
      return;
    }
    switch (bannerInfo.openingPage) {
      //Profile
      case ("USER"):
        pushScreen(
            screen: UserProfileScreen(
                userReference: bannerInfo.pageReference!,
                fromBottomNavigation: false));
        break;
      //Edit user Profile
      case ("EDIT_USER_PROFILE"):
        var data =
            Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
        pushScreen(
            screen: EditUserProfileScreen(
          userDetail: data.userDetail!,
        ));
        break;
      //Store
      case ("STORE"):
        pushScreen(
            screen: BuyerViewStoreScreen(
                storeReference: bannerInfo.pageReference!));
        break;
      //Edit store profile
      case ("EDIT_STORE_PROFILE"):
        pushScreen(
            screen: SellerStoreProfileScreen(
                storeReference: AppConstants.appData.storeReference!));
        break;
      //Product
      case ("PRODUCT"):
        pushScreen(
            screen: BuyerViewSingleProductScreen(
                productReference: bannerInfo.pageReference!));
        break;
      //Post
      case ("POST"):
        pushScreen(
            screen:
                SinglePostViewScreen(postReference: bannerInfo.pageReference!));
        break;
      //Comment
      case ("COMMENT"):
        pushScreen(
            screen:
                SinglePostViewScreen(postReference: bannerInfo.pageReference!));
        break;
      //Add post
      case ("ADD_POST"):
        pushScreen(screen: const AddPostScreen());
        break;
      //Add product
      case ("ADD_PRODUCT"):
        pushScreen(
            screen: AddProductScreen(
                storeId: AppConstants.appData.storeId!,
                storeReference: AppConstants.appData.storeReference!));
        break;
      //Delivery setting
      case ("DELIVERY_SETTINGS"):
        pushScreen(
            screen: SellerStoreDeliverySettingScreen(
                storeRef: AppConstants.appData.storeReference!,
                isFromAddProduct: false,
                isFromStore: true,
                isFromEditProduct: false));
        break;
      //Trust center
      case ("TRUST_CENTER"):
        pushScreen(
            screen: SellerTrustCenterScreen(
          storeRef: AppConstants.appData.storeReference!,
          isStoreOwnerView: true,
        ));
        break;
      //Id verification
      case ("ID_VERIFICATION"):
        pushScreen(screen: const IdVerificationScreen());
        break;
      //Return settings
      case ("RETURN_SETTINGS"):
        pushScreen(
            screen: SellerReturnStoreWarrantyScreen(
          storeRef: AppConstants.appData.storeReference!,
          fromStoreScreen: true,
        ));
        break;
      //Create store
      case ("CREATE_STORE"):
        createStoreBottomSheet();
        break;
      //Referral
      case ("REFERRAL"):
        goToCommonReferral();
        break;
      //Web
      case ("WEB"):
        CommonMethods.openAppWebView(
            webUrl: bannerInfo.pageUrl!, context: context);
        break;
      //FAQ
      case ("FAQ"):
        goToFaqScreen();
        break;
    }
  }

//endregion

  //region Go To common referral
  void goToCommonReferral() {
    var screen = const CommonReferralPage();
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go To FAQ Screen
  void goToFaqScreen() {
    FaqNavigation.navigateToFaqScreen(context);
  }
  //endregion

  //region Create store dialog
  Future createStoreBottomSheet() {
    return CommonMethods.appMinimumBottomSheets(
      context: context,
      screen: const CreateStoreOptions(),
    );
  }

  //endregion

//region Push screen
  void pushScreen({required Widget screen}) {
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion
}
