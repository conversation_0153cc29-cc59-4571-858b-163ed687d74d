import 'dart:async';
import 'package:flutter/material.dart';
class CompletedOrderBloc {
  // region Common Variables
  BuildContext context;
  late bool viewDetailActiveInActive = false;


  // endregion


  //region Controller
  final viewDetailCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  CompletedOrderBloc(this.context);
  // endregion

  // region Init
  void init() {


  }
// endregion



//region Go Back
  void goBack(){
    Navigator.pop(context);
  }
//endregion


//region On Tap View Detail
void onTapViewDetail(){
  viewDetailActiveInActive = !viewDetailActiveInActive;
  viewDetailCtrl.sink.add(viewDetailActiveInActive);
}
//endregion



}
