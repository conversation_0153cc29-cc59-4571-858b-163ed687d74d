import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/initial_store_and_people_card.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_pagination.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/features/widgets/support_the_swadesic_movement/support_the_swadesic_movement.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

class RecommendedStoreAndUserScreen extends StatefulWidget {
  final bool isRecommendedStore;
  final ScrollController? scrollController;
  const RecommendedStoreAndUserScreen(
      {super.key, required this.isRecommendedStore, this.scrollController});

  @override
  State<RecommendedStoreAndUserScreen> createState() =>
      _RecommendedStoreAndUserScreenState();
}

class _RecommendedStoreAndUserScreenState
    extends State<RecommendedStoreAndUserScreen>
    with AutomaticKeepAliveClientMixin<RecommendedStoreAndUserScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //region Bloc
  late RecommendedStoreAndUserBloc recommendedStoreAndUserBloc;
  //endregion

  //region Init
  @override
  void initState() {
    recommendedStoreAndUserBloc = RecommendedStoreAndUserBloc(
        context,
        widget.isRecommendedStore,
        widget.scrollController ?? ScrollController());
    recommendedStoreAndUserBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    recommendedStoreAndUserBloc.dispose();
    super.dispose();
  }
  //endregion

//   color: AppColors.brandGreen,
//   onRefresh: ()async{
//   await recommendedStoreAndUserBloc.init();
// },
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    // Get reference to the RecommendedStoreAndUserDataModel
    return Consumer<RecommendedStoreAndUserDataModel>(
      builder: (BuildContext context, RecommendedStoreAndUserDataModel data,
          Widget? child) {
        //Recommended list
        List<UserAndStoreInfo> recommendedList = [];
        recommendedList = widget.isRecommendedStore
            ? data.recommendedStoreAndUserList
                .where((element) => element.entityType == EntityType.STORE.name)
                .toList()
            : data.recommendedStoreAndUserList
                .where((element) => element.entityType == EntityType.USER.name)
                .toList();

        //print("Total store is ${recommendedList.length}");
        return StreamBuilder<RecommendedStoreAndUserState>(
            stream:
                recommendedStoreAndUserBloc.recommendedStoreAndUserCtrl.stream,
            initialData: RecommendedStoreAndUserState.Loading,
            builder: (context, snapshot) {
              //If store list is not null
              // if(recommendedList.isNotEmpty){
              //   return RefreshIndicator(
              //     color: AppColors.brandGreen,
              //     onRefresh: ()async{
              //       await recommendedStoreAndUserBloc.getRecommendedStore();
              //     },
              //     child: ListView.builder(
              //       itemCount: recommendedList.length +1,
              //       itemBuilder: (context, index) {
              //         if(index < recommendedList.length){
              //           return InitialStoreAndPeopleCard(
              //             isStore: true, recommendedStoreAndUser: recommendedList[index], onTapFollowSupport: (){
              //             recommendedStoreAndUserBloc.onTapFollowUnFollow(recommendedStoreAndUser: recommendedList[index]);
              //           }, onTapIcon: (){
              //             recommendedStoreAndUserBloc.goToStore(storeReference:recommendedList[index].reference! );
              //           },
              //           );
              //         }
              //         else{
              //
              //         }
              //
              //       },
              //     ),
              //   );
              // }

              //Success
              if (snapshot.data == RecommendedStoreAndUserState.Success) {
                return RefreshIndicator(
                  color: AppColors.brandBlack,
                  onRefresh: () async {
                    await recommendedStoreAndUserBloc.init();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.only(bottom: 100, top : 10),
                    shrinkWrap: true,
                    itemCount: recommendedList.length + 1,
                    controller: recommendedStoreAndUserBloc.scrollController,
                    // shrinkWrap: true,
                    physics: const AlwaysScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      if (index < recommendedList.length) {
                        return Column(
                          children: [
                            InitialStoreAndPeopleCard(
                              isStore: widget.isRecommendedStore,
                              recommendedStoreAndUser: recommendedList[index],
                              onTapFollowSupport: () {
                                recommendedStoreAndUserBloc.onTapFollowAndSupport(
                                    recommendedStoreAndUser:
                                        recommendedList[index]);
                              },
                              onTapIcon: () {
                                recommendedStoreAndUserBloc.onTapIcon(
                                    recommendedStoreAndUser:
                                        recommendedList[index]);
                              },
                              customImageContainerType:
                                  recommendedList[index].entityType ==
                                          EntityType.USER.name
                                      ? CustomImageContainerType.user
                                      : CustomImageContainerType.store,
                            ),
                            const SizedBox(
                              height: 10,
                            )
                          ],
                        );
                      } else {
                        return paginationLoading();
                      }
                    },
                  ),
                );
              }
              //Empty
              if (snapshot.data == RecommendedStoreAndUserState.Empty) {
                return NoResult(message: AppStrings.noResults);
                // return AppCommonWidgets.errorWidget(onTap:(){
                //   recommendedStoreAndUserBloc.init();
                // },errorMessage: AppStrings.noResults);
              }
              //Loading
              if (snapshot.data == RecommendedStoreAndUserState.Loading) {
                return AppCommonWidgets.appCircularProgress();
              }
              //Failed
              if (snapshot.data == RecommendedStoreAndUserState.Failed) {
                return AppCommonWidgets.errorWidget(
                    onTap: () {
                      recommendedStoreAndUserBloc.init();
                    },
                    errorMessage: widget.isRecommendedStore
                        ? AppStrings.unableToFetchRecommendedStore
                        : AppStrings.unableToFetchRecommendedUsers);
              }
              return const SizedBox();
            });
      },
    );
  }
//endregion

//region Pagination loading
//   Widget paginationLoading(){
//
//     return StreamBuilder<RecommendedStorePaginationState>(
//         stream: recommendedStoreAndUserBloc.recommendedStoreAndUserPagination.recommendedStorePaginationStateCtrl.stream,
//         initialData: RecommendedStorePaginationState.Done,
//         builder: (context, snapshot) {
//           //Fetch data instance
//           RecommendedStoreAndUserDataModel recommendedStoreAndUserDataModel = Provider.of<RecommendedStoreAndUserDataModel>(context);
//
//           //Store or user list
//           List<UserAndStoreInfo> storeOrUserList =
//           widget.isRecommendedStore?
//           recommendedStoreAndUserDataModel.recommendedStoreAndUserList.where((element) => element.entityType == EntityType.STORE.name).toList()
//               :
//           recommendedStoreAndUserDataModel.recommendedStoreAndUserList.where((element) => element.entityType == EntityType.USER.name).toList();
//
//           //If feedList is empty then return
//           if(storeOrUserList.isEmpty || storeOrUserList.length<10){
//             return const SupportTheSwadesicMovement();
//           }
//           //End
//           if(snapshot.data == RecommendedStorePaginationState.Done){
//             return const SupportTheSwadesicMovement();
//           }
//           //Loading
//           if(snapshot.data == RecommendedStorePaginationState.Loading){
//             return AppCommonWidgets.appCircularProgress(isPaginationProgress: true);
//           }
//           return const SupportTheSwadesicMovement();
//         }
//     );
//   }
//endregion

//region Pagination loading
  Widget paginationLoading() {
    return StreamBuilder<RecommendedStorePaginationState>(
        stream: recommendedStoreAndUserBloc.recommendedStoreAndUserPagination
            .recommendedStorePaginationStateCtrl.stream,
        initialData: RecommendedStorePaginationState.Loading,
        builder: (context, snapshot) {
          //If  empty then return
          // if (recommendedList.isEmpty || recommendedList.length < 10) {
          //   return const SupportTheSwadesicMovement();
          // }
          //End
          if (snapshot.data == RecommendedStorePaginationState.Done) {
            return const SupportTheSwadesicMovement();
          }
          //Loading
          if (snapshot.data == RecommendedStorePaginationState.Loading) {
            return VisibilityDetector(
                key: UniqueKey(),
                onVisibilityChanged: (visibilityInfo) {
                  var visiblePercentage = visibilityInfo.visibleFraction * 100;
                  if (visiblePercentage == 100) {
                    recommendedStoreAndUserBloc
                        .recommendedStoreAndUserPagination
                        .getPaginationRecommendedStoreAndUser();
                    //Set the current pagination state
                    recommendedStoreAndUserBloc
                            .recommendedStoreAndUserPagination
                            .currentApiCallStatus =
                        RecommendedStorePaginationState.Loading;
                  }
                },
                child: AppCommonWidgets.appCircularProgress(
                    isPaginationProgress: true));
          }
          return const SupportTheSwadesicMovement();
        });
  }
//endregion
}
