import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/user_rewards_and_invitees_response/user_rewards.dart';
import 'package:swadesic/services/user_rewards_and_invitees_service/user_reward_and_invitees_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
enum UserRewardState { Loading, Success, Failed }
class UserRewardBloc {
  // region Common Variables
  BuildContext context;
  late UserRewards userRewards;

  // endregion


  //region Controller
  final userRewardStateCtrl = StreamController<UserRewardState>.broadcast();
  //endregion

  // region | Constructor |
  UserRewardBloc(this.context);
  // endregion

  // region Init
  void init() {
  }
// endregion



//region Dispose
  void dispose(){
    userRewardStateCtrl.close();
  }
//endregion



}
