
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        @page {
            size: A4;
            margin: 0;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20mm;
            color: #333;
            box-sizing: border-box;
            width: 210mm;
            min-height: 297mm;
        }
        .page-break {
            page-break-after: always;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .logo {
            width: 150px;
            height: auto;
        }
        .invoice-title {
            font-size: 18px;
            font-weight: bold;
            text-align: right;
        }
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .info-column {
            width: 48%;
        }
        .info-item {
            margin-bottom: 5px;
        }
        .bold {
            font-weight: bold;
        }
        .address {
            white-space: pre-line;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .totals {
            width: 100%;
            border-collapse: collapse;
        }
        .totals td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .totals .label {
            text-align: left;
        }
        .totals .value {
            text-align: right;
        }
        .signature {
            margin-top: 20px;
            text-align: right;
        }
        .signature-box {
            border: 1px solid #333;
            width: 200px;
            height: 60px;
            margin-left: auto;
            margin-bottom: 5px;
            position: relative;
        }
        .signature-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .footer {
            margin-top: 20px;
            font-size: 12px;
        }
        @media print {
            body {
                width: 210mm;
                height: 297mm;
            }
        }
    </style>
</head>
<body>
<div class="header">
    <img src="https://github.com/Socially-X/public_assets/blob/MASTER/swadesic_logo_and_name.png?raw=true" alt="Swadesic Logo" class="logo">
    <div class="invoice-title">
        Invoice/ Bill of Supply/ Cash Receipt<br>
        (original for recipient)
    </div>
</div>

<div class="info-section">
    <div class="info-column">
        <div class="info-item">Storename on Swadesic: test_foot_ware</div>
        <div class="info-item bold">Supplied By:</div>
        <div class="info-item">Shoe Seller</div>
        <div class="info-item address">B. V Nagar,<br>Nellore,<br>Assam - 524004</div>
        <div class="info-item bold" style="margin-top: 10px;">Sold By:</div>
        <div class="info-item">Shoe Seller</div>
        <div class="info-item address">B. V Nagar,
            Nellore,
            Assam - 524004</div>
        <div class="info-item">PAN No: **********</div>
        <div class="info-item">GST Registration No: 22AAAAA0000A1Z5</div>
    </div>
    <div class="info-column">
        <div class="info-item bold">Billing Address:</div>
        <div class="info-item"></div>
        <div class="info-item address">Siva<br>B.V. Nagar Nellore<br>Nellore, Andhra Pradesh, 524004</div>
        <div class="info-item bold" style="margin-top: 10px;">Shipping Address:</div>
        <div class="info-item"></div>
        <div class="info-item address">Siva<br>B.V. Nagar Nellore<br>Nellore, Andhra Pradesh, 524004<br>7993047548</div>
        <div class="info-item"></div>
    </div>
</div>

<div class="info-section">
    <div class="info-column">
        <div class="info-item">Order No: O2407310115530001</div>
        <div class="info-item">Order Date: 31:07:2024 01:15:53</div>
    </div>
    <div class="info-column">
        <div class="info-item">Invoice No: INV30001</div>
        <div class="info-item">Invoice Details: Invoice for Foot Ware purchased on 31:07:2024 01:15:53</div>
        <div class="info-item">Invoice Date: 31:07:2024 01:15:53</div>
    </div>
</div>

<table>
    <thead>
    <tr>
        <th>SI.no</th>
        <th>Product & Description</th>
        <th>Unit Price</th>
        <th>Quantity</th>
        <th>Total Price</th>
    </tr>
    </thead>
    <tbody>

    <tr>
        <td>1</td>
        <td>Nike Air Jordan</td>
        <td>7500</td>
        <td>1</td>
        <td>7500</td>
    </tr>

    </tbody>
</table>

<table class="totals">
    <tr>
        <td class="label">Subtotal (Inclusive of Taxes)</td>
        <td class="value">7500</td>
    </tr>
    <tr>
        <td class="label">Shipping & Handling</td>
        <td class="value">0</td>
    </tr>
    <tr>
        <td class="label bold">Total Price</td>
        <td class="value bold">7500</td>
    </tr>
    <tr>
        <td colspan="2" class="label">Amount in Words: Seven thousand five hundred Only</td>
    </tr>
</table>

<div class="signature">
    <div class="bold">For Shoe Seller</div>
    <div class="signature-box">
        <img src="https://www.signwell.com/assets/vip-signatures/muhammad-ali-signature-3f9237f6fc48c3a04ba083117948e16ee7968aae521ae4ccebdfb8f22596ad22.svg" alt="Authorized Signature" class="signature-image">
    </div>
    <div>Authorized signatory</div>
</div>

<div class="footer">
    If you have any questions concerning this invoice, , , <EMAIL><br><br>
    <strong>THANK YOU FOR SUPPORTING A SWADESHI SMALL BUSINESS! JAI HIND!! </strong>

</div>
</body>
</html>