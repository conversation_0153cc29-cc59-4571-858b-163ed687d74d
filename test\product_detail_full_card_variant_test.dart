import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';

void main() {
  group('ProductDetailFullCard Variant Tests', () {
    late ProductDetailFullCardBloc bloc;
    late Product productWithVariants;
    late Product productWithoutVariants;

    setUp(() {
      // Create a product with variants
      productWithVariants = Product(
        productName: 'Test Product',
        brandName: 'Test Brand',
        options: {'Size': ['S', 'M', 'L'], 'Color': ['Red', 'Blue']},
        variants: [
          {
            'combinations': {'Size': 'M', 'Color': 'Red'},
            'mrp_price': 2000,
            'selling_price': 1500,
            'stock': 10,
          },
          {
            'combinations': {'Size': 'L', 'Color': 'Blue'},
            'mrp_price': 2200,
            'selling_price': 1700,
            'stock': 5,
          },
          {
            'combinations': {'Size': 'S', 'Color': 'Red'},
            'mrp_price': 1800,
            'selling_price': 1300,
            'stock': 0, // Out of stock
          },
        ],
      );

      // Create a product without variants (no-variant scenario)
      productWithoutVariants = Product(
        productName: 'Simple Product',
        brandName: 'Simple Brand',
        options: {},
        variants: [
          {
            'combinations': {},
            'mrp_price': 1000,
            'selling_price': 800,
            'stock': 20,
          },
        ],
      );
    });

    test('should initialize variants correctly for product with options', () {
      bloc = ProductDetailFullCardBloc(
        BuildContext as BuildContext,
        productWithVariants,
        false,
      );
      bloc.initializeVariants();

      expect(bloc.availableVariants.length, 3);
      expect(bloc.selectedVariant, isNotNull);
      expect(bloc.selectedVariant!.combinations.isNotEmpty, true);
      expect(bloc.hasVariants(), true);
    });

    test('should initialize variants correctly for product without options', () {
      bloc = ProductDetailFullCardBloc(
        BuildContext as BuildContext,
        productWithoutVariants,
        false,
      );
      bloc.initializeVariants();

      expect(bloc.availableVariants.length, 1);
      expect(bloc.selectedVariant, isNotNull);
      expect(bloc.selectedVariant!.combinations.isEmpty, true);
      expect(bloc.hasVariants(), false);
    });

    test('should get correct display pricing from selected variant', () {
      bloc = ProductDetailFullCardBloc(
        BuildContext as BuildContext,
        productWithVariants,
        false,
      );
      bloc.initializeVariants();

      // Should use variant pricing
      expect(bloc.getDisplaySellingPrice(), isA<int>());
      expect(bloc.getDisplayMrpPrice(), isA<int>());
      expect(bloc.getDisplayStock(), isA<int>());
      expect(bloc.getDisplayDiscountPercentage(), isA<double>());
    });

    test('should calculate discount percentage correctly', () {
      bloc = ProductDetailFullCardBloc(
        BuildContext as BuildContext,
        productWithVariants,
        false,
      );
      bloc.initializeVariants();

      // Select a specific variant
      final variant = ProductVariant(
        combinations: {'Size': 'M', 'Color': 'Red'},
        mrpPrice: 2000,
        sellingPrice: 1500,
        stock: 10,
      );
      bloc.selectVariant(variant);

      expect(bloc.getDisplayDiscountPercentage(), 25.0);
    });

    test('should filter variants with combinations correctly', () {
      bloc = ProductDetailFullCardBloc(
        BuildContext as BuildContext,
        productWithVariants,
        false,
      );
      bloc.initializeVariants();

      final variantsWithCombinations = bloc.getVariantsWithCombinations();
      expect(variantsWithCombinations.length, 3);
      
      for (final variant in variantsWithCombinations) {
        expect(variant.combinations.isNotEmpty, true);
      }
    });

    test('should handle variant selection', () {
      bloc = ProductDetailFullCardBloc(
        BuildContext as BuildContext,
        productWithVariants,
        false,
      );
      bloc.initializeVariants();

      final newVariant = ProductVariant(
        combinations: {'Size': 'L', 'Color': 'Blue'},
        mrpPrice: 2200,
        sellingPrice: 1700,
        stock: 5,
      );

      bloc.selectVariant(newVariant);
      expect(bloc.selectedVariant, equals(newVariant));
      expect(bloc.getDisplaySellingPrice(), 1700);
      expect(bloc.getDisplayMrpPrice(), 2200);
      expect(bloc.getDisplayStock(), 5);
    });

    test('should handle products with null variants gracefully', () {
      final productWithNullVariants = Product(
        productName: 'Minimal Product',
        brandName: 'Minimal Brand',
      );

      bloc = ProductDetailFullCardBloc(
        BuildContext as BuildContext,
        productWithNullVariants,
        false,
      );
      bloc.initializeVariants();

      expect(bloc.availableVariants.isEmpty, true);
      expect(bloc.selectedVariant, isNull);
      expect(bloc.hasVariants(), false);
      
      // Should return fallback values
      expect(bloc.getDisplaySellingPrice(), 799);
      expect(bloc.getDisplayMrpPrice(), 999);
      expect(bloc.getDisplayStock(), 10);
    });
  });
}
