import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_completed/hold_refund_bottomsheet/hold_refund_bottomsheet.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ReturnCompletedBloc {
  // region Common Variables
  BuildContext context;
  late BuyerMyOrderServices buyerMyOrderServices;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  final List<SubOrder> subOrderList;
  List<String> groupNameList = [];


  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  final checkBoxCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  final holdingRefundReasonTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  ReturnCompletedBloc(this.context, this.buyerSubOrderBloc, this.order, this.subOrderList);

  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    takeOutDisplayPackageNumbers();
  }

// endregion



  void takeOutDisplayPackageNumbers(){
    groupNameList.clear();
    for(var data in subOrderList){
      groupNameList.add(data.displayPackageNumber!);
    }
    ///Clear duplicates
    groupNameList=groupNameList.toSet().toList();
    //print("Package name length are ${groupNameList.length}");

  }
  //endregion
}
