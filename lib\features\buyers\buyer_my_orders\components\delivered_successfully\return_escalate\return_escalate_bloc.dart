import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/seller_return_warranty_response/seller_return_warranty_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/services/seller_settings_services/seller_return_warranty_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class ReturnEscalateBloc {
  // region Common Variables
  BuildContext context;
  final List<SubOrder> subOrderList;
  final Order order;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  late BuyerMyOrderServices buyerMyOrderServices;
  late SellerReturnWarrantySettingsService returnWarrantyService;

  bool isProductListVisible = true;
  bool isShowRefundClicked = false;
  bool isReturnRequestClicked = false;

  // Map to store return reason text controllers for each suborder
  final Map<String, TextEditingController> returnReasonControllers = {};

  // Maps to store return conditions for each suborder
  final Map<String, List<String>> returnConditionsMap = {};
  final Map<String, List<bool>> returnConditionsCheckedMap = {};

  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller
  // This is kept for backward compatibility but will be deprecated
  final returnReasonTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  ReturnEscalateBloc(this.context, this.subOrderList, this.order, this.buyerSubOrderBloc);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    returnWarrantyService = SellerReturnWarrantySettingsService();

    // Initialize text controllers for each suborder
    for (var subOrder in subOrderList) {
      // Initialize text controller
      returnReasonControllers[subOrder.suborderNumber!] = TextEditingController();

      // Initialize with empty lists for return conditions
      returnConditionsMap[subOrder.suborderNumber!] = [];
      returnConditionsCheckedMap[subOrder.suborderNumber!] = [];
    }

    // Fetch return conditions for each suborder
    fetchReturnConditions();
  }

  // Fetch return conditions for each suborder
  void fetchReturnConditions() async {
    try {
      for (var subOrder in subOrderList) {
        // Get return and warranty info for this product
        if (subOrder.productReference != null) {
          // Get the store reference from the order
          final storeRef = order.storeReference;

          // Use the existing service to get return warranty settings
          final response = await returnWarrantyService.getReturnWarrantyStoreSettings(
            storeRef: storeRef,
            productRef: subOrder.productReference
          );

          if (response.data != null &&
              response.data!.returnConditions != null &&
              response.data!.returnConditions!.isNotEmpty) {

            // Split the pipe-separated conditions
            List<String> conditions = response.data!.returnConditions!.split('|');
            returnConditionsMap[subOrder.suborderNumber!] = conditions;

            // Initialize all conditions as unchecked
            returnConditionsCheckedMap[subOrder.suborderNumber!] = List.filled(conditions.length, false);

            // Refresh UI
            bottomSheetRefresh.sink.add(true);
          }
        }
      }
    } catch (e) {
      // Handle error silently - conditions will remain empty
      // Just log the error without showing to user
      // In a production app, you would use a proper logging framework
      debugPrint("Error fetching return conditions: $e");
    }
  }
// endregion



// region On Tap Speak to seller
  void onTapSpeakToSeller({required SubOrder subOrder}){
    CommonMethods.openBuyerMyOrderDialog(subOrder: subOrder, context: context, heading:"Contact information");
  }
// endregion


  //region On tap product list
  void onTapProductList() {
    isProductListVisible = !isProductListVisible;
    bottomSheetRefresh.sink.add(true);
  }
//endregion

//region Select suborder
  void onSelectSubOrder(SubOrder subOrder) {
    subOrder.isSelected = !subOrder.isSelected;

    // If we're in return request mode, hide it when selection changes
    if (isReturnRequestClicked) {
      isReturnRequestClicked = false;
    }

    bottomSheetRefresh.sink.add(true);
  }

//endregion

  //region On tap show refund
  void onTapShowRefund(){
    //Check is suborder selected
    if(CommonMethods.sellerSelectedSubOrderNumberList(subOrderList).isEmpty){
      return CommonMethods.toastMessage(AppStrings.pleaseSelectOrders, context);
    }
    //Mark cancel button is clicked
    isShowRefundClicked = true;
    //Refresh
    bottomSheetRefresh.sink.add(true);
  }
//endregion

  //region Hide refund
  void hideRefund(){
    //Mark cancel button is false
    isShowRefundClicked = false;
    //Mark reason visible
    isReturnRequestClicked = false;
    //Refresh
    bottomSheetRefresh.sink.add(true);
  }
//endregion


  //region On tap Request return
  void onTapRequestReturn(){
    //If no suborder are selected
    if(CommonMethods.sellerSelectedSubOrderNumberList(subOrderList).isEmpty){
      return CommonMethods.toastMessage(AppStrings.pleaseSelectOrders, context);
    }

    // Clear any existing text in the text controllers for selected suborders
    for (var subOrder in subOrderList.where((element) => element.isSelected)) {
      if (returnReasonControllers.containsKey(subOrder.suborderNumber!)) {
        returnReasonControllers[subOrder.suborderNumber!]!.clear();
      }
    }

    //Mark reason visible
    isReturnRequestClicked = true;

    // Note: We're not changing isShowRefundClicked here, so it maintains its state
    // when switching to return request mode

    //Refresh
    bottomSheetRefresh.sink.add(true);
  }
  //endregion

  //region Update return conditions checked state
  void updateReturnConditionsChecked(String suborderNumber, List<bool> checkedState) {
    returnConditionsCheckedMap[suborderNumber] = checkedState;
    bottomSheetRefresh.sink.add(true);
  }
  //endregion

  //region Return product api call
  returnProductApiCall() async {
    // Store context in a local variable to avoid async gap issues
    final currentContext = context;

    //region Try
    try {
      // If no suborder are selected
      if(CommonMethods.sellerSelectedSubOrderNumberList(subOrderList).isEmpty){
        return CommonMethods.toastMessage(AppStrings.pleaseSelectOrders, currentContext);
      }

      // Get selected suborders
      List<SubOrder> selectedSuborders = subOrderList.where((element) => element.isSelected).toList();

      // Check if any selected suborder has an empty reason
      bool hasEmptyReason = false;
      for (var subOrder in selectedSuborders) {
        final controller = returnReasonControllers[subOrder.suborderNumber!];
        if (controller == null || controller.text.isEmpty) {
          hasEmptyReason = true;
          break;
        }
      }

      if (hasEmptyReason) {
        return CommonMethods.toastMessage(AppStrings.returnReasonCanNotBeEmpty, currentContext);
      }

      // Process each selected suborder individually
      for (var subOrder in selectedSuborders) {
        final reason = returnReasonControllers[subOrder.suborderNumber!]!.text;

        // Create return conditions JSON if conditions exist for this suborder
        Map<String, dynamic>? returnConditionsJson;
        if (returnConditionsMap.containsKey(subOrder.suborderNumber!) &&
            returnConditionsMap[subOrder.suborderNumber!]!.isNotEmpty &&
            returnConditionsCheckedMap.containsKey(subOrder.suborderNumber!)) {

          List<Map<String, dynamic>> conditionsList = [];
          final conditions = returnConditionsMap[subOrder.suborderNumber!]!;
          final checkedStates = returnConditionsCheckedMap[subOrder.suborderNumber!]!;

          for (int i = 0; i < conditions.length; i++) {
            conditionsList.add({
              "condition": conditions[i],
              "checked": checkedStates[i]
            });
          }

          returnConditionsJson = {"conditions": conditionsList};
        }

        await buyerMyOrderServices.returnOrder(
          subOrderNumberList: [subOrder.suborderNumber!],
          reason: reason,
          returnConditionsJson: returnConditionsJson != null ? jsonEncode(returnConditionsJson) : null
        );
      }

      // Close bottom sheet if context is still valid
      if (currentContext.mounted) {
        Navigator.pop(currentContext);

        // Show success message
        CommonMethods.toastMessage(
          "${selectedSuborders.length == 1 ? '${selectedSuborders.length} ${AppStrings.order}' : '${selectedSuborders.length} ${AppStrings.orders}'} ${AppStrings.returned}",
          currentContext
        );
      }

      // Get sub orders
      buyerSubOrderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage {
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      }
      return;
    } catch (error) {
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      }
      return;
    }
  }
//endregion





}





