import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/store_external_reviews/store_external_review_request_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

enum StoreExternalReviewState { loading, success, failed }

class StoreExternalReviewRequestScreen extends StatefulWidget {
  final String token;
  final String storeReference;
  final String userReference;

  const StoreExternalReviewRequestScreen({
    Key? key,
    required this.token,
    required this.storeReference,
    required this.userReference,
  }) : super(key: key);

  @override
  StoreExternalReviewRequestScreenState createState() =>
      StoreExternalReviewRequestScreenState();
}

class StoreExternalReviewRequestScreenState
    extends State<StoreExternalReviewRequestScreen> {
  late StoreExternalReviewRequestBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = StoreExternalReviewRequestBloc(
        context, widget.token, widget.storeReference, widget.userReference);
  }

  @override
  void dispose() {
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.appWhite,
        elevation: 0,
        leading: IconButton(
          icon: SvgPicture.asset(AppImages.backButton),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          "You're Invited to Review",
          style: AppTextStyle.heading1Medium(textColor: AppColors.appBlack),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert, color: AppColors.appBlack),
            onPressed: () {},
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return StreamBuilder<StoreExternalReviewState>(
      stream: bloc.stateCtrl.stream,
      initialData: StoreExternalReviewState.loading,
      builder: (context, snapshot) {
        if (snapshot.data == StoreExternalReviewState.loading) {
          return Center(child: AppCommonWidgets.appCircularProgress());
        }

        if (snapshot.data == StoreExternalReviewState.failed) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: AppColors.red),
                const SizedBox(height: 16),
                Text(
                  'Failed to load store details',
                  style: AppTextStyle.heading2Medium(
                      textColor: AppColors.appBlack),
                ),
                const SizedBox(height: 8),
                Text(
                  'Please try again later',
                  style: AppTextStyle.contentText0(
                      textColor: AppColors.writingBlack1),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStoreInvitation(),
                const SizedBox(height: 24),
                Align(
                  alignment: Alignment.center,
                  child: _buildStorePreview(),
                ),
                const SizedBox(height: 24),
                _buildReviewSection(),
                const SizedBox(height: 24),
                bloc.isStaticUser ? _buildSignInButton() : _buildSubmitButton(),
                const SizedBox(height: 16),
                _buildFooterText(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStoreInvitation() {
    return Text(
      '@${bloc.store.storehandle ?? 'storehandle'} has invited you to review their store based on your experience.',
      style: AppTextStyle.smallText(textColor: AppColors.appBlack),
    );
  }

  Widget _buildStorePreview() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageContainer(
              width: 170,
              height: 170,
              imageUrl: bloc.store.icon != null && bloc.store.icon!.isNotEmpty
                  ? bloc.store.icon!
                  : null,
              imageType: CustomImageContainerType.store,
            ),
            const SizedBox(height: 12),
            Text(
              'Store review for @${bloc.store.storehandle ?? 'storehandle'}',
              style: AppTextStyle.access1(
                textColor: AppColors.appBlack,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: bloc.reviewController,
          decoration: InputDecoration(
            hintText: 'Write your review...',
            hintStyle: AppTextStyle.smallText(textColor: AppColors.appBlack),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.lightGray),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
          style: AppTextStyle.smallText(textColor: AppColors.appBlack),
          maxLines: 5,
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            RatingBar.builder(
              initialRating: bloc.rating,
              minRating: 1,
              direction: Axis.horizontal,
              allowHalfRating: false,
              itemCount: 5,
              itemSize: 32,
              itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
              itemBuilder: (context, _) => const Icon(
                Icons.star,
                color: Colors.amber,
              ),
              onRatingUpdate: bloc.onRatingChanged,
            ),
            TextButton(
              onPressed: bloc.onTapAddImages,
              child: Text(
                'add images',
                style: AppTextStyle.smallText(
                  textColor: AppColors.appBlack,
                  isUnderline: true,
                ),
              ),
            ),
          ],
        ),
        if (bloc.selectedImages.isNotEmpty) ...[
          const SizedBox(height: 16),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: bloc.selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    image: DecorationImage(
                      image: FileImage(File(bloc.selectedImages[index])),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.topRight,
                    children: [
                      GestureDetector(
                        onTap: () => bloc.onTapRemoveImage(index),
                        child: Container(
                          margin: const EdgeInsets.all(4),
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 16,
                            color: AppColors.appBlack,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: bloc.onTapSubmitReview,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Submit Store Review',
          style: AppTextStyle.heading3Medium(
            textColor: AppColors.appWhite,
          ),
        ),
      ),
    );
  }

  Widget _buildSignInButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: bloc.onTapSignIn,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Sign in to Add Store Review',
          style: AppTextStyle.heading3Medium(
            textColor: AppColors.appWhite,
          ),
        ),
      ),
    );
  }

  Widget _buildFooterText() {
    return Text(
      'Your review helps other customers discover great Swadeshi businesses and supports local entrepreneurs.',
      style: AppTextStyle.smallText(textColor: AppColors.appBlack),
      textAlign: TextAlign.center,
    );
  }
}
