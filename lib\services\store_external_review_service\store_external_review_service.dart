import 'dart:io';
import 'package:dio/dio.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';

class StoreExternalReviewService {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  StoreExternalReviewService() {
    httpService = HttpService();
  }

  // endregion

  // region Check if Store External Review exists
  Future<bool> checkStoreExternalReviewExists({
    required String storeReference,
    required String? userIdentifier,
  }) async {
    if (userIdentifier == null || userIdentifier.isEmpty) {
      return false;
    }

    try {
      // Create URL for the POST request
      String url = "${AppConstants.baseUrl}/content/check_external_store_review_request_exists/";

      // Create request body
      final body = {
        "store_reference": storeReference,
        "user_identifier": userIdentifier,
      };

      // Make the API call with the body
      Map<String, dynamic> response = await httpService.postApiCall(body, url);

      // Return true if the review exists, false otherwise
      return response['success'] == true;
    } catch (e) {
      // If there's an error, assume the review doesn't exist
      // In a production environment, this should be logged properly
      // For now, we'll just return false to allow the flow to continue
      return false;
    }
  }
  // endregion

  // region Create Store External Review Request
  Future<Map<String, dynamic>> createStoreExternalReviewRequest({
    required String storeReference,
    required String? userIdentifier,
  }) async {
    // get body [for POST request]
    var body = {
      "store_reference": storeReference,
    };

    // Add user identifier if provided
    if (userIdentifier != null && userIdentifier.isNotEmpty) {
      body["user_identifier"] = userIdentifier;
    }

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.postApiCall(
      body,
      "${AppConstants.baseUrl}/content/create_external_store_review_request/",
    );

    return response;
  }
  // endregion

  // region Add Store External Review
  Future<Map<String, dynamic>> addStoreExternalReview({
    required String storeReference,
    required String commentText,
    required int ratingCount,
    required String token,
    required String entityReference,
    List<File>? images,
  }) async {
    // If no images, use the regular postApiCall method
    if (images == null || images.isEmpty) {
      // Create the request body
      var body = {
        'parent_reference': storeReference,
        'comment_text': commentText,
        'comment_type': CommentEnums.EXTERNAL_REVIEW.name,
        'entity_reference': entityReference,
        'rating_count': ratingCount.toString(),
        'external_review_token': token,
      };

      // Use the HTTP service for regular API calls
      return await httpService.postApiCall(
        body,
        "${AppConstants.baseUrl}/graphdb/add_comment/",
      );
    } else {
      // For file uploads, use the UploadFileService which is the standard way in the app
      try {
        // Create an instance of UploadFileService
        var uploadService = UploadFileService();

        // Create a FormData object
        var formData = FormData();

        // Add files to form data
        for (var i = 0; i < images.length; i++) {
          String fileName = images[i].path.split('/').last;
          formData.files.add(
            MapEntry(
              'files',
              await MultipartFile.fromFile(images[i].path, filename: fileName),
            ),
          );
        }

        // Add required fields to the form
        formData.fields.addAll([
          MapEntry('parent_reference', storeReference),
          MapEntry('comment_text', commentText),
          MapEntry('comment_type', CommentEnums.EXTERNAL_REVIEW.name),
          MapEntry('entity_reference', entityReference),
          MapEntry('rating_count', ratingCount.toString()),
          MapEntry('external_review_token', token),
        ]);

        // Set the URL
        var url = "${AppConstants.baseUrl}/graphdb/add_comment/";

        // Get headers
        var headers = uploadService.formHeader();

        // Send the request using Dio
        var response = await uploadService.dio.post(
          url,
          data: formData,
          options: Options(headers: headers),
        );

        // Return the response data
        return response.data;
      } catch (e) {
        throw ApiErrorResponseMessage(message: AppStrings.commonErrorMessage);
      }
    }
  }
  // endregion
}
