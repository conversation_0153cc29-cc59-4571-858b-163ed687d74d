import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_preview_store/create_preview_store_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_preview_store/preview_store_celebration_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class PreviewStoreLoadingScreen extends StatefulWidget {
  final String previewStoreName;
  final String previewStoreHandle;
  final bool isImageSelected;
  final String? imagePath; // For mobile
  final Uint8List? webImageBytes; // For web
  final CreatePreviewStoreBloc bloc;
  
  const PreviewStoreLoadingScreen({
    Key? key,
    required this.previewStoreName,
    required this.previewStoreHandle,
    required this.isImageSelected,
    this.imagePath,
    this.webImageBytes,
    required this.bloc,
  }) : super(key: key);

  @override
  State<PreviewStoreLoadingScreen> createState() => _PreviewStoreLoadingScreenState();
}

class _PreviewStoreLoadingScreenState extends State<PreviewStoreLoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _loadingController;
  late Animation<double> _fadeAnimation;
  bool _showSuccess = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller
    _loadingController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    // Create fade animation
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingController,
      curve: Curves.easeIn,
    ));

    // Start loading animation
    _loadingController.forward();

    // Listen to bloc state changes
    _listenToBloc();
    
    // Start the creation process
    _startCreation();
  }

  void _listenToBloc() {
    widget.bloc.stateCtrl.stream.listen((state) {
      if (mounted) {
        if (state == PreviewStoreCreationState.Success) {
          _showSuccessAndNavigate();
        } else if (state == PreviewStoreCreationState.Error) {
          _showErrorAndGoBack();
        }
      }
    });
  }

  void _showErrorAndGoBack() async {
    // Show error message
    CommonMethods.toastMessage("Failed to create preview store. Please check your internet connection and try again.", context);

    // Wait a bit before going back
    await Future.delayed(const Duration(milliseconds: 1500));

    if (mounted) {
      Navigator.pop(context);
    }
  }

  void _startCreation() async {
    // Add a small delay to show the loading screen
    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      widget.bloc.createPreviewStoreInternal();
    }
  }

  void _showSuccessAndNavigate() async {
    setState(() {
      _showSuccess = true;
    });
    
    // Show success animation for 2 seconds
    await Future.delayed(const Duration(seconds: 2));
    
    if (mounted) {
      // Navigate to celebration screen
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => PreviewStoreCelebrationScreen(
            previewStoreName: widget.previewStoreName,
            previewStoreHandle: widget.previewStoreHandle,
            isImageSelected: widget.isImageSelected,
            imagePath: widget.imagePath,
            webImageBytes: widget.webImageBytes,
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _loadingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return Opacity(
              opacity: _fadeAnimation.value,
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Store icon
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                            CommonMethods().getBorderRadius(
                              height: 120,
                              imageType: CustomImageContainerType.store,
                            ),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.appBlack.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(
                            CommonMethods().getBorderRadius(
                              height: 120,
                              imageType: CustomImageContainerType.store,
                            ),
                          ),
                          child: widget.isImageSelected
                              ? kIsWeb && widget.webImageBytes != null
                                  // Web platform with image selected
                                  ? Image.memory(
                                      widget.webImageBytes!,
                                      fit: BoxFit.cover,
                                      width: 120,
                                      height: 120,
                                    )
                                  // Mobile platform with image selected
                                  : widget.imagePath != null
                                      ? Image.file(
                                          File(widget.imagePath!),
                                          fit: BoxFit.cover,
                                          width: 120,
                                          height: 120,
                                        )
                                      : const CustomImageContainer(
                                          width: 120,
                                          height: 120,
                                          imageUrl: AppImages.storePlaceHolder,
                                          imageType: CustomImageContainerType.store,
                                        )
                              // No image selected
                              : const CustomImageContainer(
                                  width: 120,
                                  height: 120,
                                  imageUrl: AppImages.storePlaceHolder,
                                  imageType: CustomImageContainerType.store,
                                ),
                        ),
                      ),
                      
                      const SizedBox(height: 40),
                      
                      // Store name
                      Text(
                        widget.previewStoreName,
                        style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Store handle
                      Text(
                        "@${widget.previewStoreHandle}",
                        style: AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 60),
                      
                      // Loading or success animation
                      if (!_showSuccess) ...[
                        AppCommonWidgets.appCircularProgress(),
                        const SizedBox(height: 20),
                        Text(
                          "Creating your preview store...",
                          style: AppTextStyle.contentText0(textColor: AppColors.writingColor2),
                          textAlign: TextAlign.center,
                        ),
                      ] else ...[
                        // Success animation
                        Lottie.asset(
                          AppImages.celebration,
                          width: 100,
                          height: 100,
                          repeat: false,
                        ),
                        const SizedBox(height: 20),
                        Text(
                          "Preview store created successfully! 🎉",
                          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
