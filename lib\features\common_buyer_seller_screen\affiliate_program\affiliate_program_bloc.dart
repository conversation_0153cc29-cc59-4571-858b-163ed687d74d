import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/welcome_to/welcome_to_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/payout_eligibility.dart';
import 'package:swadesic/features/seller/seller_store/seller_account_balance_and_rewads/account_balance/bank_account/send_to_bank/send_to_bank_screen.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/features/widgets/transaction_card/transaction_detail_card/transaction_detail_card.dart';
import 'package:swadesic/model/affiliate_program_response/affiliate_program_detail.dart';
import 'package:swadesic/model/affiliate_program_response/affiliate_program_response.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/app_transaction/app_transaction.dart';
import 'package:swadesic/services/affiliated_program_services/affiliated_program_service.dart';
import 'package:swadesic/services/user_rewards_and_invitees_service/user_reward_and_invitees_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum AffiliateProgramState {
  Loading,
  Success,
  Failed
}

class AffiliateProgramBloc{

  //region Common variable
  BuildContext context;
  late AffiliateProgramResponse affiliateProgramResponse;
  AffiliateProgramDetail affiliateProgramDetail = AffiliateProgramDetail();
  String inviteCode = "";
  //endregion


  //region Stream controller
  final affiliateProgramStateCtrl = StreamController<AffiliateProgramState>.broadcast();
  //endregion


//region Constructor
  AffiliateProgramBloc(this.context);
//endregion


//region Init
void init(){
  getInviteCodeAndTransaction();
}
//endregion




  //region Get invitee code and transaction
  Future<void> getInviteCodeAndTransaction() async {
    //region Try
    try {
      //Api call
      //Get referral code
      inviteCode = await UserRewardAndInviteesService().getInviteCode();

      //Get invitee counts
      affiliateProgramResponse = await AffiliatedProgramService().getAffiliatedTransactions();


      //Success
      affiliateProgramStateCtrl.sink.add(AffiliateProgramState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted ? CommonMethods.toastMessage(error.message!, context) : null;
      //Failed
      affiliateProgramStateCtrl.sink.add(AffiliateProgramState.Failed);
    } catch (error) {
      //Failed
      affiliateProgramStateCtrl.sink.add(AffiliateProgramState.Failed);
    }
  }
//endregion



  //region Welcome dialog
  Future<void> openPayOutEligibilityDialog()async{
    await Future.delayed( Duration.zero);
    return  showDialog(

      context: context,

      builder: (_) => AppAnimatedDialog(child: PayOutEligibility()),
    );
  }
//endregion



  //region On tap order
  void onTapOrder({required AppTransaction appTransaction}) {
    CommonMethods.appBottomSheet(
      bottomSheetName: AppStrings.status,

      screen:TransactionDetailCard(

        appTransaction: appTransaction,

      ),
      context: context,
    ).then((value) {

    });
  }
//endregion


  //region Go to bank account
  void goToBankAccount(){
    var screen = SendToBankScreen(withDrawLimit:"0",isFromAffiliatedProgram: true,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion


}