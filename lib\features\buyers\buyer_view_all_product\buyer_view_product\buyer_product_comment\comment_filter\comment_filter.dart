import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/comment_filter/comment_filter_bloc.dart';
import 'package:swadesic/model/comment/comment_filter_model/comment_filter_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Comment filter
class CommentFilter extends StatefulWidget {
  final CommentFilterModel commentFilterModel;
  final BuyerProductCommentBloc buyerProductCommentBloc;

  const CommentFilter({Key? key, required this.commentFilterModel, required this.buyerProductCommentBloc}) : super(key: key);

  @override
  State<CommentFilter> createState() => _CommentFilterState();
}
// endregion

class _CommentFilterState extends State<CommentFilter> {
  //region Bloc
  late CommentFilterBloc commentFilterBloc;

  //endregion

  //region Init
  @override
  void initState() {
    commentFilterBloc = CommentFilterBloc(context,widget.commentFilterModel,widget.buyerProductCommentBloc);
    commentFilterBloc.init();
    super.initState();
  }

  //endregion


  //region Dispose
  @override
  void dispose() {
    commentFilterBloc.dispose();
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return filters();
  }
  // endregion

//region Filters
Widget filters(){
    return Column(
      children: [
        ///Title
        Padding(
          padding: const EdgeInsets.only(top: 20),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child: InkWell(
                    onTap: () {
                      // appTabBarBloc.onSelectTab(0);
                    },
                    child: Container(
                        padding: const EdgeInsets.all(10),
                        alignment: Alignment.center,
                        child: Text(AppStrings.filter,style: AppTextStyle.pageHeading(textColor: AppColors.writingBlack0),),
                        // child: appText(AppStrings.filter,
                        //     color: AppColors.writingBlack,
                        //     fontWeight: FontWeight.w600,
                        //     fontFamily: AppConstants.rRegular,
                        //     fontSize: 16),
                    ),
                  )),

            ],
          ),
        ),


        divider(),
        verticalSizedBox(10),
        statusType(),


        statusList(),
        verticalSizedBox(20),

        resetApply(onTapReset: (){
          //Close bottomSheet
          Navigator.pop(context);
          commentFilterBloc.buyerProductCommentBloc.resetFilter();
        },
        onTapApply: (){
          //Close bottomSheet
          Navigator.pop(context);
          commentFilterBloc.buyerProductCommentBloc.applyFilter();
        }
        )


      ],
    );
}
//endregion

  //region Status type
  Widget statusType(){
    return StreamBuilder<bool>(
        stream: commentFilterBloc.refreshFilterCtrl.stream,
      builder: (context, snapshot) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              child: Row(mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(AppStrings.statusType,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),

                  RotatedBox(
                      quarterTurns:commentFilterBloc.isExpanded?2:0,
                      child: Container(
                        padding: const EdgeInsets.all(5),
                          height: 25,
                          width: 25,
                          child: CupertinoButton(
                              onPressed: (){
                                commentFilterBloc.onTapDropDown();

                              },
                              padding: EdgeInsets.zero,
                              child: SvgPicture.asset(AppImages.downArrow,color: AppColors.darkStroke,
                              ))))
                ],
              ),
            ),
            divider()
          ],
        );
      }
    );
  }
  //endregion

  //region Status List
  Widget statusList(){
    return  StreamBuilder<bool>(
        stream: commentFilterBloc.refreshFilterCtrl.stream,
        builder: (context, snapshot) {
          return Visibility(
              visible: commentFilterBloc.isExpanded,
              child: Column(
                children: [
                  category(commentFilterBloc.commentFilterModel.comment),
                  category(commentFilterBloc.commentFilterModel.question),
                  category(commentFilterBloc.commentFilterModel.review),

                ],
              ));
        }
    );
  }
  //endregion

  // region Category
  Widget category(StatusType statusType) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 10),
          child: appRadioCheckBox(
              isRadio: false,
              isExpand: true,
              text: statusType.typeName,
              onTap: () {
                commentFilterBloc.onTapStatus(statusType: statusType);

                // supportFilterBloc.category(category: category);
              },
              isActive: statusType.isSelected,
              fontSize: 14,
              checkBoxActiveColor: AppColors.darkGray),
        ),
        divider()
      ],
    );
  }

  // endregion


//region common
Widget commonFilter({required String text}){
    return  Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          appRadioCheckBox(
              isRadio: false,
              isExpand: true,
              text: text,
              onTap: () {
                //supportFilterBloc.feedbackType(feedbackType: feedbackType);
              },
              isActive: true,
              fontSize: 14,
              checkBoxActiveColor: AppColors.darkGray),
          divider()
        ],
      ),
    );
}
//endregion

}
