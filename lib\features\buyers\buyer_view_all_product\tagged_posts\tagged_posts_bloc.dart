import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/edit_post/edit_post_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum TaggedPostsState { Loading, Success, Failed, Empty }

class TaggedPostsBloc {
  //region Common variables
  late BuildContext context;
  final String productReference;
  List<PostDetail> postList = [];
  int offset = 0;
  final int limit = 20;
  late ScrollController scrollController;
  TaggedPostsState currentState = TaggedPostsState.Loading;
  //endregion

  //region Controllers
  final taggedPostsStateCtrl = StreamController<TaggedPostsState>.broadcast();
  //endregion

  //region Constructor
  TaggedPostsBloc(this.context, this.productReference);
  //endregion

  //region Init
  init() async {
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    await getTaggedPosts();
  }
  //endregion

  //region Scroll Listener
  void _scrollListener() {
    if (scrollController.position.pixels == scrollController.position.maxScrollExtent) {
      loadMorePosts();
    }
  }
  //endregion

  //region Get Tagged Posts
  Future<void> getTaggedPosts({bool isClearPostList = true}) async {
    try {
      if (isClearPostList) {
        offset = 0;
        postList.clear();
        currentState = TaggedPostsState.Loading;
        taggedPostsStateCtrl.sink.add(TaggedPostsState.Loading);
      }

      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      // Fetch tagged posts data
      List<PostDetail> taggedPosts = await PostService().getTaggedPostsToContent(
        contentReference: productReference,
        limit: limit,
        offset: offset,
      );

      if (taggedPosts.isEmpty && postList.isEmpty) {
        currentState = TaggedPostsState.Empty;
        taggedPostsStateCtrl.sink.add(TaggedPostsState.Empty);
        return;
      }

      // Add posts to the list
      postList.addAll(taggedPosts);
      
      // Add posts to the global post data model
      postDataModel.addPostIntoList(postList: taggedPosts);

      // Update offset for pagination
      offset += taggedPosts.length;

      currentState = TaggedPostsState.Success;
      taggedPostsStateCtrl.sink.add(TaggedPostsState.Success);
    } catch (error) {
      debugPrint("Error fetching tagged posts: $error");
      currentState = TaggedPostsState.Failed;
      taggedPostsStateCtrl.sink.add(TaggedPostsState.Failed);
    }
  }
  //endregion

  //region Load More Posts
  Future<void> loadMorePosts() async {
    if (currentState == TaggedPostsState.Loading) return;

    currentState = TaggedPostsState.Loading;
    taggedPostsStateCtrl.sink.add(TaggedPostsState.Loading);
    await getTaggedPosts(isClearPostList: false);
  }
  //endregion

  //region Refresh Tagged Posts
  Future<void> refreshTaggedPosts() async {
    await getTaggedPosts(isClearPostList: true);
  }
  //endregion

  //region On tap heart
  Future<void> onTapHeart({required PostDetail postDetail}) async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);

      // Update liked count and is liked
      // Toggle the like status
      postDetail.likeStatus = !postDetail.likeStatus!;
      if (postDetail.likeStatus!) {
        postDetail.likeCount = (postDetail.likeCount ?? 0) + 1;
      } else {
        postDetail.likeCount = (postDetail.likeCount ?? 0) - 1;
      }
      // Refresh ui
      postDataModel.updateUi();
      // Api call
      await PostService().likePost(
          postReference: postDetail.postOrCommentReference!,
          likeStatus: postDetail.likeStatus!);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage("Failed to update like", context)
          : null;
      debugPrint("Error updating like: $error");
    }
  }
  //endregion

  //region On tap share
  void onTapShare({required PostDetail postDetail}) {
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: AppLinkCreateService()
            .createPostLink(postReference: postDetail.postOrCommentReference!),
        imageLink: postDetail.images!.isEmpty
            ? null
            : postDetail.images!.first.mediaPath,
        imageType: CustomImageContainerType.post,
        entityType: EntityType.POST,
        postText: postDetail.text,
        postCreatorName: postDetail.createdBy?.handle,
        postCreatorIcon: postDetail.createdBy?.icon,
        objectReference: postDetail.postOrCommentReference,
      ),
      context: context,
    );
  }
  //endregion

  //region On tap user or store icon
  void onTapUserOrStoreIcon({required String reference}) {
    late Widget screen;

    //Admin user view
    if (reference == AppConstants.appData.userReference && AppConstants.appData.isUserView!) {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }
    //Admin store view
    else if (reference == AppConstants.appData.storeReference && AppConstants.appData.isStoreView!) {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
        isStoreOwnerView: true,
      );
    }
    //If normal store
    else if (reference.split("").first == "S") {
      screen = BuyerViewStoreScreen(
        storeReference: reference,
      );
    }
    //Else normal user view
    else {
      screen = UserProfileScreen(
        userReference: reference,
      );
    }

    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to single post view
  void goToSinglePostView({required String postReference}) {
    var screen = SinglePostViewScreen(postReference: postReference);
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to edit post
  void goToEditPost({required PostDetail postDetail}) {
    Widget screen = EditPostScreen(postDetail: postDetail);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion

  //region On tap drawer
  void onTapDrawer({required PostDetail postDetail}) async {
    // Implementation for drawer tap - similar to other post screens
    // This would show options like edit, delete, etc.
  }
  //endregion

  //region Confirm delete
  void confirmDelete({required PostDetail postDetail}) {
    // Implementation for delete confirmation
    // This would show a confirmation dialog and handle post deletion
  }
  //endregion

  //region Dispose
  void dispose() {
    taggedPostsStateCtrl.close();
    scrollController.dispose();
  }
  //endregion
}
