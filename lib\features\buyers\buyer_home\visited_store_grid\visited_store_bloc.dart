import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/services/store_visit_services/store_visit_services.dart';

enum VisitedStoreState { Loading, Success, Failed, Empty, SearchEmpy }


class VisitedStoreBloc {
  // region Common Variables
  BuildContext context;
  int itemCount  = 0;
  bool isDropDown = false;
  
  ///Store list response and service
  late StoreListResponse storeListResponse = StoreListResponse();
  late StoreVisitService storeVisitService;
  List<StoreInfo> storeList = [];


  // endregion


  //region Controller

  final visitedStoreCtrl = StreamController<VisitedStoreState>.broadcast();

  //endregion

  //region Text Controller
  TextEditingController searchTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  VisitedStoreBloc(this.context);
  // endregion

  // region Init
  void init() {
    ///Inetilization
    storeVisitService = StoreVisitService();
    getRecentlyVisitedStores();
    // calculateItemCount();

  }
// endregion
  //region Get recently visited stores Api Call
  void getRecentlyVisitedStores() async {
    try {
      // visitedStoreCtrl.sink.add(VisitedStoreState.Loading);

      storeListResponse = await storeVisitService.getRecentlyVisitedStores(limit: 11,offset: 0);
      storeList.clear();
      storeList.addAll(storeListResponse.storeList!);
      ///Empty
      if(storeList.isEmpty){
        return visitedStoreCtrl.sink.add(VisitedStoreState.Empty);
      }
      ///Make false brop down
      // isDropDown = false;
      // visitedStoreCtrl.sink.add(VisitedStoreState.Success);

      ///Calculate item counts
      calculateItemCount();
      //Todo
      // visitedStoreCtrl.sink.add(VisitedStoreState.Success);
    } on ApiErrorResponseMessage {
      visitedStoreCtrl.sink.add(VisitedStoreState.Failed);
      // CommonMethods.toastMessage(AppStrings.error, AppConstants.userStoreCommonBottomNavigationContext);
      return;
    } catch (error) {
      visitedStoreCtrl.sink.add(VisitedStoreState.Failed);
      // CommonMethods.toastMessage(AppStrings.error, AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
  }
//endregion


  //region OnChange Search Field
  void onChangeSearchField() {
    storeList.clear();
    for (var data in storeListResponse.storeList!) {
      if (data.storeName!.toLowerCase().contains(searchTextCtrl.text.toLowerCase()) ||
          data.storehandle!.toLowerCase().contains(searchTextCtrl.text.toLowerCase())) {
        storeList.add(data);
      }
    }
    if(storeList.isEmpty){
      visitedStoreCtrl.sink.add(VisitedStoreState.SearchEmpy);
      return;
    }
    //Calculate item count
    calculateItemCount();
    //  Refresh screen
    visitedStoreCtrl.sink.add(VisitedStoreState.Success);
  }
  //endregion


  //region Go To Store Screen
  goToBuyerViewStore({required StoreInfo selectedStore}) {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewStores! != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }

    var screen = BuyerViewStoreScreen(storeReference: selectedStore.storeReference!);
    // var screen = BuyerViewStoreScreen(storeReference: "********",);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // screenRefreshCtrl.sink.add(true);
    });
  }
  //endregion


  //region Calculate item count
  void calculateItemCount(){
    //If item count is smaller or equal to 8
    if(storeList.length <= 8){
      itemCount = storeList.length;
    }
    //If item count is grater then 8
    if(storeList.length > 8){
      itemCount = 8;
    }
    if(storeList.isEmpty){
      visitedStoreCtrl.sink.add(VisitedStoreState.SearchEmpy);
      return;

    }
    //Refresh
    visitedStoreCtrl.sink.add(VisitedStoreState.Success);
  }
  //endregion


  //region On Tap drop down
  onTapDropdown(){
    //If list length is smaller or equal to 8 then return
    if(storeList.length<=8){
      return;
    }
    //Make drop down value reverse
    isDropDown = !isDropDown;
    //If drop down is false then call calculateItemCount method or else all item count to length of the list
    if(isDropDown){
      itemCount = storeList.length;
    }
    else{
      isDropDown = false;
      itemCount = 8;
      visitedStoreCtrl.sink.add(VisitedStoreState.Success);

      // calculateItemCount();
    }
    //Refresh
    // visitedStoreCtrl.sink.add(VisitedStoreState.Success);
    visitedStoreCtrl.sink.add(VisitedStoreState.Success);

  }
  //endregion




//region Dispose
  void dispose() {
    imageCache.clear();
    visitedStoreCtrl.close();
  }
//endregion

}
