import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/search_filter_sort/search_filter/search_screen_filter_bloc.dart';
import 'package:swadesic/model/filters/search_screen_filter_model/search_screen_filter_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_drop_down/app_drop_down.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

// region SupportFilter
class SearchScreenFilter extends StatefulWidget {
  final SearchScreenFilterModel searchScreenFilterModel;

  const SearchScreenFilter({Key? key, required this.searchScreenFilterModel}) : super(key: key);

  @override
  State<SearchScreenFilter> createState() => _SearchScreenFilterState();
}
// endregion

class _SearchScreenFilterState extends State<SearchScreenFilter> {
  //region Bloc
  late SearchScreenFilterBloc searchScreenFilterBloc;

  //endregion

  //region Init
  @override
  void initState() {
    searchScreenFilterBloc = SearchScreenFilterBloc(context, widget.searchScreenFilterModel);
    searchScreenFilterBloc.init();
    super.initState();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    searchScreenFilterBloc.dispose();
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return body();
  }

  // endregion

  // region body
  Widget body() {
    return StreamBuilder<bool>(
        stream: searchScreenFilterBloc.filterCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            children: [
              // //Store sales
              // AppDropDown(
              //     dropDownName: "Store sales",
              //     dropDownWidget: Column(
              //       children: [
              //         storeSales(searchScreenFilterBloc.searchScreenFilterModel.withSales),
              //         storeSales(searchScreenFilterBloc.searchScreenFilterModel.withoutSales),
              //
              //       ],
              //     ),
              //     collapsedWidget: verticalSizedBox(10)),
              //Deliverable
              AppDropDown(
                initialExpand: true,
                customDropdownDropDownName: Row(
                  children: [
                    Text("Deliverable to ${searchScreenFilterBloc.searchScreenFilterModel.deliveryPinCode}",
                    style: AppTextStyle.heading1Medium(textColor: AppColors.appBlack),
                    ),

                    horizontalSizedBox(10),
                    InkWell(
                        onTap: (){
                          searchScreenFilterBloc.onTapChange();

                        },
                        child: Text("change",style:  AppTextStyle.heading3Regular(textColor: AppColors.appBlack,isUnderline: true),))
                    
                  ],
                ),
                  dropDownWidget: Column(
                    children: [
                      deliverable(searchScreenFilterBloc.searchScreenFilterModel.yes),
                      deliverable(searchScreenFilterBloc.searchScreenFilterModel.no),

                    ],
                  ),
                  collapsedWidget: verticalSizedBox(10)),
              // AppTabBar(
              //     firstTabData: filter(),
              //     secondTabData: sort(),
              //     firstTabName: AppStrings.filter,
              //     secondTabName: AppStrings.sort),
              // verticalSizedBox(20),
              verticalSizedBox(20),
              resetApply(onTapReset: () {
                searchScreenFilterBloc.resetFilter(searchScreenFilterModel: widget.searchScreenFilterModel);
              }, onTapApply: () {
                searchScreenFilterBloc.onTapApply();
              })
            ],
          );
        });
  }

  // endregion


  // region Store sales
  Widget storeSales(WithSales withSales) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: appRadioCheckBox(
              isRadio: false,
              isExpand: true,
              text: withSales.typeName,
              onTap: () {
                searchScreenFilterBloc.withSale(withSales: withSales);
              },
              isActive: withSales.isSelected,
              fontSize: 14,
              checkBoxActiveColor: AppColors.darkGray),
        ),
        divider()
      ],
    );
  }

  // endregion

  // region Deliverable
  Widget deliverable(Deliverable deliverable) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: appRadioCheckBox(
              isRadio: false,
              isExpand: true,
              text: deliverable.typeName,
              onTap: () {
                searchScreenFilterBloc.deliverable(deliverable: deliverable);
              },
              isActive: deliverable.isSelected,
              fontSize: 14,
              checkBoxActiveColor: AppColors.darkGray),
        ),
        divider()
      ],
    );
  }

// endregion


/// Sort
//region sort
// Widget sort (){
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         ///Date created and Upvotes
//         Container(
//           margin: const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
//           child: Row(
//             mainAxisSize: MainAxisSize.max,
//             mainAxisAlignment: MainAxisAlignment.spaceAround,
//             children: [
//               InkWell(
//                   onTap: () {
//                     widget.supportFilterModel.earlyFirst.isSelected = true;
//                     widget.supportFilterModel.oldFirst.isSelected = false;
//                     //Refresh ui
//                     searchScreenFilterBloc.filterCtrl.sink.add(true);
//
//                     //supportSortBloc.onSelectEarly();
//                   },
//                   child: sortButtons(buttonName:widget.supportFilterModel.earlyFirst.status, isSelected:widget.supportFilterModel.earlyFirst.isSelected)
//               ),
//               title(title:AppStrings.dateCreated),
//               InkWell(
//                   onTap: () {
//                     widget.supportFilterModel.earlyFirst.isSelected = false;
//                     widget.supportFilterModel.oldFirst.isSelected = true;
//                     //Refresh ui
//                     searchScreenFilterBloc.filterCtrl.sink.add(true);
//                     //supportSortBloc.onSelectOld();
//                   },
//                   child: sortButtons(buttonName:widget.supportFilterModel.oldFirst.status, isSelected:widget.supportFilterModel.oldFirst.isSelected)
//               ),
//             ],
//           ),
//         ),
//         divider(),
//
//         ///UpVote
//         Container(
//           margin: const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
//           child: Row(
//             mainAxisSize: MainAxisSize.max,
//             mainAxisAlignment: MainAxisAlignment.spaceAround,
//             children: [
//               InkWell(
//                   onTap: () {
//                     widget.supportFilterModel.isLow.isSelected = true;
//                     widget.supportFilterModel.isHigh.isSelected = false;
//                     //Refresh ui
//                     searchScreenFilterBloc.filterCtrl.sink.add(true);
//                     //supportSortBloc.onSelectLowest();
//                   },
//                   child: sortButtons(buttonName:widget.supportFilterModel.isLow.status, isSelected:widget.supportFilterModel.isLow.isSelected)
//               ),
//               title(title: AppStrings.upVote),
//               InkWell(
//                   onTap: () {
//                     widget.supportFilterModel.isLow.isSelected = false;
//                     widget.supportFilterModel.isHigh.isSelected = true;
//                     //Refresh ui
//                     searchScreenFilterBloc.filterCtrl.sink.add(true);
//                     //supportSortBloc.onSelectHighest();
//                   },
//                   child: sortButtons(buttonName:widget.supportFilterModel.isHigh.status, isSelected:widget.supportFilterModel.isHigh.isSelected)
//               ),
//             ],
//           ),
//         ),
//         divider(),
//       ],
//     );
// }
//endregion


  //region Title
  Widget title({required String title}) {
    return Container(
        alignment: Alignment.center,
        width: 100,
        child: appText(
          title,
          fontFamily: AppConstants.rRegular,
          fontSize: 15,
          fontWeight: FontWeight.w600,
          color: AppColors.writingColor2,
        ));
  }
  //endregion

  //region Sort button
  Widget sortButtons({required String buttonName, required bool isSelected}) {
    return Container(
      alignment: Alignment.center,
      width: 100,
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
          color: isSelected ? AppColors.activeGreen : AppColors.appWhite,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.writingColor3, width: 0.5)),
      child: appText(
        buttonName,
        fontFamily: AppConstants.rRegular,
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: AppColors.writingColor2,
      ),
    );
  }
//endregion
}
