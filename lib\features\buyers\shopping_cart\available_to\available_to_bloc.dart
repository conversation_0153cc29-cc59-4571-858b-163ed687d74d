import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/user_address_services/user_address_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/services/user_profile/user_profile_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';


enum AvailableToState { Loading, Success, Failed, Empty }


class AvailableToBloc {
  // region Common Variables
  BuildContext context;
  static bool addNewAddress = false;
  static bool selectAddress = false;
  List<String> postalCodeFromAddressList = [];
  ///User Address Service
  late UserAddressService userAddressService;
  final ShoppingCartBloc shoppingCartBloc;
  // endregion

  //region Controller
   final availableToCtrl = StreamController<bool>.broadcast();


  //endregion

  //region Text ctrl
  final TextEditingController deliveryPinCodeTextCtrl = TextEditingController();
  final FocusNode numberFocus = FocusNode();
  //endregion


  // region | Constructor |
  AvailableToBloc(this.context, this.shoppingCartBloc);

  // endregion

  // region Init
  init() async {
    userAddressService = UserAddressService();
  }

// endregion


//region On change postal
void onChangePinCode({required String pinCode})async {
    //if pin code is not equal to 6 then return
  if(pinCode.length != 6){
    //Message

    // //Ui refresh
    // availableToCtrl.sink.add(true);
    return;
  }

  //Api call to edit and get cart detail
  await updatePinCodeAndGetCartDetail(pinCode: pinCode);
  //Ui refresh
  availableToCtrl.sink.add(true);
  //Api

  // //If postal field and selected is address postal code are same
  // if(shoppingCartBloc.selectedAddress.pincode==shoppingCartBloc.availableToTextCtrl.text){
  //   addNewAddress = false;
  //   selectAddress = false;
  //   availableToCtrl.sink.add(true);
  //   return;
  // }

  // //If postal code list contains postal field text
  // if(postalCodeFromAddressList.contains(shoppingCartBloc.availableToTextCtrl.text)){
  //   addNewAddress = false;
  //   selectAddress = true;
  //   updatePinCodeAndGetCartDetail();
  //   availableToCtrl.sink.add(true);
  //   return;
  // }
  //
  // //If postal code list does not contains postal field text
  // if(!postalCodeFromAddressList.contains(shoppingCartBloc.availableToTextCtrl.text)){
  //   addNewAddress = true;
  //   selectAddress = false;
  //   availableToCtrl.sink.add(true);
  //   return;
  // }
  //
}
//endregion




  //region Update pin code and get cart detail
  Future<void>updatePinCodeAndGetCartDetail({required String pinCode}) async {
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    //region Try
    try {
      //Update pin code
      BuyerHomeBloc.userDetailsResponse.userDetail!.pincode = await UserProfileService().addUserPinCode(pinCode);
      //Get cart
      await shoppingCartBloc.getCartItems();
      //Update the buy button to refresh in all loaded product
      for(var product in productDataModel.allProducts){
        product.isPinCodeChanged = true;
      }
      //Update ui
      productDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error){
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.unableToFetchShoppingCartInfo, context);
    }
  }
  //endregion






}
