import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:webview_flutter/webview_flutter.dart';

enum AppWebViewState { Loading, Success, Failed }

class AppWebViewBloc {
  // region Common Methods
  BuildContext context;
  String websiteTitle = "";
  final String url;

  // endregion
  //region Controller
  final appWebViewCtrl = StreamController<AppWebViewState>.broadcast();
  late WebViewController webViewController;
  final pageLoadingCtrl = StreamController<int>.broadcast();

  //endregion

  // region | Constructor |
  AppWebViewBloc(this.context, this.url);
  // endregion

  // region Init
  void init(){
    checkUserDevice();
  }
  // endregion

  //region If user is in web then open url in new tab
  void checkUserDevice(){
    if(CommonMethods.isWeb()){
      //Launch url in new tab
      CommonMethods.openUrl(url: url);
      //Close the screen
      Navigator.pop(context);
    }
  }
  //endregion


  //region Load url
  //endregion

//region Dispose
  void dispose(){
    appWebViewCtrl.close();
    // webViewController.clearCache();
  }
//endregion

}
