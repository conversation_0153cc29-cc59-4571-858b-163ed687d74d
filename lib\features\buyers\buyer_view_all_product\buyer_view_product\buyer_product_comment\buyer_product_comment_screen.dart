import 'dart:io';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
// import 'package:hashtagable/widgets/hashtag_text_field.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/comment_card/comment_card.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/comment_common_widgets.dart';
import 'package:swadesic/model/product_comment_response/product_all_comment_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

import '../../../../../util/app_constants.dart';

// region Buyer Product comments
class BuyerProductCommentScreen extends StatefulWidget {
  // final int? productId;
  final String? productRef;
  late String currentUserOrStore;
  final bool isWriteComment;
  final int? selectedCommentId;
  // final int productCreatorId;
  final String storeReference;
  //final ScrollController scrollController;

  BuyerProductCommentScreen(
      {Key? key,
      this.productRef,
      required this.storeReference,
      this.currentUserOrStore = "",
      required this.isWriteComment,
      this.selectedCommentId})
      : super(key: key);

  @override
  _BuyerProductCommentScreenState createState() =>
      _BuyerProductCommentScreenState();
}
// endregion

class _BuyerProductCommentScreenState extends State<BuyerProductCommentScreen> {
  // region Bloc
  late BuyerProductCommentBloc buyerProductCommentBloc;
  late UniqueKey keyTile;
  // endregion

  // region Init
  @override
  void initState() {
    keyTile = UniqueKey();
    //print(widget.productRef);
    buyerProductCommentBloc = BuyerProductCommentBloc(
        context, widget.productRef, widget.storeReference);
    buyerProductCommentBloc.init();
    //Add reference value to store reference or user reference
    widget.currentUserOrStore = (AppConstants.appData.storeReference == null
        ? AppConstants.appData.userReference
        : AppConstants.appData.storeReference!)!;
    //print(widget.currentUserOrStore);
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          ///Reset all state
          buyerProductCommentBloc.resetAllState();

          ///Clear comment field
          buyerProductCommentBloc.commentTextFieldCtrl.clear();

          ///Enable send as options
          buyerProductCommentBloc.sendAs = true;

          ///Close KeyBord
          CommonMethods.closeKeyboard(context);

          ///Disable emoji
          if (buyerProductCommentBloc.emojiVisible) {
            buyerProductCommentBloc.emojiVisibleCtrl.sink.add(false);
            buyerProductCommentBloc.emojiVisible = false;
          }
        },
        child: StreamBuilder<bool>(
            stream: buyerProductCommentBloc.screenRefreshCtrl.stream,
            builder: (context, snapshot) {
              return Scaffold(
                  backgroundColor: AppColors.appWhite,
                  appBar: appBar(),
                  body: SafeArea(child: body()));
            }));
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.comments,
        isCartVisible: false,
        isMembershipVisible: false,
        isFilterVisible: true,
        isDefaultMenuVisible: false,
        onTapFilter: () {
          buyerProductCommentBloc.onTapFilter(
              buyerProductCommentBloc: buyerProductCommentBloc);
        });
  }

  //endregion

  // region Body
  Widget body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      // alignment: Alignment.bottomCenter,
      children: [
        Expanded(child: commentList()),
        //replyBox(),

        commentFieldAndAll(),

        // verticalSizedBox(10),
      ],
    );
  }

// endregion

  //region Comment field and all
  Widget commentFieldAndAll() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        commentTextField(),
        emoji(),
        verticalSizedBox(2),
        sendAs(),
        // verticalSizedBox(10),
        ratting(),
      ],
    );
  }
  //endregion

  //region Comment List
  Widget commentList() {
    return StreamBuilder<ProductCommentState>(
        stream: buyerProductCommentBloc.buyerProductCommentStateCtrl.stream,
        initialData: ProductCommentState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == ProductCommentState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          if (snapshot.data == ProductCommentState.Empty) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  AppStrings.noCommentYet,
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.writingBlack0),
                ),
                verticalSizedBox(3),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      AppStrings.doNotShy,
                      style: AppTextStyle.contentText0(
                          textColor: AppColors.writingBlack1),
                    ),
                    SvgPicture.asset(AppImages.smileEmoji),
                  ],
                ),
              ],
            );
          }
          if (snapshot.data == ProductCommentState.FilterEmpty) {
            return Center(
                child: AppCommonWidgets.emptyResponseText(
                    emptyMessage: AppStrings.noCommentFound));
          }

          if (snapshot.data == ProductCommentState.Success) {
            return CommentList(
              storeReference: widget.storeReference,
              buyerProductCommentBloc: buyerProductCommentBloc,
              currentUserOrStore: widget.currentUserOrStore,
              commentId: widget.selectedCommentId,
            );
          }
          return const Center(
            child: Text(AppStrings.commonErrorMessage),
          );
        });
  }

  //endregion

  ///Not in use
  //region Replay Text
  // Widget replyBox(){
  //   return Container(
  //     padding: const EdgeInsets.symmetric(horizontal: 20),
  //     height: 30,
  //     width: double.infinity,
  //     color: AppColors.lightWhite3,
  //     alignment: Alignment.centerLeft,
  //     child: const Text(
  //       "replying to dileepkumar",
  //           style: TextStyle(
  //             fontFamily: "LatoRegular",
  //             fontWeight: FontWeight.w400,
  //             color: AppColors.appBlack9
  //           ),
  //     ),
  //   );
  // }

  //endregion

  //region Time Helpful and reply
  // Widget timeHelpful(
  //     {required ReplyAndComments replyAndComments}
  //     ) {
  //   // //print(clap);
  //   return SizedBox(
  //     height: 20,
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.center,
  //       crossAxisAlignment: CrossAxisAlignment.center,
  //       mainAxisSize: MainAxisSize.min,
  //       children: [
  //         Row(
  //           mainAxisAlignment: MainAxisAlignment.center,
  //           crossAxisAlignment: CrossAxisAlignment.center,
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             ///Star and rating Number
  //             replyAndComments.review==0.0?const SizedBox():Padding(
  //               padding: const EdgeInsets.only(right: 10),
  //               child: Row(
  //                 mainAxisAlignment: MainAxisAlignment.center,
  //                 crossAxisAlignment: CrossAxisAlignment.center,
  //                 mainAxisSize: MainAxisSize.min,
  //                 children: [
  //                   Text(
  //                     "${replyAndComments.review!.round()}",
  //                     style: TextStyle(
  //                         fontFamily: "LatoSemiBold", fontSize: 12, fontWeight: FontWeight.w600, color: AppColors.writingColor3),
  //                   ),
  //                   horizontalSizedBox(5),
  //                   SvgPicture.asset(AppImages.star,fit: BoxFit.cover,),
  //
  //                  // const Icon(Icons.star,color: AppColors.yellow,size: 20,),
  //                 ],
  //               ),
  //             ),
  //             ///Comment Type Question
  //             replyAndComments.commentType=="question"?Padding(
  //               padding: const EdgeInsets.only(right: 10),
  //               child: SvgPicture.asset(AppImages.commentQuestion),
  //             ):const SizedBox(),
  //             // horizontalSizedBox(12),
  //             //SvgPicture.asset(AppImages.clapFalse,fit: BoxFit.cover,),
  //             Text(
  //               "${CommonMethods.singularPluralText(item: replyAndComments.claps!, singular: "clap", plural: "claps")}",
  //               style: TextStyle(
  //                   fontFamily: "LatoSemiBold", fontSize: 12, fontWeight: FontWeight.w600, color: AppColors.writingColor3),
  //             ),
  //
  //             CupertinoButton(
  //               padding: EdgeInsets.zero,
  //               onPressed: (){
  //                 buyerProductCommentBloc.onTapReplay(commentId:replyAndComments.commentid!,userName:replyAndComments.username!);
  //               },
  //               child: Text(
  //                 AppStrings.reply,
  //                 style: TextStyle(
  //                     fontFamily: "LatoSemiBold", fontSize: 12, fontWeight: FontWeight.w600, color: AppColors.writingColor3),
  //               ),
  //             ),
  //
  //             Text(
  //               replyAndComments.commentedAt??replyAndComments.repliedAt!,
  //               style: const TextStyle(
  //                   fontFamily: "LatoSemiBold", fontSize: 12, fontWeight: FontWeight.w600, color: AppColors.writingColor3),
  //             ),
  //             //horizontalSizedBox(10),
  //             CupertinoButton(
  //               padding: EdgeInsets.zero,
  //                 onPressed: replyAndComments.reference==widget.currentUserOrStore?(){
  //                   replyAndComments.replyid!=null
  //                    ?buyerProductCommentBloc.childEditDeleteComment(childCommentId: replyAndComments.replyid!,childComment: replyAndComments.reply!)
  //                    :buyerProductCommentBloc.parentEditDeleteComment(replyAndComments.commentid!,replyAndComments.comments!,replyAndComments.commentType!);
  //                 }:(){
  //                   buyerProductCommentBloc.reportAndShareComment(parentOrChildCommentId: replyAndComments.replyid??replyAndComments.commentid!);
  //                 },
  //                 child: SvgPicture.asset(AppImages.commentOption,fit: BoxFit.cover)),
  //           ],
  //         ),
  //         Expanded(child: horizontalSizedBox(10)),
  //         BuyerHomeBloc.userDetailsResponse.userDetail!.clapCommentQuestionReview! == "0"?const SizedBox():CupertinoButton(
  //             padding: EdgeInsets.zero,
  //             onPressed: (){
  //               replyAndComments.replyid!=null
  //                   ?buyerProductCommentBloc.onTapChildClap(totalClapped: replyAndComments.claps!,clappedUserList: replyAndComments.clappedUsers!,commentId: replyAndComments.replyid!,currentUserOrStore: widget.currentUserOrStore)
  //                   : buyerProductCommentBloc.onTapParentClap(totalClapped: replyAndComments.claps!,clappedUserList: replyAndComments.clappedUsers!,commentId: replyAndComments.commentid!,currentUserOrStore: widget.currentUserOrStore);
  //             },
  //             child:replyAndComments.clappedUsers!.contains(widget.currentUserOrStore)? SvgPicture.asset(AppImages.clapTrue,color: AppColors.yellow,):SvgPicture.asset(AppImages.clapFalse)),
  //
  //       ],
  //     ),
  //   );
  // }

  //endregion

  //region Emoji
  Widget emoji() {
    return StreamBuilder<bool>(
        stream: buyerProductCommentBloc.emojiVisibleCtrl.stream,
        initialData: buyerProductCommentBloc.emojiVisible,
        builder: (context, snapshot) {
          if (buyerProductCommentBloc.emojiVisible) {
            return SizedBox(
              height: 300,
              child: EmojiPicker(
                onEmojiSelected: (category, emoji) {
                  buyerProductCommentBloc.onSelectEmoji(emoji.emoji);
                  // Do something when emoji is tapped
                },
                onBackspacePressed: () {
                  buyerProductCommentBloc.onTapEmoji();
                },
                config: const Config(
                    columns: 7,
                    verticalSpacing: 0,
                    horizontalSpacing: 0,
                    initCategory: Category.RECENT,
                    bgColor: Color(0xFFF2F2F2),
                    indicatorColor: Colors.blue,
                    iconColor: Colors.grey,
                    iconColorSelected: Colors.blue,
                    //progressIndicatorColor: Colors.blue,
                    backspaceColor: Colors.blue,
                    skinToneDialogBgColor: Colors.white,
                    skinToneIndicatorColor: Colors.grey,
                    enableSkinTones: true,
                    // showRecentsTab: true,
                    recentsLimit: 28,
                    noRecents: Text("No Recents"),
                    // noRecentsText: "No Recents",
                    // noRecentsStyle:
                    //  TextStyle(fontSize: 20, color:AppColors.appBlack26),
                    tabIndicatorAnimDuration: kTabScrollDuration,
                    categoryIcons: CategoryIcons(),
                    buttonMode: ButtonMode.CUPERTINO),
              ),
            );
          }
          return const SizedBox();
        });
  }
  //endregion

  //region Comment TextField
  Widget commentTextField() {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          //height: 43,
          decoration: const BoxDecoration(
            color: AppColors.textFieldFill1,
          ),
          child: Row(
            children: [
              CupertinoButton(
                onPressed: () {
                  buyerProductCommentBloc.onTapEmoji();
                },
                padding: EdgeInsets.zero,
                child: SvgPicture.asset(
                  AppImages.emoji,
                  fit: BoxFit.contain,
                ),
              ),
              Expanded(
                child: TextFormField(
                  minLines: 1,
                  maxLines: 5,
                  onTap: () {
                    buyerProductCommentBloc.emojiVisible = false;
                    buyerProductCommentBloc.emojiVisibleCtrl.sink.add(true);
                  },
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                  // decoratedStyle:const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.brandGreen,fontFamily:AppConstants.rRegular),
                  // basicStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack,fontFamily:AppConstants.rRegular),
                  // decorateAtSign: true,
                  //autofocus: widget.isWriteComment && BuyerHomeBloc.userDetailsResponse.userDetail!.addComment! =="1" ,
                  autofocus: widget.isWriteComment,
                  controller: buyerProductCommentBloc.commentTextFieldCtrl,
                  onChanged: (text) {
                    buyerProductCommentBloc.onChangeCommentField();
                  },

                  textCapitalization: TextCapitalization.sentences,
                  decoration: InputDecoration(
                    // prefixIcon: SvgPicture.asset(AppImages.emoji,fit:BoxFit.contain,),
                    filled: true,

                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 10),

                    fillColor: AppColors.textFieldFill1,

                    isDense: true,

                    hintText: AppStrings.commentHint,
                    hintStyle: AppTextStyle.hintText(
                        textColor: AppColors.writingBlack1),
                    border: InputBorder.none,
                    focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(22),
                        borderSide: BorderSide.none),
                    enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(22),
                        borderSide: BorderSide.none),
                  ),
                ),

                // child: TextFormField(
                //   textCapitalization: TextCapitalization.sentences,
                //   keyboardType: TextInputType.text,
                //   maxLines: 1,
                //   controller: buyerProductCommentBloc.commentTextFieldCtrl,
                //   style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack),
                //   decoration: InputDecoration(
                //     // prefixIcon: SvgPicture.asset(AppImages.emoji,fit:BoxFit.contain,),
                //     filled: true,
                //
                //     contentPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                //
                //     fillColor: AppColors.lightWhite,
                //
                //     isDense: true,
                //
                //     hintText: AppStrings.commentHint,
                //     hintStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: AppColors.appBlack3),
                //     border: InputBorder.none,
                //     // focusedBorder: OutlineInputBorder(
                //     //     borderRadius: BorderRadius.circular(22),
                //     //     borderSide: BorderSide.none
                //     //
                //     // ),
                //     // enabledBorder: OutlineInputBorder(
                //     //
                //     //     borderRadius: BorderRadius.circular(22),
                //     //     borderSide: BorderSide.none
                //     // ),
                //   ),
                //   onChanged: (vale){
                //     buyerProductCommentBloc.onChangeCommentField();
                //   },
                // ),
              ),

              ///Reply Send Button
              StreamBuilder<bool>(
                  stream: buyerProductCommentBloc
                      .replayCommentSendVisibleCtrl.stream,
                  initialData: false,
                  builder: (context, snapshot) {
                    return Visibility(
                      visible: snapshot.data!,
                      child: CupertinoButton(
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            buyerProductCommentBloc.addChildComment();
                          },
                          child: Text(
                            AppStrings.send,
                            style: AppTextStyle.access0(
                                textColor: AppColors.brandBlack),
                          )),
                    );
                  }),

              ///Edit Send button
              StreamBuilder<bool>(
                  stream: buyerProductCommentBloc.editSendVisibleCtrl.stream,
                  initialData: false,
                  builder: (context, snapshot) {
                    return Visibility(
                      visible: snapshot.data!,
                      child: CupertinoButton(
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            buyerProductCommentBloc.parentEdit
                                ? buyerProductCommentBloc.editParentComment(
                                    buyerProductCommentBloc
                                        .parentChildCommentId)
                                : buyerProductCommentBloc.editChildComment(
                                    buyerProductCommentBloc
                                        .parentChildCommentId);
                          },
                          child: Text(
                            AppStrings.send,
                            style: AppTextStyle.access0(
                                textColor: AppColors.brandBlack),
                          )),
                    );
                  }),
              // CupertinoButton(
              //   padding: EdgeInsets.zero,
              //   onPressed: () {
              //     buyerProductCommentBloc.onPostComment();
              //   },
              //   child: Text(
              //     "post",
              //     style: TextStyle(
              //         fontFamily: "LatoBold", fontSize: 14, fontWeight: FontWeight.w700, color: AppColors.brandBlue),
              //   ),
              // )
            ],
          ),
        ),
        Visibility(
            // visible:BuyerHomeBloc.userDetailsResponse.userDetail!.addComment! =="0" ,
            visible: false,
            child: InkWell(
                onTap: () {
                  CommonMethods.toastMessage(AppStrings.noAccess, context);
                },
                child: Container(
                  color: Colors.transparent,
                  height: 50,
                  width: double.infinity,
                )))
      ],
    );
  }
//endregion

  //region View Images
  Widget viewImages(ReplyAndComments comment) {
    return comment.commentReviewImage!.isEmpty
        ? const SizedBox()
        : Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        buyerProductCommentBloc.onTapViewImage(comment);
                      },
                      child: Container(
                        // constraints: BoxConstraints(
                        //   maxHeight: double.infinity,
                        // ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 5),
                        decoration: const BoxDecoration(
                            color: AppColors.textFieldFill1,
                            borderRadius: BorderRadius.all(Radius.circular(5))),
                        child: Row(
                          children: [
                            Text(
                              comment.isExpand ? "Hide image" : "View Image",
                              style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.appBlack,
                                  fontFamily: "LatoRegular"),
                            ),
                            comment.isExpand
                                ? const Icon(
                                    Icons.keyboard_arrow_up_sharp,
                                    color: AppColors.brandBlack,
                                    size: 20,
                                  )
                                : const Icon(
                                    Icons.keyboard_arrow_down_sharp,
                                    color: AppColors.brandBlack,
                                    size: 20,
                                  )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                comment.isExpand
                    ? SizedBox(
                        height: 100,
                        child: ListView.builder(
                            itemCount: comment.commentReviewImage!.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () {
                                  buyerProductCommentBloc
                                      .goToBuyerProductImageScreen(
                                          comment.commentReviewImage!);
                                },
                                child: Container(
                                  margin:
                                      const EdgeInsets.only(right: 10, top: 5),
                                  width: 100,
                                  child: extendedImage(
                                      comment.commentReviewImage![index]
                                          .reviewImage!,
                                      context,
                                      200,
                                      200,
                                      cache: true,
                                      fit: BoxFit.contain),
                                ),
                              );
                            }),
                      )
                    : const SizedBox()
              ],
            ),
          );
  }
  //endregion

  //region Send as
  Widget sendAs() {
    return StreamBuilder<bool>(
        stream: buyerProductCommentBloc.sendAsVisibleCtrl.stream,
        initialData: false,
        builder: (context, snapshot) {
          return Visibility(
            visible: snapshot.data!,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text("Post as",
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack)),
                  horizontalSizedBox(10),

                  ///Comment
                  Expanded(
                    child: CommentCommonWidgets.postAsButton(
                        buttonName: AppStrings.comment,
                        onPress: () {
                          buyerProductCommentBloc
                              .addParentCommentApiCall("comment");
                        }),
                  ),
                  horizontalSizedBox(10),

                  ///Question
                  Expanded(
                    child: CommentCommonWidgets.postAsButton(
                        buttonName: AppStrings.question,
                        onPress: () {
                          buyerProductCommentBloc
                              .addParentCommentApiCall("question");
                        }),
                  ),
                  horizontalSizedBox(10),

                  ///Review
                  Expanded(
                    child: Visibility(
                      visible: AppConstants.appData.storeReference == null,
                      child: CommentCommonWidgets.postAsButton(
                          buttonName: AppStrings.review,
                          isActive: buyerProductCommentBloc.hasReviewAccess,
                          onPress: () {
                            //If no access then show message
                            if (!buyerProductCommentBloc.hasReviewAccess) {
                              return CommonMethods.toastMessage(
                                  AppStrings.youHaveNotPurchaseThis, context);
                            } else {
                              ///Check Comment field empty
                              if (buyerProductCommentBloc
                                  .commentTextFieldCtrl.text
                                  .trim()
                                  .isEmpty) {
                                CommonMethods.toastMessage(
                                    AppStrings.emptyCommentCanNotPosted,
                                    context);
                                return;
                              }
                              buyerProductCommentBloc.onTapReview("review");
                            }
                          },
                          buttonColor:
                              buyerProductCommentBloc.commentType == "review"
                                  ? AppColors.lightGray
                                  : AppColors.appWhite),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }
  //endregion

//region Ratting
  Widget ratting() {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: StreamBuilder<bool>(
          stream: buyerProductCommentBloc.ratingVisibleCtrl.stream,
          initialData: false,
          builder: (context, snapshot) {
            // bool visibleBox = false;
            // if(AppConstants.multipleSelectedImage.isNotEmpty){
            //   visibleBox = true;
            // }
            // if(buyerProductCommentBloc.commentReviewImage!.isNotEmpty){
            //   visibleBox = true;
            //
            // }
            //

            return Visibility(
              visible: snapshot.data!,
              child: Container(
                color: Colors.white,
                height: 200,
                child: ListView(
                  shrinkWrap: true,
                  //  mainAxisAlignment: MainAxisAlignment.center,
                  // crossAxisAlignment: CrossAxisAlignment.center,
                  // mainAxisSize: MainAxisSize.min,
                  children: [
                    ///Ratting star and add image
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 30, vertical: 20),
                      child: FittedBox(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            RatingBar.builder(
                              initialRating: 5,
                              minRating: 1,
                              direction: Axis.horizontal,
                              glowColor: AppColors.yellow,
                              unratedColor: AppColors.lightGray,
                              allowHalfRating: false,
                              itemCount: 5,
                              itemPadding:
                                  const EdgeInsets.symmetric(horizontal: 5.0),
                              itemBuilder: (context, _) => const Icon(
                                Icons.star,
                                color: Colors.amber,
                              ),
                              onRatingUpdate: (rating) {
                                buyerProductCommentBloc.productRating =
                                    rating.round();
                                // //print(buyerProductCommentBloc.productRating);
                              },
                            ),
                            CommentCommonWidgets.addRateButton(
                                buttonName: AppStrings.addImages,
                                onPress: () {
                                  buyerProductCommentBloc
                                      .goToBuyerAddImageScreen();
                                })
                          ],
                        ),
                      ),
                    ),

                    ///Network and local images
                    SizedBox(
                      height:
                          !buyerProductCommentBloc.editReviewCommentHasImage &&
                                  AppConstants.multipleSelectedImage.isEmpty
                              ? 0
                              : 100,
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 15),
                        child: ListView(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          children: <Widget>[
                            ///Network image
                            buyerProductCommentBloc.editReviewCommentHasImage
                                ? ListView.builder(
                                    shrinkWrap: true,
                                    physics: const ScrollPhysics(),
                                    scrollDirection: Axis.horizontal,
                                    itemCount: buyerProductCommentBloc
                                        .commentReviewImage!.length,
                                    itemBuilder: (BuildContext, index) {
                                      return Container(
                                        margin:
                                            const EdgeInsets.only(right: 10),
                                        child: Stack(
                                          children: [
                                            extendedImage(
                                              buyerProductCommentBloc
                                                      .commentReviewImage![
                                                          index]
                                                      .reviewImage ??
                                                  "",
                                              context,
                                              100,
                                              100,
                                              imageWidth: 100,
                                              imageHeight: 100,
                                            ),
                                            // CachedNetworkImage(
                                            //   width: 100,
                                            //   height: 100,
                                            //   imageUrl: buyerProductCommentBloc.commentReviewImage![index].reviewImage!,
                                            //   fit: BoxFit.cover,
                                            //   placeholder: (context, url) => Image.asset(AppImages.noImage,cacheHeight: 200,cacheWidth: 200,),
                                            //   errorWidget: (context, url, error) => Center(child: const Icon(Icons.error)),
                                            //   maxWidthDiskCache:200 ,
                                            //   maxHeightDiskCache:200 ,
                                            //   memCacheHeight: 200,
                                            //   memCacheWidth: 200,
                                            //
                                            // ),
                                            Positioned(
                                                right: 5,
                                                top: 5,
                                                child: InkWell(
                                                    onTap: () {
                                                      buyerProductCommentBloc
                                                          .deleteCommentImage(
                                                              buyerProductCommentBloc
                                                                  .commentReviewImage![
                                                                      index]
                                                                  .reviewImageId!);
                                                    },
                                                    child: const Icon(
                                                      Icons.close,
                                                      color: AppColors.appBlack,
                                                    )))
                                          ],
                                        ),
                                      );
                                    })
                                : const SizedBox(),

                            ///Local Image
                            AppConstants.multipleSelectedImage.isNotEmpty
                                ? ListView.builder(
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    scrollDirection: Axis.horizontal,
                                    itemCount: AppConstants
                                        .multipleSelectedImage.length,
                                    itemBuilder: (BuildContext, index) {
                                      return Container(
                                        margin:
                                            const EdgeInsets.only(right: 10),
                                        child: Stack(
                                          children: [
                                            Image.file(
                                              File(AppConstants
                                                  .multipleSelectedImage[index]
                                                  .path),
                                              cacheHeight: 200,
                                              cacheWidth: 200,
                                              fit: BoxFit.cover,
                                              height: 100,
                                            ),
                                            Positioned(
                                                right: 5,
                                                top: 5,
                                                child: InkWell(
                                                    onTap: () {
                                                      buyerProductCommentBloc
                                                          .removeLocalImage(
                                                              index);
                                                    },
                                                    child: const Icon(
                                                      Icons.close,
                                                      color: AppColors.appBlack,
                                                    )))
                                          ],
                                        ),
                                      );
                                    })
                                : Container(),
                          ],
                        ),
                      ),
                    ),
                    //endregion

                    ///Rate
                    Align(
                      alignment: Alignment.center,
                      child: CommentCommonWidgets.addRateButton(
                          buttonName: AppStrings.rateTheProduct,
                          onPress: () {
                            buyerProductCommentBloc.reviewAndAddRatting();
                          }),
                    )
                  ],
                ),
              ),
            );
          }),
    );
  }
//endregion
}
