import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/how_refund_amount_calculated/how_refund_amount_calculated_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/refund_amount_calculation/refund_amount_calculation.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_drop_down/app_drop_down.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class HowRefundAmountCalculated extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order store;
  const HowRefundAmountCalculated({Key? key, required this.subOrderList, required this.buyerSubOrderBloc, required this.store}) : super(key: key);

  @override
  State<HowRefundAmountCalculated> createState() => _HowRefundAmountCalculatedState();
}


class _HowRefundAmountCalculatedState extends State<HowRefundAmountCalculated> {
  //region Bloc
  late HowRefundAmountCalculatedBloc howRefundAmountCalculatedBloc;
//endregion

//region Init
@override
  void initState() {
  howRefundAmountCalculatedBloc = HowRefundAmountCalculatedBloc(context,widget.buyerSubOrderBloc,widget.store,widget.subOrderList);
    super.initState();
  }
//endregion
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: howRefundAmountCalculatedBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: SingleChildScrollView(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      productDropDown(),

                      AppCommonWidgets.bottomListSpace(context: context)



                    ]
                ),
              )
          );
        }
    );
  }

  //region Cancelling order
  Widget cancellingOrder(){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: appText(AppStrings.cancellingOrder,fontSize:14,fontWeight: FontWeight.w400,color: AppColors.appBlack,maxLine:100,fontFamily:AppConstants.rRegular,textAlign: TextAlign.start ),
    );

  }
//endregion


  //region Product dropdown
  Widget productDropDown(){
    return StreamBuilder<bool>(
        stream: howRefundAmountCalculatedBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return Column(
            children: [
              ///List
              subOrderListDropDown(),
              verticalSizedBox(20),
              //If cancel is clicked then visible this
              ///Refund amount calculation
               RefundAmountCalculation(order: howRefundAmountCalculatedBloc.order,subOrderList: howRefundAmountCalculatedBloc.subOrderList, backgroundColor: AppColors.textFieldFill1,),
            ],
          );
        }
    );
  }
//endregion


  //region Sub order drop down
  Widget subOrderListDropDown(){
  return AppDropDown(dropDownWidget: subOrderList(),initialExpand: true,dropDownName: AppStrings.productsUnderReturn,);
  }
  //endregion

//region Sub orders list
  Widget subOrderList(){
    return Container(
      margin: const EdgeInsets.all(5),
      decoration: BoxDecoration(
          border: Border.all(  color: AppColors.lightGray2,
            width: 1,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(10))
      ),
      child: ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          itemCount: widget.subOrderList.length,
          shrinkWrap: true,
          itemBuilder:(buildContext,index){
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                buyerBottomSheetSubOrderDetails(context: context,subOrder: widget.subOrderList[index],subOrderStatus:howRefundAmountCalculatedBloc.subOrderList[index].suborderStatus!.toLowerCase(),isPriceQuantityVisible: true,showCheckBox: false),
                index==widget.subOrderList.length-1?const SizedBox():divider()
              ],
            );
          }),
    );
  }
//endregion
}
