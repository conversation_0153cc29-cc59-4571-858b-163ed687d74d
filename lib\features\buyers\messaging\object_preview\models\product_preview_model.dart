import 'entity_preview_model.dart';
import 'post_preview_model.dart';

/// Model class for product previews
class ProductPreview {
  final int productId;
  final String productReference;
  final String productName;
  final String brandName;
  final List<PostMedia> productImages;
  final EntityPreview createdBy;

  ProductPreview({
    required this.productId,
    required this.productReference,
    required this.productName,
    required this.brandName,
    required this.productImages,
    required this.createdBy,
  });

  factory ProductPreview.fromJson(Map<String, dynamic> json) {
    // Parse product images
    final List<PostMedia> images = [];
    if (json['productImages'] != null) {
      for (var imageJson in json['productImages']) {
        images.add(PostMedia.fromJson(imageJson));
      }
    }

    // Parse created by entity
    final createdBy = json['createdBy'] != null 
        ? EntityPreview.fromJson(json['createdBy'])
        : EntityPreview(
            reference: '',
            entityType: '',
            handle: '',
            name: '',
          );

    return ProductPreview(
      productId: json['productid'] ?? 0,
      productReference: json['productReference'] ?? '',
      productName: json['productName'] ?? '',
      brandName: json['brandName'] ?? '',
      productImages: images,
      createdBy: createdBy,
    );
  }
}
