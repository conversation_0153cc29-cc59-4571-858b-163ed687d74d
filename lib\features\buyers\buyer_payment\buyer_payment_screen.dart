import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_payment/buyer_payment_bloc.dart';
import 'package:swadesic/features/buyers/buyer_payment/upi_apps/upi_apps.dart';
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_field_style.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/card_expire_format.dart';
import 'package:swadesic/util/card_number_format.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Buyer Payment Screen
class BuyerPaymentScreen extends StatefulWidget {
  final GetCartPriceResponse getCartPriceResponse;
  final String orderNumber;

  const BuyerPaymentScreen(
      {Key? key, required this.getCartPriceResponse, required this.orderNumber})
      : super(key: key);

  @override
  _BuyerPaymentScreenState createState() => _BuyerPaymentScreenState();
}
// endregion

class _BuyerPaymentScreenState extends State<BuyerPaymentScreen> {
  // region Bloc
  late BuyerPaymentBloc buyerPaymentBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerPaymentBloc = BuyerPaymentBloc(
        context, widget.getCartPriceResponse, widget.orderNumber);
    buyerPaymentBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    imageCache.clear();
    buyerPaymentBloc.dispose();
    super.dispose();
  }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        AppConstants.userPersistentTabController.jumpToTab(2);

        AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
        return false;
      },
      child: GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
          buyerPaymentBloc.visibleUpiList = false;
          buyerPaymentBloc.upiCtrl.sink.add(true);
        },
        child: Scaffold(
          backgroundColor: AppColors.appWhite,
          appBar: appBar(),
          //bottomNavigationBar: bottomNavigation(snapshot.data!),
          // body: SafeArea(child: body(snapshot.data!))
          body: SafeArea(child: body()),
        ),
      ),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.paymentOptions,
        isCartVisible: false,
        isMembershipVisible: false);
  }

// endregion

  //region body
  Widget body() {
    return Stack(
      children: [
        ListView(
          padding: const EdgeInsets.only(bottom: 50),
          children: [
            shoppingPaymentHeading(),
            totalAmount(),
            verticalSizedBox(40),
            upiApps(),
            verticalSizedBox(40),
            withUpiId(),
          ],
        ),
      ],
    );
  }
//endregion

  //region Shopping, Payment and Overview
  Widget shoppingPaymentHeading() {
    return Container(
      margin: const EdgeInsets.only(bottom: 11),
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          //Shopping
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                margin: const EdgeInsets.only(right: 5.8),
                height: 20,
                width: 20,
                child: SvgPicture.asset(
                  AppImages.blueTick,
                  color: AppColors.brandBlack,
                  height: 20,
                  width: 20,
                ),
              ),
              Text(
                "cart",
                style: AppTextStyle.contentHeading0(
                    textColor: AppColors.writingBlack1),
              )
            ],
          ),
          horizontalSizedBox(40.5),
          //Payment
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                margin: const EdgeInsets.only(right: 5),
                alignment: Alignment.center,
                padding: const EdgeInsets.all(5),
                decoration: const BoxDecoration(
                    shape: BoxShape.circle, color: AppColors.darkGray),
                child: Text(
                  "2",
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appWhite),
                ),
              ),
              Text(
                "payment",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              )
            ],
          ),
          horizontalSizedBox(40.5),
          //Over view
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                margin: const EdgeInsets.only(right: 5),
                alignment: Alignment.center,
                padding: const EdgeInsets.all(5),
                decoration: const BoxDecoration(
                    shape: BoxShape.circle, color: AppColors.textFieldFill1),
                child: Text(
                  "3",
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.writingBlack1),
                ),
              ),
              Text(
                "status",
                style: AppTextStyle.contentHeading0(
                    textColor: AppColors.writingBlack1),
              )
            ],
          ),
        ],
      ),
    );
  }
  //endregion

  //region Total Amount
  Widget totalAmount() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          alignment: Alignment.centerLeft,
          color: AppColors.lightestGrey2,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
          child: Text(
            AppStrings.amountToBePaid,
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
          ),
        ),
        Container(
          margin: const EdgeInsets.only(top: 13, left: 17, right: 17),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                widget
                    .getCartPriceResponse.cartFees!.last.orderBreakupItemText!,
                style:
                    AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
              ),
              Text(
                  widget.getCartPriceResponse.cartFees!.last
                      .orderBreakupItemValue!
                      .toString(),
                  style: AppTextStyle.settingHeading1(
                      textColor: AppColors.appBlack)),
            ],
          ),
        )
      ],
    );
    // return  ShoppingCartPrice( getCartPriceResponse: widget.getCartPriceResponse,);

    // return StreamBuilder<bool>(
    //   stream: buyerPaymentBloc.totalAmountCtrl.stream,
    //   initialData: false,
    //   builder: (context, snapshot) {
    //     return Container(
    //       alignment: Alignment.center,
    //       padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 30),
    //       color: AppColors.lightWhite3,
    //       child: Column(
    //         mainAxisSize: MainAxisSize.min,
    //         children: [
    //           Visibility(
    //             visible: buyerPaymentBloc.totalAmountIsExpand,
    //             child:   Row(
    //             mainAxisSize: MainAxisSize.max,
    //             mainAxisAlignment: MainAxisAlignment.center,
    //             crossAxisAlignment: CrossAxisAlignment.center,
    //             children: [
    //               const Text(
    //                 "Cart total",
    //                 style:  TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w400, fontSize: 14, color: AppColors.appBlack),
    //               ),
    //               Expanded(child: horizontalSizedBox(10)),
    //               Text(
    //                 // "₹ ${widget.getCartPriceResponse.orderAmount!}",
    //                 "₹ ",
    //                 style: const TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 20, color: AppColors.appBlack),
    //               ),
    //               horizontalSizedBox(40),
    //             ],
    //           ),),
    //           Visibility(
    //             visible: buyerPaymentBloc.totalAmountIsExpand,
    //             child:   Row(
    //               mainAxisSize: MainAxisSize.max,
    //               mainAxisAlignment: MainAxisAlignment.center,
    //               crossAxisAlignment: CrossAxisAlignment.center,
    //               children: [
    //                 const Text(
    //                   "Shipping fee",
    //                   style: TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w400, fontSize: 14, color: AppColors.appBlack),
    //                 ),
    //                 Expanded(child: horizontalSizedBox(10)),
    //                 Text(
    //                   // "₹ ${widget.getCartPriceResponse.orderDeliveryFee!}",
    //                   "₹",
    //                   style: TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 20, color: AppColors.appBlack),
    //                 ),
    //                 horizontalSizedBox(40),
    //
    //               ],
    //             ),),
    //           //Total
    //           Row(
    //             mainAxisSize: MainAxisSize.max,
    //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //             crossAxisAlignment: CrossAxisAlignment.center,
    //             children: [
    //                Text(
    //                 "Total amount",
    //                 style: AppTextStyle.heading1SemiBold(textColor: AppColors.appBlack),
    //               ),
    //               Row(
    //                 mainAxisSize: MainAxisSize.max,
    //                 mainAxisAlignment: MainAxisAlignment.center,
    //                 crossAxisAlignment: CrossAxisAlignment.center,
    //                 children: [
    //                   Text(
    //                     // "₹ ${widget.getCartPriceResponse.grantTotal!}",
    //                     "₹ ",
    //                     style: TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 20, color: AppColors.appBlack),
    //                   ),
    //                   horizontalSizedBox(15),
    //                   InkWell(
    //                     onTap: (){
    //                       buyerPaymentBloc.onTapTotalAmount();
    //                     },
    //                     child: const SizedBox(
    //                       height: 20,
    //                       width: 20,
    //                       child: Icon(
    //                         Icons.keyboard_arrow_down_outlined,
    //                         color: AppColors.darkGray,
    //                       ),
    //                     ),
    //                   )
    //                 ],
    //               ),
    //             ],
    //           ),
    //         ],
    //       ),
    //     );
    //   }
    // );
  }
  //endregion

  //region Upi apps
  Widget upiApps() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              AppStrings.upiApps,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
          UpiApps(
            getCartPriceResponse: buyerPaymentBloc.getCartPriceResponse,
            orderNumber: buyerPaymentBloc.orderNumber,
          ),
        ],
      ),
    );
  }
  //endregion

  //region With Upi Id
  Widget withUpiId() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.withUpiId,
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          verticalSizedBox(10),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                flex: 6,
                child: TextFormField(
                  textInputAction: TextInputAction.done,
                  maxLines: 1,
                  inputFormatters: [
                    FilteringTextInputFormatter.deny(RegExp(r"\s"))
                  ],
                  controller: buyerPaymentBloc.upiUserName,
                  onChanged: (value) {
                    buyerPaymentBloc.upiUserName.text =
                        buyerPaymentBloc.upiUserName.text.toLowerCase();
                    buyerPaymentBloc.upiUserName.selection =
                        TextSelection.fromPosition(TextPosition(
                      offset: buyerPaymentBloc.upiUserName.text.length,
                    ));
                    buyerPaymentBloc.upiCtrl.sink.add(true);
                  },
                  // textCapitalization: TextCapitalization.characters,
                  //maxLength: maxLength,
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                  decoration: InputDecoration(
                      filled: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 11.5),
                      fillColor: AppColors.textFieldFill1,
                      isDense: true,
                      hintText: "username",
                      hintStyle: AppTextStyle.hintText(
                          textColor: AppColors.writingBlack1),
                      focusedBorder: AppTextFieldStyle.filledColored(
                          fieldColors: AppColors.textFieldFill1),
                      enabledBorder: AppTextFieldStyle.filledColored(
                          fieldColors: AppColors.textFieldFill1)),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Text(
                  "@",
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack),
                ),
              ),
              Expanded(
                flex: 5,
                child: StreamBuilder<bool>(
                    stream: buyerPaymentBloc.upiCtrl.stream,
                    builder: (context, snapshot) {
                      return AppCommonWidgets.dropDownOptions(
                          onTap: () {
                            buyerPaymentBloc.onTapSelectVpa();
                          },
                          context: context,
                          hintText: AppStrings.vpa,
                          value: buyerPaymentBloc.selectedUpiHandle);
                    }),
              )
            ],
          ),
          verticalSizedBox(20),

          ///Pay button
          StreamBuilder<bool>(
              stream: buyerPaymentBloc.upiCtrl.stream,
              builder: (context, snapshot) {
                return Visibility(
                  visible: buyerPaymentBloc.upiUserName.text.isEmpty ||
                          buyerPaymentBloc.selectedUpiHandle == null
                      ? false
                      : true,
                  child: InkWell(
                    onTap: () {
                      buyerPaymentBloc.upiValidate();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 13),
                      decoration: const BoxDecoration(
                        color: AppColors.brandBlack,
                        borderRadius: BorderRadius.all(Radius.circular(100)),
                      ),
                      child: Center(
                        child: Text(
                          "PAY: ${widget.getCartPriceResponse.cartFees!.last.orderBreakupItemValue!.toString()}",
                          style: AppTextStyle.button2Bold(
                              textColor: AppColors.appWhite),
                        ),
                      ),
                    ),
                  ),
                );
              }),
        ],
      ),
    );
  }
//endregion

/////// Not in use //////

//region Bottom Sheet
// Widget bottomSheet() {
//   return StreamBuilder<bool>(
//       stream: buyerPaymentBloc.bottomSheetVisible.stream,
//       initialData: false,
//       builder: (context, snapshot) {
//         return Visibility(
//           visible: snapshot.data!,
//           child: Stack(
//             children: [
//               InkWell(
//                 onTap: () {
//                   buyerPaymentBloc.bottomSheetVisible.sink.add(false);
//                 },
//                 child: Container(
//                   height: MediaQuery.of(context).size.height,
//                   width: MediaQuery.of(context).size.width,
//                   color: Colors.transparent,
//                 ),
//               ),
//               DraggableScrollableSheet(
//                 minChildSize: 0.5,
//                 initialChildSize: 0.65,
//                 expand: true,
//                 builder: (BuildContext context, ScrollController scrollController) {
//                   return Container(
//                       //padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
//                       decoration: const BoxDecoration(boxShadow: [
//                         BoxShadow(
//                           offset: Offset(2, 2),
//                           blurRadius: 12,
//                           color: Color.fromRGBO(0, 0, 0, 0.16),
//                         )
//                       ], color: AppColors.appWhite, borderRadius: BorderRadius.vertical(top: Radius.circular(50))),
//                       child: ListView(
//                         // physics: BouncingScrollPhysics(),
//                         shrinkWrap: true,
//                         padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
//                         controller: scrollController,
//                         children: [
//                           const Text(
//                             "Card detaills",
//                             textAlign: TextAlign.center,
//                             style: TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 16, color: AppColors.appBlack),
//                           ),
//                           //Card Number
//                           Padding(
//                             padding: const EdgeInsets.only(top: 30),
//                             child: TextFormField(
//                               // onEditingComplete: (){
//                               //   onEditingComplete();
//                               // },
//                               obscureText: false,
//                               textAlign: TextAlign.left,
//                               inputFormatters: <TextInputFormatter>[
//                                 FilteringTextInputFormatter.allow(RegExp(AppConstants.onlyInt)),
//                                 CardNumberFormatter(),
//                                 LengthLimitingTextInputFormatter(19)
//                               ],
//                               scrollPadding: EdgeInsets.only(bottom: MediaQuery.of(context).size.height / 5),
//
//                               onChanged: (value) {
//                                 buyerPaymentBloc.onChangeCardNumber(value);
//                               },
//                               textInputAction: TextInputAction.done,
//                               keyboardType: TextInputType.number,
//                               controller: buyerPaymentBloc.cardNumberTextCtrl,
//                               maxLines: 1,
//                               //controller: textFieldCtrl,
//                               style: const TextStyle(fontFamily: "LatoRegular", fontSize: 15, fontWeight: FontWeight.w400, color: AppColors.appBlack),
//                               decoration: InputDecoration(
//                                 filled: true,
//                                 contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
//                                 fillColor: AppColors.lightWhite3,
//                                 isDense: true,
//                                 hintText: "Card number",
//                                 hintStyle:
//                                     const TextStyle(fontSize: 14, fontFamily: "LatoSemibold", fontWeight: FontWeight.w400, color: AppColors.writingColor3),
//                                 focusedBorder: OutlineInputBorder(
//                                   borderRadius: BorderRadius.circular(10),
//                                   borderSide: const BorderSide(color: AppColors.lightWhite3, width: 1.5),
//                                 ),
//                                 enabledBorder: OutlineInputBorder(
//                                   borderRadius: BorderRadius.circular(10),
//                                   borderSide: const BorderSide(color: AppColors.lightWhite3, width: 1.5),
//                                 ),
//                               ),
//                             ),
//                             // child: colorFilledTextField(context: context, textFieldCtrl: buyerPaymentBloc.cardNumberTextCtrl, textFieldHint: "Card number", textFieldMaxLine: 1, keyboardType: TextInputType.number, textInputAction: TextInputAction.done,fillColor: AppColors.lightWhite3,hintFontSize: 14,regExp:AppConstants.onlyInt,maxCharacter: 19,styleFontSize: 15,cardNumberFormat: true,),
//                           ),
//                           //Name
//                           Padding(
//                             padding: const EdgeInsets.only(top: 15),
//                             child: colorFilledTextField(
//                                 context: context,
//                                 textFieldCtrl: buyerPaymentBloc.cardHolderName,
//                                 hintText: "Card holder name",
//                                 textFieldMaxLine: 1,
//                                 keyboardType: TextInputType.text,
//                                 textInputAction: TextInputAction.done,
//                                 fillColor: AppColors.lightWhite3,
//                                 hintFontSize: 14,
//                                 maxCharacter: 30,
//                                 fieldTextCapitalization: TextCapitalization.words,
//                                 styleFontSize: 15),
//                           ),
//                           //Exp Date and CVV
//                           Padding(
//                             padding: const EdgeInsets.only(top: 15),
//                             child: Row(
//                               mainAxisSize: MainAxisSize.min,
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               crossAxisAlignment: CrossAxisAlignment.center,
//                               children: [
//                                 // Expanded(
//                                 //     flex: 2,
//                                 //     child: colorFilledTextField(context: context, textFieldCtrl:buyerPaymentBloc.cardExpireCtrl, textFieldHint: "expiry date/validity", textFieldMaxLine: 1, keyboardType: TextInputType.number, textInputAction: TextInputAction.done,fillColor: AppColors.lightWhite3,hintFontSize: 14,cardExpireFormat: true,regExp:AppConstants.onlyInt,maxCharacter: 5,styleFontSize: 15,onChangeText: buyerPaymentBloc.onChangeExpireDate)),
//                                 Expanded(
//                                   flex: 2,
//                                   child: TextFormField(
//                                     // onEditingComplete: (){
//                                     //   onEditingComplete();
//                                     // },
//                                     obscureText: false,
//                                     textAlign: TextAlign.left,
//                                     inputFormatters: <TextInputFormatter>[
//                                       FilteringTextInputFormatter.allow(RegExp(AppConstants.onlyInt)),
//                                       CardExpirationFormatter(),
//                                       LengthLimitingTextInputFormatter(5)
//                                     ],
//                                     scrollPadding: EdgeInsets.only(bottom: MediaQuery.of(context).size.height / 5),
//
//                                     onChanged: (value) {
//                                       buyerPaymentBloc.onChangeExpireDate();
//                                     },
//                                     textInputAction: TextInputAction.done,
//                                     keyboardType: TextInputType.number,
//                                     controller: buyerPaymentBloc.cardExpireCtrl,
//                                     maxLines: 1,
//                                     //controller: textFieldCtrl,
//                                     style: const TextStyle(fontFamily: "LatoRegular", fontSize: 15, fontWeight: FontWeight.w400, color: AppColors.appBlack),
//                                     decoration: InputDecoration(
//                                       filled: true,
//                                       contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
//                                       fillColor: AppColors.lightWhite3,
//                                       isDense: true,
//                                       hintText: "expiry date/validity",
//                                       hintStyle:
//                                           const TextStyle(fontSize: 14, fontFamily: "LatoSemibold", fontWeight: FontWeight.w400, color: AppColors.writingColor3),
//                                       focusedBorder: OutlineInputBorder(
//                                         borderRadius: BorderRadius.circular(10),
//                                         borderSide: const BorderSide(color: AppColors.lightWhite3, width: 1.5),
//                                       ),
//                                       enabledBorder: OutlineInputBorder(
//                                         borderRadius: BorderRadius.circular(10),
//                                         borderSide: const BorderSide(color: AppColors.lightWhite3, width: 1.5),
//                                       ),
//                                     ),
//                                   ),
//                                 ),
//                                 horizontalSizedBox(10),
//                                 Expanded(
//                                     flex: 1,
//                                     child: colorFilledTextField(
//                                         context: context,
//                                         textFieldCtrl: buyerPaymentBloc.cvvTextCtrl,
//                                         hintText: "CVV",
//                                         textFieldMaxLine: 1,
//                                         keyboardType: TextInputType.number,
//                                         textInputAction: TextInputAction.done,
//                                         fillColor: AppColors.lightWhite3,
//                                         hintFontSize: 14,
//                                         maxCharacter: 3,
//                                         regExp: AppConstants.onlyInt,
//                                         isObscureText: true,
//                                         styleFontSize: 15)),
//
//                                 horizontalSizedBox(10),
//
//                                 SvgPicture.asset(AppImages.exclamation)
//                               ],
//                             ),
//                           ),
//                           //Save
//                           Padding(
//                             padding: const EdgeInsets.only(top: 15),
//                             child: Row(
//                               mainAxisSize: MainAxisSize.min,
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               crossAxisAlignment: CrossAxisAlignment.center,
//                               children: [
//                                 Checkbox(value: true, onChanged: (value) {}),
//                                 const Text(
//                                   "save card as per latest RBI guidelines",
//                                   textAlign: TextAlign.left,
//                                   style: TextStyle(fontFamily: "LatoRegular", fontWeight: FontWeight.w400, fontSize: 14, color: AppColors.writingColor2),
//                                 ),
//                                 Expanded(child: verticalSizedBox(10))
//                               ],
//                             ),
//                           ),
//                           //Pay
//                           InkWell(
//                             onTap: () {
//                               buyerPaymentBloc.cardTransaction();
//                             },
//                             child: Container(
//                               margin: const EdgeInsets.symmetric(vertical: 25),
//                               padding: const EdgeInsets.symmetric(vertical: 10),
//                               decoration: BoxDecoration(
//                                 color: AppColors.appWhite,
//                                 borderRadius: const BorderRadius.all(Radius.circular(10)),
//                                 boxShadow: [
//                                   BoxShadow(
//                                     offset: const Offset(0, 1),
//                                     blurRadius: 5,
//                                     color: AppColors.appBlack.withOpacity(0.2),
//                                   ),
//                                 ],
//                               ),
//                               child: const Center(
//                                 child: Text(
//                                   "PAY",
//                                   style: TextStyle(
//                                       letterSpacing: 4,
//                                       fontFamily: "LatoBold",
//                                       fontWeight: FontWeight.w700,
//                                       fontSize: 15,
//                                       color: AppColors.brandGreen),
//                                 ),
//                               ),
//                             ),
//                           )
//                         ],
//                       ));
//                 },
//               ),
//             ],
//           ),
//         );
//       });
// }
//endregion

//region Pay Using
// Widget payUsing() {
//   return  Container(
//     padding: const EdgeInsets.all(10),
//     child: Text(
//       AppStrings.payUsing,
//       style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
//     ),
//   );
// }
//endregion

  //region Credit Cards
  // Widget creditCards() {
  //   //Check Credit card is available in payment List
  //   // widget.paymentOptions.contains("Credit Card")?
  //   return Visibility(
  //     visible: widget.paymentOptions.contains("Credit Card"),
  //     child: Column(
  //       mainAxisSize: MainAxisSize.min,
  //       children: [
  //         Container(
  //           padding: const EdgeInsets.all(10),
  //
  //           child: Row(
  //             mainAxisSize: MainAxisSize.max,
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             crossAxisAlignment: CrossAxisAlignment.center,
  //             children: [
  //                Text(
  //                 AppStrings.creditCard,
  //                 style: AppTextStyle.heading2Medium(textColor: AppColors.writingColor2),
  //               ),
  //               InkWell(
  //                 onTap: () {
  //                   buyerPaymentBloc.bottomSheetVisible.sink.add(true);
  //                 },
  //                 child:  Text(
  //                   AppStrings.addManageCards,
  //                   style: AppTextStyle.smallText(textColor: AppColors.brandGreen),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //         ///Todo Un-comment
  //         // verticalSizedBox(10),
  //         // StreamBuilder<int>(
  //         //     stream: buyerPaymentBloc.creditCardCtrl.stream,
  //         //     builder: (context, snapshot) {
  //         //       return ListView.builder(
  //         //           padding: EdgeInsets.zero,
  //         //           shrinkWrap: true,
  //         //           physics: NeverScrollableScrollPhysics(),
  //         //           itemCount: 5,
  //         //           itemBuilder: (context, index) {
  //         //             return InkWell(
  //         //               onTap: () {
  //         //                 buyerPaymentBloc.onSelectCreditCard(index);
  //         //               },
  //         //               child: snapshot.data == index
  //         //                   ?
  //         //                   //Selected Card UI
  //         //                   Container(
  //         //                       margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
  //         //                       decoration: BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(10))),
  //         //                       child: Column(
  //         //                         mainAxisAlignment: MainAxisAlignment.start,
  //         //                         crossAxisAlignment: CrossAxisAlignment.start,
  //         //                         mainAxisSize: MainAxisSize.min,
  //         //                         children: [
  //         //                           Container(
  //         //                             decoration: const BoxDecoration(color: AppColors.appBlack10, borderRadius: BorderRadius.all(Radius.circular(10))),
  //         //                             alignment: Alignment.centerLeft,
  //         //                             // padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 15),
  //         //                             child: Column(
  //         //                               mainAxisSize: MainAxisSize.min,
  //         //                               mainAxisAlignment: MainAxisAlignment.start,
  //         //                               crossAxisAlignment: CrossAxisAlignment.start,
  //         //                               children: [
  //         //                                 Padding(
  //         //                                   padding: EdgeInsets.all(10),
  //         //                                   child: Text(
  //         //                                     "TATA SBI MANOJ ${index}",
  //         //                                     style: TextStyle(
  //         //                                         fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 12, color: AppColors.darkStroke),
  //         //                                   ),
  //         //                                 ),
  //         //                                 Padding(
  //         //                                   padding: const EdgeInsets.only(bottom: 15, left: 15, right: 15),
  //         //                                   child: Row(
  //         //                                     mainAxisSize: MainAxisSize.max,
  //         //                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         //                                     crossAxisAlignment: CrossAxisAlignment.center,
  //         //                                     children: const [
  //         //                                       Text(
  //         //                                         "5242-XXXX-XXXX-8729",
  //         //                                         style: TextStyle(
  //         //                                             fontFamily: "LatoRegular",
  //         //                                             letterSpacing: 4,
  //         //                                             fontWeight: FontWeight.w400,
  //         //                                             fontSize: 15,
  //         //                                             color: AppColors.lightWhite3),
  //         //                                       ),
  //         //                                       Text(
  //         //                                         "01/27",
  //         //                                         style: TextStyle(
  //         //                                             fontFamily: "LatoRegular",
  //         //                                             letterSpacing: 4,
  //         //                                             fontWeight: FontWeight.w400,
  //         //                                             fontSize: 15,
  //         //                                             color: AppColors.lightWhite3),
  //         //                                       ),
  //         //                                     ],
  //         //                                   ),
  //         //                                 ),
  //         //                               ],
  //         //                             ),
  //         //                           ),
  //         //                           Container(
  //         //                             margin: EdgeInsets.symmetric(vertical: 20, horizontal: 20),
  //         //                             // color: Colors.white,
  //         //                             child: Row(
  //         //                               mainAxisSize: MainAxisSize.min,
  //         //                               children: [
  //         //                                 Container(
  //         //                                     width: 100,
  //         //                                     decoration: BoxDecoration(
  //         //                                         borderRadius: BorderRadius.all(Radius.circular(10)),
  //         //                                         border: Border.all(color: AppColors.lightestGrey, width: 2)),
  //         //                                     child: colorFilledTextField(
  //         //                                         context: context,
  //         //                                         textFieldCtrl: buyerPaymentBloc.cvvTextCtrl,
  //         //                                         hintText: "CVV",
  //         //                                         textFieldMaxLine: 1,
  //         //                                         keyboardType: TextInputType.number,
  //         //                                         textInputAction: TextInputAction.done,
  //         //                                         fillColor: AppColors.white,
  //         //                                         maxCharacter: 3,
  //         //                                         regExp: AppConstants.onlyInt,
  //         //                                         textAlign: TextAlign.center,
  //         //                                         hintFontSize: 15,
  //         //                                         styleFontSize: 15,
  //         //                                         hintTextLetterSpacing: 4,
  //         //                                         letterSpacing: 4,
  //         //                                         isObscureText: true)),
  //         //                                 Padding(
  //         //                                   padding: const EdgeInsets.only(left: 10, right: 30),
  //         //                                   child: SvgPicture.asset(AppImages.exclamation),
  //         //                                 ),
  //         //                                 Expanded(
  //         //                                   child: Container(
  //         //                                     padding: const EdgeInsets.symmetric(vertical: 10),
  //         //                                     decoration: BoxDecoration(
  //         //                                       color: AppColors.white,
  //         //                                       borderRadius: const BorderRadius.all(Radius.circular(10)),
  //         //                                       boxShadow: [
  //         //                                         BoxShadow(
  //         //                                           offset: const Offset(0, 1),
  //         //                                           blurRadius: 5,
  //         //                                           color: AppColors.appBlack.withOpacity(0.2),
  //         //                                         ),
  //         //                                       ],
  //         //                                     ),
  //         //                                     child: Center(
  //         //                                       child: Text(
  //         //                                         "PAY",
  //         //                                         style: TextStyle(
  //         //                                             letterSpacing: 4,
  //         //                                             fontFamily: "LatoBold",
  //         //                                             fontWeight: FontWeight.w700,
  //         //                                             fontSize: 15,
  //         //                                             color: AppColors.brandGreen),
  //         //                                       ),
  //         //                                     ),
  //         //                                   ),
  //         //                                 )
  //         //                               ],
  //         //                             ),
  //         //                           )
  //         //                         ],
  //         //                       ),
  //         //                     )
  //         //                   :
  //         //                   //Not selected Card UI
  //         //                   Container(
  //         //                       decoration: const BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(10))),
  //         //                       alignment: Alignment.centerLeft,
  //         //                       margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
  //         //                       // padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 15),
  //         //                       child: Column(
  //         //                         mainAxisSize: MainAxisSize.min,
  //         //                         mainAxisAlignment: MainAxisAlignment.start,
  //         //                         crossAxisAlignment: CrossAxisAlignment.start,
  //         //                         children: [
  //         //                           Padding(
  //         //                             padding: EdgeInsets.all(10),
  //         //                             child: Text(
  //         //                               "TATA SBI MANOJ $index",
  //         //                               style:
  //         //                                   TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 12, color: AppColors.darkStroke),
  //         //                             ),
  //         //                           ),
  //         //                           Padding(
  //         //                             padding: const EdgeInsets.only(bottom: 15, left: 15, right: 15),
  //         //                             child: Row(
  //         //                               mainAxisSize: MainAxisSize.max,
  //         //                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         //                               crossAxisAlignment: CrossAxisAlignment.center,
  //         //                               children: const [
  //         //                                 Text(
  //         //                                   "5242-XXXX-XXXX-8729",
  //         //                                   style: TextStyle(
  //         //                                       fontFamily: "LatoRegular",
  //         //                                       letterSpacing: 4,
  //         //                                       fontWeight: FontWeight.w400,
  //         //                                       fontSize: 15,
  //         //                                       color: AppColors.writingBlack),
  //         //                                 ),
  //         //                                 Text(
  //         //                                   "01/27",
  //         //                                   style: TextStyle(
  //         //                                       letterSpacing: 4,
  //         //                                       fontFamily: "LatoRegular",
  //         //                                       fontWeight: FontWeight.w400,
  //         //                                       fontSize: 15,
  //         //                                       color: AppColors.writingBlack),
  //         //                                 ),
  //         //                               ],
  //         //                             ),
  //         //                           ),
  //         //                         ],
  //         //                       ),
  //         //                     ),
  //         //             );
  //         //           });
  //         //     }),
  //       ],
  //     ),
  //   );
  // }
  //endregion

  //region Upi
  // Widget upi() {
  //   return Visibility(
  //     visible: widget.paymentOptions.contains("BHIM UPI"),
  //     child: Column(
  //       mainAxisSize: MainAxisSize.min,
  //       children: [
  //         Container(
  //           padding: const EdgeInsets.all(10),
  //           child: Row(
  //             mainAxisSize: MainAxisSize.max,
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             crossAxisAlignment: CrossAxisAlignment.center,
  //             children: [
  //                Text(
  //                 AppStrings.upi,
  //                 style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
  //               ),
  //
  //             ],
  //           ),
  //         ),
  //         verticalSizedBox(10),
  //         Padding(
  //           padding: const EdgeInsets.symmetric(horizontal: 12),
  //           child: Column(
  //             mainAxisSize: MainAxisSize.min,
  //             children: [
  //               GridView(
  //                 shrinkWrap: true,
  //                 //itemCount: buyerHomeBloc.recentlyVisitedItemCount,
  //                 gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //                   crossAxisCount: 3,
  //                   childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 2),
  //                   mainAxisSpacing: 10,
  //                   crossAxisSpacing: 10,
  //                 ),
  //                 physics: const NeverScrollableScrollPhysics(),
  //                 children: [
  //                   //G-Pay
  //                   InkWell(
  //                     onTap: () {
  //                       // buyerPaymentBloc.launchURL("tez://pay?pa=8076219463@kotak&pn=Dinesh&am=1&tn=Test Payment&cu=INR");
  //                       buyerPaymentBloc.launchURL("");
  //                       //buyerHomeBloc.goToBuyerViewStore(data[index]);
  //                     },
  //                     child: Container(
  //                       padding: const EdgeInsets.symmetric(horizontal: 10),
  //                       decoration: const BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(20))),
  //                       child: Center(
  //                         child: SvgPicture.asset(AppImages.upiIconList[0]),
  //                       ),
  //                     ),
  //                   ),
  //                   //Paytm
  //                   InkWell(
  //                     onTap: () {
  //                       // buyerPaymentBloc.launchURL("paytm://pay?pa=8076219463@kotak&pn=Dinesh&am=1&tn=Test Payment&cu=INR");
  //                       buyerPaymentBloc.launchURL("");
  //                     },
  //                     child: Container(
  //                       padding: const EdgeInsets.symmetric(horizontal: 10),
  //                       decoration: const BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(20))),
  //                       child: Center(
  //                         child: SvgPicture.asset(AppImages.upiIconList[2]),
  //                       ),
  //                     ),
  //                   ),
  //                   //PhonePay
  //                   InkWell(
  //                     onTap: () {
  //                       // buyerPaymentBloc.launchURL("phonepe://pay?pa=8076219463@kotak&pn=Dinesh&am=1&tn=Test Payment&cu=INR");
  //                       buyerPaymentBloc.launchURL("");
  //                     },
  //                     child: Container(
  //                       padding: const EdgeInsets.symmetric(horizontal: 10),
  //                       decoration: const BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(20))),
  //                       child: Center(
  //                         child: SvgPicture.asset(AppImages.upiIconList[1]),
  //                       ),
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               verticalSizedBox(10),
  //               InkWell(
  //                 onTap: () {
  //                   // buyerPaymentBloc.launchURL("upi://pay?pa=8076219463@kotak&pn=Dinesh&am=1&tn=Test Payment&cu=INR");
  //                   buyerPaymentBloc.launchURL("");
  //
  //                 },
  //                 child: Container(
  //                   padding: const EdgeInsets.symmetric(horizontal: 10),
  //                   decoration: const BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(10))),
  //                   child: Row(
  //                     mainAxisSize: MainAxisSize.min,
  //                     children: [
  //                       SvgPicture.asset(AppImages.upiIconList[3]),
  //                       horizontalSizedBox(20),
  //                        Expanded(
  //                         child: Text(
  //                           "Other UPI apps",
  //                           style: AppTextStyle.access0(textColor: AppColors.appBlack),
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               )
  //             ],
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  //endregion

  //region Debit Cards
  // Widget debitCards() {
  //   return Visibility(
  //     visible: widget.paymentOptions.contains("Debit Card"),
  //     child: Column(
  //       mainAxisSize: MainAxisSize.min,
  //       children: [
  //         Container(
  //           alignment: Alignment.centerLeft,
  //           margin: const EdgeInsets.only(top: 50),
  //           padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
  //           child: const Row(
  //             mainAxisSize: MainAxisSize.max,
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             crossAxisAlignment: CrossAxisAlignment.center,
  //             children: [
  //               Text(
  //                 AppStrings.debitCard,
  //                 style: TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 15, color: AppColors.writingColor2),
  //               ),
  //               Text(
  //                 AppStrings.addManageCards,
  //                 style: TextStyle(fontFamily: "LatoRegular", fontWeight: FontWeight.w400, fontSize: 12, color: AppColors.primaryGreen),
  //               ),
  //             ],
  //           ),
  //         ),
  //         ///Todo Un-comment
  //         // verticalSizedBox(10),
  //         // StreamBuilder<int>(
  //         //     stream: buyerPaymentBloc.creditCardCtrl.stream,
  //         //     builder: (context, snapshot) {
  //         //       return ListView.builder(
  //         //           shrinkWrap: true,
  //         //           physics: NeverScrollableScrollPhysics(),
  //         //           itemCount: 5,
  //         //           itemBuilder: (context, index) {
  //         //             return InkWell(
  //         //               onTap: () {
  //         //                 buyerPaymentBloc.onSelectCreditCard(index);
  //         //               },
  //         //               child: snapshot.data == index
  //         //                   ?
  //         //                   //Selected Card UI
  //         //                   Container(
  //         //                       margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
  //         //                       decoration: BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(10))),
  //         //                       child: Column(
  //         //                         mainAxisAlignment: MainAxisAlignment.start,
  //         //                         crossAxisAlignment: CrossAxisAlignment.start,
  //         //                         mainAxisSize: MainAxisSize.min,
  //         //                         children: [
  //         //                           Container(
  //         //                             decoration: const BoxDecoration(color: AppColors.appBlack10, borderRadius: BorderRadius.all(Radius.circular(10))),
  //         //                             alignment: Alignment.centerLeft,
  //         //                             // padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 15),
  //         //                             child: Column(
  //         //                               mainAxisSize: MainAxisSize.min,
  //         //                               mainAxisAlignment: MainAxisAlignment.start,
  //         //                               crossAxisAlignment: CrossAxisAlignment.start,
  //         //                               children: [
  //         //                                 Padding(
  //         //                                   padding: EdgeInsets.all(10),
  //         //                                   child: Text(
  //         //                                     "TATA SBI MANOJ ${index}",
  //         //                                     style: TextStyle(
  //         //                                         fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 12, color: AppColors.darkStroke),
  //         //                                   ),
  //         //                                 ),
  //         //                                 Padding(
  //         //                                   padding: const EdgeInsets.only(bottom: 15, left: 15, right: 15),
  //         //                                   child: Row(
  //         //                                     mainAxisSize: MainAxisSize.max,
  //         //                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         //                                     crossAxisAlignment: CrossAxisAlignment.center,
  //         //                                     children: const [
  //         //                                       Text(
  //         //                                         "5242-XXXX-XXXX-8729",
  //         //                                         style: TextStyle(
  //         //                                             fontFamily: "LatoRegular",
  //         //                                             letterSpacing: 4,
  //         //                                             fontWeight: FontWeight.w400,
  //         //                                             fontSize: 15,
  //         //                                             color: AppColors.lightWhite3),
  //         //                                       ),
  //         //                                       Text(
  //         //                                         "01/27",
  //         //                                         style: TextStyle(
  //         //                                             fontFamily: "LatoRegular",
  //         //                                             letterSpacing: 4,
  //         //                                             fontWeight: FontWeight.w400,
  //         //                                             fontSize: 15,
  //         //                                             color: AppColors.lightWhite3),
  //         //                                       ),
  //         //                                     ],
  //         //                                   ),
  //         //                                 ),
  //         //                               ],
  //         //                             ),
  //         //                           ),
  //         //                           Container(
  //         //                             margin: EdgeInsets.symmetric(vertical: 20, horizontal: 20),
  //         //                             // color: Colors.white,
  //         //                             child: Row(
  //         //                               mainAxisSize: MainAxisSize.min,
  //         //                               children: [
  //         //                                 Container(
  //         //                                     width: 100,
  //         //                                     decoration: BoxDecoration(
  //         //                                         borderRadius: BorderRadius.all(Radius.circular(10)),
  //         //                                         border: Border.all(color: AppColors.lightestGrey, width: 2)),
  //         //                                     child: colorFilledTextField(
  //         //                                         context: context,
  //         //                                         textFieldCtrl: buyerPaymentBloc.cvvTextCtrl,
  //         //                                         hintText: "CVV",
  //         //                                         textFieldMaxLine: 1,
  //         //                                         keyboardType: TextInputType.number,
  //         //                                         textInputAction: TextInputAction.done,
  //         //                                         fillColor: AppColors.white,
  //         //                                         maxCharacter: 3,
  //         //                                         regExp: AppConstants.onlyInt,
  //         //                                         textAlign: TextAlign.center,
  //         //                                         hintFontSize: 15,
  //         //                                         styleFontSize: 15,
  //         //                                         hintTextLetterSpacing: 4,
  //         //                                         letterSpacing: 4,
  //         //                                         isObscureText: true)),
  //         //                                 Padding(
  //         //                                   padding: const EdgeInsets.only(left: 10, right: 30),
  //         //                                   child: SvgPicture.asset(AppImages.exclamation),
  //         //                                 ),
  //         //                                 Expanded(
  //         //                                   child: Container(
  //         //                                     padding: const EdgeInsets.symmetric(vertical: 10),
  //         //                                     decoration: BoxDecoration(
  //         //                                       color: AppColors.white,
  //         //                                       borderRadius: const BorderRadius.all(Radius.circular(10)),
  //         //                                       boxShadow: [
  //         //                                         BoxShadow(
  //         //                                           offset: const Offset(0, 1),
  //         //                                           blurRadius: 5,
  //         //                                           color: AppColors.appBlack.withOpacity(0.2),
  //         //                                         ),
  //         //                                       ],
  //         //                                     ),
  //         //                                     child: Center(
  //         //                                       child: Text(
  //         //                                         "PAY",
  //         //                                         style: TextStyle(
  //         //                                             letterSpacing: 4,
  //         //                                             fontFamily: "LatoBold",
  //         //                                             fontWeight: FontWeight.w700,
  //         //                                             fontSize: 15,
  //         //                                             color: AppColors.brandGreen),
  //         //                                       ),
  //         //                                     ),
  //         //                                   ),
  //         //                                 )
  //         //                               ],
  //         //                             ),
  //         //                           )
  //         //                         ],
  //         //                       ),
  //         //                     )
  //         //                   :
  //         //                   //Not selected Card UI
  //         //                   Container(
  //         //                       decoration: const BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(10))),
  //         //                       alignment: Alignment.centerLeft,
  //         //                       margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
  //         //                       // padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 15),
  //         //                       child: Column(
  //         //                         mainAxisSize: MainAxisSize.min,
  //         //                         mainAxisAlignment: MainAxisAlignment.start,
  //         //                         crossAxisAlignment: CrossAxisAlignment.start,
  //         //                         children: [
  //         //                           Padding(
  //         //                             padding: EdgeInsets.all(10),
  //         //                             child: Text(
  //         //                               "TATA SBI MANOJ $index",
  //         //                               style:
  //         //                                   TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 12, color: AppColors.darkStroke),
  //         //                             ),
  //         //                           ),
  //         //                           Padding(
  //         //                             padding: const EdgeInsets.only(bottom: 15, left: 15, right: 15),
  //         //                             child: Row(
  //         //                               mainAxisSize: MainAxisSize.max,
  //         //                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         //                               crossAxisAlignment: CrossAxisAlignment.center,
  //         //                               children: const [
  //         //                                 Text(
  //         //                                   "5242-XXXX-XXXX-8729",
  //         //                                   style: TextStyle(
  //         //                                       fontFamily: "LatoRegular",
  //         //                                       letterSpacing: 4,
  //         //                                       fontWeight: FontWeight.w400,
  //         //                                       fontSize: 15,
  //         //                                       color: AppColors.writingBlack),
  //         //                                 ),
  //         //                                 Text(
  //         //                                   "01/27",
  //         //                                   style: TextStyle(
  //         //                                       letterSpacing: 4,
  //         //                                       fontFamily: "LatoRegular",
  //         //                                       fontWeight: FontWeight.w400,
  //         //                                       fontSize: 15,
  //         //                                       color: AppColors.writingBlack),
  //         //                                 ),
  //         //                               ],
  //         //                             ),
  //         //                           ),
  //         //                         ],
  //         //                       ),
  //         //                     ),
  //         //             );
  //         //           });
  //         //     }),
  //       ],
  //     ),
  //   );
  // }
  //endregion

  //region Net Banking
  // Widget netBanking() {
  //   return Visibility(
  //     visible: widget.paymentOptions.contains("Net Banking"),
  //     child: StreamBuilder<bool>(
  //         stream: buyerPaymentBloc.bankCtrl.stream,
  //         builder: (context, snapshot) {
  //           return Column(
  //             mainAxisSize: MainAxisSize.min,
  //             mainAxisAlignment: MainAxisAlignment.start,
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               //Net Banking Text
  //               Container(
  //                 alignment: Alignment.centerLeft,
  //                 margin: const EdgeInsets.only(top: 50),
  //                 padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
  //                 child: const Text(
  //                   AppStrings.netBanking,
  //                   maxLines: 1,
  //                   overflow: TextOverflow.visible,
  //                   style: TextStyle(fontFamily: "LatoSemibold", fontWeight: FontWeight.w600, fontSize: 15, color: AppColors.writingColor2),
  //                 ),
  //               ),
  //               verticalSizedBox(10),
  //
  //               //Net banking Bank select
  //               InkWell(
  //                 onTap: () {
  //                   buyerPaymentBloc.openBankSearchDialog();
  //                 },
  //                 child: Container(
  //                   margin: const EdgeInsets.symmetric(horizontal: 10),
  //                   padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
  //                   decoration: const BoxDecoration(color: AppColors.lightWhite3, borderRadius: BorderRadius.all(Radius.circular(10))),
  //                   child: Row(
  //                     children: [
  //                       Expanded(
  //                         child: Text(
  //                           buyerPaymentBloc.selectedBank,
  //                           overflow: TextOverflow.clip,
  //                           maxLines: 1,
  //                           style: const TextStyle(fontFamily: "LatoRegular", fontWeight: FontWeight.w400, fontSize: 16, color: AppColors.writingBlack),
  //                         ),
  //                       ),
  //                       Expanded(child: horizontalSizedBox(20)),
  //                       const Icon(Icons.keyboard_arrow_down)
  //                     ],
  //                   ),
  //                   // child: DropdownButton<String>(
  //                   //
  //                   //   isExpanded: true,
  //                   //
  //                   //   underline: const SizedBox(),
  //                   //   hint: const FittedBox(
  //                   //     child: Text("sel",
  //                   //       style: TextStyle(
  //                   //           fontFamily: "LatoRegular",
  //                   //           fontWeight: FontWeight.w400,
  //                   //           fontSize: 16,
  //                   //           color: AppColors.appBlack3
  //                   //       )
  //                   //       ,),
  //                   //   ),
  //                   //   isDense: true,
  //                   //
  //                   //   value: buyerPaymentBloc.selectedUpiOption ,
  //                   //   items:buyerPaymentBloc.upiList.map<DropdownMenuItem<String>>((String value){
  //                   //     return DropdownMenuItem(
  //                   //
  //                   //         value: value,
  //                   //
  //                   //
  //                   //         child:Text(value,  style: TextStyle(
  //                   //             fontFamily: "LatoRegular",
  //                   //             fontWeight: FontWeight.w400,
  //                   //             fontSize: 16,
  //                   //             color: AppColors.appBlack8
  //                   //         ),) );
  //                   //   }).toList(),
  //                   //   onChanged: (value){
  //                   //     // //print(value);
  //                   //     buyerPaymentBloc.onSelectUpiOption(value!);
  //                   //   },
  //                   // ),
  //                 ),
  //               ),
  //               buyerPaymentBloc.selectedBank == "select your bank"
  //                   ? const SizedBox()
  //                   : InkWell(
  //                       onTap: () {
  //                         buyerPaymentBloc.netBankingTransaction();
  //                       },
  //                       child: Container(
  //                         margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
  //                         padding: const EdgeInsets.symmetric(vertical: 10),
  //                         decoration: BoxDecoration(
  //                           color: AppColors.appWhite,
  //                           borderRadius: const BorderRadius.all(Radius.circular(10)),
  //                           boxShadow: [
  //                             BoxShadow(
  //                               offset: const Offset(0, 1),
  //                               blurRadius: 5,
  //                               color: AppColors.appBlack.withOpacity(0.2),
  //                             ),
  //                           ],
  //                         ),
  //                         child: const Center(
  //                           child: Text(
  //                             "Open bank site & PAY",
  //                             style: TextStyle(
  //                                 letterSpacing: 1, fontFamily: "LatoBold", fontWeight: FontWeight.w700, fontSize: 15, color: AppColors.brandGreen),
  //                           ),
  //                         ),
  //                       ),
  //                     )
  //             ],
  //           );
  //         }),
  //   );
  // }
  //endregion
}
