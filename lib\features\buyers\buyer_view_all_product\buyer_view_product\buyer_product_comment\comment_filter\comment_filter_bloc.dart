import 'dart:async';
import 'dart:core';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_bloc.dart';

import '../../../../../../model/comment/comment_filter_model/comment_filter_model.dart';

class CommentFilterBloc {
  // region Common Variables

  BuildContext context;
  bool isExpanded = true;
  final CommentFilterModel commentFilterModel;
  final BuyerProductCommentBloc buyerProductCommentBloc;





  // var uuid = Uuid();

  // endregion

  //region Controller
  final refreshFilterCtrl = StreamController<bool>.broadcast();

  //endregion


  // region | Constructor |
  CommentFilterBloc(this.context, this.commentFilterModel, this.buyerProductCommentBloc);

  // endregion

  // region Init
  void init(){

  }

// endregion
  
  


//region On tap drop down
void onTapDropDown(){
  isExpanded = !isExpanded;
  refreshFilterCtrl.sink.add(true);
}
//endregion


  //region On tap Status type
  void onTapStatus({required StatusType statusType}) {
    //Revers the data
    statusType.isSelected  = !statusType.isSelected;
    //Refresh
    if(!refreshFilterCtrl.isClosed){
      refreshFilterCtrl.sink.add(true);
    }
  }
// endregion
  
  



//region Dispose
void dispose(){
  refreshFilterCtrl.close();
}
//endregion


}


