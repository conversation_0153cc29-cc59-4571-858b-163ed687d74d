import 'package:flutter/material.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:any_link_preview/any_link_preview.dart';
import 'dart:developer' as developer;

// Check if text contains a URL
bool hasUrl(String text) {
  final urlRegex = RegExp(
    r'(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)',
    caseSensitive: false,
  );
  return urlRegex.hasMatch(text);
}

// Extract all URLs from text
List<String> extractAllUrls(String text) {
  final urlRegex = RegExp(
    r'(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)',
    caseSensitive: false,
  );

  final matches = urlRegex.allMatches(text);
  final urls = <String>[];

  for (final match in matches) {
    String url = match.group(0)!;

    // Add https:// if not present
    if (!url.startsWith('http')) {
      url = 'https://$url';
    }

    urls.add(url);
  }

  developer.log('Extracted URLs: $urls', name: 'URLHelpers');

  return urls;
}

// Get the first URL from text
String? extractFirstUrl(String text) {
  final urls = extractAllUrls(text);
  return urls.isNotEmpty ? urls.first : null;
}

// Widget to display a link preview
class LinkPreview extends StatelessWidget {
  final String url;
  final bool isMe;

  const LinkPreview({
    Key? key,
    required this.url,
    required this.isMe,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isMe
            ? AppColors.appRichBlack.withOpacity(0.9)
            : AppColors.textFieldFill2,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMe ? Colors.grey[800]! : Colors.grey[300]!,
          width: 0.5,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: AnyLinkPreview(
        link: url,
        displayDirection: UIDirection.uiDirectionHorizontal,
        showMultimedia: true,
        bodyMaxLines: 2,
        backgroundColor: Colors.transparent,
        removeElevation: true,
        titleStyle: AppTextStyle.access0(
          textColor: isMe ? Colors.white : AppColors.appBlack,
        ),
        bodyStyle: AppTextStyle.smallTextRegular(
          textColor: isMe ? Colors.white70 : AppColors.writingBlack0,
        ),
        errorBody: 'Unable to load preview',
        errorTitle: 'Link Preview',
        errorWidget: Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Icon(
                Icons.link,
                color: isMe ? Colors.white70 : AppColors.writingColor2,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  url,
                  style: TextStyle(
                    color: isMe ? Colors.white : AppColors.writingColor2,
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
        placeholderWidget: Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: isMe ? Colors.white70 : AppColors.brandBlack,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Loading preview...',
                  style: TextStyle(
                    color: isMe ? Colors.white70 : AppColors.writingColor2,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
        cache: const Duration(hours: 1),
      ),
    );
  }
}

// Widget to display multiple link previews
class MultiLinkPreview extends StatelessWidget {
  final List<String> urls;
  final bool isMe;

  const MultiLinkPreview({
    Key? key,
    required this.urls,
    required this.isMe,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment:
          isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: urls.map((url) => LinkPreview(url: url, isMe: isMe)).toList(),
    );
  }
}
