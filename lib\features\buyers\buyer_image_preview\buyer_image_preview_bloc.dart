import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';


class BuyerImagePreviewBloc {
  // region Common Variables
  BuildContext context;
  bool zoomStatus = false;
  final int imageIndex;
  bool hideAndVisibleAppBar = false;
  //region Page View Controller
  // ScrollController scrollController = ScrollController();

  //endregion

  //region Controller
  late TransformationController transformationController;
  final hideAndVisibleAppBarCtrl = StreamController<bool>.broadcast();
  final sliderCtrl = StreamController<int>.broadcast();
  final refreshCtrl = StreamController<bool>.broadcast();
  final PageController pageController = PageController();
  PhotoViewController photoViewController = PhotoViewController();
  //endregion

  // region | Constructor |
  BuyerImagePreviewBloc(this.context, this.imageIndex);
  // endregion

  // region Init
  void init() {
    transformationController = TransformationController();
    jumpToImage();
    //Listen on zoom
    photoViewController.outputStateStream.listen((event) {


      //Refresh ui
      refreshCtrl.sink.add(true);
    });
  }
// endregion

  //region Jump tp image
  void jumpToImage()async{
    await Future.delayed(const Duration(microseconds: 1000));
    onSelectSmallImage(imageIndex);
  }
  //endregion


  //region On Change Slider
  void onChangeSlider(int index){
    sliderCtrl.sink.add(index);
    // transformationController.value = Matrix4.identity();
  }
//endregion

  /*
  * This will stream the selected image index
  * */
//region On Select Small image
void onSelectSmallImage(int index){
  zoomStatus = false;
  pageController.jumpToPage(index);
  //Make zoom status to false
  zoomStatus = false;
  //Zoom scale to
  // photoViewController.scale = 0.39272727272727276;
  //Refresh ui
  sliderCtrl.sink.add(index);
}
//endregion



//regin On tap image
void onTapImage(){
  hideAndVisibleAppBar = !hideAndVisibleAppBar;
  hideAndVisibleAppBarCtrl.sink.add(hideAndVisibleAppBar);
}
//endregion




}