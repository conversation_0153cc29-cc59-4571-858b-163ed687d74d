import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_home/visited_store_grid/visited_store_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

//region VisitedStores
class VisitedStores extends StatefulWidget {
  const VisitedStores({Key? key}) : super(key: key);

  @override
  State<VisitedStores> createState() => _VisitedStoresState();
}
//endregion

class _VisitedStoresState extends State<VisitedStores> {
  //Width
  double width = 0.0;
  //region Bloc
  late VisitedStoreBloc visitedStoreBloc;

  //endregion

  //endregion
  //region Init
  @override
  void initState() {
    visitedStoreBloc = VisitedStoreBloc(context);
    visitedStoreBloc.init();
    super.initState();
  }

  //endregion

  //region Did update  widget

  @override
  void didUpdateWidget(covariant VisitedStores oldWidget) {
    visitedStoreBloc = VisitedStoreBloc(context);
    visitedStoreBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    visitedStoreBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
     builder: (context,boxConstraints){
       width = boxConstraints.maxWidth;
       return Column(
         mainAxisSize: MainAxisSize.min,
         children: [
           ///Todo un-comment
           // StreamBuilder<VisitedStoreState>(
           //     stream: visitedStoreBloc.visitedStoreCtrl.stream,
           //     initialData: VisitedStoreState.Loading,
           //     builder: (context, snapshot) {
           //       //print(snapshot.data);
           //       if (snapshot.data == VisitedStoreState.Success) {
           //         return AppSearchField(
           //           textEditingController: visitedStoreBloc.searchTextCtrl,
           //           onChangeText: () {
           //             visitedStoreBloc.onChangeSearchField();
           //           }, hintText: AppStrings.searchRecentlyVisited,
           //         );
           //       }
           //
           //       return AppSearchField(
           //         hintText: AppStrings.searchRecentlyVisited,
           //         textEditingController: visitedStoreBloc.searchTextCtrl,
           //         onChangeText: () {
           //           visitedStoreBloc.onChangeSearchField();
           //         },
           //         // isActive: visitedStoreBloc.storeListResponse.storeList!.isNotEmpty,
           //         isActive:true,
           //       );
           //     }),
           body(),
         ],
       );
     },
    );
  }

  //region Body
  Widget body() {
    //Todo
    //Remove isNotEmpty to isEmpty
    return Column(
      children: [
        ///Drop dwown button
        VisibilityDetector(
          onVisibilityChanged: (visibilityInfo) {
            var visiblePercentage = visibilityInfo.visibleFraction * 100;
            if (visiblePercentage == 100) {
              visitedStoreBloc.init();
            }
          },
          key: const Key("recent store"),

          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: StreamBuilder<VisitedStoreState>(
                stream: visitedStoreBloc.visitedStoreCtrl.stream,
                builder: (context, snapshot) {
                  return Container(
                    // height: 32,
                    margin: const EdgeInsets.only(top: 30, bottom: 25),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppStrings.recentlyVisited,
                          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                        ),
                        Visibility(
                          visible: visitedStoreBloc.storeList.length > 8,
                          child: CupertinoButton(
                              padding: EdgeInsets.zero,
                              onPressed: () {
                                // visitedStoreBloc.getRecentlyVisitedStores();
                                visitedStoreBloc.onTapDropdown();
                              },
                              child: RotatedBox(
                                quarterTurns: visitedStoreBloc.isDropDown ? 2 : 0,
                                child: SvgPicture.asset(
                                  AppImages.expand,
                                  fit: BoxFit.contain,
                                ),
                              )),
                        )
                      ],
                    ),
                  );
                }),
          ),
        ),

        ///Store grid
        StreamBuilder<VisitedStoreState>(
            stream: visitedStoreBloc.visitedStoreCtrl.stream,
            initialData: VisitedStoreState.Loading,
            builder: (context, snapshot) {
              //print(snapshot.data);
              if(snapshot.data == VisitedStoreState.SearchEmpy){
                return Row(
                  children: [
                    Expanded(
                      child: AppCommonWidgets.emptyResponseText(emptyMessage: AppStrings.youHaveNotViewedAnyStore),
                    ),
                  ],
                );
              }
              if (snapshot.data == VisitedStoreState.Empty) {
                return Row(
                  children: [
                    Expanded(
                      child: AppCommonWidgets.emptyResponseText(emptyMessage: AppStrings.youHaveNotViewedAnyStore),
                    ),
                  ],
                );
              }
              if (snapshot.data == VisitedStoreState.SearchEmpy) {
                  return Row(
                    children: [
                      Expanded(
                        child: AppCommonWidgets.emptyResponseText(emptyMessage: AppStrings.youHaveNotViewedAnyStore),
                      ),
                    ],
                  );
              }
              if (snapshot.data == VisitedStoreState.Success) {

                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: Column(
                    children: [
                      GridView.builder(

                        shrinkWrap: true,
                        //itemCount: buyerHomeBloc.recentlyVisitedItemCount,
                        itemCount: visitedStoreBloc.isDropDown ? visitedStoreBloc.itemCount + 1 : visitedStoreBloc.itemCount,
                        gridDelegate:  SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 4,
                          /// childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.5),
                           mainAxisSpacing: 20,
                          /// crossAxisSpacing: 15,
                          // childAspectRatio: 0.7
                          crossAxisSpacing: 15,
                          mainAxisExtent: width/5 +  CommonMethods.textHeight(
                            textStyle: AppTextStyle.subTitle(textColor: AppColors.appBlack), context: context,) + 6,

                          /// mainAxisExtent: 100,
                        ),
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        itemBuilder: (context, index) {
                          // //print("Ui side item count is ${visitedStoreBloc.itemCount}");
                          ///Collapsed button
                          if (index == visitedStoreBloc.itemCount && visitedStoreBloc.isDropDown) {
                            return Column(
                              children: [
                                Container(
                                  height: width / 5,
                                  width: width / 5,
                                  decoration: BoxDecoration(
                                    color: AppColors.appWhite,
                                    // color: AppColors.appWhite,
                                    borderRadius: BorderRadius.circular(0.4130 * width / 5),
                                    // boxShadow: AppColors.appShadow,
                                  ),
                                  child: CupertinoButton(
                                    borderRadius: BorderRadius.circular( 0.4130 * width / 5),
                                    padding: EdgeInsets.zero,
                                    onPressed: () {
                                      visitedStoreBloc.onTapDropdown();
                                    },
                                    child: SvgPicture.asset(
                                      AppImages.doubleDropDown,
                                      // width: 30,
                                      // height: 30,
                                      fit: BoxFit.cover,
                                      color: AppColors.appBlack,
                                    ),
                                  ),
                                ),
                              ],
                            );
                          }

                          ///Greed stores
                          return AppCommonWidgets.storeCardGreed(
                            context: context,
                            width: width,
                            onTap: () {
                              visitedStoreBloc.goToBuyerViewStore(selectedStore: visitedStoreBloc.storeList[index]);
                            },
                            storeInfo: visitedStoreBloc.storeList[index],
                          );
                        },
                      ),
                    ],
                  ),
                );
              }
              if (snapshot.data == VisitedStoreState.Loading) {
                return AppCommonWidgets.appCircularProgress();
              }
              if (snapshot.data == VisitedStoreState.Failed) {
                return AppCommonWidgets.errorWidget(
                    errorMessage: AppStrings.unableToLoadRecentlyVisitedStores,
                    onTap: (){
                  visitedStoreBloc.getRecentlyVisitedStores();
                });
              }
              return const SizedBox();
            }),
      ],
    );
  }
//endregion
}
