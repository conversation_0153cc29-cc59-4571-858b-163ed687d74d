import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';


enum ShoppingCartPriceState { Loading, Success, Failed, Empty }

class ShoppingCartPriceBloc {
  // region Common Methods
  BuildContext context;

  final GetCartPriceResponse getCartPriceResponse;


  // endregion
  //region Controller
  final shoppingCartPriceStateCtrl = StreamController<ShoppingCartPriceState>.broadcast();

  //endregion

  // region | Constructor |
  ShoppingCartPriceBloc(this.context, this.getCartPriceResponse);

  // endregion

  // region Init
  init() {
  }
  // endregion



}
