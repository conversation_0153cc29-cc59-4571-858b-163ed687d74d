import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/closed_store_dialog/closed_store_dialog_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class ClosedStoreDialog extends StatefulWidget {
  final List<CartStore> closedStoreList;
  final ShoppingCartBloc shoppingCartBloc;

  const ClosedStoreDialog(
      {super.key,
      required this.closedStoreList,
      required this.shoppingCartBloc});

  @override
  State<ClosedStoreDialog> createState() => _ClosedStoreDialogState();
}

class _ClosedStoreDialogState extends State<ClosedStoreDialog> {
  //region Bloc
  late ClosedStoreDialogBloc closedStoreDialogBloc;
  //endregion

  // region Init
  @override
  void initState() {
    closedStoreDialogBloc = ClosedStoreDialogBloc(
        context, widget.closedStoreList, widget.shoppingCartBloc);
    closedStoreDialogBloc.init();
    super.initState();
  }

  // endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //Message
          Text(
            AppStrings.storesForTheseProductsAreNotAcceptingOrdersRightNow,
            textAlign: TextAlign.center,
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),

          //Store icons
          storeIcons(),
          //Action button
          actionButton(),
        ],
      ),
    );
  }

//endregion

//region Store icons
  Widget storeIcons() {
    // for (int i = 0; i < 5; i++) {
    //   widget.closedStoreList.add(widget.closedStoreList.first);
    // }
    return Container(
      margin: const EdgeInsets.only(top: 30, bottom: 40),
      child: Wrap(
        alignment: WrapAlignment.center, // Aligns items in the center
        spacing: 10.0, // Spacing between items
        runSpacing: 10.0, // Spacing between rows
        children: closedStoreDialogBloc.closedProductList.map((item) {
          return Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(11)),
            height: 25,
            width: 25,
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                //Go to single product view
                var screen = BuyerViewSingleProductScreen(
                  productReference: item.productReference!,
                );
                var route = MaterialPageRoute(builder: (context) => screen);
                Navigator.push(context, route);
              },
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(11),
                  child: extendedImage(item.prodImages, context, 50, 50,
                      imageHeight: 25,
                      imageWidth: 25,
                      customPlaceHolder: AppImages.storePlaceHolder)),
            ),
          );
        }).toList(),
      ),
    );
  }

//endregion

//region Action buttons
  Widget actionButton() {
    return Row(
      children: [
        //Move to wish list
        Expanded(
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () async {
              //Add to wish list
              await closedStoreDialogBloc.shoppingCartBloc.addToWishListApi(
                  productReferenceList:
                      closedStoreDialogBloc.closedStoreProductReferenceList);
              //Remove product from cart
              await closedStoreDialogBloc.shoppingCartBloc
                  .removeMultipleCartItemsApi(
                      cartItemIdList:
                          closedStoreDialogBloc.closedStoreProductCartIdList);
              //Closed dialog
              Navigator.pop(context, true);
              //Get cart
              await closedStoreDialogBloc.shoppingCartBloc.getCartItems();
              //Get cart price
              await closedStoreDialogBloc.shoppingCartBloc
                  .getShoppingCartPrice();
            },
            child: Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(80),
                color: AppColors.brandBlack,
              ),
              child: Text(
                AppStrings.moveToWishList,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.access0(textColor: AppColors.appWhite),
              ),
            ),
          ),
        ),
        const SizedBox(
          width: 10,
        ),
        //order later
        Expanded(
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(80),
                color: AppColors.textFieldFill1,
              ),
              child: Text(
                AppStrings.orderLater,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.access0(textColor: AppColors.appBlack),
              ),
            ),
          ),
        ),
      ],
    );
  }
//endregion
}
