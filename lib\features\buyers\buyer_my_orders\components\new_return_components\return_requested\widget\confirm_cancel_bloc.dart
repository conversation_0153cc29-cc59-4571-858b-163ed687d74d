import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/waiting_for_confirmation/waiting_for_confirmation_bloc.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ConfirmCancelBloc {
  // region Common Variables
  BuildContext context;
  final List<SubOrder> suborderList;
  final WaitingForConfirmationBloc waitingForConfirmationBloc;




  // String estimatedDeliveryDate = DateFormat("dd-MM-yyyy").format(DateTime.now());
  // String estimatedDeliveryDate = DateFormat("dd-MM-yyyy").format(DateTime.now());

  // endregion

  //region Controller
  final confirmAndCancelCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  ConfirmCancelBloc(this.context, this.suborderList, this.waitingForConfirmationBloc);

  // endregion

  // region Init
  void init() {
    waitingForConfirmationBloc.onSelectChangeDate();
    // defaultEstimatedDeliveryDate();
  }

// endregion





//
// //region Default delivery date time
//   void defaultEstimatedDeliveryDate() {
//
//     //Apply minimum date time
//     waitingForConfirmationBloc.bottomSheetEstimatedDeliveryDate = CommonMethods.minimumAndMaximumDateTime(dateList:suborderList.map((e) => e.estimatedDeliveryDate!.replaceAll("-","/")).toList(),isMinimum: false );
//
//     //
//     // if (suborderList.first.estimatedDeliveryDate == null) {
//     //   return;
//     // }
//     // DateTime tempDate = DateFormat("dd/MM/yyyy").parse(suborderList.first.estimatedDeliveryDate!.replaceAll("-", "/"));
//     // estimatedDeliveryDate = DateFormat("dd-MM-yyyy").format(tempDate);
//     // //print(estimatedDeliveryDate);
//     confirmAndCancelCtrl.sink.add(true);
//   }
//
// //endregion




}
