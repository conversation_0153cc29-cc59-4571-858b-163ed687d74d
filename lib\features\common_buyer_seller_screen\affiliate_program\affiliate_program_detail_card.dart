import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_bloc.dart';
import 'package:swadesic/features/widgets/app_container/app_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class AffiliateProgramDetailCard extends StatefulWidget {
  final AffiliateProgramBloc affiliateProgramBloc;
  const AffiliateProgramDetailCard({super.key, required this.affiliateProgramBloc});

  @override
  State<AffiliateProgramDetailCard> createState() => _AffiliateProgramDetailCardState();
}

class _AffiliateProgramDetailCardState extends State<AffiliateProgramDetailCard> {




  //Stream controller
  final dropDownCtrl = StreamController<bool>.broadcast();





  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
Widget body(){
    return StreamBuilder<bool>(
        stream: dropDownCtrl.stream,
        initialData: true,
        builder: (context, snapshot) {
          return InkWell(
            highlightColor: Colors.transparent,
            hoverColor: Colors.transparent,
            onTap:  snapshot.data! ?(){
              dropDownCtrl.sink.add(!snapshot.data!);
            }: null,
            // padding: EdgeInsets.zero,
            child: AppBorderContainer(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                    color: AppColors.textFieldFill2,
                    // borderRadius: BorderRadius.all(Radius.circular(MediaQuery.of(context).size.width * 0.02))
                ),
                // margin: const EdgeInsets.only(left: 15,right: 15,bottom: 12),
                padding: const EdgeInsets.symmetric(vertical: 16,horizontal: 10),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text("${widget.affiliateProgramBloc.affiliateProgramDetail.title}",style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack).copyWith(height:0),),
                        SizedBox(
                          height: 24,width: 24,
                          child: CupertinoButton(
                            onPressed: (){
                              dropDownCtrl.sink.add(!snapshot.data!);
                            },
                            padding: EdgeInsets.zero,
                            child: RotatedBox(
                                quarterTurns: snapshot.data!?3:1,
                                child: SvgPicture.asset(AppImages.arrow3,height: 24,width: 24,)),
                          ),
                        )
                      ],
                    ),
                    expandView()
                  ],
                ),
              ),
            ),
          );
        }
    );
}
//endregion





//region Expand view
  Widget expandView(){
    return StreamBuilder<bool>(
        stream:dropDownCtrl.stream,
        initialData: true,
        builder: (context, snapshot) {
          //False
          if(!snapshot.data!){
            return  const SizedBox();
          }
          return Column(
            children: [
              detail(),
            ],
          );
        }
    );
  }
//endregion


//region Detail
  Widget detail(){
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 13),
      padding: const EdgeInsets.symmetric(horizontal: 0.0,vertical: 7.0),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text(widget.affiliateProgramBloc.affiliateProgramDetail.description!,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack).copyWith(height: 1.5),),
          ),
          iconAndText(
            icon: AppImages.affiliateShare,
            text: widget.affiliateProgramBloc.affiliateProgramDetail.steps![0].text!,

          ),
          iconAndText(
            icon: AppImages.affiliateReferral,
            text: widget.affiliateProgramBloc.affiliateProgramDetail.steps![1].text!,

          ),
          iconAndText(
            icon: AppImages.affiliateMoney,
            text: widget.affiliateProgramBloc.affiliateProgramDetail.steps![2].text!,

          ),


          InkWell(
            onTap: (){
              widget.affiliateProgramBloc.openPayOutEligibilityDialog();
            },
            child: Container(
              alignment: Alignment.centerLeft,
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: Text("**Affiliate Payout Eligibility",style: AppTextStyle.smallText(textColor: AppColors.appBlack,isUnderline: true),)),
          ),


        ],
      ),

    );
  }
//endregion



//region Icon and text 
Widget iconAndText({required String icon, required String text}){
    return Container(
      margin: const EdgeInsets.only(top: 10),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Row(
        children: [
          Image.asset(icon,height: 30,),
          const SizedBox(width: 10,),
          Expanded(child: Text(text,style: AppTextStyle.access0(textColor: AppColors.appBlack),))

        ],
      ),
    );
}
//endregion





}
