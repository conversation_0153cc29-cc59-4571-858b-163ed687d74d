import 'package:flutter/material.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'dart:developer' as developer;
import 'package:url_launcher/url_launcher.dart';
import 'package:swadesic/features/buyers/messaging/utils/image_preview_dialog.dart';

// Check if mime type is an image
bool isImageMimeType(String? mimeType) {
  if (mimeType == null) return false;
  return mimeType.startsWith('image/');
}

// Get file icon based on mime type
IconData getFileIcon(String? mimeType) {
  if (mimeType == null) return Icons.insert_drive_file;

  if (mimeType.startsWith('image/')) return Icons.image;
  if (mimeType.startsWith('video/')) return Icons.video_file;
  if (mimeType.startsWith('audio/')) return Icons.audio_file;
  if (mimeType.startsWith('text/')) return Icons.description;
  if (mimeType.contains('pdf')) return Icons.picture_as_pdf;
  if (mimeType.contains('word') || mimeType.contains('document')) return Icons.description;
  if (mimeType.contains('excel') || mimeType.contains('sheet')) return Icons.table_chart;

  return Icons.insert_drive_file;
}

// Format file size
String formatFileSize(int bytes) {
  if (bytes < 1024) return '$bytes B';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
  if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
}

// Build attachment preview widget
Widget buildAttachmentPreview(dynamic attachment, bool isMe, BuildContext context) {
  // Log the entire attachment for debugging
  developer.log(
    'Attachment data: $attachment',
    name: 'FileAttachmentHelpers'
  );

  // Check if the attachment has a file extension that indicates an image
  final String originalName = attachment['originalName'] ?? '';
  final String fileExtension = originalName.toLowerCase().split('.').last;
  final bool isImageByExtension = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].contains(fileExtension);

  // Check by MIME type
  final String? mimeType = attachment['mimetype'];
  final bool isImageByMimeType = isImageMimeType(mimeType);

  // Use either method to determine if it's an image
  final bool isImage = isImageByMimeType || isImageByExtension;

  developer.log(
    'Is image by MIME type: $isImageByMimeType, Is image by extension: $isImageByExtension, Final decision: $isImage',
    name: 'FileAttachmentHelpers'
  );

  // Get the file URL and ensure it has the correct base URL
  String fileUrl = attachment['url'] ?? '';

  // If the URL is a relative path (starts with '/'), prepend the base URL
  if (fileUrl.startsWith('/')) {
    // Use the newMessaging_baseUrl for file URLs
    fileUrl = AppConstants.newMessaging_baseUrl + fileUrl;

    developer.log(
      'Constructed file URL: $fileUrl',
      name: 'FileAttachmentHelpers'
    );
  }

  // Log the URL for debugging
  developer.log(
    'File URL: $fileUrl',
    name: 'FileAttachmentHelpers'
  );
  developer.log(
    'Attachment: $attachment',
    name: 'FileAttachmentHelpers'
  );

  // For images, show a more prominent display
  if (isImage) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: GestureDetector(
        onTap: () {
          // Open image viewer on tap
          showDialog(
            context: context,
            builder: (context) => ImagePreviewDialog(
              imageAttachments: [attachment],
              initialIndex: 0,
            ),
            barrierColor: Colors.black.withOpacity(0.9),
          );
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.network(
            fileUrl,
            headers: {
              'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
            },
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width * 0.65, // Wider image
            height: 200, // Taller image
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                width: MediaQuery.of(context).size.width * 0.65,
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                        : null,
                  ),
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              developer.log(
                'Error loading image: ${error.toString()}',
                name: 'FileAttachmentHelpers',
                stackTrace: stackTrace,
              );
              return Container(
                width: MediaQuery.of(context).size.width * 0.65,
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 40),
                    const SizedBox(height: 8),
                    Text(
                      'Failed to load image',
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack,
                      ),
                    ),
                    Text(
                      attachment['originalName'] ?? 'Image',
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // For non-image files, show a file preview
  return Container(
    margin: const EdgeInsets.only(bottom: 8),
    decoration: BoxDecoration(
      color: Colors.grey[200],
      borderRadius: BorderRadius.circular(12),
    ),
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async {
          // Open file
          try {
            final Uri url = Uri.parse(fileUrl);
            if (await canLaunchUrl(url)) {
              await launchUrl(url, mode: LaunchMode.externalApplication);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not open file: $fileUrl')),
              );
            }
          } catch (e) {
            developer.log(
              'Error opening file: ${e.toString()}',
              name: 'FileAttachmentHelpers'
            );
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error opening file: ${e.toString()}')),
            );
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          constraints: const BoxConstraints(
            maxWidth: 200,
          ),
          padding: const EdgeInsets.all(12),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                getFileIcon(attachment['mimetype']),
                size: 40,
                color: Colors.grey[700],
              ),
              const SizedBox(width: 12),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      attachment['originalName'] ?? 'File',
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            formatFileSize(attachment['size'] ?? 0),
                            style: AppTextStyle.contentText0(
                              textColor: AppColors.appBlack,
                            ),
                          ),
                        ),
                        Icon(
                          Icons.download,
                          size: 16,
                          color: Colors.grey[700],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
