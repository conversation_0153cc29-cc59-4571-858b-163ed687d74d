import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class SellerContactInfoDialog extends StatefulWidget {
  final String title;
  final SubOrder subOrder;
  const SellerContactInfoDialog({Key? key, required this.title, required this.subOrder}) : super(key: key);

  @override
  State<SellerContactInfoDialog> createState() => _SellerContactInfoDialogState();
}

class _SellerContactInfoDialogState extends State<SellerContactInfoDialog> {
  @override
  Widget build(BuildContext context) {
    return  body();
  }

  //region Body
Widget body(){
return SizedBox(
  height: 300,
  width: double.maxFinite,
  child:   Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      appText(widget.title,color: AppColors.writingColor2,fontSize: 16,fontWeight: FontWeight.w700,fontFamily: AppConstants.rRegular),
      verticalSizedBox(10),
      contactList(phoneNumberList:widget.subOrder.storeContactInfo!.phoneNumber!),
      email(emailList: widget.subOrder.storeContactInfo!.emailId!),
      verticalSizedBox(10),
      Align(
          alignment: Alignment.centerLeft,
          child: appText("click to call/mail and hold to copy",color: AppColors.writingColor3,fontSize: 14,fontWeight: FontWeight.w400,fontFamily: AppConstants.rRegular)),

    ],
  ),
);
}
//endregion



//region List of Contact number
Widget contactList({required List<String> phoneNumberList}){
    return ListView.builder(
      shrinkWrap: true,
        itemCount:phoneNumberList.length ,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder:(context,index){
      return Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Theme(
            data: ThemeData(unselectedWidgetColor: AppColors.lightStroke),
            child: InkWell(
              onTap: (){
                CommonMethods.openDialPad(phoneNumber:phoneNumberList[index]);

              },
              onLongPress: (){
                Navigator.of(context).pop();
                CommonMethods.copyText(context, phoneNumberList[index]);
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(10),
                child: appText(phoneNumberList[index],color: AppColors.appBlack,fontSize: 16,fontWeight: FontWeight.w700,fontFamily: AppConstants.rRegular),
              ),
            ),
          ),
          divider()
        ],
      );
    });
}
//endregion


//region List of email
  Widget email({required List<String> emailList}){
    return ListView.builder(
        shrinkWrap: true,
        itemCount:emailList.length ,
        itemBuilder:(context,index){
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Theme(
                data: ThemeData(unselectedWidgetColor: AppColors.lightStroke),
                child: InkWell(
                  onTap: (){
                    CommonMethods.openEmail(emailId:emailList[index]);
                  },
                  onLongPress: (){
                    Navigator.of(context).pop();
                    CommonMethods.copyText(context, emailList[index]);
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(10),
                    child: appText(emailList[index],color: AppColors.appBlack,fontSize: 16,fontWeight: FontWeight.w700,fontFamily: AppConstants.rRegular),
                  ),
                ),
              ),
              divider()
            ],
          );
        });
  }
//endregion


}

