import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/communication/welcome_store_screen.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_preview_store/create_preview_store_screen.dart';

class StoreCreationAnnouncement extends StatelessWidget {
  const StoreCreationAnnouncement({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(10),
      padding: const EdgeInsets.only(left: 10, right: 10, top: 20, bottom: 20),
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor1),
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black.withOpacity(0.1),
        //     blurRadius: 10,
        //     offset: const Offset(0, 4),
        //   ),
        // ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            "Let's setup your Swadesic store",
            style: AppTextStyle.pageHeadingBold(textColor: AppColors.appBlack),
          ),
          const SizedBox(height: 5),

          // Description
          Text(
            'Set up your free store and go live with a built-in community. Swadesic manages payments, orders, and trust — so you can focus on growing your brand.',
            style:
                AppTextStyle.contentText0(textColor: AppColors.writingBlack2),
          ),
          const SizedBox(height: 15),

          Row(
            children: [
              Expanded(
                flex: 2, // Slightly less flex for the shorter button
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    final homeAccessBloc = HomeAccessBloc(context);
                    const inviteCode = '';
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => WelcomeStoreScreen(
                          inviteCode: inviteCode,
                          homeAccessBloc: homeAccessBloc,
                        ),
                      ),
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    alignment: Alignment.center,
                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                    decoration: const BoxDecoration(
                      color: AppColors.appBlack,
                      borderRadius: BorderRadius.all(Radius.circular(100)),
                    ),
                    child: Text(
                      'Create Store',
                      overflow: TextOverflow.ellipsis,
                      style: AppTextStyle.access0(textColor: AppColors.appWhite),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                flex: 3, // More flex for the longer button
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CreatePreviewStoreScreen(),
                      ),
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    alignment: Alignment.center,
                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                    decoration: const BoxDecoration(
                      color: AppColors.textFieldFill1,
                      borderRadius: BorderRadius.all(Radius.circular(100)),
                    ),
                    child: Text(
                      'Visualize your Storefront',
                      overflow: TextOverflow.visible,
                      style: AppTextStyle.access0(textColor: AppColors.appBlack),
                    ),
                  ),
                ),
              ),
            ],
          )

        ],
      ),
    );
  }
}
