import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_availability/product_availability_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/product_availability/product_availability_locations.dart';
import 'package:swadesic/features/seller/seller_dashboard_and_rewards/seller_dashboard/our_meter/seller_dashboard_our_meter_tool_tips/our_meter_order_type.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class ProductAvailability extends StatefulWidget {
  final Product product;
  final bool isUnderLineText;

  const ProductAvailability({super.key, required this.product, this.isUnderLineText = false});

  @override
  State<ProductAvailability> createState() => _ProductAvailabilityState();
}

class _ProductAvailabilityState extends State<ProductAvailability> {
  //region Bloc
  late ProductAvailabilityBloc productAvailabilityBloc;

  //endregion

  //region Init
  @override
  void initState() {
    productAvailabilityBloc = ProductAvailabilityBloc(context, widget.product);
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        availability(),
        underLineText(),
      ],
    );
  }

  //region Availability
  Widget availability() {
    return Visibility(
      visible: !widget.isUnderLineText,
      child: Container(
        margin: const EdgeInsets.only(left: 16, right: 16),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            InkWell(
              onTap: () {
                productAvailabilityBloc.availableInLocation();
              },
              child: Container(
                  child: Text(
                "Availability: View delivery locations",
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              )),
            ),
            //If product quantity is less then 10 and greater then 0
            Visibility(
                visible: productAvailabilityBloc.product.inStock! < 10 && productAvailabilityBloc.product.inStock! > 0,
                child: Container(
                    padding: const EdgeInsets.only(left: 16, right: 16),
                    child: Text(
                      "Only ${productAvailabilityBloc.product.inStock!} units left",
                      style: AppTextStyle.smallText(textColor: AppColors.orange),
                    ))),
          ],
        ),
      ),
    );
  }

//endregion

//region UnderLineText
  Widget underLineText() {
    return Visibility(
      visible: widget.isUnderLineText,
      child: InkWell(
          onTap: () {
            productAvailabilityBloc.availableInLocation();
          },
          child: Text(
            AppStrings.viewDeliveryLocations,
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack, isUnderline: true),
          )),
    );
  }
//endregion
}
