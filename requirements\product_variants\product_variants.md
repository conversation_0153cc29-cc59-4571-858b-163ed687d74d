
# Product Options Api

this api is used to add product options 

### Product Options
When creating or updating a product, you can define options that will be available for variants. Options are defined as key-value pairs where the key is the option name (e.g., "color", "size") and the value is an array of possible values for that 

#### Example Request: Create Product with Options
```http
POST /product/productlist/
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "product_name": "Premium Cotton T-Shirt",
  "brand_name": "Swadesi Wear",
  "product_description": "High quality cotton t-shirt with a comfortable fit",
  "product_category": "Apparel",
  "mrp_price": 199900,  // ₹1,999.00
  "selling_price": 149900,  // ₹1,499.00
  "in_stock": 100,
  "store_reference": "STR123456",
  "options": {
    "color": ["Red", "Blue", "Black"],
    "size": ["S", "M", "L", "XL"],
    "sleeve_length": ["Short Sleeve", "Long Sleeve"]
  },
  "hashtags": "fashion,casual,summer",
  "targeted_gender": "U",
  "swadeshi_brand": "FULLY_SWADESHI_BRAND",
  "swadeshi_made": "FULLY_SWADESHI_MADE"
}
```

#### Example Request: Update Product with New Options
```http
PUT /product/productdetails/
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "product_reference": "PROD789012",
  "options": {
    "color": ["Red", "Blue", "Black", "White"], 
    "size": ["XS", "S", "M", "L", "XL"],        
    "sleeve_length": ["Short Sleeve", "Long Sleeve", "Sleeveless"]  
  },
  "in_stock": 150  // Updated stock
}
```

#### Success Response (Create)
- **Status Code:** 201 Created
- **Content-Type:** application/json

```json
{
  "message": "Product created successfully",
  "product": {
    "product_reference": "PROD123456",
    "product_name": "Premium Cotton T-Shirt",
    "brand_name": "Swadesi Wear",
    "options": {
      "color": ["Red", "Blue", "Black"],
      "size": ["S", "M", "L", "XL"],
      "sleeve_length": ["Short Sleeve", "Long Sleeve"]
    },
    "created_date": "2023-06-29T12:00:00Z"
  }
}
```

#### Success Response (Update)
- **Status Code:** 200 OK
- **Content-Type:** application/json

```json
{
  "message": "Product updated successfully",
  "product": {
    "product_reference": "PROD789012",
    "product_name": "Premium Cotton T-Shirt",
    "options": {
      "color": ["Red", "Blue", "Black", "White"],
      "size": ["XS", "S", "M", "L", "XL"],
      "sleeve_length": ["Short Sleeve", "Long Sleeve", "Sleeveless"]
    },
    "in_stock": 150,
    "modified_date": "2023-06-29T12:30:00Z"
  }
}
```

#### Error Responses
- **400 Bad Request**: Missing required fields or invalid data
- **401 Unauthorized**: Invalid or missing authentication token
- **404 Not Found**: Product not found (for updates)
- **500 Internal Server Error**: Server error during product creation/update

### Rename Product options
Api that takes product_reference, old_option_name, new_option_name as input and rename the options and also update all the variants combinations that has old_option_name.

#### request
```bash
curl --location 'http://192.168.1.6:8000/product/update_option/' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <your_jwt_token>' \
--data '{
    "product_reference": "P1750677110400549UCDQ",
    "current_name": "size",
    "new_name": "fit"
  }'
```

#### response
```json
{
    "message": "Option name and/or values updated successfully."
}
```


------------------

# Product Variants API

This API provides endpoints to manage product variants in the system. It allows retrieving existing variants and creating/updating variants for products.

## Base URL
```
{baseusrl}/product/
```

## Authentication
All endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Endpoints

### 1. Get Product Variants
Retrieves all active variants for a specific product.

**Endpoint:** `GET /productvariants/`

#### Query Parameters
| Parameter | Type   | Required | Description                          |
|-----------|--------|----------|--------------------------------------|
| product_reference | string | Yes      | The reference ID of the product      |

#### Response
- **Status Code:** 200 OK
- **Content-Type:** application/json

```json
{
    "message": "success",
    "product_variants": [
        {
            "product_variantid": 1,
            "variant_reference": "PV1234567890",
            "product_reference": "PROD123",
            "combinations": {
                "color": "Red",
                "size": "M"
            },
            "mrp_price": 1999,
            "selling_price": 1499,
            "stock": 50,
            "is_active": true,
            "created_date": "2023-01-01T12:00:00Z",
            "modified_date": "2023-01-01",
            "variant_version": "1.0.0"
        }
    ]
}
```

#### Error Responses
- **400 Bad Request**: Missing or invalid product_reference
- **404 Not Found**: Product not found
- **401 Unauthorized**: Invalid or missing authentication token

---

### 2. Create/Update Product Variant
Creates a new product variant or updates an existing one if a variant with the same combinations exists.

**Endpoint:** `POST /productvariants/`

#### Request Body
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| product_reference | string | Yes | Reference ID of the parent product |
| mrp_price | integer | Yes | Maximum Retail Price (in paise) |
| selling_price | integer | Yes | Selling Price (in paise) |
| stock | integer | Yes | Available stock quantity |
| combinations | object | Yes | JSON object representing variant attributes (e.g., {"color": "Red", "size": "M"}) |

#### Example Request
```json
{
    "product_reference": "PROD123",
    "mrp_price": 1999,
    "selling_price": 1499,
    "stock": 50,
    "combinations": {
        "color": "Red",
        "size": "M"
    }
}
```

#### Success Response (New Variant)
- **Status Code:** 201 Created
- **Content-Type:** application/json

```json
{
    "message": "Variant created",
    "product_variant": {
        "product_variantid": 1,
        "variant_reference": "PV1234567890",
        "product_reference": "PROD123",
        "combinations": {
            "color": "Red",
            "size": "M"
        },
        "mrp_price": 1999,
        "selling_price": 1499,
        "stock": 50,
        "is_active": true,
        "created_date": "2023-01-01T12:00:00Z",
        "modified_date": "2023-01-01",
        "variant_version": "1.0.0"
    }
}
```

#### Success Response (Existing Variant Updated)
- **Status Code:** 200 OK
- **Content-Type:** application/json

```json
{
    "message": "Variant updated",
    "product_variant": {
        "product_variantid": 1,
        "variant_reference": "PV1234567890",
        "product_reference": "PROD123",
        "combinations": {
            "color": "Red",
            "size": "M"
        },
        "mrp_price": 1799,
        "selling_price": 1499,
        "stock": 30,
        "is_active": true,
        "created_date": "2023-01-01T12:00:00Z",
        "modified_date": "2023-01-02",
        "variant_version": "1.0.1"
    }
}
```

#### Error Responses
- **400 Bad Request**: Missing required fields or invalid data
- **404 Not Found**: Product not found
- **401 Unauthorized**: Invalid or missing authentication token
- **500 Internal Server Error**: Server error during variant creation/update

### 3. Delete Product Variant
Deletes a specific product variant by variant_reference.

**Endpoint:** `DELETE product/productvariants/?variant_reference=PV1234567890`

#### Query Parameters
| Parameter | Type   | Required | Description                          |
|-----------|--------|----------|--------------------------------------|
| variant_reference | string | Yes      | The reference ID of the variant      |

#### Example Request
```http
DELETE product/productvariants/?variant_reference=PV1234567890
Authorization: Bearer <your_jwt_token>
```

#### Success Response
- **Status Code:** 200 OK
- **Content-Type:** application/json

```json
{
    "message": "success"
}
```

#### Error Responses
- **400 Bad Request**: Missing or invalid variant_reference
- **404 Not Found**: Variant not found
- **401 Unauthorized**: Invalid or missing authentication token

## Notes
- The `combinations` field must be a valid JSON object
- Variant version is automatically incremented on updates
- Only active variants (is_active=True) are returned in the GET request
- The variant_reference is automatically generated by the system

## Versioning
- Version information is maintained in the `variant_version` field
- Version format follows Semantic Versioning (e.g., 1.0.0)
- Version is automatically updated when variant details are modified

