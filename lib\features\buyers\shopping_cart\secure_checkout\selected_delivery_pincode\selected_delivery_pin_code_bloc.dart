import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/user_address_services/user_address_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/services/user_profile/user_profile_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';


enum AvailableToState { Loading, Success, Failed, Empty }


class SelectedDeliveryPinCodeBloc {
  // region Common Variables
  BuildContext context;
   bool addNewAddress = false;
   bool selectAddress = false;
  ///User Address Service
  late UserAddressService userAddressService;
  final ShoppingCartBloc shoppingCartBloc;
  final SecureCheckoutBloc secureCheckoutBloc;

  // endregion

  //region Controller
  final availableToCtrl = StreamController<bool>.broadcast();


  //endregion

  //region Text ctrl
  final TextEditingController deliveryPinCode = TextEditingController();
  final FocusNode numberFocus = FocusNode();
  //endregion


  // region | Constructor |
  SelectedDeliveryPinCodeBloc(this.context, this.shoppingCartBloc, this.secureCheckoutBloc);

  // endregion

  // region Init
  init() async {
    userAddressService = UserAddressService();
    deliveryPinCode.text = BuyerHomeBloc.userDetailsResponse.userDetail!.pincode!;
    checkIsAddressAvailable();
  }

// endregion


//region OCheck ia address available
  void checkIsAddressAvailable()async {

    //Ui refresh
    availableToCtrl.sink.add(true);
    //Api

    //If selected pin code is null and postal pin code is contains in the address list
    if(secureCheckoutBloc.selectedAddress.useraddressid == null &&
        secureCheckoutBloc.shoppingCartAddressResponse.addressList!.any((e) => e.pincode == deliveryPinCode.text)
    ){
      addNewAddress = false;
      selectAddress = true;
      availableToCtrl.sink.add(true);
      return;
    }


    //If postal field and selected is address postal code are same
    if(secureCheckoutBloc.selectedAddress.pincode==deliveryPinCode.text){
      addNewAddress = false;
      selectAddress = false;
      availableToCtrl.sink.add(true);
      return;
    }

    //If delivery address list contains delivery pin code
    //Add that address is not selected
    //And selected address is not null
    if (
    secureCheckoutBloc.selectedAddress.useraddressid != null &&
    secureCheckoutBloc.shoppingCartAddressResponse.addressList!.any((e) => e.pincode == deliveryPinCode.text)
    &&
        secureCheckoutBloc.selectedAddress.pincode != deliveryPinCode.text
    ) {
      addNewAddress = false;
      selectAddress = true;
      availableToCtrl.sink.add(true);
      return;
    }


    //If postal code list does not contains postal field text
    if(!secureCheckoutBloc.shoppingCartAddressResponse.addressList!.any((e) => e.pincode == deliveryPinCode.text)){
      addNewAddress = true;
      selectAddress = false;
      availableToCtrl.sink.add(true);
      return;
    }

  }
//endregion






}
