import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/seller_add_product_detail_api_response/seller_add_product_response.dart';
import 'package:swadesic/model/seller_delivery_setting_response/seller_delivery_setting_response.dart';
import 'package:swadesic/model/seller_return_warranty_response/seller_return_warranty_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/seller_add_product_detail_services/seller_add_product_detail_services.dart';
import 'package:swadesic/services/seller_settings_services/seller_delivery_settings_service.dart';
import 'package:swadesic/services/seller_settings_services/seller_return_warranty_service.dart';
import 'package:swadesic/services/platform_file_upload_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/services/product_variant_service/product_variant_service.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';

enum ProductPreviewState { Loading, Success, Failed }

class ProductPreviewBloc {
  // region Common Variables
  BuildContext context;

  ///Add product
  late SellerAddProductDetailServices sellerAddProductDetailServices;
  late AddProductDetailResponse addProductDetailResponse;

  ///Delivery setting
  late SellerDeliverySettingsService sellerDeliverySettingsService;
  final SellerDeliveryStoreResponse sellerDeliveryStoreResponse;

  ///Return settings
  late SellerReturnWarrantySettingsService sellerReturnWarrantySettingsService;
  final SellerReturnWarrantyResponse? productLevelReturnSettingResponse;

  final Product product;

  // Selected variant for preview (defaults to first variant or no-variant data)
  ProductVariant? selectedVariant;

  late var uploadFileService = UploadFileService();
  final int? storeId;
  final String storeReference;
  // final StoreInfo storeInfo;

  // endregion

  //region Controller
  final sliderCtrl = StreamController<int>.broadcast();
  final publishCtrl = StreamController<ProductPreviewState>.broadcast();
  PageController imageSlider = PageController();
  //endregion

  // region | Constructor |
  ProductPreviewBloc(
      this.context,
      this.storeId,
      this.storeReference,
      this.sellerDeliveryStoreResponse,
      this.productLevelReturnSettingResponse,
      this.product);
  // endregion

  // region Init
  void init() {
    sellerAddProductDetailServices = SellerAddProductDetailServices();
    sellerDeliverySettingsService = SellerDeliverySettingsService();
    sellerReturnWarrantySettingsService = SellerReturnWarrantySettingsService();
    // addProductData = AddProductData();
    // storeProductResponse = StoreProductResponse();

    // Initialize selected variant for preview
    initializeSelectedVariant();
  }
// endregion

  //region Add product Api Call
  void addProductDetailApiCall() async {
    //region Try
    try {
      //print(AppConstants.appData.userId);
      publishCtrl.sink.add(ProductPreviewState.Loading);
      addProductDetailResponse = await sellerAddProductDetailServices
          .sellerAddProduct(storeReference: storeReference, product: product);

      //Upload image
      await startUpload();
      //Add delivery settings
      await addDeliveryStoreSettings();
      //Add return warranty
      await addReturnAndWarranty();
      //Create variants if any
      await createProductVariants();
      //Product is published
      CommonMethods.toastMessage(AppStrings.productIsPublished, context);
      
      // Navigate to product view screen
      if (context.mounted) {
        // Close all previous screens
        Navigator.popUntil(context, (route) => route.isFirst);
        // Navigate to product view screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BuyerViewStoreScreen(storeReference: storeReference),
          ),
        );
      } else {
        // Fallback to just closing the screen if context is not available
        goBackFromWhereItOpen();
      }
    }
    //endregion
    on ApiErrorResponseMessage catch (e) {
      context.mounted
          ? CommonMethods.toastMessage(e.message.toString(), context)
          : null;
      return;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;
      return;
    }
  }
  //endregion

  //region On Change Slider
  void onChangeSlider(int index) {
    sliderCtrl.sink.add(index);
  }
  //endregion

  ///Delivery settings
  //region Add Delivery Store Settings API Call
  Future<void> addDeliveryStoreSettings() async {
    //If null then return
    if (sellerDeliveryStoreResponse.message == null) {
      return;
    }
    //region Try
    try {
      publishCtrl.sink.add(ProductPreviewState.Loading);

      // Check if user selected "save as store default"
      if (sellerDeliveryStoreResponse.saveAsStoreDefault) {
        // Save as store-level settings (productId: null)
        await sellerDeliverySettingsService.addAddProductDeliverySetting(
          storeReference: storeReference,
          productLevelDeliverySettings: sellerDeliveryStoreResponse,
          productId: null, // null for store-level settings
        );
        print("=== PRODUCT PREVIEW: SAVED AS STORE DEFAULT ===");
      } else {
        // Save as product-level settings (with productId)
        await sellerDeliverySettingsService.addAddProductDeliverySetting(
          storeReference: storeReference,
          productLevelDeliverySettings: sellerDeliveryStoreResponse,
          productId: addProductDetailResponse.data!.productid!,
        );
        print("=== PRODUCT PREVIEW: SAVED AS PRODUCT LEVEL ===");
      }
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }

  //endregion
  ///Return warranty
  //region Create Return and warranty
  Future<void> addReturnAndWarranty() async {
    //Check is return warranty is null then return
    if (productLevelReturnSettingResponse!.message == null) {
      return;
    }
    //region Try
    try {
      // Check if user selected "save as store default"
      if (productLevelReturnSettingResponse!.saveAsStoreDefault) {
        // Save as store-level settings (productReference: null)
        await sellerReturnWarrantySettingsService
            .addReturnWarrantyStoreSettings(
                storeReference: storeReference,
                productReference: null, // null for store-level settings
                productLevelSettingResponse:
                    productLevelReturnSettingResponse!);
        print(
            "=== PRODUCT PREVIEW: RETURN SETTINGS SAVED AS STORE DEFAULT ===");
      } else {
        // Save as product-level settings (with productReference)
        await sellerReturnWarrantySettingsService
            .addReturnWarrantyStoreSettings(
                storeReference: storeReference,
                productReference:
                    addProductDetailResponse.data!.productReference,
                productLevelSettingResponse:
                    productLevelReturnSettingResponse!);
        print(
            "=== PRODUCT PREVIEW: RETURN SETTINGS SAVED AS PRODUCT LEVEL ===");
      }
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(error.toString(), context);
      return;
    }
  }
  //endregion

  //region Go back from it comes from
  goBackFromWhereItOpen() {
    // Clear images for both platforms
    AppConstants.multipleSelectedImage.clear();
    AppConstants.webProductImages.clear();
    Navigator.pop(context);
    Navigator.pop(context);
  }
  //endregion

  // region Start Upload
  Future<void> startUpload() async {
    try {
      if (kIsWeb) {
        // For web platform
        // Use PlatformFileUploadService to upload web images
        final platformFileUploadService = PlatformFileUploadService();

        for (var webImage in AppConstants.webProductImages) {
          await platformFileUploadService.uploadProductImage(
            productReference: addProductDetailResponse.data!.productReference!,
            bytes: webImage['bytes'] as Uint8List,
            fileName: webImage['name'] as String,
          );
        }
      } else {
        // For mobile platform
        // Upload Multiple file
        for (int i = 0; i < AppConstants.multipleSelectedImage.length; i++) {
          await uploadFileService.uploadProductImage(
            filePath: AppConstants.multipleSelectedImage[i].path,
            url:
                "${AppConstants.baseUrl}/product/productimages/${addProductDetailResponse.data!.productReference}/",
            fileNameWithExtension:
                AppConstants.multipleSelectedImage.first.name,
            parameter: {},
          );
        }
      }
    } on ApiErrorResponseMessage {
      publishCtrl.sink.add(ProductPreviewState.Failed);
      // Store context in a local variable to avoid BuildContext across async gaps warning
      final currentContext = context;
      if (currentContext.mounted) {
        CommonMethods.toastMessage(
            AppStrings.productImageUnableToAdd, currentContext);
      }
      return;
    } catch (error) {
      publishCtrl.sink.add(ProductPreviewState.Failed);
      // Store context in a local variable to avoid BuildContext across async gaps warning
      final currentContext = context;
      if (currentContext.mounted) {
        CommonMethods.toastMessage(
            AppStrings.productImageUnableToAdd, currentContext);
      }
      return;
    }
  }

// endregion

  //region Go to Buyer Product Detail Screen
  void buyerProductDetail() {
    return;
    // final DateTime now = DateTime.now();
    // final DateFormat formatter = DateFormat('dd:MM:yyyy');
    // final String formatted = formatter.format(now);
    // var screen = BuyerProductDetailsScreen(
    //   productCategory:AddEditProductFieldsBloc.productCategoryTextCtrl.text,
    //   productDesc:AddEditProductFieldsBloc.productDescNameTextCtrl.text ,
    //   productVersion:"1.0.0",
    //   url:AddEditProductFieldsBloc.promoLinkTextCtrl.text,
    //   productReference:"",
    //   updatedDate:"$formatted 00:00:00",
    //   storeReference:"",
    //   createdDate: "$formatted 00:00:00",
    //   returnPeriod:5 ,
    //   isAskSellerVisible: false,
    // );
    // var route = CupertinoPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }
//endregion

  //region Create Product Variants
  Future<void> createProductVariants() async {
    try {
      // Check if product has variants to create
      if (product.variants != null && product.variants!.isNotEmpty) {
        final productVariantService = ProductVariantService();
        final productReference = addProductDetailResponse.data?.productReference;

        if (productReference != null) {
          // Create each variant
          for (var variantJson in product.variants!) {
            final variant = ProductVariant.fromJson(variantJson);

            await productVariantService.createOrUpdateVariant(
              productReference: productReference,
              mrpPrice: variant.mrpPrice,
              sellingPrice: variant.sellingPrice,
              stock: variant.stock,
              combinations: variant.combinations,
            );
          }
        }
      }
    } catch (e) {
      // Log error but don't fail the entire product creation
      print("Error creating variants: $e");
    }
  }
  //endregion

  //region Initialize Selected Variant
  void initializeSelectedVariant() {
    try {
      if (product.variants != null && product.variants!.isNotEmpty) {
        // Convert variants from JSON
        final variants = product.variants!
            .map((variantJson) {
              try {
                return ProductVariant.fromJson(variantJson);
              } catch (e) {
                print("Error parsing variant: $e");
                return null;
              }
            })
            .where((variant) => variant != null)
            .cast<ProductVariant>()
            .toList();

        if (variants.isNotEmpty) {
          // If product has options, select first variant with combinations
          if (product.options != null && product.options!.isNotEmpty) {
            selectedVariant = variants.firstWhere(
              (variant) => variant.combinations.isNotEmpty,
              orElse: () => variants.first,
            );
          } else {
            // If no options, select the no-variant data (empty combinations)
            try {
              selectedVariant = variants.firstWhere(
                (variant) => variant.combinations.isEmpty,
              );
            } catch (e) {
              // If no variant with empty combinations found, use first variant
              selectedVariant = variants.isNotEmpty ? variants.first : null;
            }
          }
        }
      }
    } catch (e) {
      print("Error initializing selected variant: $e");
      selectedVariant = null;
    }
  }
  //endregion

  //region Get Display Pricing
  int getDisplayMrpPrice() {
    if (selectedVariant != null) {
      return selectedVariant!.mrpPrice;
    }
    // Fallback to product-level data or default value
    return product.mrpPrice ?? 999;
  }

  int getDisplaySellingPrice() {
    if (selectedVariant != null) {
      return selectedVariant!.sellingPrice;
    }
    // Fallback to product-level data or default value
    return product.sellingPrice ?? 799;
  }

  int getDisplayStock() {
    if (selectedVariant != null) {
      return selectedVariant!.stock;
    }
    // Fallback to product-level data or default value
    return product.inStock ?? 10;
  }
  //endregion
}
