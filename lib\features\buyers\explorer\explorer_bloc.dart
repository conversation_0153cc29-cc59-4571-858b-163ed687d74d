import 'dart:async';
import 'package:flutter/material.dart';

class ExplorerBloc {
  // region Common Variables
  BuildContext context;
  bool openForOrder = false;

  // endregion


  //region Controller
  final storeOnlineCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  ExplorerBloc(this.context);
  // endregion

  // region Init
  void init() {


  }
// endregion
  //region On Tap Search field
  void onTapSearchField(){
    // var screen = const BuyerSearchScreen();
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }
//endregion



}
