import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class SearchInitialUserAndStoreBloc{
  //region Common variable
  late BuildContext context;
  final TabController tabController;
  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final refreshTabCtrl = StreamController<bool>.broadcast();
  final  ScrollController scrollController   = ScrollController();

//endregion
  //region Constructor
  SearchInitialUserAndStoreBloc(this.context, this.tabController);
  //endregion
//region Init
  void init(){
    tabController.addListener(() {
      refreshTabCtrl.sink.add(true);
    });
  }
//endregion


//region Dispose
void dispose(){
  tabController.dispose();
}
//endregion


}