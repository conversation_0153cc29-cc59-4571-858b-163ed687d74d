// import 'package:expandable/expandable.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_rating_bar/flutter_rating_bar.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_common_widget.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_bloc.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/components/confirmed_not_yet_shipped/confirmed_not_yet_shipped_bloc.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/delivered_successfully_bloc.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/not_delivered/not_delivered_bloc.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/rate_review/rate_review_bloc.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/return/return_bloc.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/components/shipping_in_progress/shipping_in_progress_bloc.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/components/waiting_confirmation/waiting_confirmation_bloc.dart';
// import 'package:swadesic/model/order_response/get_order_response.dart';
// import 'package:swadesic/util/app_colors.dart';
// import 'package:swadesic/util/app_constants.dart';
// import 'package:swadesic/util/app_images.dart';
// import 'package:swadesic/util/app_strings.dart';
// import 'package:swadesic/util/common_widgets.dart';
// import 'package:swadesic/model/order_response/sub_order.dart';
//
//
// class Return extends StatefulWidget {
//   final SubOrder selectedSubOrder;
//   final Order store;
//   final BuyerMyOrdersBloc buyerMyOrdersBloc;
//
//   const Return({Key? key, required this.selectedSubOrder, required this.store, required this.buyerMyOrdersBloc}) : super(key: key);
//
//   @override
//   State<Return> createState() => _ReturnState();
// }
//
// class _ReturnState extends State<Return> {
//   // region Bloc
//   late ReturnBloc returnBloc;
//
//   // endregion
//
//   // region Init
//   @override
//   void initState() {
//     //print(widget.selectedSubOrder.productName);
//     returnBloc = ReturnBloc(context,widget.store);
//     returnBloc.init();
//     super.initState();
//   }
//
//   // endregion
//
//   @override
//   Widget build(BuildContext context) {
//     return SingleChildScrollView(
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           inThisProcess(),
//           verticalSizedBox(20),
//           refundAmountCalculation(),
//           returnReason(),
//           whichOfThese(),
//           isThisProduct(),
//           verticalSizedBox(20),
//           sellerNote(),
//         ],
//       ),
//     );
//   }
//
// //region in this process
// Widget inThisProcess(){
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: appText(
//           AppStrings.inThisProcess,
//           color: AppColors.writingColor2,
//           fontWeight: FontWeight.w600,
//           fontFamily: AppConstants.rRegular,
//           fontSize: 16,
//           maxLine: 100),
//     );
// }
// //endregion
//
//
//   //region Refund amount calculation
//   Widget refundAmountCalculation(){
//     return Container(
//       color: AppColors.lightestGrey,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Container(
//             padding: EdgeInsets.all(10),
//             child: Row(
//               mainAxisSize: MainAxisSize.max,
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               crossAxisAlignment: CrossAxisAlignment.center,
//               children: [
//                 appText("Refund amount calculation",color:AppColors.writingColor2,fontWeight: FontWeight.w600,fontFamily:AppConstants.rRegular,fontSize:16),
//                 Expanded(child: horizontalSizedBox(10)),
//                 appText("Know more",color:AppColors.writingColor3,fontWeight: FontWeight.w400,fontFamily:AppConstants.rRegular,fontSize:12),
//
//               ],
//             ),
//           ),
//           verticalSizedBox(10),
//           titleAndPrice(title: "Product refund",price: "1500"),
//           verticalSizedBox(10),
//           titleAndPrice(title: "Shipping fee reversal",price: "1500"),
//           verticalSizedBox(10),
//           titleAndPrice(title: "Promotions discount adjustment",price: "1500"),
//           Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 60,vertical: 10),
//             child: divider(),
//           ),
//           grandAndSubTotal(title: "Sub total",price: "1500"),
//           verticalSizedBox(10),
//           titleAndPrice(title: "Transaction fees (3%)",price: "1500"),
//           promotionBox(),
//           grandAndSubTotal(title: "Total refund amount",price: "1500"),
//           verticalSizedBox(20),
//
//
//
//
//
//
//
//         ],
//       ),
//     );
//   }
// //endregion
//
//
//
//
//   ///Return reason
//   //region Return reason
//   Widget returnReason(){
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       mainAxisAlignment: MainAxisAlignment.start,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 20),
//           child: checkBoxHeading("Return reasons"),
//         ),
//         //Product looks different
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//             SizedBox(
//                 height: 20,
//                 width: 20,
//                 child: Checkbox(
//                     activeColor: AppColors.primaryGreen,
//                     value: true, onChanged:(value){})),
//               horizontalSizedBox(10),
//               checkBoxText("Product looks different")
//
//           ],),
//         ),
//         verticalSizedBox(10),
//         //I didn’t like it
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               SizedBox(
//                   height: 20,
//                   width: 20,
//                   child: Checkbox(
//                       activeColor: AppColors.primaryGreen,
//                       value: true, onChanged:(value){})),
//               horizontalSizedBox(10),
//               checkBoxText("I didn’t like it")
//
//             ],),
//         ),
//         verticalSizedBox(10),
//         //I received a defective product
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               SizedBox(
//                   height: 20,
//                   width: 20,
//                   child: Checkbox(
//                       activeColor: AppColors.primaryGreen,
//                       value: true, onChanged:(value){})),
//               horizontalSizedBox(10),
//               checkBoxText("I received a defective product")
//
//             ],),
//         ),
//         verticalSizedBox(10),
//         //Blank under line
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               SizedBox(
//                   height: 20,
//                   width: 20,
//                   child: Checkbox(
//                       activeColor: AppColors.primaryGreen,
//                       value: true, onChanged:(value){})),
//               horizontalSizedBox(10),
//               Expanded(child: underLineTextField(context: context, textFieldCtrl: TextEditingController(), textFieldHint: "", textFieldMaxLine: 1, keyboardType:TextInputType.text , textInputAction:TextInputAction.done))
//
//             ],),
//         ),
//         verticalSizedBox(10),
//
//       ],
//     );
//   }
//   //endregion
//
//   ///Which of these return conditions satisfied?
//   //region Which of these return conditions satisfied?
//   Widget whichOfThese(){
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       mainAxisAlignment: MainAxisAlignment.start,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 20),
//           child: checkBoxHeading("Which of these return conditions satisfied?"),
//         ),
//         //lightly worn
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               SizedBox(
//                   height: 20,
//                   width: 20,
//                   child: Checkbox(
//                       activeColor: AppColors.primaryGreen,
//                       value: true, onChanged:(value){})),
//               horizontalSizedBox(10),
//               checkBoxText("lightly worn")
//
//             ],),
//         ),
//         verticalSizedBox(10),
//         //with tags still on
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               SizedBox(
//                   height: 20,
//                   width: 20,
//                   child: Checkbox(
//                       activeColor: AppColors.primaryGreen,
//                       value: true, onChanged:(value){})),
//               horizontalSizedBox(10),
//               checkBoxText("with tags still on")
//
//             ],),
//         ),
//         verticalSizedBox(10),
//         //seal not opened
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               SizedBox(
//                   height: 20,
//                   width: 20,
//                   child: Checkbox(
//                       activeColor: AppColors.primaryGreen,
//                       value: true, onChanged:(value){})),
//               horizontalSizedBox(10),
//               checkBoxText("seal not opened")
//
//             ],),
//         ),
//         verticalSizedBox(10),
//
//       ],
//     );
//   }
//   //endregion
//
//   ///Is this product delivered in good condition?
//
//   //region Is this product delivered in good condition?
//   Widget isThisProduct(){
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       mainAxisAlignment: MainAxisAlignment.start,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 20),
//           child: checkBoxHeading("Is this product delivered in good condition?"),
//         ),
//         //lightly worn
//         Row(
//           children: [
//             //yes
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 20),
//               child: Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   SizedBox(
//                       height: 20,
//                       width: 20,
//                       child: Checkbox(
//                           activeColor: AppColors.primaryGreen,
//                           value: true, onChanged:(value){})),
//                   horizontalSizedBox(10),
//                   checkBoxText("Yes")
//
//                 ],),
//             ),
//             //no
//             Padding(
//               padding: const EdgeInsets.only(right: 20),
//               child: Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   SizedBox(
//                       height: 20,
//                       width: 20,
//                       child: Checkbox(
//                           activeColor: AppColors.primaryGreen,
//                           value: true, onChanged:(value){})),
//                   horizontalSizedBox(10),
//                   checkBoxText("No")
//
//                 ],),
//             ),
//             //Partially good
//             Padding(
//               padding: const EdgeInsets.only(right: 20),
//               child: Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   SizedBox(
//                       height: 20,
//                       width: 20,
//                       child: Checkbox(
//                           activeColor: AppColors.primaryGreen,
//                           value: true, onChanged:(value){})),
//                   horizontalSizedBox(10),
//                   checkBoxText("Partially good")
//
//                 ],),
//             ),
//           ],
//         ),
//
//
//
//       ],
//     );
//   }
//   //endregion
//
//   ///Seller note and field
//   //region Seller note
//   Widget sellerNote() {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Container(
//               alignment: Alignment.centerLeft,
//               child:checkBoxHeading("Note to seller")
//           ),
//           verticalSizedBox(10),
//           colorFilledTextField(
//             context: context,
//             textFieldCtrl: returnBloc.notesToSeller,
//             hintText: "write..",
//             hintFontSize: 14,
//             textFieldMaxLine: 4,
//             keyboardType: TextInputType.name,
//             textInputAction: TextInputAction.done,
//             // onChangeText: sellerOnBoardingBloc.onTextChange,
//             regExp: AppConstants.onlyStringWithSpace,
//             fieldTextCapitalization: TextCapitalization.words,
//             maxCharacter: 50,
//           ),
//           verticalSizedBox(10),
//           appText(AppStrings.refundAmount,color: AppColors.writingColor2,fontWeight: FontWeight.w400,fontFamily:AppConstants.rRegular,fontSize: 14,maxLine: 100 ),
//           verticalSizedBox(10),
//           appText("send amount to another account",color: AppColors.writingColor2,fontWeight: FontWeight.w400,fontFamily:AppConstants.rRegular,fontSize: 14,maxLine: 100,isUnderLine: true ),
//          verticalSizedBox(10),
//           Row(
//             children: [
//               buyerMyOrderCancelButton(buttonName:"Proceed to return",buttonColor: AppColors.lightGray,onPress: (){
//                 returnBloc.returnProduct(subOrderNumber:widget.selectedSubOrder.suborderNumber!, buyerMyOrdersBloc: widget.buyerMyOrdersBloc);
//               }),
//             ],
//           )
//
//
//         ],
//       ),
//     );
//   }
//   //endregion
//
//
//
//
//   //region Title and price
//   Widget titleAndPrice({required String title, required String price} ){
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Row(
//         mainAxisSize: MainAxisSize.max,
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           appText(title,color:AppColors.writingBlack,fontWeight: FontWeight.w600,fontFamily:AppConstants.rRegular,fontSize:15),
//           Expanded(child: horizontalSizedBox(10)),
//           appText("₹$price",color:AppColors.writingBlack,fontWeight: FontWeight.w600,fontFamily:AppConstants.rRegular,fontSize:15),
//
//         ],
//       ),
//     );
//   }
// //endregion
//
//
//
//   //region Promotion box
//   Widget promotionBox(){
//     return Container(
//       margin: EdgeInsets.all(20),
//       padding: EdgeInsets.all(10),
//       width: double.infinity,
//       decoration: BoxDecoration(
//           color: AppColors.lightWhite3,
//           border: Border.all(color: AppColors.lightStroke)
//       ),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.center,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           appText("Promotions discount adjustment",color:AppColors.writingBlack,fontWeight: FontWeight.w600,fontFamily:AppConstants.rRegular,fontSize:14),
//           verticalSizedBox(10),
//           appText("Applied promotion was:  Free delivery for purchaising more than 5 products",color:AppColors.writingBlack,fontWeight: FontWeight.w400,fontFamily:AppConstants.rRegular,fontSize:14,maxLine: 4),
//
//
//         ],
//       ),
//     );
//   }
//   //endregion
//
//
//
// //region Grand and sub total
//   Widget grandAndSubTotal({required title, required price}){
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Row(
//         mainAxisSize: MainAxisSize.max,
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           appText(title,color:AppColors.writingBlack,fontWeight: FontWeight.w700,fontFamily:AppConstants.rRegular,fontSize:15),
//           Expanded(child: horizontalSizedBox(10)),
//           appText("₹${price}",color:AppColors.writingBlack,fontWeight: FontWeight.w700,fontFamily:AppConstants.rRegular,fontSize:15),
//
//         ],
//       ),
//     );
//   }
// //endregion
//
//
//
// //region Checkbox heading
// Widget checkBoxHeading(String heading){
//     return appText(heading,color:AppColors.appBlack,fontWeight: FontWeight.w400,fontFamily:AppConstants.rRegular,fontSize:16,opacity: 0.7);
// }
// //endregion
//
//
// //region Checkbox text
//   Widget checkBoxText(String heading){
//     return appText(heading,color:AppColors.writingColor2,fontWeight: FontWeight.w400,fontFamily:AppConstants.rRegular,fontSize:15);
//   }
// //endregion
//
// }
