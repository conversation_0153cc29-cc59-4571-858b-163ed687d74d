import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductDetailCommonWidget{

  //region Heading
  static Widget heading({required String heading}){
    return appText(heading,fontFamily: AppConstants.rRegular,color: AppColors.appBlack,fontSize: 16,fontWeight: FontWeight.w700,maxLine: 100);
  }
//endregion

  //region Title
  static Widget title({required String title}){
    return Container(
        margin: const EdgeInsets.only(bottom: 5),
        child: Text(title,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),));
  }
//endregion


  //region Sub Title
  static Widget subTitle({required String subtitle,double bottomMargin = 20 }){
    return Container(
        alignment: Alignment.centerLeft,
        margin: EdgeInsets.only(bottom: bottomMargin),
        child: Text(subtitle,
          maxLines: 5,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),));
  }
//endregion
}