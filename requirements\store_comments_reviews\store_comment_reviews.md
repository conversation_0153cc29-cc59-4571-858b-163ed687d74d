# Store Comments Reviews
API to fetch all the comments and reviews of the store and its products



## APIs

### Fetch Store Comments Reviews
```bash
curl --location '{base_url}/lean/get_store_comments/?limit=100&offset=0&visitor_reference=U1719579800140&entity_reference=S1721930951916&comment_types=EXTERNAL_REVIEW%2CCOMMENT%2CREVIEW%2CQUESTION' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>'

```

Response:
success response:
```json
{
    "message": "success",
    "data": [
        {
            "comment_reference": "CO202506181815338817",
            "comment_text": "My comment for product 3",
            "created_date": "2025-06-18 18:15:33.885937+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 0,
            "rating_count": null,
            "level": 1,
            "comment_type": "COMMENT",
            "main_parent_id": "P1748859602906626RGWO",
            "commenter_reference": "U1744450273600",
            "user_reference": "U1744450273600",
            "store_reference": null,
            "tagged_references_json": [
                {
                    "type": "USER",
                    "order": 1,
                    "reference": "U1749293572687"
                }
            ],
            "tagged_users_count": 1,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text": null,
            "content_headers": [],
            "reviewed_reference_json": [],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT",
            "icon": "/media/profile_image/er_1748858846831.jpg",
            "handle": "krishna_k",
            "name": "Krishna Kanth ",
            "reference": "U1744450273600"
        },
        {
            "comment_reference": "CO202506181815296192",
            "comment_text": "My comment for product 2",
            "created_date": "2025-06-18 18:15:29.518705+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 0,
            "rating_count": null,
            "level": 1,
            "comment_type": "REVIEW",
            "main_parent_id": "P1748859602906626RGWO",
            "commenter_reference": "U1744450273600",
            "user_reference": "U1744450273600",
            "store_reference": null,
            "tagged_references_json": [
                {
                    "type": "USER",
                    "order": 1,
                    "reference": "U1749293572687"
                }
            ],
            "tagged_users_count": 1,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text": "verified review @hanuman_coderz/vegeta-poster",
            "content_headers": [
                {
                    "handle": "@hanuman_coderz/vegeta-poster",
                    "reference": "P1748859602906626RGWO"
                }
            ],
            "reviewed_reference_json": [
                {
                    "icon": "post_images/1748859494851.jpg",
                    "name": "Vegeta poster-Hanuman coders",
                    "type": "PRODUCT",
                    "order": 1,
                    "handle": "@hanuman_coderz",
                    "reference": "P1748859602906626RGWO"
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT",
            "icon": "/media/profile_image/er_1748858846831.jpg",
            "handle": "krishna_k",
            "name": "Krishna Kanth ",
            "reference": "U1744450273600"
        },
        {
            "comment_reference": "CO202506181815246765",
            "comment_text": "My comment for product 1",
            "created_date": "2025-06-18 18:15:24.454086+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 0,
            "rating_count": null,
            "level": 1,
            "comment_type": "COMMENT",
            "main_parent_id": "P1748859602906626RGWO",
            "commenter_reference": "U1744450273600",
            "user_reference": "U1744450273600",
            "store_reference": null,
            "tagged_references_json": [
                {
                    "type": "USER",
                    "order": 1,
                    "reference": "U1749293572687"
                }
            ],
            "tagged_users_count": 1,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text": null,
            "content_headers": [],
            "reviewed_reference_json": [],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT",
            "icon": "/media/profile_image/er_1748858846831.jpg",
            "handle": "krishna_k",
            "name": "Krishna Kanth ",
            "reference": "U1744450273600"
        },
        {
            "comment_reference": "CO202506181815163934",
            "comment_text": "My comment for product",
            "created_date": "2025-06-18 18:15:16.208258+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 0,
            "rating_count": null,
            "level": 1,
            "comment_type": "COMMENT",
            "main_parent_id": "P1748859602906626RGWO",
            "commenter_reference": "U1744450273600",
            "user_reference": "U1744450273600",
            "store_reference": null,
            "tagged_references_json": [
                {
                    "type": "USER",
                    "order": 1,
                    "reference": "U1749293572687"
                }
            ],
            "tagged_users_count": 1,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text": null,
            "content_headers": [],
            "reviewed_reference_json": [],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT",
            "icon": "/media/profile_image/er_1748858846831.jpg",
            "handle": "krishna_k",
            "name": "Krishna Kanth ",
            "reference": "U1744450273600"
        },
        {
            "comment_reference": "CO202504141918241770",
            "comment_text": "My review local",
            "created_date": "2025-04-14 19:18:24.214682+05:30",
            "is_deleted": false,
            "like_count": 0,
            "comment_count": 0,
            "repost_count": 0,
            "repost_plus_count": 0,
            "save_count": 0,
            "share_count": 0,
            "analytics_view_count": 1,
            "rating_count": 4.0,
            "level": 1,
            "comment_type": "EXTERNAL_REVIEW",
            "main_parent_id": "P1748859602906626RGWO",
            "commenter_reference": "U1744450273600",
            "user_reference": "U1744450273600",
            "store_reference": null,
            "tagged_references_json": [
                {
                    "type": "USER",
                    "order": 1,
                    "reference": "U1749293572687"
                }
            ],
            "tagged_users_count": 1,
            "tagged_stores_count": 0,
            "tagged_products_count": 0,
            "comment_images": [],
            "content_header_text": "external review @hanuman_coderz/vegeta-poster",
            "content_headers": [
                {
                    "handle": "@hanuman_coderz/vegeta-poster",
                    "reference": "P1748859602906626RGWO"
                }
            ],
            "reviewed_reference_json": [
                {
                    "icon": "post_images/1748859494851.jpg",
                    "name": "Vegeta poster-Hanuman coders",
                    "type": "PRODUCT",
                    "order": 1,
                    "handle": "@hanuman_coderz",
                    "reference": "P1748859602906626RGWO"
                }
            ],
            "save_status": false,
            "repost_status": false,
            "content_category": "POST",
            "like_status": false,
            "content_type": "COMMENT",
            "icon": "/media/profile_image/er_1748858846831.jpg",
            "handle": "krishna_k",
            "name": "Krishna Kanth ",
            "reference": "U1744450273600"
        }
    ]
}
```
failure response:
```json
{
    "message": "Invalid comment type"
}
```

