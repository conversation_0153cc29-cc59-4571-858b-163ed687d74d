import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:swadesic/model/label_data/label_data.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class ProductLabelsDetailBloc{
  //region Common variable
  late BuildContext context;
  List<Label> ownedList = [];
  List<Label> brandList = [];
  List<Label> madeList = [];
  bool isDropDownVisible = false;
  List<TableRow> tableRowList = [];
  final bool isMade;
  final bool isBrand;
  final bool isOwned;
  final String currentFlag;


  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final refreshLabelCtrl = StreamController<bool>.broadcast();
//endregion
  //region Constructor
  ProductLabelsDetailBloc(this.context, this.isMade, this.isBrand, this.isOwned, this.currentFlag);
  //endregion
//region Init
  void init(){

    //Add data to list
    addDataToList();
    //Add table row
    addTabRow();

  }
//endregion

  //region Add data to the list
  void addDataToList(){
    //Owned data
    ownedList.add(Label("","Fully Swadeshi",3.0,"Businesses that are fully Indian-owned and operated",""));
    ownedList.add(Label("","Mainly Swadeshi",2.0,"Businesses with a majority Indian ownership (above 50% equity) but may have some foreign investment or involvement",""));
    ownedList.add(Label("","Partially Swadeshi",1.0,"Businesses with a minority Indian ownership (below 50% equity) and majority foreign ownership",""));
    ownedList.add(Label("","Videshi",0.0,"Businesses that are entirely foreign-owned",""));

//Brand data
    brandList.add(Label("","Fully Swadeshi",3.0,"Brands that are fully Indian-owned and operated",""));
    brandList.add(Label("","Mainly Swadeshi",2.0,"Brands with a majority Indian ownership (above 50% equity) but may have some foreign investment or involvement",""));
    brandList.add(Label("","Partially Swadeshi",1.0,"Brands with a minority Indian ownership (below 50% equity) and majority foreign ownership",""));
    brandList.add(Label("","Videshi",0.0,"Brands that are entirely foreign-owned",""));

//Made data
    madeList.add(Label("","Fully Swadeshi",3.0,"Products made entirely in India, using local materials and labor.",""));
    madeList.add(Label("","Mainly Swadeshi",2.0,"Products mostly made in India with some foreign materials and labor.",""));
    madeList.add(Label("","Partially Swadeshi",1.0,"Products assembled in India with local labor with mostly foreign components",""));
    madeList.add(Label("","Videshi",0.0,"Products entirely made outside India, using foreign materials and labor.",""));

  }
  //endregion

  //region Add Tab row
  void addTabRow(){
    //Adding first default row
    tableRowList.add( TableRow(
      decoration: const BoxDecoration(color:AppColors.textFieldFill1,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.0),
          topRight: Radius.circular(8.0),// Adjust the radius as needed
        ),
      ),
      children: [
        TableCell(
          child: Container(
              margin: const EdgeInsets.all(10),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(isMade?AppStrings.swadeshiMadeLabel

                      :isBrand?AppStrings.swadeshiBrandLabel

                      :AppStrings.swadeshiOwnedLabel,

                    style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
                  // Text("${currentFlag} label",
                  //
                  //   style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
                  Text(AppStrings.explanation,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),),
                ],
              )),

        ),
        // TableCell(
        //   child: Container(
        //       margin: const EdgeInsets.all(10),
        //       child: Text(AppStrings.explanation,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),)),
        // ),
      ],
    ),);
    //If isMade or isBrand or isOwned
      for(var data in isMade?madeList:isBrand?brandList:isOwned?ownedList:ownedList){
        tableRowList.add( TableRow(
          children: [
            TableCell(
              child:  Container(

                  margin: const EdgeInsets.all(10),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(data.labelName,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
                      Text(data.labelDetail,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),),
                    ],
                  )),

            ),
            // TableCell(
            //   child:  Container(
            //       margin: const EdgeInsets.all(10),
            //       child: Text(data.labelDetail,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),)),
            // ),
          ],
        ),);
      }

    
  }
  //endregion


//region Dispose
  void dispose(){
    refreshLabelCtrl.close();
  }
//endregion
}