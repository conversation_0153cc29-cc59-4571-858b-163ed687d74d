import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_payment/buyer_payment_status/buyer_payment_status_bloc.dart';
import 'package:swadesic/features/buyers/buyer_payment/buyer_payment_status/buyer_payment_status_common_widgets.dart';
import 'package:swadesic/model/buyer_payment_options_responses/payment_status_check.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Buyer Payment Status
class BuyerPaymentStatus extends StatefulWidget {
  final String paymentStatus;
  final PaymentStatusCheckResponse paymentStatusCheckResponse;

  const BuyerPaymentStatus(
      {Key? key,
      required this.paymentStatus,
      required this.paymentStatusCheckResponse})
      : super(key: key);

  @override
  _BuyerPaymentStatusState createState() => _BuyerPaymentStatusState();
}
// endregion

class _BuyerPaymentStatusState extends State<BuyerPaymentStatus> {
  // region Bloc
  late BuyerPaymentStatusBloc buyerPaymentStatusBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerPaymentStatusBloc =
        BuyerPaymentStatusBloc(context, widget.paymentStatusCheckResponse);
    buyerPaymentStatusBloc.init();
    super.initState();
  }

  //region Dispose
  @override
  void dispose() {
    imageCache.clear();
    super.dispose();
  }
  //endregion

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: SafeArea(child: body()),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.orderStatus,
        isCartVisible: false,
        isMembershipVisible: false);

    // return AppBar(
    //   backgroundColor: AppColors.white,
    //   leading: CupertinoButton(
    //       onPressed: () {
    //         shoppingCartBloc.goBack();
    //         // onTapLeading();
    //       },
    //       padding: EdgeInsets.zero,
    //       child: SvgPicture.asset(AppImages.backButton, color: AppColors.appBlack, fit: BoxFit.fill)),
    //
    //   titleSpacing: 0,
    //
    //   centerTitle: false,
    //   title: Text(
    //     AppStrings.shoppingCart,
    //     style: const TextStyle(
    //       fontFamily: "LatoBold",
    //       fontSize: 19,
    //       fontWeight: FontWeight.w700,
    //       color: AppColors.appBlack,
    //     ),
    //   ),
    //   elevation: 0,
    //   automaticallyImplyLeading: false,
    //   //region Next Button
    //
    //   actions: [
    //     InkWell(
    //         onTap: () {
    //           //addProductBloc.goBackToSellerHomeScreen();
    //         },
    //         //padding: EdgeInsets.zero,
    //         child: SvgPicture.asset(
    //           AppImages.messageIcon,
    //           fit: BoxFit.contain,
    //           color: AppColors.appBlack,
    //         )),
    //
    //     ///Drawer
    //     InkWell(
    //       // padding: EdgeInsets.zero,
    //       onTap: () {
    //         shoppingCartBloc.onReportAndSuggest();
    //       },
    //       child: Container(
    //         padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
    //         margin: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
    //         child: SvgPicture.asset(AppImages.drawerIcon),
    //       ),
    //     )
    //   ],
    //
    //   //endregion
    // );
    //
    // return buyerAppBar(
    //   leadingVisible: true,
    //     onTapDrawer:shoppingCartBloc.goBack(),
    //     leadingIcon:SvgPicture.asset(
    //     AppImages.backButton,color: AppColors.appBlack,fit: BoxFit.fill),
    //     context: context,drawerIconEnable: true,titleText:AppStrings.shoppingCart,
    // basketVisible: false
    // );
  }

// endregion

  //region Body
  body() {
    return Column(
      children: [
        card(),
        info(),
      ],
    );
  }
//endregion

//region Card
  Widget card() {
    //Success
    if (widget.paymentStatus == 'PAYMENT_SUCCESS') {
      return BuyerPaymentStatusCommonWidgets.paymentStatusCard(
          status: widget.paymentStatus,
          paymentStatusCheckResponse: widget.paymentStatusCheckResponse,
          onTapView: () {
            buyerPaymentStatusBloc.goToBuyerMyOrder();
          },
          context: context);
    }
    //Waiting
    if (widget.paymentStatus == 'PAYMENT_PENDING') {
      return BuyerPaymentStatusCommonWidgets.paymentStatusCard(
          status: widget.paymentStatus,
          paymentStatusCheckResponse: widget.paymentStatusCheckResponse,
          onTapView: () {
            buyerPaymentStatusBloc.goToBuyerMyOrder();
          },
          context: context);
    }
    //Waiting
    if (widget.paymentStatus == 'PAYMENT_CANCELLED') {
      return BuyerPaymentStatusCommonWidgets.paymentStatusCard(
          status: widget.paymentStatus,
          paymentStatusCheckResponse: widget.paymentStatusCheckResponse,
          onTapView: () {
            buyerPaymentStatusBloc.goToBuyerMyOrder();
          },
          context: context);
    }
    return Container();
  }
//endregion

//region Info
  Widget info() {
    if (widget.paymentStatus == 'PAYMENT_CANCELLED') {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: Container(
                width: double.infinity,
                alignment: Alignment.center,
                margin: const EdgeInsets.symmetric(horizontal: 17),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100),
                    color: AppColors.brandBlack),
                padding: const EdgeInsets.symmetric(vertical: 15.5),
                child: SizedBox(
                    child: Text(
                  AppStrings.goBackToHome,
                  style: AppTextStyle.heading1Medium(
                      textColor: AppColors.appWhite),
                ))),
          ),
          // verticalSizedBox(10),
          // Container(
          //     width: double.infinity,
          //     alignment: Alignment.center,
          //     margin:const  EdgeInsets.symmetric(horizontal: 17),
          //     decoration: BoxDecoration(
          //         borderRadius: BorderRadius.circular(100),
          //         color: AppColors.brandGreen
          //     ),
          //     padding: const EdgeInsets.symmetric(vertical: 15.5),
          //     child: SizedBox(
          //         height: 20,
          //         child: Text(AppStrings.goBackToHome,style: AppTextStyle.heading1Medium(textColor: AppColors.appWhite),))
          // ),
        ],
      );
    }

    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(7),
          color: AppColors.textFieldFill1),
      margin: const EdgeInsets.symmetric(horizontal: 5),
      padding: const EdgeInsets.all(10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.note,
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
          verticalSizedBox(10),
          Text(
            AppStrings.whenYouAreOrderingMultiple,
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          ),
        ],
      ),
    );
  }

//endregion
}
