import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_price/shopping_cart_price_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_price/shopping_cart_price_common_widgets.dart';
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_drop_down/app_drop_down.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

// region Shopping cart price
class ShoppingCartPrice extends StatefulWidget {
  final GetCartPriceResponse getCartPriceResponse;


  const ShoppingCartPrice({Key? key, required this.getCartPriceResponse}) : super(key: key);

  @override
  _ShoppingCartPriceState createState() => _ShoppingCartPriceState();
}
// endregion

class _ShoppingCartPriceState extends State<ShoppingCartPrice> {
  // region Buyer Image Preview Bloc
  late ShoppingCartPriceBloc shoppingCartPriceBloc;
  // endregion

  // region Init
  @override
  void initState() {
    shoppingCartPriceBloc = ShoppingCartPriceBloc(context,widget.getCartPriceResponse);
    shoppingCartPriceBloc.init();
    super.initState();
  }
  // endregion
  //region Dis update
  @override
  void didUpdateWidget(covariant ShoppingCartPrice oldWidget) {
    shoppingCartPriceBloc = ShoppingCartPriceBloc(context,widget.getCartPriceResponse);
    shoppingCartPriceBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Dispose
  @override
  void dispose() {

    super.dispose();
  }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return body();
  }
  // endregion


//region Body
Widget body(){
    return AppDropDown(

      dropDownWidget: priceDetails(isBreakupShow: true),
      initialExpand: false,
      collapsedWidget: priceDetails(isBreakupShow: false),
      customDropdownDropDownName: Text(AppStrings.grandTotal,style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),),);
}
//endregion


//region Price details
Widget priceDetails({required bool isBreakupShow}){
  return ListView.builder(
    padding: const EdgeInsets.symmetric(horizontal: 17.5),
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: widget.getCartPriceResponse.cartFees!.length,
      itemBuilder: (context, index) {
        //Last index
        if(index == (widget.getCartPriceResponse.cartFees!.length-1)){
          return   Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(widget.getCartPriceResponse.cartFees!.last.orderBreakupItemText!,style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),),
                Text(widget.getCartPriceResponse.cartFees!.last.orderBreakupItemValue!,style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),),
              ],
            ),
          );
        }
        //Normal
        return ShoppingCartPriceCommonWidgets.subTitle(
          cartFees:widget.getCartPriceResponse.cartFees![index],
          breakupPriceVisible: isBreakupShow,
        );
      });
}
//endregion


}

