import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/refund_amount_calculation/refund_amount_calculation.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/refund_amount_calculation/refund_amount_calculation_common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/refund_cost_breakup/refund_cost_breakup_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/refund_cost_breakup/refund_cost_on_store/refund_cost_on_store.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/refund_cost_breakup/return_cost_more_details/return_cost_more_details.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class RefundCostBreakup extends StatefulWidget {
  final SubOrder suborder;
  final Order order;
  final bool isSellerView;

  const RefundCostBreakup({Key? key, required this.suborder, required this.order, required this.isSellerView}) : super(key: key);

  @override
  State<RefundCostBreakup> createState() => _RefundCostBreakupState();
}

class _RefundCostBreakupState extends State<RefundCostBreakup> {
  //region Bloc
  late RefundCostBreakupBloc refundCostBreakupBloc;

  //endregion
  //region Init
  @override
  void initState() {
    refundCostBreakupBloc = RefundCostBreakupBloc(context, widget.suborder, widget.order, widget.isSellerView);
    refundCostBreakupBloc.init();
    super.initState();
  }

  //endregion
  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        //CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        appBar: appBar(),
        body: SafeArea(child: body()),
      ),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isTitleVisible: true,
        isCustomTitle: false,
        title: AppStrings.refundCostBreakup,
        isDefaultMenuVisible: true,
        isCartVisible: false,
        isMembershipVisible: true,
        onTapDrawer: () {
          // buyerViewStoreBloc.goToSellerAccountScreen();
        });
  }

//endregion

//region Body
  Widget body() {
    return SingleChildScrollView(
      child: Column(
        children: [
          product(),
          widget.isSellerView?
          StreamBuilder<RefundAmountBreakupState>(
              initialData: RefundAmountBreakupState.Loading,
              stream: refundCostBreakupBloc.refundAmountCalculationStateCtrl.stream,
            builder: (context, snapshot) {
                if(snapshot.data== RefundAmountBreakupState.Success){
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      refundCostOnStore(),
                      sellerRefundAmountCalculation(),
                      verticalSizedBox(10),
                      moreDetails(),
                    ],
                  );
                }
                if(snapshot.data== RefundAmountBreakupState.Loading){
                  return AppCommonWidgets.appCircularProgress();
                }

                return AppCommonWidgets.errorMessage(error:"Refund cost breakup currently not available");


            }
          ):buyerRefundAmountCalculation(),
        ],
      ),
    );
  }

//endregion

//region Product
  Widget product() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: AppCommonWidgets.subOrderInfo(
          subOrder: widget.suborder,
          onTap: () {},
          context: context,
          isCheckBoxVisible: false,
          isPriceDetailVisible: true,
          isStatusVisible: false,
          isArrowVisible: false),
    );
    // return CancelledOrReturnedProductsCommonWidgets.productCard(subOrder: widget.suborder, context: context, order: widget.order, isPriceVisible:true,isDividerVisible: false,showArrow: false);
  }

//endregion


//region Buyer side refund amount calculation
  Widget buyerRefundAmountCalculation() {
    return RefundAmountCalculation(
      subOrderList: [refundCostBreakupBloc.subOrder],
      order: refundCostBreakupBloc.order,
      backgroundColor: AppColors.appWhite,
    );
  }

//endregion

//region Refund cost on store
  Widget refundCostOnStore() {
    return RefundCostOnStore(
      order: refundCostBreakupBloc.order,
      suborder: refundCostBreakupBloc.subOrder,
      refundAmountCalculationDetailList: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.refundCostOnStore!,
    );
  }

//endregion





  //region Seller refund amount calculation
  Widget sellerRefundAmountCalculation() {
    return Container(
        color: AppColors.appWhite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            //Title
            refundAmountTitle(),

            //Product and refund details
            productAndRefundDetails(),
          ],
        ));
  }

  //endregion

  //region Refund amount title
  Widget refundAmountTitle() {
    return Container(
      color: AppColors.textFieldFill1,
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            AppStrings.refundAmountCalculation,
            style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
          ),
          InkWell(
              onTap: () {
                refundCostBreakupBloc.onTapShowBreakup();
              },
              child: Container(
                  margin: const EdgeInsets.only(right: 10),
                  child: Text(
                    refundCostBreakupBloc.shoBreakup?AppStrings.hideBreakup:AppStrings.showBreakup,
                    style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
                  )))
        ],
      ),
    );
  }

  //endregion

//region Product and refund details
  Widget productAndRefundDetails() {
    return Container(
      margin: const EdgeInsets.only(top: 15,left: 17,right: 17,bottom: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //Product detail
          ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.productDetails!.length,
              itemBuilder: (context, index) {
                //Last index
                // if(index == (refundCostBreakupBloc.sellerRefundAmountCalculationResponse.productDetails!.length-1)){
                //   return RefundAmountCalculationCommonWidgets.title(
                //     refundAmountCalculationDetail: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.productDetails!.last,
                //     price: Text("${refundCostBreakupBloc.sellerRefundAmountCalculationResponse.productDetails![index].orderBreakupItemValue!}",style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack),),
                //
                //   );
                // }
                //Normal
                return RefundAmountCalculationCommonWidgets.subTitle(
                  refundAmountCalculationDetail: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.productDetails![index],
                  breakupPriceVisible: refundCostBreakupBloc.shoBreakup,
                );
              }),
          //Divider
          Container(
            margin: const EdgeInsets.symmetric(vertical: 11,horizontal: 50),
            padding: const EdgeInsets.symmetric(vertical:10,horizontal: 10),
            child: const Divider(color: AppColors.lightStroke,height: 1),
          ),

          //Refund detail
          ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.refundDetails!.length,
              itemBuilder: (context, index) {
                //Last index
                // if(index == (refundCostBreakupBloc.sellerRefundAmountCalculationResponse.refundDetails!.length-1)){
                //   return RefundAmountCalculationCommonWidgets.title(
                //     refundAmountCalculationDetail: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.refundDetails!.last,
                //     price: Text("${refundCostBreakupBloc.sellerRefundAmountCalculationResponse.refundDetails![index].orderBreakupItemValue!}",style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),),
                //   );
                // }
                //Normal
                return RefundAmountCalculationCommonWidgets.subTitle(
                  refundAmountCalculationDetail: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.refundDetails![index],
                  breakupPriceVisible: refundCostBreakupBloc.shoBreakup,
                );
              }),

        ],
      ),
    );
  }
//endregion
  

//region More details
  Widget moreDetails() {
    return RefundCostMoreDetails(
      order: refundCostBreakupBloc.order,
      suborder: refundCostBreakupBloc.subOrder,
      requestAndSuborderStatus: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.moreDetails![0].requestAndSuborderStatus!,
      productAndPayment: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.moreDetails![1].productAndPayment!,
      storeRefundPolicy: refundCostBreakupBloc.sellerRefundAmountCalculationResponse.moreDetails![2].storeRefundPolicy!,
    );
  }
//endregion
}
