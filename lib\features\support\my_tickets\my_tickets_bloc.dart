import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/support/feedback_item/feedback_item_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/support/get_all_feedback_response.dart';
import 'package:swadesic/services/add_feedback_responses/add_feedback_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum MyTicketsState { Loading, Success, Failed }

class MyTicketsBloc {
  // region Common Variables
  late BuildContext context;
  final String entityReference;
  final AddSupportServices addSupportServices = AddSupportServices();

  // Controllers
  final myTicketsCtrl = StreamController<MyTicketsState>.broadcast();
  final TextEditingController searchFieldTextCtrl = TextEditingController();

  // Data
  late GetAllFeedbackResponse getAllTicketsResponse;
  List<FeedbackDetail> rootTicketsList = [];
  List<FeedbackDetail> finalFilteredTicketsList = [];

  // endregion

  // region Constructor
  MyTicketsBloc(this.context, this.entityReference);
  // endregion

  // region Init
  Future<void> init() async {
    await getAllMyTickets();
  }
  // endregion

  // region Get all my tickets (SENT)
  Future<void> getAllMyTickets() async {
    try {
      // Loading state
      myTicketsCtrl.sink.add(MyTicketsState.Loading);
      
      // API call to get tickets sent BY this entity (SENT)
      getAllTicketsResponse = await addSupportServices.getTicketsByType(
        type: 'SENT',
        entityReference: entityReference,
      );
      
      // Calculate day difference
      calculateDayDifference();
      
      // Clear and populate root list
      rootTicketsList.clear();
      rootTicketsList.addAll(getAllTicketsResponse.feedbackDetailList ?? []);
      
      // Apply search filter
      applyFilter();
      
      // Success state
      myTicketsCtrl.sink.add(MyTicketsState.Success);
    } on ApiErrorResponseMessage {
      myTicketsCtrl.sink.add(MyTicketsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    } catch (error) {
      myTicketsCtrl.sink.add(MyTicketsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
  // endregion

  // region Calculate day difference
  void calculateDayDifference() {
    for (var element in getAllTicketsResponse.feedbackDetailList ?? []) {
      if (element.date != null) {
        element.dayDifference = int.parse(CommonMethods.dateTimeAmPm(date: element.date!)[0]);
      }
    }
  }
  // endregion

  // region Apply filter
  void applyFilter() {
    finalFilteredTicketsList.clear();
    finalFilteredTicketsList.addAll(rootTicketsList);
  }
  // endregion

  // region On search
  void onSearch() {
    //If field is empty then store Default data
    if (searchFieldTextCtrl.text.isEmpty) {
      //Clear searched tickets
      finalFilteredTicketsList.clear();
      //Add all data to finalFilteredTicketsList
      finalFilteredTicketsList.addAll(rootTicketsList);
      //Success state
      myTicketsCtrl.sink.add(MyTicketsState.Success);
    }
    //Clear searched tickets
    finalFilteredTicketsList.clear();
    //Search the text field data and filter
    for (var data in rootTicketsList) {
      if (data.brief!.toLowerCase().contains(searchFieldTextCtrl.text.toLowerCase()) ||
          data.details!.toLowerCase().contains(searchFieldTextCtrl.text.toLowerCase()) ||
          data.name!.toLowerCase().contains(searchFieldTextCtrl.text.toLowerCase()) ||
          data.feedbackId!.toString().contains(searchFieldTextCtrl.text.toLowerCase())) {
        finalFilteredTicketsList.add(data);
      }
    }
    //Success state
    myTicketsCtrl.sink.add(MyTicketsState.Success);
  }
  // endregion

  // region Go to ticket detail
  void goToTicketDetail({required FeedbackDetail ticketDetail, required int ticketId}) {
    var screen = FeedbackItemScreen(
      feedbackId: ticketId,
      isAdmin: AppConstants.adminUserReference.contains(AppConstants.appData.userReference!),
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // Refresh screen
      myTicketsCtrl.sink.add(MyTicketsState.Success);
    });
  }
  // endregion

  // region On tap upvote
  void onTapUpVote({required FeedbackDetail rootFeedback}) {
    // Handle upvote logic if needed
    addVoteApiCall(rootFeedback: rootFeedback);
  }
  // endregion

  // region Add vote API
  Future<void> addVoteApiCall({required FeedbackDetail rootFeedback}) async {
    try {
      await addSupportServices.addVote(feedbackId: rootFeedback.feedbackId!);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
  // endregion

  // region Dispose
  void dispose() {
    myTicketsCtrl.close();
    searchFieldTextCtrl.dispose();
  }
  // endregion
}
