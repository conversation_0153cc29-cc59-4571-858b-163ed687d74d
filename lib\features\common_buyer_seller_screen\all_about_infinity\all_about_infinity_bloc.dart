import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/invite_reward_info/invite_reward_info_response.dart';
import 'package:swadesic/model/user_rewards_and_invitees_response/user_rewards.dart';
import 'package:swadesic/services/invite_reward_info_service/invite_reward_info_service.dart';
import 'package:swadesic/services/user_rewards_and_invitees_service/user_reward_and_invitees_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';
enum AllAboutInfinityState { Loading, Success, Failed }

class AllAboutInfinityBloc {
  // region Common Variables
  BuildContext context;
  late InviteRewardInfo inviteRewardInfo = InviteRewardInfo();
  final EntityType entityType;

  List<String> userViewImages = [AppImages.productDiscount,AppImages.userWithShare,AppImages.storeWithShare];
  List<String> storeViewImages = [AppImages.flash,AppImages.greenSwadesicLogo,AppImages.shareThreeLine,AppImages.greenSwadesicLogo];
  // endregion


  //region Controller
  final allAboutInfinityStateCtrl = StreamController<AllAboutInfinityState>.broadcast();
  final dropDownCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  AllAboutInfinityBloc(this.context,this.entityType);
  // endregion

  // region Init
  void init() {
    getRewardInfo();

  }
// endregion


  //region On tap dropdown
  void onTapDropDown({required bool value}){
    dropDownCtrl.sink.add(!value);
  }
  //endregion


  //region Get reward info
  Future <void> getRewardInfo() async {
    try {
      inviteRewardInfo = await InviteRewardInfoService().getInviteRewardInfo();
      //Add imaged in reward info
      //If user view
      if (entityType == EntityType.USER) {
        for (int i = 0; i < inviteRewardInfo.userView!.allAboutInfinityDetailList!.length; i++) {
          if (i < userViewImages.length) {
            inviteRewardInfo.userView!.allAboutInfinityDetailList![i].image = userViewImages[i];
          } else {
            break; // Exit the loop if we run out of images
          }
        }
      }
      //If store view
      if (entityType == EntityType.STORE) {
        for (int i = 0; i < inviteRewardInfo.storeView!.allAboutInfinityDetailList!.length; i++) {
          if (i < storeViewImages.length) {
            inviteRewardInfo.storeView!.allAboutInfinityDetailList![i].image = storeViewImages[i];
          } else {
            break; // Exit the loop if we run out of images
          }
        }
      }
      //Success
      allAboutInfinityStateCtrl.sink.add(AllAboutInfinityState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //Failed
      allAboutInfinityStateCtrl.sink.add(AllAboutInfinityState.Failed);
    } catch (error) {
      //Failed
      allAboutInfinityStateCtrl.sink.add(AllAboutInfinityState.Failed);
    }
  }
  //endregion


//region Dispose
void dispose(){
  dropDownCtrl.close();
  allAboutInfinityStateCtrl.close();
}
//endregion



}
