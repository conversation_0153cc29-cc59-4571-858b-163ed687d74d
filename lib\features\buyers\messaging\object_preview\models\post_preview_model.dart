import 'entity_preview_model.dart';

/// Model class for post media
class PostMedia {
  final String mediaId;
  final String? mediaType;
  final String mediaPath;
  final int order;

  PostMedia({
    required this.mediaId,
    this.mediaType,
    required this.mediaPath,
    required this.order,
  });

  factory PostMedia.fromJson(Map<String, dynamic> json) {
    return PostMedia(
      mediaId: json['mediaId'] ?? '',
      mediaType: json['mediaType'],
      mediaPath: json['mediaPath'] ?? '',
      order: json['order'] ?? 1,
    );
  }
}

/// Model class for post previews
class PostPreview {
  final String contentType;
  final String postReference;
  final String postText;
  final List<PostMedia> postImages;
  final EntityPreview createdBy;
  final bool isDeleted;

  PostPreview({
    required this.contentType,
    required this.postReference,
    required this.postText,
    required this.postImages,
    required this.createdBy,
    required this.isDeleted,
  });

  factory PostPreview.fromJson(Map<String, dynamic> json) {
    // Parse post images
    final List<PostMedia> images = [];
    if (json['postImages'] != null) {
      for (var imageJson in json['postImages']) {
        images.add(PostMedia.fromJson(imageJson));
      }
    }

    // Parse created by entity
    final createdBy = json['createdBy'] != null 
        ? EntityPreview.fromJson(json['createdBy'])
        : EntityPreview(
            reference: '',
            entityType: '',
            handle: '',
            name: '',
          );

    return PostPreview(
      contentType: json['contentType'] ?? '',
      postReference: json['postReference'] ?? '',
      postText: json['postText'] ?? '',
      postImages: images,
      createdBy: createdBy,
      isDeleted: json['isDeleted'] ?? false,
    );
  }
}
