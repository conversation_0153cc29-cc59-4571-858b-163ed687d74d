import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class ProductOptionService {
  late HttpService httpService;

  ProductOptionService() {
    httpService = HttpService();
  }

  /// Rename a product option
  /// This API takes product_reference, current_name, new_name as input 
  /// and renames the options and also updates all the variants combinations 
  /// that have the old option name.
  Future<Map<String, dynamic>> renameProductOption({
    required String productReference,
    required String currentName,
    required String newName,
  }) async {
    try {
      final body = {
        'product_reference': productReference,
        'current_name': currentName,
        'new_name': newName,
      };

      final response = await httpService.postApiCall(
        body,
        AppConstants.updateProductOption,
      );

      return response;
    } catch (e) {
      rethrow;
    }
  }
}
