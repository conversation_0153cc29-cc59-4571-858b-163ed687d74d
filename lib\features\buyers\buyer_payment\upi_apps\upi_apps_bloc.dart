import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_screen.dart';
import 'package:swadesic/features/buyers/buyer_payment/payment_waiting/upi_payment_wating/upi_payment_waiting_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_payment_options_responses/payment_status_check.dart';
import 'package:swadesic/model/payment_model/upi_initiate_response.dart';
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';
import 'package:swadesic/services/buyer_payment_services/upi_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:upi_india/upi_india.dart';
import 'package:upi_india/upi_response.dart';
import 'package:url_launcher/url_launcher.dart';

class UpiAppBloc {
  // region Common Methods
  BuildContext context;
  UpiIndia upiIndia = UpiIndia();
  List<UpiApp>? apps;
  Future<UpiResponse>? _transaction;
  // endregion
  //region Controller
  final upiAppCtrl = StreamController<List<UpiApp>>.broadcast();
  final GetCartPriceResponse getCartPriceResponse;
  late UpiInitiateResponse upiInitiateResponse;
  final String orderNumber;

  //endregion

  // region | Constructor |
  UpiAppBloc(this.context, this.getCartPriceResponse, this.orderNumber);

  // endregion

  // region Init
  void init() {
    //Add all upi apps in list
    addAllUpiAppInAppList();
  }

  // endregion

//region Add all upi apps in list
  void addAllUpiAppInAppList() {
    upiIndia.getAllUpiApps(mandatoryTransactionId: false).then((value) {
      apps = value;
      //Broadcast UPI apps
      upiAppCtrl.sink.add(apps!);
    }).catchError((e) {
      apps = [];
    });
  }

//endregion

//region Initiate transaction
  Future<void> initiateTransaction({required UpiApp app, required String upiUrl}) async {
    final uri = Uri.parse(upiUrl);

    final receiverUpiId = uri.queryParameters['pa'] ?? '';
    final receiverName = uri.queryParameters['pn'] ?? '';
    final amount = double.tryParse(uri.queryParameters['am'] ?? '0') ?? 0;
    final transactionRefId = uri.queryParameters['tr'] ?? '';
    final transactionNote = uri.queryParameters['tn'] ?? '';
    final merchantId = uri.queryParameters['mc'] ?? '';
    final urlSource = uri.queryParameters['utm_source'] ?? '';
     try{
        await upiIndia.startTransaction(
         app: app,
         receiverUpiId: receiverUpiId,
         receiverName: receiverName,
         transactionRefId: transactionRefId,
         transactionNote: transactionNote,
         amount: amount,
         merchantId: merchantId,
         url: urlSource,
       ).then((value) {
          //print("Response is ${value}");
        });

       goToUpiUpiPaymentWaitingScreen(app: app);

     }
     catch(error){
       goToUpiUpiPaymentWaitingScreen(app: app);
     }
    // //print(transactionResult);

    // try {
    //   // UpiResponse transactionResult = await upiIndia.startTransaction(
    //
    //   // //print(transactionResult.status);
    // } catch (error) {
    //   context.mounted?CommonMethods.toastMessage(AppStrings.unableToOpenUpi, context):null;
    //   return;
    // }
  }

//endregion


//region UPI Intent initiate
  upiInitiateIntent({required UpiApp app}) async {
    //region Try
    try {
      upiInitiateResponse = await UpiService().getUpiIntent(
        orderNumber: orderNumber,
        deliveryFee: getCartPriceResponse.cartFees!
            .firstWhere((element) => element.orderBreakupItemText == "Delivery fees")
            .orderBreakupItemValue
            .toString(),
        cartTotal: getCartPriceResponse.cartFees!
            .firstWhere((element) => element.orderBreakupItemText == "Product total")
            .orderBreakupItemValue
            .toString(),
        totalAmount: getCartPriceResponse.cartFees!
            .firstWhere((element) => element.orderBreakupItemText == "Grand total")
            .orderBreakupItemValue
            .toString(),
        upiIntent: app.packageName
      );
      //Open upi app and initiate payment
       initiateTransaction(app: app,upiUrl: upiInitiateResponse.instrumentResponse.intentUrl);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted?CommonMethods.toastMessage(error.message!, context):null;
      return;
    } catch (error) {
      //print(error);
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion




//region Go to UPI payment waiting screen
  void goToUpiUpiPaymentWaitingScreen({required UpiApp app}) {
    var screen = UpiPaymentWaitingScreen(

      amount: getCartPriceResponse.cartFees!.firstWhere((element) => element.orderBreakupItemText == "Grand total").orderBreakupItemValue.toString().replaceAll("₹", ""),
      transactionId: upiInitiateResponse.txnToken,
      orderNumber: upiInitiateResponse.orderNumber,
      paymentChannel: app.packageName,
      isUpiIntent: true,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route).then((value) {
    //
    // });
    Navigator.pushReplacement(context, route).then((value) {
    });
  }
//endregion

}
