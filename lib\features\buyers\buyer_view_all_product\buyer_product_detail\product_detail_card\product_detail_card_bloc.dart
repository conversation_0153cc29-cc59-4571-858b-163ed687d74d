
import 'dart:async';

import 'package:flutter/material.dart';


class ProductDetailCardBloc {
  // region Common Variables
  BuildContext context;
  bool isExpanded = false;
  // endregion

  //region Text editing controller
  //endregion


  //region Controller
  final refreshCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  ProductDetailCardBloc(this.context);

  // endregion

  // region Init
  void init() {

  }
// endregion

//region On tap expand and collapse
void onTapExpandAndCollapse(){
    //Reverse expand value
  isExpanded = !isExpanded;
  //refresh
  refreshCtrl.sink.add(true);
}
//endregion






}
