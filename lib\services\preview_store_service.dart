import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:swadesic/features/data_model/preview_store_data_model/preview_store_data_model.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/image_converter.dart';

class PreviewStoreService {
  final HttpService httpService = HttpService();
  final Dio dio = Dio();
  final int uploadTimeOutInSecond = 1000;

  //region Form header
  dynamic formHeader() {
    var headers = {
      'Authorization': 'Bearer ${AppConstants.appData.accessToken}',
      'Content-Type': 'multipart/form-data',
    };
    //If web
    if (kIsWeb) {
      headers["Cache-Control-Speed"] = "medium";
    }
    return headers;
  }
  //endregion

  //region Create Preview Store - Mobile Platform
  Future<PreviewStoreResponse> createPreviewStoreMobile({
    required String previewStoreName,
    required String previewStoreHandle,
    File? previewStoreIcon,
  }) async {
    try {
      var formData = FormData();

      // Add text fields to form data
      formData.fields.addAll([
        MapEntry('user_reference', AppConstants.appData.userReference ?? ""),
        MapEntry('preview_store_name', previewStoreName.trim()),
        MapEntry('preview_storehandle', previewStoreHandle.trim()),
      ]);

      // Add file if provided
      if (previewStoreIcon != null) {
        String fileName = previewStoreIcon.path.split("/").last;
        formData.files.add(
          MapEntry(
            'preview_store_icon',
            await MultipartFile.fromFile(
              previewStoreIcon.path,
              filename: CommonMethods.trimFileName(fileName: fileName),
            ),
          ),
        );
      }

      var headers = formHeader();

      var response = await dio
          .post(
            AppConstants.createPreviewStore,
            options: Options(headers: headers),
            data: formData,
            onSendProgress: (send, total) {},
          )
          .timeout(Duration(seconds: uploadTimeOutInSecond));

      return PreviewStoreResponse.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }
  //endregion

  //region Create Preview Store - Web Platform
  Future<PreviewStoreResponse> createPreviewStoreWeb({
    required String previewStoreName,
    required String previewStoreHandle,
    Uint8List? previewStoreIconBytes,
    String? previewStoreIconName,
  }) async {
    try {
      var formData = FormData();

      // Add text fields to form data
      formData.fields.addAll([
        MapEntry('user_reference', AppConstants.appData.userReference ?? ""),
        MapEntry('preview_store_name', previewStoreName.trim()),
        MapEntry('preview_storehandle', previewStoreHandle.trim()),
      ]);

      // Add file if provided
      if (previewStoreIconBytes != null && previewStoreIconName != null) {
        Uint8List processedBytes = previewStoreIconBytes;
        String processedFileName = previewStoreIconName;

        // Check if the image is a PNG and convert if needed
        if (previewStoreIconName.toLowerCase().contains('png')) {
          // Convert PNG to JPEG to avoid server-side errors
          processedBytes = ImageConverter.convertPngToJpeg(previewStoreIconBytes);

          // Update the filename to reflect the new format
          processedFileName = previewStoreIconName.replaceAll(
              RegExp(r'\.png$', caseSensitive: false), '.jpg');

          debugPrint('Converted PNG to JPEG: $processedFileName');
        }

        formData.files.add(
          MapEntry(
            'preview_store_icon',
            MultipartFile.fromBytes(
              processedBytes,
              filename: processedFileName,
            ),
          ),
        );
      }

      var headers = formHeader();

      var response = await dio
          .post(
            AppConstants.createPreviewStore,
            options: Options(headers: headers),
            data: formData,
            onSendProgress: (send, total) {},
          )
          .timeout(Duration(seconds: uploadTimeOutInSecond));

      return PreviewStoreResponse.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }
  //endregion

  //region Create Preview Store - Universal Method
  Future<PreviewStoreResponse> createPreviewStore({
    required String previewStoreName,
    required String previewStoreHandle,
    // Mobile platform parameters
    File? previewStoreIcon,
    // Web platform parameters
    Uint8List? previewStoreIconBytes,
    String? previewStoreIconName,
  }) async {
    if (kIsWeb) {
      return createPreviewStoreWeb(
        previewStoreName: previewStoreName,
        previewStoreHandle: previewStoreHandle,
        previewStoreIconBytes: previewStoreIconBytes,
        previewStoreIconName: previewStoreIconName,
      );
    } else {
      return createPreviewStoreMobile(
        previewStoreName: previewStoreName,
        previewStoreHandle: previewStoreHandle,
        previewStoreIcon: previewStoreIcon,
      );
    }
  }
  //endregion
}
