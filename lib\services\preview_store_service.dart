import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:swadesic/features/data_model/preview_store_data_model/preview_store_data_model.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/image_converter.dart';

class PreviewStoreService {
  final HttpService httpService = HttpService();
  final Dio dio = Dio();
  final int uploadTimeOutInSecond = 1000;

  //region Form header
  dynamic formHeader() {
    var headers = {
      'Authorization': 'Bearer ${AppConstants.appData.accessToken}',
      'Content-Type': 'multipart/form-data',
    };
    //If web
    if (kIsWeb) {
      headers["Cache-Control-Speed"] = "medium";
    }
    return headers;
  }
  //endregion

  //region Create Preview Store - Mobile Platform
  Future<PreviewStoreResponse> createPreviewStoreMobile({
    required String previewStoreName,
    required String previewStoreHandle,
    File? previewStoreIcon,
  }) async {
    try {
      var formData = FormData();

      // Add text fields to form data
      formData.fields.addAll([
        MapEntry('user_reference', AppConstants.appData.userReference ?? ""),
        MapEntry('preview_store_name', previewStoreName.trim()),
        MapEntry('preview_storehandle', previewStoreHandle.trim()),
      ]);

      // Debug logging
      debugPrint('Creating preview store with data:');
      debugPrint('User Reference: ${AppConstants.appData.userReference}');
      debugPrint('Store Name: ${previewStoreName.trim()}');
      debugPrint('Store Handle: ${previewStoreHandle.trim()}');
      debugPrint('Has Image: ${previewStoreIcon != null}');

      // Add file if provided, otherwise add an empty file part
      if (previewStoreIcon != null) {
        String fileName = previewStoreIcon.path.split("/").last;
        debugPrint('Image file name: $fileName');
        formData.files.add(
          MapEntry(
            'preview_store_icon',
            await MultipartFile.fromFile(
              previewStoreIcon.path,
              filename: CommonMethods.trimFileName(fileName: fileName),
            ),
          ),
        );
      } else {
        // Add an empty file part when no image is provided
        formData.files.add(
          MapEntry(
            'preview_store_icon',
            MultipartFile.fromBytes([], filename: ''),
          ),
        );
      }

      var headers = formHeader();

      var response = await dio
          .post(
            AppConstants.createPreviewStore,
            options: Options(headers: headers),
            data: formData,
            onSendProgress: (send, total) {},
          )
          .timeout(Duration(seconds: uploadTimeOutInSecond));

      debugPrint('Preview store creation successful');
      return PreviewStoreResponse.fromJson(response.data);
    } catch (e) {
      debugPrint('Error in createPreviewStoreMobile: $e');
      if (e is DioError) {
        debugPrint('DioError details: ${e.response?.data}');
        debugPrint('Status code: ${e.response?.statusCode}');
      }
      rethrow;
    }
  }
  //endregion

  //region Create Preview Store - Web Platform
  Future<PreviewStoreResponse> createPreviewStoreWeb({
    required String previewStoreName,
    required String previewStoreHandle,
    Uint8List? previewStoreIconBytes,
    String? previewStoreIconName,
  }) async {
    try {
      var formData = FormData();

      // Add text fields to form data
      formData.fields.addAll([
        MapEntry('user_reference', AppConstants.appData.userReference ?? ""),
        MapEntry('preview_store_name', previewStoreName.trim()),
        MapEntry('preview_storehandle', previewStoreHandle.trim()),
      ]);

      // Debug logging
      debugPrint('Creating preview store (web) with data:');
      debugPrint('User Reference: ${AppConstants.appData.userReference}');
      debugPrint('Store Name: ${previewStoreName.trim()}');
      debugPrint('Store Handle: ${previewStoreHandle.trim()}');
      debugPrint('Has Image: ${previewStoreIconBytes != null && previewStoreIconName != null}');

      // Add file if provided
      if (previewStoreIconBytes != null && previewStoreIconName != null) {
        Uint8List processedBytes = previewStoreIconBytes;
        String processedFileName = previewStoreIconName;

        debugPrint('Image file name: $previewStoreIconName');

        // Check if the image is a PNG and convert if needed
        if (previewStoreIconName.toLowerCase().contains('png')) {
          // Convert PNG to JPEG to avoid server-side errors
          processedBytes = ImageConverter.convertPngToJpeg(previewStoreIconBytes);

          // Update the filename to reflect the new format
          processedFileName = previewStoreIconName.replaceAll(
              RegExp(r'\.png$', caseSensitive: false), '.jpg');

          debugPrint('Converted PNG to JPEG: $processedFileName');
        }

        formData.files.add(
          MapEntry(
            'preview_store_icon',
            MultipartFile.fromBytes(
              processedBytes,
              filename: processedFileName,
            ),
          ),
        );
      }

      var headers = formHeader();

      var response = await dio
          .post(
            AppConstants.createPreviewStore,
            options: Options(headers: headers),
            data: formData,
            onSendProgress: (send, total) {},
          )
          .timeout(Duration(seconds: uploadTimeOutInSecond));

      debugPrint('Preview store creation (web) successful');
      return PreviewStoreResponse.fromJson(response.data);
    } catch (e) {
      debugPrint('Error in createPreviewStoreWeb: $e');
      if (e is DioError) {
        debugPrint('DioError details: ${e.response?.data}');
        debugPrint('Status code: ${e.response?.statusCode}');
      }
      rethrow;
    }
  }
  //endregion

  //region Create Preview Store - Universal Method
  Future<PreviewStoreResponse> createPreviewStore({
    required String previewStoreName,
    required String previewStoreHandle,
    // Mobile platform parameters
    File? previewStoreIcon,
    // Web platform parameters
    Uint8List? previewStoreIconBytes,
    String? previewStoreIconName,
  }) async {
    if (kIsWeb) {
      return createPreviewStoreWeb(
        previewStoreName: previewStoreName,
        previewStoreHandle: previewStoreHandle,
        previewStoreIconBytes: previewStoreIconBytes,
        previewStoreIconName: previewStoreIconName,
      );
    } else {
      return createPreviewStoreMobile(
        previewStoreName: previewStoreName,
        previewStoreHandle: previewStoreHandle,
        previewStoreIcon: previewStoreIcon,
      );
    }
  }
  //endregion
}
