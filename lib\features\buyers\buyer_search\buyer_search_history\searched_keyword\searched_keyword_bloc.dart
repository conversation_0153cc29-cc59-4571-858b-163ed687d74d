import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/buyer_search_history_bloc.dart';
import 'package:swadesic/model/buyer_search_response/buyer_search_history_response.dart';



class SearchedKeyWordBloc {
  // region Common Variables
  BuildContext context;
  // final List<History> searchedKeywordList;
  final BuyerSearchHistoryBloc buyerSearchHistoryBloc;

  int itemCount = 3;
  // endregion


  //region Controller
  final refreshCtrl = StreamController<bool>.broadcast();
  // region | Constructor |
  SearchedKeyWordBloc(this.context,this.buyerSearchHistoryBloc);

  // endregion


  // region Init
  void init() async {
  }

// endregion


  //region On tap view more
  void onTapViewMore({required bool isIncrease,required List<History> searchedKeywordList}){
    if(isIncrease){
      itemCount = itemCount+3;
    }
    if(searchedKeywordList.length <= itemCount){
      itemCount = searchedKeywordList.length;
      refreshCtrl.sink.add(true);
      return;
    }
    if(searchedKeywordList.length >= itemCount){
      refreshCtrl.sink.add(true);

    }

  }
  //endregion


  //region On press cross
  void onPressCross({required History history,required List<History> searchedKeywordList}){
    //Remove history local
    searchedKeywordList.removeWhere((element){
      return element.searchHistoryId == history.searchHistoryId;
    });
    //Item count check
    //print(searchedKeywordList);
    onTapViewMore(isIncrease: false,searchedKeywordList: searchedKeywordList);
    //Remove single history api call
    buyerSearchHistoryBloc.removeSingleHistory(history: history);
    //Refresh
    refreshCtrl.sink.add(true);
  }
  //endregion






//region Dispose
  void dispose() {
    imageCache.clear();
    refreshCtrl.close();
  }
//endregion


}
