import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class PaymentPendingBloc {
  // region Common Variables
  BuildContext context;
  final Order order;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  late BuyerMyOrderServices buyerMyOrderServices;
  final List<SubOrder> subOrderList;



  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  PaymentPendingBloc(this.context,this.order,this.buyerSubOrderBloc, this.subOrderList);
  // endregion

  // region Init
  void init() {
  }
// endregion







}
