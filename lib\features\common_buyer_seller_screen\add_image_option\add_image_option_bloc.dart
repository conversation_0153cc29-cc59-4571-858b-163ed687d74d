import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class AddImageOptionBloc {
  // region Common Variables
  BuildContext context;
  final ImagePicker picker = ImagePicker();

  // var selectedImage;
  List<File> selectedImage = [];

  // endregion

  //region Controller
  final blankCtrl = StreamController<String>.broadcast();
  //endregion

  // region | Constructor |
  AddImageOptionBloc(this.context);

  // endregion

  // region Init
  void init() async {}

// endregion

//region Open Gallery
  void openGallery() async {
    try {
      //Selecting image from gallery
      List<XFile> galleryImage = (await picker.pickMultiImage( requestFullMetadata: true,));
      //If image from camera is empty then return
      if (galleryImage.isEmpty) return;
      //Compress image
      for (var image in galleryImage) {
        //Compress image
        var newFile = await CommonMethods.compressImage(originalFile: File(image.path));
        //print(newFile.path);
        // Convert XFile to File and add in to the list
        selectedImage.add(File(newFile.path));
      }
      //Close screen and send data back
      context.mounted ? Navigator.pop(context, selectedImage) : null;
    } catch (e) {
      //Error
      context.mounted ? CommonMethods.toastMessage(AppStrings.noImageIsSelected, context) : null;
    }
  }

//endregion

//region Open Camera
  void openCamera() async {
    try {
      XFile imageFromCamera = (await picker.pickImage(source: ImageSource.camera,))!;
      //If image from camera is empty then return
      if (imageFromCamera.path.isEmpty) return;
      //Compress image
      var compressedFile = await CommonMethods.compressImage(originalFile: File(imageFromCamera.path));
      // Convert XFile to File and add in to the list
      selectedImage.add(File(compressedFile.path));
      //Close screen and send data back
      context.mounted ? Navigator.pop(context, selectedImage) : null;
    } catch (e) {
      //Error
      context.mounted ? CommonMethods.toastMessage(AppStrings.noImageIsSelected, context) : null;
    }
  }

//endregion

  //region Image crop
  Future<XFile> crop({required File file}) async {
    File? croppedFile = await CommonMethods.imageCrop(
      file: File(file.path),
    );
    // userDetail.icon = files!.path;
    // isChanged = true;
    if (croppedFile != null) {
      return XFile(croppedFile.path);
    } else {
      return XFile(file.path);
    }
    // isImageSelected = true;
    // refreshCtrl.sink.add(true);
  }
//endregion

}
