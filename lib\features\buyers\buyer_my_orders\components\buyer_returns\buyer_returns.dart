import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/buyer_returns/buyer_returns_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/contact_info/contact_info.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class BuyerReturns extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const BuyerReturns(
      {Key? key,
      required this.subOrderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<BuyerReturns> createState() => _BuyerReturnsState();
}

class _BuyerReturnsState extends State<BuyerReturns> {
  // region Bloc
  late BuyerReturnBloc buyerReturnBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerReturnBloc = BuyerReturnBloc(
        context, widget.order, widget.buyerSubOrderBloc, widget.subOrderList);
    buyerReturnBloc.init();
    super.initState();
  }

  // endregion

  //region Dis update
  @override
  void didUpdateWidget(covariant BuyerReturns oldWidget) {
    buyerReturnBloc = BuyerReturnBloc(
        context, widget.order, widget.buyerSubOrderBloc, widget.subOrderList);
    buyerReturnBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Build

  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion

  //region Body
  Widget body() {
    // return Text("hehe");
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: const BoxDecoration(
          color: AppColors.appWhite,
          border: Border(bottom: BorderSide(color: AppColors.lightStroke))),
      child: ExpandablePanel(
        //region Theme
        theme: const ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(),
        //endregion
        collapsed: speakWithStore(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            speakWithStore(),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: buyerReturnBloc.subOrderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Package number
                        Visibility(
                          visible: buyerReturnBloc.subOrderList[index]
                                      .displayPackageNumber !=
                                  null &&
                              buyerReturnBloc.subOrderList[index]
                                  .displayPackageNumber!.isNotEmpty,
                          child: Container(
                            margin: const EdgeInsets.only(bottom: 10),
                            child: Text(
                              "Package: ${buyerReturnBloc.subOrderList[index].displayPackageNumber ?? ''}",
                              style: AppTextStyle.contentHeading0(
                                  textColor: AppColors.brandBlack),
                            ),
                          ),
                        ),

                        //Sub order filter
                        subOrderFilter(
                            subOrder: buyerReturnBloc.subOrderList[index]),

                        //Divider
                        Visibility(
                          visible:
                              buyerReturnBloc.subOrderList.length - 1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.thumbUpIcon,
      componentName: AppStrings.returns,
      suborderList: buyerReturnBloc.subOrderList,
      isEstimateDeliveryShow: false,
      isBuyerSideDeliveredOnShow: false,
      isSellerSideDelivered: false,
      additionalWidgets: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.hereYouCanSeeProductsBuyer,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Speak with store
  Widget speakWithStore() {
    return Row(
      children: [
        Expanded(
            child: AppCommonWidgets.activeButton(
                buttonName: AppStrings.speakWithStore,
                onTap: () async {
                  await CommonMethods.appMinimumBottomSheets(
                      bottomSheetName: AppStrings.contactDetails,
                      screen: ContactInfo(
                          phoneNumbers: buyerReturnBloc.subOrderList.first
                              .storeContactInfo!.phoneNumber!,
                          email: buyerReturnBloc
                              .subOrderList.first.storeContactInfo!.emailId!),
                      context: context);
                }))
      ],
    );
    // return BuyerMyOrderCommonWidgets.speakWithCustomer(subOrderList: buyerReturnBloc.subOrderList,buyerSubOrderBloc: buyerReturnBloc.buyerSubOrderBloc);
  }

//endregion

  //region Sub order filter
  Widget subOrderFilter({required SubOrder subOrder}) {
    ///Refunded ("suborder_status": "RETURNED_TO_SELLER"
    ///&& "refund_status" : "REFUNDED"
    ///&& "refunded_date": not null)
    if (subOrder.suborderStatus == AppConstants.returnedToSeller &&
        subOrder.refundDetails!.first.refundStatus == AppConstants.refunded) {
      // if(subOrder.suborderStatus == AppConstants.refundHold){
      return refunded(subOrder: subOrder);
    }

    ///Refund initiated (
    ///"suborder_status": "RETURNED_TO_SELLER"
    ///&& "refund_status" : "REFUND_INITIATED"
    /// && "refunded_date": not null)
    if (subOrder.suborderStatus == AppConstants.returnedToSeller &&
        subOrder.refundDetails!.first.refundStatus ==
            AppConstants.refundInitiated) {
      // if(subOrder.suborderStatus == AppConstants.refundHold){
      return refundInitiated(subOrder: subOrder);
    }

    ///Return requested
    if (subOrder.suborderStatus == AppConstants.returnRequestedStatus) {
      return requested(subOrder: subOrder);
    }

    ///Return accepted
    if (subOrder.suborderStatus == AppConstants.returnRequestConfirmed) {
      return accepted(subOrder: subOrder);
    }

    ///Returned
    if (subOrder.suborderStatus == AppConstants.returnedToSeller) {
      return returned(subOrder: subOrder);
    }

    ///Refund hold
    if (subOrder.suborderStatus == AppConstants.refundHold) {
      return refundHold(subOrder: subOrder);
    }

    return const SizedBox();
  }
  //endregion

  ///return requested
//region Requested
  Widget requested({required SubOrder subOrder}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSizedBox(10),
        //Return requested date and status
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: Text(
              "Return requested on ${CommonMethods.dateTimeAmPm(date: subOrder.returnInitiateDate!)[1]} ",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            )),
            Container(
              padding: const EdgeInsets.all(5.0),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  color: AppColors.appWhite,
                  border: Border.all(color: AppColors.appBlack, width: 1)),
              child: Text(
                "Requested",
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              ),
            )
          ],
        ),
        verticalSizedBox(10),
        //Delivered on
        Text(
          "Delivered on ${CommonMethods.dateTimeAmPm(date: subOrder.deliveredDate!)[1]} ",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        productInfoCard(
          context: context,
          subOrder: subOrder,
        ),

        ///Return reason
        Text(
          "Return reason: ${subOrder.returnReason!}",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),

        ///Button
        Row(
          children: [
            AppCommonWidgets.subOrderButton(
                buttonName: "Cancel return request",
                onTap: () {
                  buyerReturnBloc.cancelReturnRequest(subOrder: subOrder);
                  // //Mark only selected
                  // waitingForConfirmationBloc.subOrderList[index].isSelected = true;
                  // // //Open bottom sheet
                  // waitingForConfirmationBloc.onTapCancel();
                  // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                },
                horizontalPadding: 25),
            const Expanded(child: SizedBox())
          ],
        ),
        verticalSizedBox(10),
      ],
    );
  }
//endregion

  ///Accepted
//region Accepted
  Widget accepted({required SubOrder subOrder}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSizedBox(10),
        //Return requested date and status
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: Text(
              "Return requested on ${CommonMethods.dateTimeAmPm(date: subOrder.returnInitiateDate!)[1]} ",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            )),
            Container(
              padding: const EdgeInsets.all(5.0),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  color: AppColors.appWhite,
                  border: Border.all(color: AppColors.appBlack, width: 1)),
              child: Text(
                "Accepted",
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              ),
            )
          ],
        ),
        verticalSizedBox(10),
        //Delivered on
        Text(
          "Delivered on ${CommonMethods.dateTimeAmPm(date: subOrder.deliveredDate!)[1]} ",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),

        productInfoCard(
          context: context,
          subOrder: subOrder,
        ),
        Text(
          "Return reason: ${subOrder.returnReason!}",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),
        Text(
          AppStrings.speakWithStoreAndUnderstand,
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),

        Row(
          children: [
            AppCommonWidgets.subOrderButton(
                buttonName: "Cancel return request",
                onTap: () {
                  buyerReturnBloc.cancelReturnRequest(subOrder: subOrder);
                  // //Mark only selected
                  // waitingForConfirmationBloc.subOrderList[index].isSelected = true;
                  // // //Open bottom sheet
                  // waitingForConfirmationBloc.onTapCancel();
                  // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                },
                horizontalPadding: 25),
            const Expanded(child: SizedBox())
          ],
        ),
        verticalSizedBox(10),
      ],
    );
  }
//endregion

  ///Returned
//region Returned
  Widget returned({required SubOrder subOrder}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSizedBox(10),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: Text(
              "Return requested on ${CommonMethods.dateTimeAmPm(date: subOrder.returnInitiateDate!)[1]} ",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            )),
            Container(
              padding: const EdgeInsets.all(5.0),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  color: AppColors.appWhite,
                  border: Border.all(color: AppColors.appBlack, width: 1)),
              child: Text(
                "Returned",
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              ),
            )
          ],
        ),
        productInfoCard(context: context, subOrder: subOrder),
        Text(
          "Return reason: ${subOrder.returnReason!}",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),
        Text(
          AppStrings.refundWillBeShortly,
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),
      ],
    );
  }
//endregion

  ///Refund hold
//region Refund hold
  Widget refundHold({required SubOrder subOrder}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSizedBox(10),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Text(
                "Return received on ${CommonMethods.dateTimeAmPm(date: subOrder.returnedDate!)[1]} ",
                style:
                    AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(5.0),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  color: AppColors.appWhite,
                  border: Border.all(color: AppColors.red, width: 1)),
              child: Text(
                "Refund on hold",
                style: AppTextStyle.smallText(textColor: AppColors.red),
              ),
            )
          ],
        ),
        productInfoCard(
            context: context,
            subOrder: subOrder,
            isCancelledOnVisible: false,
            isYouWillReceiveVisible: false),
        Text(
          "Return reason: ${subOrder.returnReason!}",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),
        Text(
          "Refund hold reason: ${subOrder.refundDetails!.first.refundHoldReason!}",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),
        Text(
          AppStrings.weRecommendYouToSpeakWithStore,
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),
        //Button
        Row(
          children: [
            AppCommonWidgets.subOrderButton(
                buttonName: AppStrings.needHelpFromSwadesic,
                onTap: () {
                  buyerReturnBloc
                      .onTapNeedHelp(selectedSuborderList: [subOrder]);
                  // buyerReturnBloc.cancelReturnRequest(subOrder: subOrder);
                  // //Mark only selected
                  // waitingForConfirmationBloc.subOrderList[index].isSelected = true;
                  // // //Open bottom sheet
                  // waitingForConfirmationBloc.onTapCancel();
                  // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                },
                horizontalPadding: 25),
            const Expanded(child: SizedBox())
          ],
        ),
        verticalSizedBox(10)
      ],
    );
  }
//endregion

  ///Refund initiated (
  ///"suborder_status": "RETURNED_TO_SELLER"
  ///&& "refund_status" : "REFUND_INITIATED"
  /// && "refunded_date": not null)
//region Refund initiated
  Widget refundInitiated({required SubOrder subOrder}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSizedBox(10),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: Text(
              "Return received on ${CommonMethods.dateTimeAmPm(date: subOrder.returnedDate!)[1]} ",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            )),
            Container(
              padding: const EdgeInsets.all(5.0),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  color: AppColors.appWhite,
                  border: Border.all(color: AppColors.appBlack, width: 1)),
              child: Text(
                "Refund initiated",
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              ),
            )
          ],
        ),

        productInfoCard(
          context: context,
          subOrder: subOrder,
        ),
        Text(
          "Return reason: ${subOrder.returnReason!}",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),

        ///Refund initiated date
        Row(
          children: [
            Expanded(
              child: Text(
                "Refund initiated on ${CommonMethods.dateTimeAmPm(date: subOrder.refundDetails!.first.refundedDate!)[1]}",
                style: AppTextStyle.contentHeading0(
                    textColor: AppColors.brandBlack),
              ),
            ),
          ],
        ),
        verticalSizedBox(10),
      ],
    );
  }
//endregion

  ///Refunded ("suborder_status": "RETURNED_TO_SELLER"
  ///&& "refund_status" : "REFUNDED"
  ///&& "refunded_date": not null)
//region Refund initiated
  Widget refunded({required SubOrder subOrder}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        verticalSizedBox(10),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: Text(
              "Return received on ${CommonMethods.dateTimeAmPm(date: subOrder.returnedDate!)[1]} ",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            )),
            Container(
              padding: const EdgeInsets.all(5.0),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  color: AppColors.appWhite,
                  border: Border.all(color: AppColors.appBlack, width: 1)),
              child: Text(
                "Refunded",
                style: AppTextStyle.smallText(textColor: AppColors.appBlack),
              ),
            )
          ],
        ),
        productInfoCard(context: context, subOrder: subOrder),
        Text(
          "Return reason: ${subOrder.returnReason!}",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),

        ///Refund initiated date
        Row(
          children: [
            Expanded(
              child: Text(
                "Refund initiated on ${CommonMethods.dateTimeAmPm(date: subOrder.refundDetails!.first.refundedDate!)[1]}",
                style: AppTextStyle.contentHeading0(
                    textColor: AppColors.brandBlack),
              ),
            ),
          ],
        ),
        verticalSizedBox(10),
      ],
    );
  }
//endregion
}
