import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/add_and_edit_image/add_and_edit_image_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/getProduct_image_response/get_seller_product_Image_response.dart';
import 'package:swadesic/model/product_images.dart';
import 'package:swadesic/services/get_product_and_image/get_product_and_image.dart';
import 'package:swadesic/services/seller_hide_delete_service/seller_hide_delete_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class BuyerSelectedImagePreviewBloc {
  // region Common Variables
  BuildContext context;
  late ProductAndImageServices productAndImageServices;
  ProductImageResponse? productImageResponse;
  List<ProductImages> productImages = [];
  late SellerHideDeleteService sellerHideDeleteService;
  // endregion

  //region Text Editing Controller
  TextEditingController brandNameTextCtrl = TextEditingController();

  //endregion

  //region Controller
  final selectedImageCtrl = StreamController<ProductImages>.broadcast();
  final gridViewRefreshCtrl = StreamController<bool>.broadcast();

  //endregion

  // region | Constructor |
  BuyerSelectedImagePreviewBloc(this.context);

  // endregion

  // region Init
  void init() {
      addImages();





  }




// endregion

  //region Add Images to the Model class
  addImages(){
    productImages.clear();
    // if(productImageResponse!= null){
    //   // add all Network Images
    //   for (var image in productImageResponse!.data!) {
    //     productImages.add(ProductImages(XFile(""), image.productImage, image.productimageid!));
    //   }
    // }

    // add All Local Images
    for (var image in AppConstants.multipleSelectedImage){
      productImages.add(ProductImages(image, "", 0));
    }
    gridViewRefreshCtrl.sink.add(true);

  }
  //endregion





//region Go back to Add And Edit Image Screen
  void goBackToAddAndEditImage() {
    var screen = const AddAndEditImageScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      init();
      // refresh
    });
  }
//endregion


  // region Delete Image
  void deleteImage(int imageId) async {
    try {
      //print(imageId);

      sellerHideDeleteService.deleteImage(imageId);
    }on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
    catch(error){
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

// endregion





//region Go back to Add Product
//   void goBackToAddProduct(){
//     Navigator.pop(context);
//     Navigator.pop(context);
//   }
//endregion

}
