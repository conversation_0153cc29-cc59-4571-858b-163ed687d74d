import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:swadesic/features/buyers/buyer_payment/payment_waiting/payment_waiting_bloc.dart';
import 'package:swadesic/features/buyers/buyer_payment/payment_waiting/upi_and_card_waiting/upi_and_card_waiting.dart';
import 'package:swadesic/features/buyers/buyer_payment/payment_waiting/upi_payment_wating/upi_payment_waiting_bloc.dart';
import 'package:swadesic/features/buyers/buyer_payment/razor_pay_payment/razor_pay_payment_wating/razor_pay_payment_waiting_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:webview_flutter/webview_flutter.dart';

// region Razor pay payment Waiting Screen
class RazorPayPaymentWaitingScreen extends StatefulWidget {
  final String razorPayPaymentId;
  final String razorPayOrderId;
  final String razorPaySignature;
  final String transactionId;
  final String grandTotal;
  final String orderNumber;

  const RazorPayPaymentWaitingScreen(
      {Key? key,
      required this.transactionId,
      required this.grandTotal,
      required this.orderNumber,
      required this.razorPayPaymentId,
      required this.razorPayOrderId,
      required this.razorPaySignature})
      : super(key: key);

  @override
  _RazorPayPaymentWaitingScreenState createState() => _RazorPayPaymentWaitingScreenState();
}
// endregion

class _RazorPayPaymentWaitingScreenState extends State<RazorPayPaymentWaitingScreen> {
  // region Bloc
  late RazorPayPaymentWaitingBloc razorPayPaymentWaitingBloc;

  // endregion

  // region Init
  @override
  void initState() {
    razorPayPaymentWaitingBloc = RazorPayPaymentWaitingBloc(
      context,
      widget.transactionId,
      widget.grandTotal,
      widget.orderNumber,
      widget.razorPayPaymentId,
      widget.razorPayOrderId,
      widget.razorPaySignature,
    );
    razorPayPaymentWaitingBloc.init();
    super.initState();
  }

  //region Dispose
  @override
  void dispose() {
    imageCache.clear();
    razorPayPaymentWaitingBloc.dispose();
    super.dispose();
  }

  //endregion

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),

      body: SafeArea(
        child: info(),
      ),
      // body: SafeArea(
      //     // child: const SizedBox()
      //     //
      //     //
      //     //     ///Todo Un-comment
      //   child: StreamBuilder<bool>(
      //     stream: paymentWaitingBloc.screenRefreshCtrl.stream,
      //     initialData: false,
      //     builder: (context, snapshot) {
      //       //If from UPI
      //       if(widget.paymentType == "upi"){
      //         return const UpiAndCardWaiting(isUpiWaiting: true,);
      //       }
      //       return WebView(
      //       // initialUrl: "https://google.com",
      //       javascriptMode: JavascriptMode.unrestricted,
      //       onWebViewCreated: (controller){
      //         paymentWaitingBloc.webViewController = controller;
      //         paymentWaitingBloc.webViewController.loadUrl(Uri.dataFromString(paymentWaitingBloc.bankWeb(),mimeType: 'text/html').toString());
      //       },
      //       // onProgress: (data){
      //       //   //print("Progress is $data");
      //       // },
      //       // onPageStarted: (url){
      //       //   //print("On Page Started $url");
      //       // },
      //       //   onPageFinished: (url){
      //       //
      //       //   },
      //       );
      //     }
      //   ),
      //
      // ),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context, isCustomTitle: false, title: AppStrings.paymentOptions, isCartVisible: false, isMembershipVisible: false);
    // return buyerAppBar(
    //
    //     leadingIcon:SvgPicture.asset(
    //     AppImages.backButton,color: AppColors.appBlack,fit: BoxFit.fill),context: context,titleText:AppStrings.paymentOptions,drawerIconEnable:true,basketVisible: false,messageVisible: false,searchVisible: false, );
    //
  }

  //endregion

//region Icon and info
  Widget info() {
    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: Container(margin: const EdgeInsets.only(top: 43, bottom: 100), child: SvgPicture.asset(AppImages.upiWaiting))),
          //Your upi got a notification
          Container(
            margin: const EdgeInsets.only(bottom: 116),
            child: Text(
             "We are confirming the payment & order",
              textAlign: TextAlign.center,
              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
          //Three does
          Container(margin: const EdgeInsets.only(bottom: 20), child: Lottie.asset(AppImages.threeDotsAnimation, height: 50)),
          //Please don't go back
          Container(
            child: Text(
              AppStrings.pleaseDoNotGoBack,
              textAlign: TextAlign.center,
              style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack0),
            ),
          ),
        ],
      ),
    );
  }
//endregion
}
