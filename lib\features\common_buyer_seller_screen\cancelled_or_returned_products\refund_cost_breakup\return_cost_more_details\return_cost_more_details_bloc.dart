import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/refund_amount_calculation_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';



class ReturnCostMoreDetailsBloc {
  // region Common Variables
  BuildContext context;
  final SubOrder subOrder;
  final Order order;
  final List<RefundAmountCalculationDetail> requestAndSuborderStatus;
  final List<RefundAmountCalculationDetail> productAndPayment;
  final List<RefundAmountCalculationDetail> storeRefundPolicy;



  // endregion

  //region Text Editing Controller

  //endregion


  //region Controller
  final imageCtrl = StreamController<bool>.broadcast();
  //endregion


  // region | Constructor |
  ReturnCostMoreDetailsBloc(this.context, this.subOrder, this.order, this.requestAndSuborderStatus, this.productAndPayment, this.storeRefundPolicy,);

  // endregion

  // region Init
  void init() {

  }
// endregion



}
