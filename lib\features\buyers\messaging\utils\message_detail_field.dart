import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:http/http.dart' as http;
import 'package:file_picker/file_picker.dart';
import 'dart:convert';
import 'package:swadesic/util/app_constants.dart';
import 'dart:io';

class MessageDetailField extends StatefulWidget {
  final TextEditingController controller;
  final Function(String, List<Map<String, dynamic>>) onSend;

  const MessageDetailField({
    Key? key,
    required this.controller,
    required this.onSend,
  }) : super(key: key);

  @override
  State<MessageDetailField> createState() => _MessageDetailFieldState();
}

class _MessageDetailFieldState extends State<MessageDetailField> {
  bool _showSendButton = false;
  bool showEmoji = false;
  final FocusNode _focusNode = FocusNode();
  List<PlatformFile> selectedFiles = [];
  bool isUploading = false;
  Map<String, bool> uploadingFiles = {};
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxFiles = 5;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        setState(() {
          showEmoji = false;
        });
      }
    });
  }

  void _onTextChanged() {
    final hasText = widget.controller.text.isNotEmpty;
    if (hasText != _showSendButton) {
      setState(() {
        _showSendButton = hasText;
      });
    }
  }

  void _onEmojiSelected(String emoji) {
    final text = widget.controller.text;
    final textSelection = widget.controller.selection;
    final newText = text.replaceRange(
      textSelection.start,
      textSelection.end,
      emoji,
    );
    final emojiLength = emoji.length;
    widget.controller.text = newText;
    widget.controller.selection = textSelection.copyWith(
      baseOffset: textSelection.start + emojiLength,
      extentOffset: textSelection.start + emojiLength,
    );
  }

  void _toggleEmojiPicker() {
    setState(() {
      showEmoji = !showEmoji;
      if (showEmoji) {
        _focusNode.unfocus();
      }
    });
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    _focusNode.dispose();
    super.dispose();
  }

  Future<Map<String, dynamic>> uploadFile(PlatformFile file) async {
    final request = http.MultipartRequest(
      'POST',
      Uri.parse(
          '${AppConstants.messagingFileUploadEndpoint}?entity_reference=U1719579800140'),
    );
    request.headers['Authorization'] =
        'Bearer ${AppConstants.appData.messagingToken}';
    request.files.add(await http.MultipartFile.fromPath('files', file.path!));

    final response = await request.send();
    final responseData = await http.Response.fromStream(response);
    final jsonResponse = json.decode(responseData.body);

    if (jsonResponse['success']) {
      // Transform the response data to match the expected format
      final fileData = jsonResponse['data'][0];
      return {
        'url': fileData['url'] ?? '',
        'originalName': file.name,
        'size': file.size,
        'mimetype': fileData['mimetype'] ?? _getMimeType(file.extension ?? ''),
        'filename': fileData['filename'] ?? '',
        'type': fileData['type'] ?? '',
      };
    }
    throw Exception('Failed to upload file: ${file.name}');
  }

  String _getMimeType(String extension) {
    switch (extension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
      case 'docx':
        return 'application/msword';
      case 'xls':
      case 'xlsx':
        return 'application/vnd.ms-excel';
      default:
        return 'application/octet-stream';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (selectedFiles.isNotEmpty)
          Container(
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.all(10),
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(12)),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Attachments (${selectedFiles.length}/$maxFiles)',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      for (var i = 0; i < selectedFiles.length; i++)
                        Container(
                          constraints: BoxConstraints(
                            maxWidth: selectedFiles[i]
                                        .extension
                                        ?.toLowerCase()
                                        .split(',')
                                        .any((ext) => [
                                              'jpg',
                                              'jpeg',
                                              'png',
                                              'gif'
                                            ].contains(ext)) ??
                                    false
                                ? 150
                                : 200,
                          ),
                          margin: EdgeInsets.only(
                              right: i == selectedFiles.length - 1 ? 0 : 12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Stack(
                            children: [
                              if (selectedFiles[i]
                                      .extension
                                      ?.toLowerCase()
                                      .split(',')
                                      .any((ext) => [
                                            'jpg',
                                            'jpeg',
                                            'png',
                                            'gif'
                                          ].contains(ext)) ??
                                  false && selectedFiles[i].path != null)
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.file(
                                    File(selectedFiles[i].path!),
                                    height: 150,
                                    width: 150,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        height: 150,
                                        width: 150,
                                        color: Colors.grey[200],
                                        child: const Icon(
                                          Icons.broken_image,
                                          color: Colors.grey,
                                          size: 48,
                                        ),
                                      );
                                    },
                                  ),
                                )
                              else
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Icon(
                                        _getFileIcon(
                                            selectedFiles[i].extension ?? ''),
                                        size: 36,
                                        color: Colors.grey[600],
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        selectedFiles[i].name,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        _formatFileSize(selectedFiles[i].size),
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              if (uploadingFiles[selectedFiles[i].path] ??
                                  false)
                                Positioned.fill(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.3),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Center(
                                      child: SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              Positioned(
                                right: 4,
                                top: 4,
                                child: InkWell(
                                  onTap: () {
                                    setState(() {
                                      selectedFiles.remove(selectedFiles[i]);
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.5),
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.close,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: AppColors.textFieldFill1,
                  border: Border(
                    top: BorderSide(
                      color: Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  borderRadius: BorderRadius.all(
                    Radius.circular(30.0),
                  ),
                ),
                child: Row(
                  children: [
                    //emoji icon
                    InkWell(
                      onTap: () {
                        _toggleEmojiPicker();
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(left: 10),
                        child:
                            // decoration: BoxDecoration(
                            //   border: Border.all(
                            //     color: AppColors.appBlack, // Set the color of the outline
                            //     width: 0.5, // Set the width of the outline
                            //     style: BorderStyle.solid,
                            //      // Set the style of the border
                            //   ),
                            //   borderRadius: BorderRadius.circular(30), // Optional: round the corners
                            // ),
                            SvgPicture.asset(
                          AppImages.smileGreyEmoji,
                          height: 30,
                        ),
                      ),
                    ),
                    //Attach file icon
                    InkWell(
                      onTap: () async {
                        if (selectedFiles.length >= maxFiles) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content:
                                    Text('Maximum $maxFiles files allowed')),
                          );
                          return;
                        }

                        // Open file picker
                        final result = await FilePicker.platform.pickFiles(
                          allowMultiple: true,
                          type: FileType.any,
                        );

                        if (result != null && result.files.isNotEmpty) {
                          // Filter out files that exceed size limit
                          final validFiles = result.files.where((file) {
                            if (file.size > maxFileSize) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                    content: Text(
                                        '${file.name} exceeds 10MB limit')),
                              );
                              return false;
                            }
                            return true;
                          }).toList();

                          // Add only up to the maximum allowed files
                          final remainingSlots =
                              maxFiles - selectedFiles.length;
                          final filesToAdd =
                              validFiles.take(remainingSlots).toList();

                          if (validFiles.length > remainingSlots) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text(
                                      'Only added first $remainingSlots files')),
                            );
                          }

                          setState(() {
                            selectedFiles.addAll(filesToAdd);
                          });
                        }
                      },
                      child: Padding(
                          padding: const EdgeInsets.only(left: 10),
                          child: SvgPicture.asset(
                            AppImages.addMessageFile,
                            height: 28,
                            color: AppColors.appBlack,
                          )),
                    ),

                    Expanded(
                      child: TextField(
                        controller: widget.controller,
                        focusNode: _focusNode,
                        maxLines: null,
                        decoration: const InputDecoration(
                          hintText: 'Type a message...',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 15, vertical: 10),
                        ),
                        onChanged: (value) {
                          setState(() {});
                        },
                      ),
                    ),

                    // Send button
                    Container(
                      margin: const EdgeInsets.only(left: 8, right: 8),
                      child: isUploading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : InkWell(
                              onTap: (selectedFiles.isNotEmpty ||
                                      widget.controller.text.trim().isNotEmpty)
                                  ? _handleSendMessage
                                  : null,
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: (selectedFiles.isNotEmpty ||
                                          widget.controller.text
                                              .trim()
                                              .isNotEmpty)
                                      ? AppColors.brandBlack
                                      : Colors.grey,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.send,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                    ),
                  ],
                ),
              ),
              // Emoji picker
              if (showEmoji)
                SizedBox(
                  height: 300,
                  child: EmojiPicker(
                    onEmojiSelected: (category, emoji) {
                      final text = widget.controller.text;
                      final selection = widget.controller.selection;
                      final cursorPosition = selection.baseOffset;

                      // Handle invalid cursor position
                      if (cursorPosition < 0) {
                        // If no cursor position, just append to the end
                        widget.controller.text = text + emoji.emoji;
                        // Move cursor to end
                        widget.controller.selection =
                            TextSelection.fromPosition(
                          TextPosition(offset: widget.controller.text.length),
                        );
                      } else {
                        // Insert at cursor position
                        final newText = text.replaceRange(
                          selection.start,
                          selection.end,
                          emoji.emoji,
                        );
                        widget.controller.text = newText;
                        // Move cursor after the inserted emoji
                        widget.controller.selection =
                            TextSelection.fromPosition(
                          TextPosition(
                              offset: cursorPosition + emoji.emoji.length),
                        );
                      }
                    },
                    onBackspacePressed: () {
                      // Handle backspace if needed
                    },
                    config: const Config(
                      columns: 7,
                      verticalSpacing: 0,
                      horizontalSpacing: 0,
                      initCategory: Category.RECENT,
                      bgColor: Color(0xFFF2F2F2),
                      indicatorColor: Colors.blue,
                      iconColor: Colors.grey,
                      iconColorSelected: Colors.blue,
                      backspaceColor: Colors.blue,
                      skinToneDialogBgColor: Colors.white,
                      skinToneIndicatorColor: Colors.grey,
                      enableSkinTones: true,
                      recentsLimit: 28,
                      noRecents: Text("No Recents"),
                      tabIndicatorAnimDuration: kTabScrollDuration,
                      categoryIcons: CategoryIcons(),
                      buttonMode: ButtonMode.CUPERTINO,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getFileIcon(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      // return Icons.image;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  Future<void> _handleSendMessage() async {
    if (selectedFiles.isEmpty && widget.controller.text.trim().isEmpty) return;

    setState(() {
      isUploading = true;
    });

    try {
      List<Map<String, dynamic>> attachments = [];

      // Upload all selected files
      for (var file in selectedFiles) {
        setState(() {
          uploadingFiles[file.path!] = true;
        });

        try {
          final fileData = await uploadFile(file);
          attachments.add(fileData);
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to upload ${file.name}: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        } finally {
          setState(() {
            uploadingFiles[file.path!] = false;
          });
        }
      }

      // Send message with attachments
      if (attachments.isNotEmpty || widget.controller.text.trim().isNotEmpty) {
        widget.onSend(widget.controller.text, attachments);

        // Clear the input and files
        setState(() {
          selectedFiles.clear();
          widget.controller.clear();
          uploadingFiles.clear();
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        isUploading = false;
      });
    }
  }
}
