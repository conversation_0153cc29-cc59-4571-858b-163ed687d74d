import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/tagged_posts/tagged_posts_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Tagged Posts Screen
class TaggedPostsScreen extends StatefulWidget {
  final String productReference;
  final String productName;

  const TaggedPostsScreen({
    super.key,
    required this.productReference,
    required this.productName,
  });

  @override
  State<TaggedPostsScreen> createState() => _TaggedPostsScreenState();
}
//endregion

class _TaggedPostsScreenState extends State<TaggedPostsScreen> {
  late TaggedPostsBloc taggedPostsBloc;

  //region Init
  @override
  void initState() {
    taggedPostsBloc = TaggedPostsBloc(
      context,
      widget.productReference,
    );
    taggedPostsBloc.init();
    super.initState();
  }
  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      body: body(),
    );
  }
  //endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      onTapLeading: () {
        Navigator.pop(context);
      },
      context: context,
      isCustomTitle: false,
      title: "Tagged Posts",
      isMembershipVisible: false,
      isCustomMenuVisible: false,
      isDefaultMenuVisible: false,
      isCartVisible: false,
    );
  }
  //endregion

  //region Body
  Widget body() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        await taggedPostsBloc.refreshTaggedPosts();
      },
      child: StreamBuilder<TaggedPostsState>(
        stream: taggedPostsBloc.taggedPostsStateCtrl.stream,
        initialData: TaggedPostsState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == TaggedPostsState.Loading && taggedPostsBloc.postList.isEmpty) {
            return Center(
              child: AppCommonWidgets.appCircularProgress(),
            );
          } else if (snapshot.data == TaggedPostsState.Empty) {
            return AppCommonWidgets.errorWidget(
              errorMessage: "No posts found tagged with this product",
              onTap: () {
                taggedPostsBloc.refreshTaggedPosts();
              },
            );
          } else if (snapshot.data == TaggedPostsState.Failed) {
            return AppCommonWidgets.errorWidget(
              errorMessage: AppStrings.commonErrorMessage,
              onTap: () {
                taggedPostsBloc.refreshTaggedPosts();
              },
            );
          } else {
            return postsList();
          }
        },
      ),
    );
  }
  //endregion

  //region Posts List
  Widget postsList() {
    return Consumer<PostDataModel>(
      builder: (BuildContext context, PostDataModel postDataModel, Widget? child) {
        List<PostDetail> postList = taggedPostsBloc.postList;

        return Container(
          // margin: const EdgeInsets.symmetric(horizontal: 16),
          child: ListView.separated(
            separatorBuilder: (context, index) => const SizedBox(height: 20),
            controller: taggedPostsBloc.scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: postList.length + 1,
            itemBuilder: (context, index) {
              if (index < postList.length) {
                return PostCard(
                  postDetail: postList[index],
                  onTapDelete: () {
                    taggedPostsBloc.confirmDelete(postDetail: postList[index]);
                  },
                  onTapDrawer: () {
                    taggedPostsBloc.onTapDrawer(postDetail: postList[index]);
                  },
                  onTapEdit: () {
                    taggedPostsBloc.goToEditPost(postDetail: postList[index]);
                  },
                  onTapHeart: () {
                    taggedPostsBloc.onTapHeart(postDetail: postList[index]);
                  },
                  onTapReport: () {},
                  onTapShare: () {
                    taggedPostsBloc.onTapShare(postDetail: postList[index]);
                  },
                  onTapProfileImage: () {
                    taggedPostsBloc.onTapUserOrStoreIcon(
                      reference: postList[index].createdBy!.userOrStoreReference!,
                    );
                  },
                  onTapPost: () {
                    taggedPostsBloc.goToSinglePostView(
                      postReference: postList[index].postOrCommentReference!,
                    );
                  },
                );
              } else {
                return paginationLoading();
              }
            },
          ),
        );
      },
    );
  }
  //endregion

  //region Pagination Loading
  Widget paginationLoading() {
    return StreamBuilder<TaggedPostsState>(
      stream: taggedPostsBloc.taggedPostsStateCtrl.stream,
      initialData: TaggedPostsState.Loading,
      builder: (context, snapshot) {
        if (snapshot.data == TaggedPostsState.Loading && taggedPostsBloc.postList.isNotEmpty) {
          return Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            child: AppCommonWidgets.appCircularProgress(isPaginationProgress: true),
          );
        }
        return const SizedBox();
      },
    );
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    taggedPostsBloc.dispose();
    super.dispose();
  }
  //endregion
}
