import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/all_about_infinity/all_about_infinity_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page.dart';
import 'package:swadesic/model/invite_reward_info/invite_reward_info_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class AllAboutInfinity extends StatefulWidget {
  final EntityType entityType;
  const AllAboutInfinity({super.key, required this.entityType});

  @override
  State<AllAboutInfinity> createState() => _AllAboutInfinityState();
}

class _AllAboutInfinityState extends State<AllAboutInfinity> {
  //region Bloc
  late AllAboutInfinityBloc allAboutInfinityBloc;
  //endregion

  //region Init
  @override
  void initState() {
    allAboutInfinityBloc = AllAboutInfinityBloc(context, widget.entityType);
    allAboutInfinityBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    allAboutInfinityBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<Object>(
        stream: allAboutInfinityBloc.allAboutInfinityStateCtrl.stream,
        initialData: AllAboutInfinityState.Loading,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data == AllAboutInfinityState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          //Success
          if (snapshot.data == AllAboutInfinityState.Success) {
            return dropDown();
          }
          return const SizedBox();
        });
  }
//endregion

//region Drop down
  Widget dropDown() {
    return StreamBuilder<bool>(
        stream: allAboutInfinityBloc.dropDownCtrl.stream,
        initialData: true,
        builder: (context, snapshot) {
          return CupertinoButton(
            onPressed: !snapshot.data!
                ? () {
                    allAboutInfinityBloc.onTapDropDown(value: snapshot.data!);
                  }
                : null,
            padding: EdgeInsets.zero,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                  color: AppColors.textFieldFill2,
                  borderRadius: BorderRadius.all(Radius.circular(
                      MediaQuery.of(context).size.width * 0.02))),
              margin: const EdgeInsets.only(left: 15, right: 15, bottom: 12),
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 10),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        "${widget.entityType == EntityType.STORE ? "All about Flash" : "All about Infinity"}",
                        style: AppTextStyle.sectionHeading(
                                textColor: AppColors.appBlack)
                            .copyWith(height: 0),
                      ),
                      SizedBox(
                        height: 24,
                        width: 24,
                        child: CupertinoButton(
                          onPressed: () {
                            allAboutInfinityBloc.onTapDropDown(
                                value: snapshot.data!);
                          },
                          padding: EdgeInsets.zero,
                          child: RotatedBox(
                              quarterTurns: snapshot.data! ? 3 : 1,
                              child: SvgPicture.asset(
                                AppImages.arrow3,
                                height: 24,
                                width: 24,
                              )),
                        ),
                      )
                    ],
                  ),
                  expandView(),
                ],
              ),
            ),
          );
        });
  }
//endregion

//region Expand view
  Widget expandView() {
    return StreamBuilder<bool>(
        stream: allAboutInfinityBloc.dropDownCtrl.stream,
        initialData: true,
        builder: (context, snapshot) {
          //False
          if (!snapshot.data!) {
            return const SizedBox();
          }
          return Column(
            children: [
              infinityAndRupee(),
              discountInfo(),
              //Invite button
              inviteFriendsAndStore()
            ],
          );
        });
  }
//endregion

//region Infinity and rupee
  Widget infinityAndRupee() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 13),
      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 7.0),
      child: Column(
        children: [
          //If store then show
          widget.entityType == EntityType.STORE
              ? Container(
                  margin: const EdgeInsets.symmetric(vertical: 6),
                  child: Row(
                    children: [
                      //Infinity
                      // Container(
                      //     margin: const EdgeInsets.symmetric(vertical: 10.5),
                      //     child: Image.asset(AppImages.infinity,height: 20,width: 40,)),
                      // const SizedBox(width: 14),
                      // Container(
                      //     margin: const EdgeInsets.symmetric(vertical: 10.5),
                      //     child: Image.asset(AppImages.equal,width: 20,)),
                      // const SizedBox(width: 14),
                      Container(
                          padding: const EdgeInsets.all(5),
                          child: Image.asset(
                            AppImages.flash,
                            height: 30,
                            width: 20,
                          )),
                      const SizedBox(width: 14),
                      Container(
                          margin: const EdgeInsets.symmetric(vertical: 10.5),
                          child: Image.asset(
                            AppImages.equal,
                            width: 20,
                          )),
                      const SizedBox(width: 14),
                      Container(
                          margin: const EdgeInsets.symmetric(vertical: 10.5),
                          child: Image.asset(
                            AppImages.rupee,
                            width: 20,
                          )),
                    ],
                  ),
                )
              :
              //If user then show
              Container(
                  margin: const EdgeInsets.symmetric(vertical: 6),
                  child: Row(
                    children: [
                      //Infinity
                      Container(
                          margin: const EdgeInsets.symmetric(vertical: 10.5),
                          child: Image.asset(
                            AppImages.infinity,
                            height: 20,
                            width: 40,
                          )),
                      const SizedBox(width: 14),
                      Container(
                          margin: const EdgeInsets.symmetric(vertical: 10.5),
                          child: Image.asset(
                            AppImages.equal,
                            width: 20,
                          )),
                      const SizedBox(width: 14),
                      Container(
                          padding: const EdgeInsets.all(5),
                          child: Image.asset(
                            AppImages.rupee,
                            height: 30,
                            width: 20,
                          )),
                    ],
                  ),
                ),
          const SizedBox(height: 10),
          Text(
            widget.entityType == EntityType.STORE
                ? AppStrings.infinityPointsOrFlashPoint
                : AppStrings.oneInfinityPoints,
            textAlign: TextAlign.left,
            maxLines: 3,
            style: AppTextStyle.access0(textColor: AppColors.appBlack),
          )
        ],
      ),
    );
  }
//endregion

//region Discount info
  Widget discountInfo() {
    //If user view
    if (widget.entityType == EntityType.USER) {
      return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: allAboutInfinityBloc
            .inviteRewardInfo.userView!.allAboutInfinityDetailList!.length,
        itemBuilder: (context, index) {
          return imageAndDetail(
              allAboutInfinityDetail: allAboutInfinityBloc.inviteRewardInfo
                  .userView!.allAboutInfinityDetailList![index]);
        },
      );
    }
    //If store view
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: allAboutInfinityBloc
          .inviteRewardInfo.storeView!.allAboutInfinityDetailList!.length,
      itemBuilder: (context, index) {
        return imageAndDetail(
            allAboutInfinityDetail: allAboutInfinityBloc.inviteRewardInfo
                .storeView!.allAboutInfinityDetailList![index]);
      },
    );
  }
//endregion

  //region Image and detail
  Widget imageAndDetail(
      {required AllAboutInfinityDetail allAboutInfinityDetail}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            allAboutInfinityDetail.image,
            height: 40,
            width: 40,
          ),
          const SizedBox(width: 5),
          //Get up to
          Expanded(
            child: Column(
              children: [
                //Title
                Text(
                  allAboutInfinityDetail.title!,
                  style: AppTextStyle.access0(textColor: AppColors.appBlack),
                ),
                //If sub title is null then reurn container
                allAboutInfinityDetail.subTitle != null
                    ? Text(
                        allAboutInfinityDetail.subTitle!,
                        style: AppTextStyle.smallText(
                            textColor: AppColors.writingBlack1),
                      )
                    : const SizedBox(),
              ],
            ),
          ),
        ],
      ),
    );
  }
  //endregion

//region Invite friends and and store
  Widget inviteFriendsAndStore() {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        var screen = CommonReferralPage();
        var route = CupertinoPageRoute(builder: (context) => screen);
        Navigator.push(
            AppConstants.userStoreCommonBottomNavigationContext, route);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: AppColors.brandBlack,
            borderRadius:
                BorderRadius.circular(MediaQuery.of(context).size.width * 0.5)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              AppImages.basketWithStar,
              height: 40,
              width: 40,
            ),
            const SizedBox(width: 10),
            Text(
              "Invite Friends & Stores",
              style: AppTextStyle.access1(textColor: AppColors.appWhite),
            ),
          ],
        ),
      ),
    );
  }
//endregion
}
