import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/buyer_cancelling_order/buyer_cancelling_order_screen.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';


class ScheduledForShippingBloc {
  // region Common Variables
  BuildContext context;
  late BuyerMyOrderServices buyerMyOrderServices;
  final Order store;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final List<SubOrder> subOrderList;



  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  ScheduledForShippingBloc(this.context, this.store, this.buyerSubOrderBloc, this.subOrderList);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();

  }
// endregion



// region On tap Tracking detail
  Future onTapCancel() {
    return CommonMethods.appBottomSheet(screen: BuyerCancellingOrderScreen(
      subOrderList: subOrderList,
      store: store, buyerSubOrderBloc:buyerSubOrderBloc,
    ), context: context, bottomSheetName: AppStrings.cancellingTheOrder).then((value) {
      //Unselect all
      CommonMethods.subOrderSelectUnSelectAll(isSelectAll: false, subOrderList: subOrderList);
      //Refresh sub order screen.
      buyerSubOrderBloc.buyerSubOrderStateCtrl.sink.add(BuyerSubOrderState.Success);

    });
  }
// endregion






}
