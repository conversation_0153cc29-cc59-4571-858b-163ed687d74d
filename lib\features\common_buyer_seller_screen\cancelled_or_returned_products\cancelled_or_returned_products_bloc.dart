import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/refund_cost_breakup/refund_cost_breakup.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';



class CancelledOrReturnedProductsBloc {
  // region Common Variables
  BuildContext context;
  final List<SubOrder> subOrderList;
  final Order order;
  final bool isSellerView;

  // endregion

  //region Text Editing Controller

  //endregion


  //region Controller
  final imageCtrl = StreamController<bool>.broadcast();
  //endregion


  // region | Constructor |
  CancelledOrReturnedProductsBloc(this.context, this.subOrderList, this.order, this.isSellerView);

  // endregion

  // region Init
  void init() {

  }
// endregion

//region On tap product
void onTapProduct({required SubOrder subOrder}){
  var screen =  RefundCostBreakup(suborder: subOrder, order:order, isSellerView: isSellerView,);
  var route = MaterialPageRoute(builder: (context) => screen);
  Navigator.push(context, route);
}
//endregion

}
