import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_recommended_products/search_recommended_products_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum SearchRecommendedProductsPaginationState { Loading, Done }

class SearchRecommendedProductsPagination {
  //region Context
  late BuildContext context;
  late SearchRecommendedProductsBloc searchRecommendedProductsBloc;
  // bool isLoadingPaginationData = false;
  SearchRecommendedProductsPaginationState currentApiCallStatus = SearchRecommendedProductsPaginationState.Done;

  //endregion

  //region Controller
  final searchRecommendedProductsPaginationStateCtrl = StreamController<SearchRecommendedProductsPaginationState>.broadcast();
  //endregion

//region Constructor
  SearchRecommendedProductsPagination(this.context, this.searchRecommendedProductsBloc);
//endregion

  //region On pagination loading visible
  void onPaginationLoadingVisible() async {
    await getPaginationFeeds();
  }

  //endregion

  //region Get pagination recommended products
  Future<void> getPaginationFeeds() async {
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    try {
      //If api call status is Loading then return
      if(currentApiCallStatus == SearchRecommendedProductsPaginationState.Loading){
        return;
      }
      //Loading
      searchRecommendedProductsPaginationStateCtrl.sink.add(SearchRecommendedProductsPaginationState.Loading);
      //Current api call status is Loading
      currentApiCallStatus = SearchRecommendedProductsPaginationState.Loading;
      //Api call
      List<Product> productList = await RecommendedStoreAndUserServices()
          .getRecommendedProducts(limit: 10, offset: searchRecommendedProductsBloc.recommendedProductsList.length,context: context);
      //Add in search recommended bloc recommendedProductList
      searchRecommendedProductsBloc.recommendedProductsList.addAll(productList);
      //Add recommended store to data model
      productDataModel.addProductIntoList(products: productList);
      //If feed list is empty
      if (productList.isEmpty) {
        //Current api call status is Done
        currentApiCallStatus = SearchRecommendedProductsPaginationState.Done;
        //Empty
        return searchRecommendedProductsPaginationStateCtrl.sink.add(SearchRecommendedProductsPaginationState.Done);
      }
      //Current api call status is Done
      currentApiCallStatus = SearchRecommendedProductsPaginationState.Done;

    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(error.message.toString(), context) : null;
      //Done
      searchRecommendedProductsPaginationStateCtrl.sink.add(SearchRecommendedProductsPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = SearchRecommendedProductsPaginationState.Done;

    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
      //Done
      searchRecommendedProductsPaginationStateCtrl.sink.add(SearchRecommendedProductsPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = SearchRecommendedProductsPaginationState.Done;
    }
  }
//endregion
}
