import 'package:flutter/material.dart';
import 'package:swadesic/model/order_response/refund_amount_calculation_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class RefundAmountCalculationCommonWidgets{


  //region  title
  static Widget title({required RefundAmountCalculationDetail refundAmountCalculationDetail,required Widget price}){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(refundAmountCalculationDetail.orderBreakupItemText!,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),
            price,
          ],
        ),

      ],
    );

  }
  //endregion



  //region Sub title
  static Widget subTitle({required RefundAmountCalculationDetail refundAmountCalculationDetail,bool breakupPriceVisible = false}){
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(refundAmountCalculationDetail.orderBreakupItemText!,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),
              Text(refundAmountCalculationDetail.orderBreakupItemValue!,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),
            ],
          ),

          ///Breakup amount
          refundAmountCalculationDetail.orderBreakupItemSubtext==null?const SizedBox():Visibility(
            visible: breakupPriceVisible,
            child: Container(
              margin: const EdgeInsets.only(top: 3),
                child: Text(refundAmountCalculationDetail.orderBreakupItemSubtext!,style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),)),
          )
        ],
      ),
    );

  }
  //endregion

}