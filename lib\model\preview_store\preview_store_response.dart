import 'package:swadesic/features/data_model/preview_store_data_model/preview_store_data_model.dart';

class PreviewStoreListResponse {
  final String message;
  final List<PreviewStoreData> data;

  PreviewStoreListResponse({
    required this.message,
    required this.data,
  });

  factory PreviewStoreListResponse.fromJson(Map<String, dynamic> json) {
    return PreviewStoreListResponse(
      message: json['message'] as String,
      data: (json['data'] as List)
          .map((store) => PreviewStoreData.fromJson(store as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'data': data.map((store) => store.toJson()).toList(),
    };
  }
}
