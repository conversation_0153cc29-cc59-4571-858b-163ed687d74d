import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/messaging/object_preview/models/object_preview_models.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'dart:developer' as developer;

/// Widget to display product previews
class ProductPreviewWidget extends StatelessWidget {
  final ProductPreview product;
  final bool isMe;

  const ProductPreviewWidget({
    Key? key,
    required this.product,
    required this.isMe,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get the first image URL if available
    String? imageUrl;
    if (product.productImages.isNotEmpty) {
      final imagePath = product.productImages.first.mediaPath;
        imageUrl = '${AppConstants.baseUrl}${imagePath}';
    }

    // Get the creator icon URL
    String creatorIconUrl = '';
    if (product.createdBy.icon != null && product.createdBy.icon!.isNotEmpty) {
        creatorIconUrl = '${AppConstants.baseUrl}${product.createdBy.icon!}';
      } 

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isMe ? AppColors.appRichBlack.withOpacity(0.9) : AppColors.textFieldFill2,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMe ? Colors.grey[800]! : Colors.grey[300]!,
          width: 0.5,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToProductScreen(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with creator info
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    // Creator avatar
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.grey[300],
                      backgroundImage: creatorIconUrl.isNotEmpty
                          ? NetworkImage(creatorIconUrl)
                          : null,
                      child: creatorIconUrl.isEmpty
                          ? Icon(
                              Icons.store,
                              color: Colors.grey[600],
                              size: 16,
                            )
                          : null,
                    ),
                    const SizedBox(width: 8),
                    // Creator name
                    Expanded(
                      child: Text(
                        product.createdBy.name,
                        style: AppTextStyle.access0(
                          textColor: isMe ? Colors.white : AppColors.appBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Product type indicator
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: isMe ? Colors.white24 : Colors.grey[300],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'PRODUCT',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: isMe ? Colors.white : Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Product image if available
              if (imageUrl != null)
                Container(
                  height: 180,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                  ),
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      developer.log(
                        'Error loading product image: ${error.toString()}',
                        name: 'ProductPreviewWidget',
                        stackTrace: stackTrace,
                      );
                      return Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.grey[500],
                          size: 40,
                        ),
                      );
                    },
                  ),
                ),

              // Product details
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product name
                    Text(
                      product.productName,
                      style: AppTextStyle.contentText0(
                        textColor: isMe ? Colors.white : AppColors.appBlack,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    // Brand name
                    Text(
                      'Brand: ${product.brandName}',
                      style: AppTextStyle.smallTextRegular(
                        textColor: isMe ? Colors.white70 : Colors.grey[700]!,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Navigate to the product screen
  void _navigateToProductScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BuyerViewSingleProductScreen(
          productReference: product.productReference,
        ),
      ),
    );
  }
}
