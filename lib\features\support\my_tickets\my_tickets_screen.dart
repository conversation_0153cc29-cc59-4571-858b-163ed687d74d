import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/support/my_tickets/my_tickets_bloc.dart';
import 'package:swadesic/features/support/support_common_widgets.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class MyTicketsScreen extends StatefulWidget {
  final String entityReference;
  
  const MyTicketsScreen({
    Key? key,
    required this.entityReference,
  }) : super(key: key);

  @override
  MyTicketsScreenState createState() => MyTicketsScreenState();
}

class MyTicketsScreenState extends State<MyTicketsScreen>
    with AutomaticKeepAliveClientMixin<MyTicketsScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  late MyTicketsBloc myTicketsBloc;

  @override
  void initState() {
    super.initState();
    myTicketsBloc = MyTicketsBloc(context, widget.entityReference);
    myTicketsBloc.init();
  }

  //region Dispose
  @override
  void dispose() {
    PaintingBinding.instance.imageCache.clear();
    imageCache.clear();
    myTicketsBloc.dispose();
    super.dispose();
  }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: StreamBuilder<MyTicketsState>(
        stream: myTicketsBloc.myTicketsCtrl.stream,
        builder: (context, snapshot) {
          return GestureDetector(
            onTap: () {
              CommonMethods.closeKeyboard(context);
            },
            child: body(),
          );
        },
      ),
    );
  }

  // endregion

  Widget body() {
    return StreamBuilder<MyTicketsState>(
      stream: myTicketsBloc.myTicketsCtrl.stream,
      initialData: MyTicketsState.Loading,
      builder: (context, snapshot) {
        // Loading
        if (snapshot.data == MyTicketsState.Loading) {
          return AppCommonWidgets.appCircularProgress();
        }
        
        // Success
        if (snapshot.data == MyTicketsState.Success) {
          return Column(
            children: [
              searchField(),
              myTicketsBloc.finalFilteredTicketsList.isEmpty
                  ? Expanded(
                      child: RefreshIndicator(
                        color: AppColors.brandBlack,
                        onRefresh: () async {
                          await myTicketsBloc.init();
                        },
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: Container(
                            alignment: Alignment.center,
                            height: MediaQuery.of(context).size.width,
                            child: NoResult(message: AppStrings.noTicketsYet, showIcon: false),
                          ),
                        ),
                      ),
                    )
                  : Expanded(child: ticketsList()),
            ],
          );
        }
        
        // Failed
        if (snapshot.data == MyTicketsState.Failed) {
          return AppCommonWidgets.errorWidget(onTap: () {
            myTicketsBloc.init();
          });
        }
        
        return AppCommonWidgets.appCircularProgress();
      },
    );
  }

//region Search field
  Widget searchField() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Row(
        children: [
          Expanded(
            child: AppSearchField(
              textEditingController: myTicketsBloc.searchFieldTextCtrl,
              hintText: "Search tickets with id, keywords",
              onChangeText: (v) {
                myTicketsBloc.onSearch();
              },
              onTapSuffix: () {
                myTicketsBloc.onSearch();
              },
              isAutoFocus: false,
              isActive: true,
            ),
          ),
          horizontalSizedBox(10),
          InkWell(
              onTap: () {
                // Add filter functionality if needed
                // myTicketsBloc.onTapFilter();
              },
              child: SvgPicture.asset(AppImages.filter2))
        ],
      ),
    );
  }
//endregion

//region Tickets list
  Widget ticketsList() {
    return Scrollbar(
      child: RefreshIndicator(
        color: AppColors.brandBlack,
        onRefresh: () async {
          await myTicketsBloc.init();
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: myTicketsBloc.finalFilteredTicketsList.length,
            itemBuilder: (BuildContext context, int index) {
              return InkWell(
                onTap: () {
                  myTicketsBloc.goToTicketDetail(
                    ticketDetail: myTicketsBloc.finalFilteredTicketsList[index],
                    ticketId: myTicketsBloc.finalFilteredTicketsList[index].feedbackId!,
                  );
                },
                child: SupportScreenCommonWidgets.allFeedbackCard(
                  feedbackDetail: myTicketsBloc.finalFilteredTicketsList[index],
                  onTapVote: () {
                    myTicketsBloc.onTapUpVote(
                      rootFeedback: myTicketsBloc.finalFilteredTicketsList[index],
                    );
                  },
                  onTapRightArrow: () {
                    // Handle right arrow tap if needed
                  },
                ),
              );
            },
          ),
        ),
      ),
    );
  }

//endregion
}
