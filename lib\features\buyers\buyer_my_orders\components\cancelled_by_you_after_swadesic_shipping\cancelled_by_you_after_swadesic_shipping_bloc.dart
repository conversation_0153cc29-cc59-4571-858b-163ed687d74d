import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/how_refund_amount_calculated/how_refund_amount_calculated.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class CancelledByYouAfterShippingBloc {
  // region Common Variables
  BuildContext context;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  late BuyerMyOrderServices buyerMyOrderServices;
  late GetOrderResponse buyerMyOrderResponse;
  final Order store;
  final List<SubOrder> subOrderList;

  // New properties
  bool isPartialCancel = false; // Check if any suborders have status other than cancelled_in_transit
  bool isReturnAccepted = false; // Check if the store allows return of products
  Map<String, bool> packageCancellationStatusMap = {}; // Map to store cancellation status for each package
  List<String> groupNameList = []; // List to store package numbers

  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  CancelledByYouAfterShippingBloc(this.context, this.buyerSubOrderBloc, this.store, this.subOrderList);

  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();

    // Calculate isPartialCancel - check if any suborders have status other than cancelled_in_transit
    isPartialCancel = store.subOrderList?.any((subOrder) =>
      subOrder.suborderStatus != 'cancelled_in_transit') ?? false;

    // Check if the store allows product returns
    isReturnAccepted = subOrderList.isNotEmpty &&
      subOrderList.first.returnAndWarrantyDescription != null &&
      subOrderList.first.returnAndWarrantyDescription!.isNotEmpty;

    // Calculate cancellation status for each package
    calculatePackageCancellationStatus();

    // Extract package numbers from suborders
    takeOutDisplayPackageNumbers();
  }

// endregion

// region On how refund calculated
  Future onTapHowRefundCalculated() {
    return CommonMethods.appBottomSheet(
        screen: HowRefundAmountCalculated(
      subOrderList: subOrderList,
      store: store, buyerSubOrderBloc:buyerSubOrderBloc,
    ), context: context, bottomSheetName: AppStrings.refundAmountCalculation).then((value) {
      //Unselect all
      CommonMethods.subOrderSelectUnSelectAll(isSelectAll: false, subOrderList: subOrderList);
    });
  }
// endregion

// region Getter methods for new properties
  // Check if any suborders have status other than cancelled_in_transit
  bool get isPartialCancellation => isPartialCancel;

  // Check if the store allows return of products
  bool get isProductReturnAccepted => isReturnAccepted;

  // Check if a specific package has partial cancellation
  bool isPartialCancellationForPackage(String packageNumber) {
    return packageCancellationStatusMap[packageNumber] ?? false;
  }

  // Get cancellation message based on package cancellation status and delivery status
  String getCancellationMessage(String packageNumber) {
    bool isPartialCancelForPackage = packageCancellationStatusMap[packageNumber] ?? false;

    // Check if any suborder in this package has ORDER_DELIVERED status
    bool isDelivered = false;

    // Find all suborders for this package
    if (store.subOrderList != null) {
      for (var suborder in store.subOrderList!) {
        if (suborder.displayPackageNumber == packageNumber) {
          // Check if delivered
          if (suborder.suborderStatus == 'ORDER_DELIVERED') {
            isDelivered = true;
            break;
          }
        }
      }
    }

    // If the package is delivered and it's a partial cancellation
    if (isDelivered && isPartialCancelForPackage) {
      return "Your package has been delivered. For the cancelled product, a return order will be initiated by the seller. Please keep the cancelled product ready for pickup.";
    }
    // If the package is delivered and it's a full cancellation
    else if (isDelivered && !isPartialCancelForPackage) {
      return "Your package has been delivered. Since you cancelled all products in this package, please initiate a return for the entire package.";
    }
    // If it's a partial cancellation but not delivered yet
    else if (isPartialCancelForPackage) {
      return "You have cancelled a product after shipping. It will still reach you. After 24hrs a return order will be placed for that cancelled product by the seller. Meanwhile, if you have any other products in the same package that you need to return, mark them as return within that 24 hrs. Seller will process the refund once RTO is completed.";
    }
    // If it's a full cancellation and not delivered yet
    else {
      return "Since you cancelled all the products in the package, just reject the package at delivery time. RTO will be initiated automatically. Seller will process the refund once RTO is completed.";
    }
  }

  // Calculate cancellation status for each package
  void calculatePackageCancellationStatus() {
    // We need to check if there are other non-cancelled suborders in the same package
    // First, get all package numbers from the cancelled suborders
    Set<String> cancelledPackageNumbers = {};
    for (var suborder in subOrderList) {
      if (suborder.displayPackageNumber != null) {
        cancelledPackageNumbers.add(suborder.displayPackageNumber!);
      }
    }

    // For each cancelled package, check if there are non-cancelled suborders in the same package
    for (var packageNumber in cancelledPackageNumbers) {
      // Count total suborders in this package from the full order list
      int totalSubordersInPackage = 0;
      int cancelledSubordersInPackage = 0;

      // Check all suborders in the order
      if (store.subOrderList != null) {
        for (var suborder in store.subOrderList!) {
          if (suborder.displayPackageNumber == packageNumber) {
            totalSubordersInPackage++;

            // Check if this suborder is in our cancelled list
            bool isInCancelledList = subOrderList.any((cancelledSuborder) =>
              cancelledSuborder.suborderNumber == suborder.suborderNumber);

            if (isInCancelledList) {
              cancelledSubordersInPackage++;
            }
          }
        }
      }

      // If not all suborders in the package are cancelled, it's a partial cancellation
      bool isPartialCancellation = cancelledSubordersInPackage < totalSubordersInPackage;
      packageCancellationStatusMap[packageNumber] = isPartialCancellation;

      // Debug log - only in development
      if (AppEnvironment.environment == Environment.dev) {
        debugPrint('Package: $packageNumber - Total: $totalSubordersInPackage, Cancelled: $cancelledSubordersInPackage, IsPartial: $isPartialCancellation');
      }
    }
  }

  //region Take out Display package number
  void takeOutDisplayPackageNumbers() {
    groupNameList.clear();
    for (var data in subOrderList) {
      if (data.displayPackageNumber != null) {
        groupNameList.add(data.displayPackageNumber!);
      }
    }
    ///Clear duplicates
    groupNameList = groupNameList.toSet().toList();
  }
  //endregion
// endregion
}
