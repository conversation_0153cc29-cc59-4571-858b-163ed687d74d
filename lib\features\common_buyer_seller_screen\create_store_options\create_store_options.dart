import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/comment_common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_preview_store/create_preview_store_screen.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding_get_started/seller_onboarding_get_started_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class CreateStoreOptions extends StatefulWidget {
  final bool allowCreateStore;
  final bool allowCreateTestStore;
  const CreateStoreOptions(
      {super.key,
      this.allowCreateStore = true,
      this.allowCreateTestStore = true});

  @override
  State<CreateStoreOptions> createState() => _CreateStoreOptionsState();
}

class _CreateStoreOptionsState extends State<CreateStoreOptions> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Container(
        margin: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.stretch,
          
          children: [
            //Create a store
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: InkWell(
                onTap: () async {
                  if (CommonMethods().isStaticUser()) {
                    return CommonMethods().goToSignUpFlow();
                  }
                  var screen = const CreatePreviewStoreScreen();
                  var route = MaterialPageRoute(builder: (context) => screen);
                  Navigator.push(
                      AppConstants.userStoreCommonBottomNavigationContext,
                      route);
                },
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  
                  children: [
                    SizedBox(
                      height: 35,
                      child: Row(
                        // mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            AppImages.storeIcon,
                            height: 30,
                            width: 30,
                          ),
                          const SizedBox(width: 10,),
                          Text( 
                            "Create a Swadesic store",
                            style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 5,),
                    
                    Text(
                      "Sell your products and services online/offline with zero commissions with order management, inventory, tracking, notifications and complete control over your business, branding and content.",
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack,
                      ),
                    ),
                    const SizedBox(height: 20,),
                    SizedBox(
                      width: double.infinity,
                      child: AppCommonWidgets.activeButton(
                        buttonName: "Start your store now",
                        onTap: () async {
                      //Check do we allow create store
                      if (!widget.allowCreateStore) {
                        return CommonMethods.toastMessage(
                            AppStrings.youCanNotCreateMoreThen, context,
                            toastShowTimer: 5);
                      }
                      // Allow store creation on web platform
                      //Else if static user then open login screen
                      else if (CommonMethods().isStaticUser()) {
                        return CommonMethods().goToSignUpFlow();
                      }
                      var screen = const SellerOnBoardingGetStartedScreen(
                        isTestStore: false,
                      );
                      var route = MaterialPageRoute(builder: (context) => screen);
                      Navigator.push(
                          AppConstants.userStoreCommonBottomNavigationContext,
                          route);
                    },
                      ),
                    ),
                    
                    
                  ],
                ),
              ),
            ),
            
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 25, horizontal: 10),
              child: Text('Or', style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
            ), 
            // Container(height: 1, width: 200, color: Colors.black),
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              
              children: [
                // //Create store
                // InkWell(
                //     onTap: () async {
                //       //Check do we allow create store
                //       if (!widget.allowCreateStore) {
                //         return CommonMethods.toastMessage(
                //             AppStrings.youCanNotCreateMoreThen, context,
                //             toastShowTimer: 5);
                //       }
                //       // Allow store creation on web platform
                //       //Else if static user then open login screen
                //       else if (CommonMethods().isStaticUser()) {
                //         return CommonMethods().goToSignUpFlow();
                //       }
                //       var screen = const SellerOnBoardingGetStartedScreen(
                //         isTestStore: false,
                //       );
                //       var route = MaterialPageRoute(builder: (context) => screen);
                //       Navigator.push(
                //           AppConstants.userStoreCommonBottomNavigationContext,
                //           route);
                //     },
                //     child: card(
                //         icon: AppImages.storeIcon,
                //         title: "Create a Swadesic store",
                //         subTitle: "This is a public store, visible to everyone. ")),
                // const SizedBox(height: 20),
                //Create test store
                InkWell(
                    onTap: () async {
                      //Check do we allow create test store
                      if (!widget.allowCreateTestStore) {
                        return CommonMethods.toastMessage(
                            AppStrings.youCanNotCreateMoreThen, context,
                            toastShowTimer: 5);
                      }
                      // Allow test store creation on web platform
                      //Else if static user then open login screen
                      else if (CommonMethods().isStaticUser()) {
                        return CommonMethods().goToSignUpFlow();
                      }
                      var screen = const SellerOnBoardingGetStartedScreen(
                        isTestStore: true,
                      );
                      var route = MaterialPageRoute(builder: (context) => screen);
                      Navigator.push(
                          AppConstants.userStoreCommonBottomNavigationContext,
                          route);
                    },
                    child: card(
                        icon: AppImages.testStoreIcon,
                        title: "Create a Test store",
                        subTitle:
                            "Test store will visible to you only. You can place test orders and testother flows upfront.")
                  ),
                const SizedBox(height: 20),

                  //Create a preview store
                InkWell(
                    onTap: () async {
                      //Check do we allow create test store
                      if (!widget.allowCreateTestStore) {
                        return CommonMethods.toastMessage(
                            AppStrings.youCanNotCreateMoreThen, context,
                            toastShowTimer: 5);
                      }
                      // Allow test store creation on web platform
                      //Else if static user then open login screen
                      else if (CommonMethods().isStaticUser()) {
                        return CommonMethods().goToSignUpFlow();
                      }
                      var screen = const CreatePreviewStoreScreen();
                      var route = MaterialPageRoute(builder: (context) => screen);
                      Navigator.push(
                          AppConstants.userStoreCommonBottomNavigationContext,
                          route);
                    },
                    child: card(
                        icon: AppImages.previewStoreIcon,
                        title: "Create a Preview store",
                        subTitle:
                            "Get a live glimpse of what your buyers will see when they visit your Swadesic store - built with sample data to spark your vision. Preview store will visible to you and others with storelink.")
                  ),
              ],
            ),
            
            
              const SizedBox(height: 50,),
          ],
        )
      );
  }
//endregion

//region Card
  Widget card(
      {required String title, required String subTitle, required String icon}) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: AppColors.textFieldFill1,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: AppColors.lightStroke),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(icon),
          const SizedBox(
            width: 5,
          ),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack),
                ),
                Text(
                  subTitle,
                  maxLines: 3,
                  style: AppTextStyle.smallTextRegular(
                      textColor: AppColors.appBlack),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
//endregion
}
