import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_payment/buyer_payment_status/buyer_payment_status_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_payment_options_responses/payment_status_check.dart';
import 'package:swadesic/services/buyer_payment_services/buyer_payment_services.dart';
import 'package:swadesic/services/buyer_payment_services/upi_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:webview_flutter/webview_flutter.dart';


class UpiPaymentWaitingBloc {
  // region Common Methods
  BuildContext context;
  // final String paymentType;
  bool helloVisible = false;
  late PaymentStatusCheckResponse paymentStatusCheckResponse;
   String paymentStatus = "PAYMENT_PENDING";
  final String transactionId;
  final String paymentChannel;
  final String amount;
  final String orderNumber;
  final bool isUpiIntent;


//TXN_FAILURE
  //PENDING

  // endregion
  //region Controller
  final screenRefreshCtrl = StreamController<bool>.broadcast();
  late WebViewController webViewController;
  //endregion

  // region | Constructor |
  UpiPaymentWaitingBloc(this.context, this.transactionId, this.paymentChannel, this.amount, this.orderNumber, this.isUpiIntent);
  // endregion

  // region Init
  void init()async{
    await Future.delayed(const Duration(seconds:3));

    checkStatus();
    //netBankingTransaction();
    // if(paymentType == "bank"){
    //   await Future.delayed(const Duration(milliseconds:1500 ));
    //   goToWebView('https://netbanking.kotak.com/knb2/');
    // }
  }
  // endregion


  //region Call upi status every 2 second
  void checkStatus()async{
    //Success
    if(paymentStatus == 'PAYMENT_SUCCESS'){
      // screenRefreshCtrl.sink.add(true);
      goToPaymentStatusScreen(paymentStatus:paymentStatus );
      return;
    }
    //Cancelled
    else if(paymentStatus != 'PAYMENT_SUCCESS' && paymentStatus != 'PAYMENT_PENDING'){
      // screenRefreshCtrl.sink.add(false);
      goToPaymentStatusScreen(paymentStatus: "PAYMENT_CANCELLED");
      return;
    }
    else{
      await Future.delayed(const Duration(milliseconds:100));
      checkUpiPaymentStatus();
    }
  }
  //endregion


  //region Check upi payment status
  checkUpiPaymentStatus()async{
    //region Try
    try{
      paymentStatusCheckResponse = await UpiService().getUpiPaymentStatus(transactionId: transactionId, paymentChannel: paymentChannel, amount: amount, orderNumber: orderNumber);
        // paymentStatusCheckResponse = await buyerPaymentServices.paymentStatusCheck();
        paymentStatus = paymentStatusCheckResponse.data!.body!.resultInfo!.resultStatus!;
        // //print(paymentStatus);
        checkStatus();
    }
    //endregion
    on ApiErrorResponseMessage catch(error) {
      //print(error.message);
          CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);


      return;
    }
    catch(error){
      //print(error);
      var snackBar =  SnackBar(content: Text(error.toString()));
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
      return;
    }
  }
  //endregion



  //region  Html web
  String bankWeb (){
   return AppConstants.htmlForm;
  }
  //endregion

//region Go to Buyer payment Status Screen
  void goToPaymentStatusScreen({required String paymentStatus}) {
    var screen = BuyerPaymentStatus(paymentStatus:paymentStatus, paymentStatusCheckResponse: paymentStatusCheckResponse,);
    var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route).then((value) {
    //
    // });
    Navigator.pushReplacement(context, route).then((value) {
    });
  }
//endregion

//region Dispose
void dispose(){
  screenRefreshCtrl.close();
}
//endregion

}
