import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/model/support/create_support_response.dart';
import 'package:swadesic/services/add_feedback_responses/add_feedback_services.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class NeedResolutionBloc {
  // region Common Variables
  BuildContext context;
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  late BuyerMyOrderServices buyerMyOrderServices;
  late AddSupportServices addSupportServices;
  final String escalationReason;
  final String? packageNumber;


  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  final notesTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  NeedResolutionBloc(this.context, this.subOrderList, this.buyerSubOrderBloc, this.order,this.escalationReason, this.packageNumber,);

  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    addSupportServices = AddSupportServices();
  }

// endregion

  //region Create escalation support ticket
  Future<void> _createEscalationSupportTicket() async {
    try {
      await addSupportServices.addSupport(
        screenCategory: "Orders/Delivery flow", // Using existing category from buyer category list
        title: "BUYER ESCALATION - PRODUCT NOT DELIVERED",
        detail: "[${subOrderList.first.suborderNumber}] - ${notesTextCtrl.text}",
        feedbackType: "ESCALATION", // Using ESCALATION type as requested
        context: context,
        group: "DELIVERY_FAILURE",
        targetReference: order.storeReference!, // Store reference as target
      );
    } catch (error) {
      // Log error but don't stop the main escalation process
      // Support ticket creation failure shouldn't prevent order escalation
    }
  }
  //endregion

  //region Add need resolution
  addNeedResolution() async {
    //Check text field is empty or not
    if(notesTextCtrl.text.isEmpty){
      return CommonMethods.toastMessage(AppStrings.notesCanNotBeEmpty, context);
    }

    // Store context reference to avoid async gap issues
    final currentContext = context;

    //Sub order number list
    List<String> subOrderNumberList = [];
    for(var data  in subOrderList){
      subOrderNumberList.add(data.suborderNumber!);
    }

    //region Try
    try {
      // First, create the escalation in the order system
      await buyerMyOrderServices.buyerNeedResolution(
          orderNumber: order.orderNumber!,
          escalationReason:escalationReason,
          notes: notesTextCtrl.text,
          subOrderNumberList: subOrderNumberList.join("|"),
          subOrderStatus: subOrderList.first.suborderStatus!,
          packageNumber: packageNumber,
          storeReference: order.storeReference!,
       );

      // Then, create a support ticket for escalation tracking
      await _createEscalationSupportTicket();

      // Check if context is still mounted before using it
      if (currentContext.mounted) {
        //Close bottom sheet
        Navigator.pop(currentContext);
        //Message
        CommonMethods.toastMessage(AppStrings.escalationSuccessful, currentContext);
      }

      //Call the get sub order api
      buyerSubOrderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage {
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      }
      return;
    } catch (error) {
      //print(error);
      if (currentContext.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      }
      return;
    }
  }
//endregion

}
