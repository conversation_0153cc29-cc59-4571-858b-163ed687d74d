import 'dart:async';

import 'package:flutter/material.dart';

class BuyerSuggestImprovementBloc {
  // region Common Variables
  BuildContext context;
  DateTime selectedDate = DateTime.now();
  bool isAddUpdateFieldVisible = false;
  DateTimeRange dateTimeRange = DateTimeRange(start: DateTime.now(),end: DateTime(DateTime.now().year,DateTime.now().month,DateTime.now().day + 5));

  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller
  final deliveryPersonNameCtrl = TextEditingController();
  final deliveryPersonContactCtrl = TextEditingController();
  final trackingLinkCtrl = TextEditingController();
  final dateCtrl = TextEditingController();
  final cancelReasonTextCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  BuyerSuggestImprovementBloc(this.context);
  // endregion

  // region Init
  void init() {

  }
// endregion




//region OnTap Calender
  void openCalender() async{
    DateTimeRange? pickedRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(DateTime.now().year,DateTime.now().month,DateTime.now().day),
      lastDate:DateTime(DateTime.now().year+2),
      initialDateRange: dateTimeRange,
      // builder: (context,child){
      //   return Theme(
      //     data: ThemeData.light().copyWith(
      //       primaryColor: AppColors.green2,
      //       colorScheme: ColorScheme.light(primary: AppColors.green4),
      //       buttonTheme: ButtonThemeData(
      //           textTheme: ButtonTextTheme.primary
      //       ),
      //     ),
      //     child: child!,
      //   );
      // }
    );
    if(pickedRange != null && pickedRange!=dateTimeRange){
      dateTimeRange =  pickedRange;
    }

    //print(dateTimeRange.start);
    //print(dateTimeRange.end);


  }
//endregion


//region On tap Add and update
  void onTapAddUpdate(){
    isAddUpdateFieldVisible = !isAddUpdateFieldVisible;
    bottomSheetRefresh.sink.add(true);
  }
//endregion

//region Open calender
  Future<void> onTapCalender(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: selectedDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    if (picked != null && picked != selectedDate) {
      selectedDate = picked;
    }
  }
  //endregion




}
