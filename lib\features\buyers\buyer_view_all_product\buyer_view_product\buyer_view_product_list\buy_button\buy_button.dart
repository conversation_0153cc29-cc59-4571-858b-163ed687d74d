import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/buy_button_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/disclaimer_bottom_sheet.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buy_button/pickup_locations_bottom_sheet.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';

class BuyButton extends StatefulWidget {
  final Product product;
  final bool isFromAddProduct;
  final bool isGoToLatestVersion;
  final ProductDetailFullCardBloc? productDetailFullCardBloc;

  const BuyButton(
      {super.key,
      required this.product,
      this.isFromAddProduct = false,
      required this.isGoToLatestVersion,
      this.productDetailFullCardBloc});

  @override
  State<BuyButton> createState() => _BuyButtonState();
}

class _BuyButtonState extends State<BuyButton> {
  //Buy button bloc
  late BuyButtonBloc buyButtonBloc;

  //region Init
  @override
  void initState() {
    buyButtonBloc = BuyButtonBloc(context, productDetailFullCardBloc: widget.productDetailFullCardBloc);
    buyButtonBloc.buttonRefreshCtrl.sink.add(true);
    super.initState();
  }

  @override
  void dispose() {
    buyButtonBloc.buttonRefreshCtrl.close();
    buyButtonBloc.isButtonLoadingCtrl.close();
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Helper Methods
  /// Check if the selected variant is out of stock
  bool _isSelectedVariantOutOfStock() {
    // If no productDetailFullCardBloc or no selected variant, fall back to product.inStock
    if (widget.productDetailFullCardBloc == null ||
        widget.productDetailFullCardBloc!.selectedVariant == null) {
      return widget.product.inStock == 0;
    }

    // Get the selected variant reference
    String? selectedVariantReference = widget.productDetailFullCardBloc!.selectedVariant!.variantReference;

    // If no variant reference, fall back to product.inStock
    if (selectedVariantReference == null) {
      return widget.product.inStock == 0;
    }

    // Find the matching variant in availableVariants and check its stock
    for (var variant in widget.productDetailFullCardBloc!.availableVariants) {
      if (variant.variantReference == selectedVariantReference) {
        return variant.stock <= 0;
      }
    }

    // If variant not found in availableVariants, fall back to product.inStock
    return widget.product.inStock == 0;
  }
  //endregion

  //region Body
  Widget body() {
    return Consumer<ProductDataModel>(
      builder: (BuildContext context, ProductDataModel value, Widget? child) {
        // Product product = value.allProducts.firstWhere((element) => element.productReference == widget.product.productReference);
        Product product;
        //if from add product
        if (widget.isFromAddProduct) {
          product = widget.product;
        } else {
          product = value.allProducts.firstWhere((element) =>
              element.productReference == widget.product.productReference);
        }

        //  for (var data in value.allProducts) {
        //   if (data.productReference == widget.product.productReference) {
        //     product = data;
        //   }
        // }

        // Product product = widget.product;
        return StreamBuilder<bool>(
            stream: buyButtonBloc.buttonRefreshCtrl.stream,
            initialData: true,
            builder: (context, snapshot) {
              ///If pin code changed
              if (product.isPinCodeChanged) {
                return pinCodeChange(product: product);
              }

              ///Seller view
              if (AppConstants.appData.isStoreView!) {
                return sellerViewButton(product: product);
              }

              ///Buyer view
              else if (AppConstants.appData.isUserView!) {
                return buyerViewButton(product: product);
              }

              return const SizedBox();
            });
      },
    );
  }

//endregion

  ///Pin code changed
  //region Pin code change
  Widget pinCodeChange({required Product product}) {
    return StreamBuilder<bool>(
        stream: buyButtonBloc.isButtonLoadingCtrl.stream,
        initialData: false,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data!) {
            return AppCommonWidgets.appCircularProgress();
          }
          //Else
          return ProductCommonWidgets.refreshToUpdate(
              product: product,
              context: context,
              onTapBuy: () {
                buyButtonBloc.getSingleProduct(product: product);
              });
        });
  }

  //endregion

  ///Buyer- Test
  //region Buyer view and test store button action
  Widget buyerViewAndTestStoreButton({required Product product}) {
    // Retrieve the data from the StoreInfoModel
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context);

    ///If same product+variant combination already in cart (Go to cart)
    String? selectedVariantReference = widget.productDetailFullCardBloc?.selectedVariant?.variantReference;
    if (shoppingCartQuantityDataModel.isProductVariantInCart(widget.product.productReference!, selectedVariantReference??"")) {
      return Opacity(
          opacity: _isSelectedVariantOutOfStock() ||
                  !buyButtonBloc.isProductBelongsToLoggedInUser(
                      storeHandle: widget.product.storehandle!,
                      context: context)
              ? 0.4
              : 1.0,
          child: const SizedBox()
          // child: ProductCommonWidgets.buyAddAndGoToButton(
          //     product: widget.product,
          //     buttonName: AppStrings.goToCart,
          //     textColor: AppColors.appWhite,
          //     onTap: () {
          //       buyButtonBloc.onTapTestButton(product: product) ? buyButtonBloc.gotoShoppingCart() : null;
          //     }),
          );
    }

    ///If cart is not empty (Add to cart) - but this specific variant is not in cart
    if (shoppingCartQuantityDataModel.productReferenceList.isNotEmpty) {
      return Opacity(
          opacity: _isSelectedVariantOutOfStock() ||
                  !buyButtonBloc.isProductBelongsToLoggedInUser(
                      storeHandle: widget.product.storehandle!,
                      context: context)
              ? 0.4
              : 1.0,
          child: const SizedBox()
          // child: ProductCommonWidgets.buyAddAndGoToButton(
          //     product: widget.product,
          //     buttonName: AppStrings.addToCart,
          //     textColor: AppColors.appWhite,
          //     onTap: () {
          //       // buyButtonBloc.addToCart(product: widget.product,goToCart: false);
          //       buyButtonBloc.onTapTestButton(product: product) ? buyButtonBloc.addToCart(product: widget.product, goToCart: false) : null;
          //     }),
          );
    }

    ///If cart is empty (Buy now)
    if (shoppingCartQuantityDataModel.productReferenceList.isEmpty) {
      return Opacity(
          opacity: _isSelectedVariantOutOfStock() ||
                  !buyButtonBloc.isProductBelongsToLoggedInUser(
                      storeHandle: widget.product.storehandle!,
                      context: context)
              ? 0.4
              : 1.0,
          child: const SizedBox()
          // child: ProductCommonWidgets.buyAddAndGoToButton(
          //     product: widget.product,
          //     buttonName: AppStrings.buyNow,
          //     textColor: AppColors.appWhite,
          //     onTap: () {
          //       // buyButtonBloc.addToCart(product: widget.product,goToCart: true);
          //       buyButtonBloc.onTapTestButton(product: product) ? buyButtonBloc.addToCart(product: widget.product, goToCart: true) : null;
          //     }),
          );
    }

    return const SizedBox();
  }

//endregion

  ///Buyer
  //region Buyer view button action
  Widget buyerViewButton({required Product product}) {
    //print("\n=== Buy Button State ===");
    //print("Product Reference: ${product.productReference}");
    //print("Is Buy Enabled: ${product.isBuyEnable}");
    //print("Product Status Message: ${product.productStatusMessage}");
    //print("Fulfillment Options: ${product.fulfillmentOptions}");
    //print("Is Deleted: ${product.isDeleted}");
    //print("Is Go To Latest Version: ${widget.isGoToLatestVersion}");
    //print("isdeliverable: ${product.deliverable}");
    //print("====================\n");

    // Retrieve the data from the StoreInfoModel
    ShoppingCartQuantityDataModel shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context);

    ///Pin code is changed
    if (product.isPinCodeChanged) {
      //print("Buy Button - Showing Pin Code Change UI");
      return pinCodeChange(product: product);
    }

    ///If product is deleted
    else if (product.isDeleted!) {
      //print("Buy Button - Showing Deleted Product UI");
      return ProductCommonWidgets.deletedProductButton(
          product: product, context: context);
    }

    ///If is go to latest version is true
    else if (widget.isGoToLatestVersion) {
      //print("Buy Button - Showing Go To Latest Version UI");
      return ProductCommonWidgets.sellerBuyAndUpdateStock(
          product: product,
          onTap: () {
            buyButtonBloc.goToSingleProductScreen(
                productReference: product.productReference!);
          },
          context: context,
          message: "${AppStrings.goToLatestVersion}");
    }

    ///If buy enable is true
    else if (product.isBuyEnable!) {
      //TODO: Check variant stock if variants exist
      if (_isSelectedVariantOutOfStock()) {
        // Create a temporary product with out of stock message
        Product tempProduct = Product(
          productReference: product.productReference,
          productName: product.productName,
          brandName: product.brandName,
          productStatusMessage: 'The product is currently out of stock',
          isBuyEnable: false,
        );
        return ProductCommonWidgets.statusMessageButton(
            product: tempProduct,
            context: context);
      }
      //print("Buy Button - Product is Buy Enabled");
      if (product.fulfillmentOptions == "IN_STORE_PICKUP") {
        //print("Buy Button - Showing IN_STORE_PICKUP button");
        return Container(
          decoration: BoxDecoration(
            color: AppColors.appBlack,
            borderRadius: BorderRadius.circular(
                CommonMethods.calculateWebWidth(context: context) * 0.03),
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(
                CommonMethods.calculateWebWidth(context: context) * 0.03),
            child: InkWell(
              onTap: () => buyButtonBloc.showPickupLocations(product: product),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 14),
                width: double.infinity,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        AppStrings.buyAtStore,
                        style:
                            AppTextStyle.access0(textColor: AppColors.appWhite),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      } else if (product.fulfillmentOptions == "DELIVERY_AND_IN_STORE_PICKUP") {
            if(product.deliverable == true){
                return Container(
                  decoration: BoxDecoration(
                    color: AppColors.appBlack,
                    borderRadius: BorderRadius.circular(
                        CommonMethods.calculateWebWidth(context: context) * 0.03),
                  ),
                  child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(
                      CommonMethods.calculateWebWidth(context: context) * 0.03),
                  child: InkWell(
                    onTap: () {
                      //Return if static user action
                      if (CommonMethods().isStaticUser()) {
                        CommonMethods().goToSignUpFlow();
                        return;
                      }
                      //If order able is false
                      else if (!product.isBuyEnable!) {
                        CommonMethods.toastMessage(
                            product.productStatusMessage!, context);
                        return;
                      }
                      //Return go to buy or add to cart
                      buyButtonBloc.goAddAndBuy(product: product);
                    },
                    child: Container(
                      width: double.infinity,
                      height: 43, // Fixed height for better tap target
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24), // Half of height for pill shape
                      ),
                      clipBehavior: Clip.antiAlias, // Ensure the child respects the border radius
                      child: Row(
                        children: [
                          const SizedBox(width: 50),
                          Expanded(
                            child: Container(
                              alignment: Alignment.center,
                              decoration: const BoxDecoration(
                                color: AppColors.appBlack, // Assuming red is the main button color
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(24),
                                  bottomLeft: Radius.circular(24),
                                ),
                              ),
                              child: Text(
                                ProductCommonWidgets.buttonNames(
                                  product: product,
                                  shoppingCartQuantityDataModel:
                                      shoppingCartQuantityDataModel,
                                  selectedVariantReference: widget.productDetailFullCardBloc?.selectedVariant?.variantReference,
                                ),
                                style: AppTextStyle.access0(textColor: AppColors.appWhite),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                          Container(
                            width: 56, // Fixed width for the arrow section
                            height: double.infinity,
                            decoration: const BoxDecoration(
                              color: AppColors.appBlack,
                              borderRadius: BorderRadius.only(
                                topRight: Radius.circular(24),
                                bottomRight: Radius.circular(24),
                              ),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () => buyButtonBloc.showBuyOptions(product: product),
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(24),
                                  bottomRight: Radius.circular(24),
                                ),
                                child: Center(
                                  child: SvgPicture.asset(
                                    AppImages.arrow,
                                    height: 20,
                                    width: 28,
                                    color: AppColors.appWhite,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ));
            }
            else{
              return Container(
                decoration: BoxDecoration(
                  color: AppColors.appBlack,
                  borderRadius: BorderRadius.circular(
                      CommonMethods.calculateWebWidth(context: context) * 0.03),
                ),
                child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(
                      CommonMethods.calculateWebWidth(context: context) * 0.03),
                  child: InkWell(
                    onTap: () => buyButtonBloc.showPickupLocations(product: product),
                    child: Container(
                      padding:
                          const EdgeInsets.symmetric(vertical: 10, horizontal: 14),
                      width: double.infinity,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Text(
                              AppStrings.buyAtStore,
                              style:
                                  AppTextStyle.access0(textColor: AppColors.appWhite),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
              
            }
      }
      else{
        if (product.deliverable == true){
          return Container(
                decoration: BoxDecoration(
                  color: AppColors.appBlack,
                  borderRadius: BorderRadius.circular(
                      CommonMethods.calculateWebWidth(context: context) * 0.03),
                ),
                child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(
                      CommonMethods.calculateWebWidth(context: context) * 0.03),
                  child: InkWell(
                    onTap: () => buyButtonBloc.goAddAndBuy(product: product),
                    child: Container(
                      padding:
                          const EdgeInsets.symmetric(vertical: 10, horizontal: 14),
                      width: double.infinity,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Text(
                              AppStrings.buyNow,
                              style:
                                  AppTextStyle.access0(textColor: AppColors.appWhite),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
        }
        else {
          return ProductCommonWidgets.statusMessageButton(
          product: product, context: context);
        }
        
      } 
      // else {
      //   return ProductCommonWidgets.buyAddAndGoToButton(
      //       product: product,
      //       onTapBuy: () {
      //         //Return if static user action
      //         if (CommonMethods().isStaticUser()) {
      //           CommonMethods().goToSignUpFlow();
      //           return;
      //         }
      //         //If order able is false
      //         else if (!product.isBuyEnable!) {
      //           CommonMethods.toastMessage(
      //               product.productStatusMessage!, context);
      //           return;
      //         }
      //         //Return go to buy or add to cart
      //         buyButtonBloc.goAddAndBuy(product: product);
      //       },
      //       context: context,
      //       selectedVariantReference: widget.productDetailFullCardBloc?.selectedVariant?.variantReference);
      // }
    }

    ///If is buy enable is false
    else if (!product.isBuyEnable!) {
      //print("Buy Button - Buy Not Enabled: ${product.productStatusMessage}");
      return ProductCommonWidgets.statusMessageButton(
          product: product, context: context);
    }

    return const SizedBox();
  }

//endregion

  ///Seller
  //region Seller view button action
  Widget sellerViewButton({required Product product}) {
    final loggedInUser = Provider.of<LoggedInUserInfoDataModel>(
        context); // Access the provided data

    ///If from add product
    if (widget.isFromAddProduct) {
      return ProductCommonWidgets.buyAddAndGoToButton(
          product: product, onTapBuy: () {}, context: context,
          selectedVariantReference: widget.productDetailFullCardBloc?.selectedVariant?.variantReference);
    }

    ///If product is deleted
    else if (product.isDeleted!) {
      return ProductCommonWidgets.deletedProductButton(
          product: product, context: context);
    }

    ///If is go to latest version is true
    else if (widget.isGoToLatestVersion) {
      return ProductCommonWidgets.sellerBuyAndUpdateStock(
          product: product,
          onTap: () {
            buyButtonBloc.goToSingleProductScreen(
                productReference: product.productReference!);
          },
          context: context,
          message: "${AppStrings.goToLatestVersion}");
    }

    ///Seller view other store product
    else if (product.storeReference != AppConstants.appData.storeReference) {
      return ProductCommonWidgets.sellerBuyAndUpdateStock(
          product: product,
          onTap: () {
            buyButtonBloc.switchToBuyer(
                productReference: product.productReference!);
          },
          context: context,
          message: "${AppStrings.buyAs} ${loggedInUser.userDetail!.userName}");
    }

    ///Seller view own product
    else if (product.storeReference == AppConstants.appData.storeReference) {
      return ProductCommonWidgets.sellerBuyAndUpdateStock(
          product: product,
          onTap: () {
            buyButtonBloc.onTapUpdateStock(product: widget.product);
          },
          context: context,
          message: "${AppStrings.updateInventory}");
    }

    return const SizedBox();

    ///If from add product
    // if (widget.isFromAddProduct) {
    //   return ProductCommonWidgets.buyAddAndGoToButton(
    //       buttonColor: AppColors.brandGreen, textColor: AppColors.appWhite, product: widget.product, buttonName: AppStrings.buyNow, onTap: () {});
    // }

    ///If seller viewing own product
    // else if (AppConstants.appData.storeReference == widget.product.storeReference) {
    //   return ProductCommonWidgets.buyAddAndGoToButton(
    //       product: widget.product,
    //       buttonName: AppStrings.updateStock,
    //       onTap: () {
    //         buyButtonBloc.onTapUpdateStock(product: widget.product);
    //       });
    // }

    ///If Store view other store product
    // else if (AppConstants.appData.storeReference! != widget.product.storeReference) {
    //   return ProductCommonWidgets.buyAddAndGoToButton(
    //       product: widget.product,
    //       buttonName: AppStrings.switchToBuyer,
    //       onTap: () {
    //         buyButtonBloc.switchToBuyer(productReference: widget.product.productReference!);
    //       });
    // }
  }

//endregion
}
