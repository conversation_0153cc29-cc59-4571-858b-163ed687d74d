import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';

class ReturnRequestedBloc {
  // region Common Variables
  BuildContext context;
  late BuyerMyOrderServices buyerMyOrderServices;
  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  ReturnRequestedBloc(this.context);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
  }
// endregion









}
