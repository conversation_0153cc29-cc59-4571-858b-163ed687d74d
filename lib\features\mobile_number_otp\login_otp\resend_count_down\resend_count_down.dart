import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/mobile_number_otp/login_otp/login_otp_bloc.dart';
import 'package:swadesic/features/mobile_number_otp/login_otp/resend_count_down/resend_count_down_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class ResendCountDown extends StatefulWidget {
  final bool isResendMobileOtp;
  final bool isResendEmailOtp;
  final Function onTap;
  final bool isFromOnboardingScreen;
  const ResendCountDown(
      {super.key,
      this.isResendMobileOtp = false,
      this.isResendEmailOtp = false,
      required this.onTap,
      this.isFromOnboardingScreen = false});

  @override
  State<ResendCountDown> createState() => _ResendCountDownState();
}

class _ResendCountDownState extends State<ResendCountDown> {
  //region Bloc
  late ResendCountDownBloc resendCountDownBloc;
  //endregion
  //region Init
  @override
  void initState() {
    resendCountDownBloc = ResendCountDownBloc(context);
    resendCountDownBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    resendCountDownBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return countDownFromOnBoarding();
    // return widget.isFromOnboardingScreen
    //     ? countDownFromOnBoarding()
    //     : countDown();
  }
  //region Count down
  Widget countDown({bool isShowOnLeftSide = false}) {
    return Container(
      padding: const EdgeInsets.only(bottom: 6),
      child: Align(
        alignment: isShowOnLeftSide
            ? Alignment.centerLeft
            : Alignment.centerRight,
        child: SizedBox(
          height: 30,
          child: StreamBuilder<int>(
              stream: resendCountDownBloc.countDownCtrl.stream,
              initialData: resendCountDownBloc.startTime,
              builder: (context, snapshot) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isShowOnLeftSide)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          snapshot.data == 0 ? "" : "${snapshot.data}s",
                          style: AppTextStyle.smallText(
                              textColor: AppColors.appBlack),
                        ),
                      ),
                    
                    CupertinoButton(
                        disabledColor: snapshot.data != 0
                            ? AppColors.textFieldFill1
                            : AppColors.brandBlack,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(120)),
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        color: AppColors.brandBlack,
                        onPressed: snapshot.data == 0
                            ? () {
                                // //Resend OTP
                                // resendCountDownBloc.loginOtpBloc.resendOtp(body: widget.isResendMobileOtp?{
                                //   "send_otp_to":"${widget.loginOtpBloc.phoneNumber}"
                                // }
                                // :
                                // {
                                //   "send_otp_to":"${widget.loginOtpBloc.email}"
                                // }
                                // );
                                widget.onTap();
                                //Restart count down start
                                resendCountDownBloc.countDown();
                              }
                            : null,
                        child: Center(
                          child: Center(
                              child: Text(
                            AppStrings.resend,
                            style: AppTextStyle.smallText(
                                textColor: snapshot.data != 0
                                    ? AppColors.disableBlack
                                    : AppColors.appWhite),
                          )),
                        )),
                    if (!isShowOnLeftSide)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          snapshot.data == 0 ? "" : "${snapshot.data}s",
                          style: AppTextStyle.smallText(
                              textColor: AppColors.appBlack),
                        ),
                      ),
                  ],
                );
              }),
        ),
      ),
    );
  }
//endregion

//region CountDownFromOnboarding
  Widget countDownFromOnBoarding() {
    return StreamBuilder<int>(
        stream: resendCountDownBloc.countDownCtrl.stream,
        initialData: resendCountDownBloc.startTime,
        builder: (context, snapshot) {
          return CupertinoButton(
            // disabledColor: snapshot.data != 0 ? AppColors.inActiveGreen.withOpacity(0.5) : AppColors.inActiveGreen,
            borderRadius: BorderRadius.circular(
                CommonMethods.calculateWebWidth(context: context) * 0.03),
            padding: const EdgeInsets.symmetric(horizontal: 10),
            color: AppColors.textFieldFill1,
            onPressed: snapshot.data == 0
                ? () {
                    widget.onTap();
                    //Restart count down start
                    resendCountDownBloc.countDown();
                  }
                : null,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Text(snapshot.data == 0 ? "" : "${snapshot.data}s",
                //     style: AppTextStyle.smallText(textColor: AppColors.appBlack)),
                // const SizedBox(width: 11),
                Text(
                  snapshot.data == 0
                      ? AppStrings.resendButton
                      : "${AppStrings.resendButton} in ${snapshot.data}s",
                  style: AppTextStyle.smallText(
                      textColor: snapshot.data == 0
                          ? AppColors.appBlack
                          : AppColors.writingBlack1),
                )
              ],
            ),
          );
        });
  }
//endregion
}
