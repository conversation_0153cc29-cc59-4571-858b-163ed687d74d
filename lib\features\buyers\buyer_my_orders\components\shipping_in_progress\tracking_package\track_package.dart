import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/shipping_in_progress/deliver_and_return_person_logistic/delivery_and_return_person_logistic.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/shipping_in_progress/tracking_package/track_package_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/shipping_history/shipping_history_screen.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class TrackPackage extends StatefulWidget {
  final List<SubOrder> suborderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;

  const TrackPackage({
    Key? key,
    required this.suborderList,
    required this.buyerSubOrderBloc,
    required this.order,
  }) : super(key: key);

  @override
  State<TrackPackage> createState() => _TrackPackageState();
}

class _TrackPackageState extends State<TrackPackage> {
  // region Bloc
  late TrackPackageBloc trackPackageBloc;

  // endregion

  // region Init
  @override
  void initState() {
    trackPackageBloc = TrackPackageBloc(context, widget.suborderList);
    trackPackageBloc.init();
    super.initState();
  }

  // endregion

  @override
  Widget build(BuildContext context) {
    return Container(
        color: AppColors.appWhite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              deliveryUpdateHistory(),
              verticalSizedBox(40),

              ///Drop down
              StreamBuilder<bool>(
                  stream: trackPackageBloc.bottomSheetRefresh.stream,
                  builder: (context, snapshot) {
                    return InkWell(
                      onTap: () {
                        trackPackageBloc.onTapProductList();
                      },
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 6.5),
                        decoration: BoxDecoration(
                            color: AppColors.lightestGrey2,
                            borderRadius: BorderRadius.circular(7)),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ///Drop down
                            Container(
                              padding: const EdgeInsets.all(10),
                              child: Row(
                                children: [
                                  //Grand total
                                  Expanded(
                                      child: Text(
                                    AppStrings.productList,
                                    style: AppTextStyle.settingHeading1(
                                        textColor: AppColors.appBlack),
                                  )),
                                  horizontalSizedBox(10),
                                  trackPackageBloc.isProductListVisible
                                      ? RotatedBox(
                                          quarterTurns: 3,
                                          child: RotatedBox(
                                              quarterTurns: 4,
                                              child: SvgPicture.asset(
                                                  AppImages.arrow3)),
                                        )
                                      : RotatedBox(
                                          quarterTurns: 1,
                                          child: SvgPicture.asset(
                                              AppImages.arrow3))
                                ],
                              ),
                            ),

                            ///List of product
                            Visibility(
                                visible: trackPackageBloc.isProductListVisible,
                                child: subOrderList()),

                            ///Selected product count
                            Visibility(
                              visible: !trackPackageBloc.isProductListVisible,
                              child: Container(
                                  alignment: Alignment.centerLeft,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 10),
                                  child: Text(
                                    widget.suborderList.length == 1
                                        ? "${widget.suborderList.length} suborder"
                                        : "${widget.suborderList.length} suborders",
                                    style: AppTextStyle.settingText(
                                        textColor: AppColors.appBlack),
                                  )),
                            )
                          ],
                        ),
                      ),
                    );
                  }),
              // suborderList(),

              moreDetail(),
              AppCommonWidgets.bottomListSpace(context: context),
            ],
          ),
        ));
  }

  //region Delivery update history
  Widget deliveryUpdateHistory() {
    return ShippingHistoryScreen(
      dropDownTitle: AppStrings.deliveryUpdateHistory,
      pNumber: widget.suborderList.first.packageNumber!,
      orderNumber: widget.suborderList.first.packageNumber!,
      isSeller: false,
    );
  }
  //endregion

  //region Sub orders list
  Widget subOrderList() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.suborderList.length,
        shrinkWrap: true,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppCommonWidgets.subOrderInfo(
                  isCheckBoxVisible: false,
                  subOrder: widget.suborderList[index],
                  onTap: () {
                    // widget.waitingForConfirmationBloc.onSelectSubOrder(widget.suborderList[index]);
                    // widget.waitingForConfirmationBloc.onSelectSubOrder(widget.suborderList[index]);
                  },
                  context: context),
              index == widget.suborderList.length - 1
                  ? const SizedBox()
                  : const Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                      child: Divider(
                        color: AppColors.lightGray,
                        height: 1,
                        thickness: 1,
                      ),
                    )
            ],
          );
        });
  }

//endregion

//region More detail
  Widget moreDetail() {
    return DeliveryAndReturnPersonLogistic(
      buyerSubOrderBloc: widget.buyerSubOrderBloc,
      order: widget.order,
      suborderList: widget.suborderList,
    );
  }
//endregion
}
