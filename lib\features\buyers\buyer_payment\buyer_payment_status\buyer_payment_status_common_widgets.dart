import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/model/buyer_payment_options_responses/payment_status_check.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class BuyerPaymentStatusCommonWidgets{

  //region Payment status card
  static Widget paymentStatusCard({required String status,
    required  PaymentStatusCheckResponse paymentStatusCheckResponse,
    required onTapView,
    required BuildContext context

  }) {
    // // Retrieve the data from the StoreInfoModel
    // ShoppingCartQuantityDataModel sellerOwnStoreInfoDataModel = Provider.of<ShoppingCartQuantityDataModel>(context);
    //
    // sellerOwnStoreInfoDataModel.productReferenceList.clear();
    // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [

        Stack(
          alignment: Alignment.topCenter,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 35),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                padding: const EdgeInsets.only(top: 60, bottom: 20),
                decoration: BoxDecoration(
                  color: AppColors.lightWhite3,
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(1, 1),
                      blurRadius: 5,
                      color: AppColors.appBlack.withOpacity(0.2),
                    ),
                  ],
                ),
                width: double.infinity,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                     Text(
                       status=="PAYMENT_SUCCESS"?AppStrings.thankYouForYourOrder:status=="TXN_WAITING"?AppStrings.waitingButDoNotWorry:AppStrings.orderCancelledAndWeDoNot,
                         textAlign: TextAlign.center,

                         style:AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
                    ),
                    verticalSizedBox(5),
                     Container(
                       padding: const EdgeInsets.symmetric(horizontal: 5),
                       child: Text(
                           status=="PAYMENT_SUCCESS"?AppStrings.weWillInformSeller:status=="TXN_WAITING"?AppStrings.weWillUpdateYou:AppStrings.sorryForTheTnconvenience,
                          textAlign: TextAlign.center,
                          style:AppTextStyle.contentText0(textColor: AppColors.appBlack)

                    ),
                     ),
                    verticalSizedBox(20),
                    //order request number
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children:  [
                         SizedBox(
                          // width: 100,
                          child: Text(
                            "Order request #",
                            textAlign: TextAlign.left,
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)

                          ),
                        ),
                         Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: Text(":",
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)
                          ),
                        ),
                        SizedBox(
                          width: 130,
                          child: Text(
                            paymentStatusCheckResponse.data!.body!.orderId!,
                            textAlign: TextAlign.start,
                              style:AppTextStyle.contentText0(textColor: AppColors.appBlack)

                          ),
                        ),
                      ],
                    ),
                    verticalSizedBox(8),
                    //Item orders
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                         SizedBox(
                          width: 100,
                          child: Text(
                            "Items ordered",
                            textAlign: TextAlign.left,
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)

                          ),
                        ),
                         Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: Text(":",
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)
                          ),
                        ),
                        SizedBox(
                          width: 130,
                          child: Text(
                            paymentStatusCheckResponse.itemsOrdered!.toString(),
                            textAlign: TextAlign.start,
                              style:AppTextStyle.contentText0(textColor: AppColors.appBlack)

                          ),
                        ),
                      ],
                    ),
                    verticalSizedBox(8),
                    //Order from
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                         SizedBox(
                          width: 100,
                          child: Text(
                            "Ordered from",
                            textAlign: TextAlign.left,
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)

                          ),
                        ),
                         Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: Text(":",
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)

                          ),
                        ),
                        SizedBox(
                          width: 130,
                          child: Text(
                            "${paymentStatusCheckResponse.orderedFrom!} stores",
                            textAlign: TextAlign.start,
                              style:AppTextStyle.contentText0(textColor: AppColors.appBlack)

                          ),
                        ),
                      ],
                    ),
                    verticalSizedBox(8),
                    //Order value
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children:  [
                         SizedBox(
                          width: 100,
                          child: Text(
                            "Order value",
                            textAlign: TextAlign.left,
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)

                          ),
                        ),
                         Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: Text(":",
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)
                          ),
                        ),
                        SizedBox(
                          width: 130,
                          child: Text(
                            "₹${paymentStatusCheckResponse.data!.body!.txnAmount}",
                            textAlign: TextAlign.start,
                            style:AppTextStyle.contentText0(textColor: AppColors.appBlack)
                            ,
                          ),
                        ),
                      ],
                    ),
                    verticalSizedBox(8),
                    //Est. Delivery
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                         SizedBox(
                          width: 100,
                          child: Text(
                            "Est. Delivery",
                            textAlign: TextAlign.left,
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)

                          ),
                        ),
                         Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: Text(":",
                              style:AppTextStyle.contentText0(textColor: AppColors.writingBlack0)
                          ),
                        ),
                        SizedBox(
                          width: 130,
                          child: Text(
                            paymentStatusCheckResponse.estimatedDeliveryDate!.replaceAll("/", "-"),
                            textAlign: TextAlign.start,
                              style:AppTextStyle.contentText0(textColor: AppColors.appBlack)

                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            Container(
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.appWhite,
                ),
                padding: const EdgeInsets.all(5),
                child: SvgPicture.asset(
                  status == "PAYMENT_SUCCESS"?AppImages.paymentDone:AppImages.paymentFailed,
                  height: 68,
                  width: 68,
                ))
          ],
        ),

        Container(
          margin: const EdgeInsets.only(top: 10,bottom: 30,left: 25,right: 25),
          child:Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                  onTap: (){
                    onTapView();
                  },
                  child: Text(AppStrings.viewMyOrder,style: AppTextStyle.access0(textColor: AppColors.appBlack),)),
              ///Todo un-comment
              // Row(
              //   mainAxisSize: MainAxisSize.min,
              //   mainAxisAlignment: MainAxisAlignment.center,
              //   crossAxisAlignment: CrossAxisAlignment.center,
              //   children: [
              //     Text(AppStrings.howDeliveryWork,style: AppTextStyle.smallTextBold(textColor: AppColors.appBlack,isUnderLine: true),),
              //     SizedBox(
              //         height: 20,
              //         width: 20,
              //         child: Icon(Icons.keyboard_arrow_down,size: 20,))
              //   ],
              // ),
            ],
          ),
        )
      ],
    );
  }

//endregion

}