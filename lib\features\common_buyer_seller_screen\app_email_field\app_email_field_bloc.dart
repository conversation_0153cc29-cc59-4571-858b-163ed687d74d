import 'dart:async';
import 'package:flutter/material.dart';


class AppEmailFieldBloc {
  // region Common Methods
  BuildContext context;
  final Function(bool?) isValid;
  bool? isEmailValid ;


  //endregion

  //region Text ctrl
  final TextEditingController emailTextCtrl;
  //endregion

  //region Controller
  final refreshCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  AppEmailFieldBloc(this.context, this.isValid, this.emailTextCtrl);
  // endregion

  // region Init
  init() {


  }
  // endregion



  //region On change email
  void onChangeEmail(){
    //Validate
    isValid(checkValidation(emailTextCtrl.text));
    //Is valid
    isEmailValid = checkValidation(emailTextCtrl.text);
    //Refresh
    refreshCtrl.sink.add(true);
  }
  //endregion


  //region Check validation
  bool checkValidation(String email) {
    const pattern = r'^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$';
    final regExp = RegExp(pattern);
    return regExp.hasMatch(email);
  }
  //endregion


//region Dispose
  void dispose(){
    // splashStateCtrl.close();

  }
//endregion
}
