import 'package:flutter/material.dart';
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class ShoppingCartPriceCommonWidgets{



  //region Sub title
  static Widget subTitle({required CartFees cartFees,bool breakupPriceVisible = false}){
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(cartFees.orderBreakupItemText!,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),
              Text(cartFees.orderBreakupItemValue!,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),
            ],
          ),

          cartFees.orderBreakupItemSubtext==null?const SizedBox():Visibility(
            visible: breakupPriceVisible,
            child: Container(
                margin: const EdgeInsets.only(top: 3),
                child: Text(CommonMethods.shoppingCardPriceBreakup(input: cartFees.orderBreakupItemSubtext!),
                style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
                )),
          )
        ],
      ),
    );

  }
//endregion

}