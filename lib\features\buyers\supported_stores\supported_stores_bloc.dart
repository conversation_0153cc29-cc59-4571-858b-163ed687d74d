import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/supported_stores/supportedStores_pagination.dart';
import 'package:swadesic/features/providers/store_info_provider/store_info_provider.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart'as store_list_response;
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/services/followers_and_supporters_service/followers_and_supporters_service.dart';
import 'package:swadesic/services/store_follow_services/store_follow_services.dart';

import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

import '../buyer_home/buyer_home_bloc.dart';
enum SupportedStoresState { Loading, Success, Failed, Empty, SearchEmpty }
enum SupportedStoresPaginationState { Loading, Success, End  }

class SupportedStoresBloc {
  // region Common Variables
  BuildContext context;
  late StoreFollowServices storeFollowServices;
  // late GetFollowedStoreResponse getFollowedStoreResponse;
  // late List<Data> followedStores = [];
  // late visited_store.Data selectedStore;
  late store_list_response.StoreListResponse storeListResponse;
  late List<StoreInfo> storeList = [];
  late  ScrollController scrollController = ScrollController() ;
  late SupportedStoresPagination supportedStoresPagination;
  bool isPaginationEnded = false;

  int limit = 10;
  int offset = 0;



  // endregion

  //region Text Editing Controller
  TextEditingController searchTextCtrl = TextEditingController();
  //endregion

  //region Controller

  final storeYouFollowedCtrl = StreamController<SupportedStoresState>.broadcast();
  ValueNotifier<SupportedStoresPaginationState> paginationIsLoadingValueNotifier = ValueNotifier(SupportedStoresPaginationState.Success);

  //endregion


  // region | Constructor |
  SupportedStoresBloc(this.context);
  // endregion

  // region Init
  void init() {

    //Get supported store
    // Provider.of<StoreInfoProvider>(context, listen: false).getSupportedStores();

    supportedStoresPagination = SupportedStoresPagination(context,this);
    storeFollowServices = StoreFollowServices();
    //selectedStore = visited_store.Data();
    getFollowedStores();


  }
// endregion


  //region OnChange Search Field
  void onChangeSearchField(){
    storeList.clear();
    for(var data in storeListResponse.storeList!){
      if(data.storeName!.toLowerCase().contains(searchTextCtrl.text.toLowerCase()) || data.storehandle!.toLowerCase().contains( searchTextCtrl.text.toLowerCase())) storeList.add(data);
    }
    //If Store list response has no data
    if(storeListResponse.storeList!.isEmpty){
      return storeYouFollowedCtrl.sink.add(SupportedStoresState.Empty);
    }
    //If no data then search empty
    if(storeList.isEmpty){
      return storeYouFollowedCtrl.sink.add(SupportedStoresState.SearchEmpty);

    }
    // set state
    storeYouFollowedCtrl.sink.add(SupportedStoresState.Success);
  }
  //endregion


//region Go back
void goBack(){
  Navigator.pop(context);
}

//endregion


  //region Get stores and user
  // Future<void> getStoresAndPeople() async {
  //   //region Try
  //   try {
  //     offset = 0;
  //     //Pagination ended
  //     isPaginationEnded = false;
  //     //Api call
  //     // List<RecommendedStoreAndUser> data = await RecommendedStoreAndUserServices().getRecommendedStoreAndUser(isRecommendedStore: true, limit: limit, offset: offset);
  //     List<RecommendedStoreAndUser> data = await FollowersAndSupportersService().followersAndSupporters(reference: reference,entityType: entityType,followEnum: requiredList, limit: limit, offset: offset);
  //
  //     //Empty
  //     if(data.isEmpty){
  //       return storeAndPeopleCtrl.sink.add(StoreAndPeopleState.Empty);
  //     }
  //     //Clear store and people
  //     storesAndPeople.clear();
  //     //Add all data in storesAndPeople
  //     storesAndPeople.addAll(data);
  //
  //     //Success
  //     storeAndPeopleCtrl.sink.add(StoreAndPeopleState.Success);
  //   }
  //   //endregion
  //   on ApiErrorResponseMessage catch (error) {
  //     context.mounted ? CommonMethods.toastMessage(error.message!, context) : null;
  //   } catch (error) {
  //     //Failed
  //     storeAndPeopleCtrl.sink.add(StoreAndPeopleState.Failed);
  //     // return context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
  //   }
  // }

//endregion

//region Get Followed Store
  void getFollowedStores()async{
    try{
      //Loading
      // storeYouFollowedCtrl.sink.add(SupportedStoresState.Loading);

      //Clear store list
      storeList.clear();
      // List<RecommendedStoreAndUser> data = await FollowersAndSupportersService().followersAndSupporters(reference: AppConstants.appData.isUserView!?AppConstants.appData.userReference!:AppConstants.appData.storeReference!,
      //     entityType: EntityType.STORE,
      //     followEnum: FollowEnum.FOLLOWING,
      //     limit: limit, offset: offset);

      storeListResponse = await storeFollowServices.getFollowedStores(AppConstants.appData.userId!);
      ///Add data to the model
      storeList.addAll(storeListResponse.storeList!);
      if(storeListResponse.storeList!.isEmpty){
        return storeYouFollowedCtrl.sink.add(SupportedStoresState.Empty);
      }

      // //print(getFollowedStoreResponse.data!.first.storeid);
      storeYouFollowedCtrl.sink.add(SupportedStoresState.Success);
    }
    on ApiErrorResponseMessage {
      storeYouFollowedCtrl.sink.add(SupportedStoresState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
    catch(error){
      storeYouFollowedCtrl.sink.add(SupportedStoresState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }


  }
//endregion

//region Go To Store Screen
//   void goToBuyerViewStore(StoreDetail selectedStore){
//   void goToBuyerViewStore(visited_store.Data data){
   goToBuyerViewStore(StoreInfo selectedStore){
    ///Access check
    if(BuyerHomeBloc.userDetailsResponse.userDetail!.viewStores! != "1"){
      return CommonMethods.toastMessage(AppStrings.noAccess, context);
    }
    var screen =  BuyerViewStoreScreen(storeReference:selectedStore.storeReference!);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value){
      // init();
    });
  }
//endregion




}
