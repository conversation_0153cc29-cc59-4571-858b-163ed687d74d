import 'dart:convert';

class MentionParser {
  /// Parses encoded mention and extracts display text
  /// Input: {{mention:{"reference":"S1721930951916","display_text":"@kitten_kreeps"}}}
  /// Output: @kitten_kreeps
  static String extractDisplayText(String encodedMention) {
    try {
      // Remove the outer wrapper {{mention: and }}
      String jsonPart = encodedMention
          .replaceFirst('{{mention:', '')
          .replaceFirst('}}', '');
      
      // Parse the JSON
      Map<String, dynamic> mentionData = jsonDecode(jsonPart);
      
      // Return the display text
      return mentionData['display_text'] ?? encodedMention;
    } catch (e) {
      // If parsing fails, return the original text
      return encodedMention;
    }
  }

  /// Extracts reference from encoded mention
  /// Input: {{mention:{"reference":"S1721930951916","display_text":"@kitten_kreeps"}}}
  /// Output: S1721930951916
  static String extractReference(String encodedMention) {
    try {
      // Remove the outer wrapper {{mention: and }}
      String jsonPart = encodedMention
          .replaceFirst('{{mention:', '')
          .replaceFirst('}}', '');
      
      // Parse the JSON
      Map<String, dynamic> mentionData = jsonDecode(jsonPart);
      
      // Return the reference
      return mentionData['reference'] ?? '';
    } catch (e) {
      // If parsing fails, return empty string
      return '';
    }
  }

  /// Converts encoded text to display text for UI
  /// Replaces all encoded mentions with their display text
  static String convertToDisplayText(String encodedText) {
    // Regex to match encoded mentions
    RegExp mentionRegex = RegExp(r'\{\{mention:\{[^}]*\}\}\}');
    
    return encodedText.replaceAllMapped(mentionRegex, (match) {
      String encodedMention = match.group(0) ?? '';
      return extractDisplayText(encodedMention);
    });
  }

  /// Converts display text back to encoded format
  /// This is used when we need to send data to API
  static String convertToEncodedText(String displayText, List<Map<String, dynamic>> mentions) {
    String result = displayText;
    
    for (Map<String, dynamic> mention in mentions) {
      String displayMention = mention['display_text'] ?? '';
      String reference = mention['reference'] ?? '';
      
      if (displayMention.isNotEmpty && reference.isNotEmpty) {
        String encodedMention = '{{mention:{"reference":"$reference","display_text":"$displayMention"}}}';
        result = result.replaceAll(displayMention, encodedMention);
      }
    }
    
    return result;
  }

  /// Checks if a string contains encoded mentions
  static bool hasEncodedMentions(String text) {
    RegExp mentionRegex = RegExp(r'\{\{mention:\{[^}]*\}\}\}');
    return mentionRegex.hasMatch(text);
  }

  /// Gets all encoded mentions from text
  static List<String> getAllEncodedMentions(String text) {
    RegExp mentionRegex = RegExp(r'\{\{mention:\{[^}]*\}\}\}');
    return mentionRegex.allMatches(text).map((match) => match.group(0) ?? '').toList();
  }

  /// Gets all display mentions from text
  static List<String> getAllDisplayMentions(String text) {
    List<String> encodedMentions = getAllEncodedMentions(text);
    return encodedMentions.map((encoded) => extractDisplayText(encoded)).toList();
  }
}
