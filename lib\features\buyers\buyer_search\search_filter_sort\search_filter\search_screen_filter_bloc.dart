import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/model/filters/search_screen_filter_model/search_screen_filter_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_field_style.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class SearchScreenFilterBloc {
  // region Common Methods
  BuildContext context;
  final SearchScreenFilterModel searchScreenFilterModel;

  // TransactionFilterModel transactionFilterModel;
  // SelectedTime selectedTransactionTime = SelectedTime("", false);

  // endregion
  //region Controller
  final filterCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text controller
  final TextEditingController pinCodeTextCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  SearchScreenFilterBloc(this.context, this.searchScreenFilterModel);

  // endregion

  // region Init
  void init() {
    // by default all time selected
    // transactionFilterModel.allMonth.isSelected = true;
    // selectedTransactionTime.selectedTimeType = transactionFilterModel.allMonth.selectedTimeType;
    // if (!filterCtrl.isClosed) filterCtrl.sink.add(true);
  }

  // endregion

  //region On tap change
  Future onTapChange() {
    //Add pincode
    pinCodeTextCtrl.text = searchScreenFilterModel.deliveryPinCode;

    return showDialog(
        context: context,
        barrierDismissible: true,
        useSafeArea: true,
        builder: (dialogContext) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),

            // scrollable: true,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                    margin: const EdgeInsets.only(bottom: 20),
                    child: Text(
                      "Change pincode",
                      style: AppTextStyle.heading2Bold(
                          textColor: AppColors.appBlack),
                    )),
                TextFormField(
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.done,
                  maxLines: 1,

                  controller: pinCodeTextCtrl,
                  onChanged: (value) {},
                  // textCapitalization: TextCapitalization.characters,
                  inputFormatters: <TextInputFormatter>[
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(6),
                  ],
                  //maxLength: maxLength,
                  style: AppTextStyle.heading2Medium(
                      textColor: AppColors.appBlack),
                  decoration: InputDecoration(
                      filled: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      fillColor: AppColors.textFieldFill1,
                      isDense: true,
                      hintText: AppStrings.pinCode,
                      hintStyle: AppTextStyle.heading2Regular(
                          textColor: AppColors.writingColor3),
                      focusedBorder: AppTextFieldStyle.filledColored(
                          fieldColors: AppColors.textFieldFill1),
                      enabledBorder: AppTextFieldStyle.filledColored(
                          fieldColors: AppColors.textFieldFill1)),
                ),
                verticalSizedBox(20),
                InkWell(
                  onTap: () {
                    searchScreenFilterModel.deliveryPinCode =
                        pinCodeTextCtrl.text;
                    //Refresh
                    if (!filterCtrl.isClosed) filterCtrl.sink.add(true);
                    //Close
                    Navigator.pop(dialogContext);
                  },
                  child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(80),
                          color: AppColors.brandBlack),
                      child: Text(
                        "Save",
                        style: AppTextStyle.button1Bold(
                            textColor: AppColors.appWhite),
                      )),
                )
              ],
            )));

    // return CommonMethods.appDialogBox(
    //     context: context,
    //     widget:
    // );
    // showDialog(
    //   context: context,
    //   builder: (BuildContext context) {
    //     return AppCommonWidgets.dial
    //     return AlertDialog(
    //       title: Text('Change PIN code'),
    //       content: TextField(
    //         decoration: InputDecoration(
    //           hintText: 'Enter new PIN code',
    //         ),
    //       ),
    //       actions: [
    //         TextButton(
    //           child: Text('Cancel'),
    //           onPressed: () {
    //             Navigator.of(context).pop();
    //           },
    //         ),
    //         TextButton(
    //           child: Text('Save'),
    //           onPressed: () {
    //             // Perform the PIN code change logic here
    //             Navigator.of(context).pop();
    //           },
    //         ),
    //       ],
    //     );
    //   },
    // );
  }
  //endregion

  //region Deliverable
  void deliverable({required Deliverable deliverable}) {
    deliverable.isSelected = !deliverable.isSelected;
    if (!filterCtrl.isClosed) filterCtrl.sink.add(true);
  }
  // endregion

  //region Store sale
  void withSale({required WithSales withSales}) {
    withSales.isSelected = !withSales.isSelected;
    if (!filterCtrl.isClosed) filterCtrl.sink.add(true);
  }
  // endregion

  //region Date created
  // void dateCreated({required bool isEarly,required DateCreated dateCreated}) {
  //   isEarly?dateCreated.
  //   status.isSelected = !status.isSelected;
  //   if (!filterCtrl.isClosed) filterCtrl.sink.add(true);
  // }
  // endregion

  // region timeTypeSelection
  // void timeTypeSelection(SelectedTime selectedTime) {
  //   // selectedTransactionTime.selectedTimeType = selectedTime.selectedTimeType;
  //   // if (!filterCtrl.isClosed) filterCtrl.sink.add(true);
  // }

  // endregion

//region On select reset
  void resetFilter({required SearchScreenFilterModel searchScreenFilterModel}) {
    searchScreenFilterModel.reset();
    onTapApply();
    if (!filterCtrl.isClosed) filterCtrl.sink.add(true);
  }

//endregion

//region On tap Apply
  void onTapApply() {
    Navigator.pop(context, searchScreenFilterModel);
  }

//endregion

//region Dispose
  void dispose() {
    filterCtrl.close();
  }
//endregion
}
