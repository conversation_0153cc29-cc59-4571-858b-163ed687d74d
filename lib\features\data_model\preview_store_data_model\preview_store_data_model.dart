class PreviewStoreResponse {
  String? message;
  PreviewStoreData? data;

  PreviewStoreResponse({this.message, this.data});

  PreviewStoreResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    data = json['data'] != null ? PreviewStoreData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class PreviewStoreData {
  final int previewStoreId;
  final String userReference;
  final int userId;
  final String previewStoreReference;
  final String previewStoreName;
  final String previewStorehandle;
  final String? previewStoreIcon;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PreviewStoreData({
    required this.previewStoreId,
    required this.userReference,
    required this.userId,
    required this.previewStoreReference,
    required this.previewStoreName,
    required this.previewStorehandle,
    this.previewStoreIcon,
    this.createdAt,
    this.updatedAt,
  });

  factory PreviewStoreData.fromJson(Map<String, dynamic> json) {
    return PreviewStoreData(
      previewStoreId: json['preview_store_id'] as int? ?? 0,
      userReference: json['user_reference'] as String? ?? '',
      userId: json['user_id'] as int? ?? 0,
      previewStoreReference: json['preview_store_reference'] as String? ?? '',
      previewStoreName: json['preview_store_name'] as String? ?? '',
      previewStorehandle: json['preview_storehandle'] as String? ?? '',
      previewStoreIcon: json['preview_store_icon'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['preview_store_id'] = previewStoreId;
    data['user_reference'] = userReference;
    data['user_id'] = userId;
    data['preview_store_reference'] = previewStoreReference;
    data['preview_store_name'] = previewStoreName;
    data['preview_storehandle'] = previewStorehandle;
    data['preview_store_icon'] = previewStoreIcon;
    data['created_at'] = createdAt?.toIso8601String();
    data['updated_at'] = updatedAt?.toIso8601String();
    return data;
  }
}
