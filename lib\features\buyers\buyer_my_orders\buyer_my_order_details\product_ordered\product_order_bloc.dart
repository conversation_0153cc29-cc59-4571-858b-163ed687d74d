import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';


class ProductOrderedBloc {
  // region Common Methods
  BuildContext context;
  bool isProductOrderedVisible = false;
  // endregion


  //region Controller
  final productOrderCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  ProductOrderedBloc(this.context);

  // endregion

  // region Init
  void init() {

  }
// endregion

//region On tap product order
void onTapProductOrder(){
  isProductOrderedVisible = !isProductOrderedVisible;
  productOrderCtrl.sink.add(true);
}
//endregion


  //region Go To Store Screen
  goToBuyerViewStore({required String storeReference}) {
    //Close bottom sheet
    Navigator.pop(context);
    ///Access check
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewStores! != "1") {
      return CommonMethods.toastMessage(AppStrings.noAccess, context);
    }
    var screen = BuyerViewStoreScreen(storeReference: storeReference,);
    // var screen = BuyerViewStoreScreen(storeReference: "S4185590",);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // screenRefreshCtrl.sink.add(true);
    });
  }
//endregion






}
