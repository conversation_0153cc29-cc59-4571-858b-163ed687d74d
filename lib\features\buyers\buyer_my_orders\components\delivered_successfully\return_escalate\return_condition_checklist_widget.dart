import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class ReturnConditionChecklistWidget extends StatefulWidget {
  final List<String> conditions;
  final Function(List<bool>) onConditionsChanged;
  final List<bool> initialCheckedState;

  const ReturnConditionChecklistWidget({
    Key? key,
    required this.conditions,
    required this.onConditionsChanged,
    required this.initialCheckedState,
  }) : super(key: key);

  @override
  State<ReturnConditionChecklistWidget> createState() =>
      _ReturnConditionChecklistWidgetState();
}

class _ReturnConditionChecklistWidgetState
    extends State<ReturnConditionChecklistWidget> {
  late List<bool> _checkedConditions;

  @override
  void initState() {
    super.initState();
    // Initialize with the provided initial state or create a new list if empty
    _checkedConditions = widget.initialCheckedState.isNotEmpty
        ? List.from(widget.initialCheckedState)
        : List.filled(widget.conditions.length, false);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            "Return Conditions",
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),
        ),

        // Conditions list with Met/Unmet toggles
        ...List.generate(
          widget.conditions.length,
          (index) => _buildConditionItem(index),
        ),

        // Spacing
        verticalSizedBox(16),
      ],
    );
  }

  Widget _buildConditionItem(int index) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Condition text
          Expanded(
            child: Text(
              widget.conditions[index],
              style: AppTextStyle.contentText0(
                textColor: AppColors.appBlack,
              ),
            ),
          ),

          // Spacing
          horizontalSizedBox(12),

          // Met/Unmet toggle
          GestureDetector(
            onTap: () => _toggleCondition(index),
            child: Container(
              width: 70, // Fixed width for consistent size
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: _checkedConditions[index]
                    ? AppColors.brandBlack.withOpacity(0.1)
                    : AppColors.lightGray.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _checkedConditions[index]
                      ? AppColors.brandBlack
                      : AppColors.lightGray,
                  width: 1.0,
                ),
              ),
              child: Center(
                child: Text(
                  _checkedConditions[index] ? "Met" : "Unmet",
                  style: AppTextStyle.smallText(
                    textColor: _checkedConditions[index]
                        ? AppColors.brandBlack
                        : AppColors.writingColor2,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleCondition(int index) {
    setState(() {
      _checkedConditions[index] = !_checkedConditions[index];
      widget.onConditionsChanged(_checkedConditions);
    });
  }
}
