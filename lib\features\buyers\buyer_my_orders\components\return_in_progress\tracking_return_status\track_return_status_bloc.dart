import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';

class TrackReturnStatusBloc {
  // region Common Variables
  BuildContext context;
  late BuyerMyOrderServices buyerMyOrderServices;
  bool isProductListDropDownVisible = false;
  bool isUpdateFromSellerVisible = false;


  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  TrackReturnStatusBloc(this.context);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();

  }
// endregion


//region On tap Product list Drop down
  void onTapProductList(){
    isProductListDropDownVisible = !isProductListDropDownVisible;
    bottomSheetRefresh.sink.add(true);

  }
//endregion

//region On tap Delay
void onTapDelay(){
  isUpdateFromSellerVisible = !isUpdateFromSellerVisible;
  bottomSheetRefresh.sink.add(true);


}
//endregion


}
