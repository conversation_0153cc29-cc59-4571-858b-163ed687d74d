import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:swadesic/features/buyers/buyer_payment/payment_waiting/payment_waiting_bloc.dart';
import 'package:swadesic/features/buyers/buyer_payment/payment_waiting/upi_and_card_waiting/upi_and_card_waiting.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:webview_flutter/webview_flutter.dart';

// region Buyer Payment Waiting Screen
class PaymentWaitingScreen extends StatefulWidget {
  final String paymentType;
  final bool isUpiPay;
  const PaymentWaitingScreen({Key? key, required this.paymentType,  this.isUpiPay = false}) : super(key: key);

  @override
  _PaymentWaitingScreenState createState() => _PaymentWaitingScreenState();
}
// endregion

class _PaymentWaitingScreenState extends State<PaymentWaitingScreen> {
  // region Bloc
  late PaymentWaitingBloc paymentWaitingBloc;

  // endregion

  // region Init
  @override
  void initState() {
    paymentWaitingBloc = PaymentWaitingBloc(context,widget.paymentType);
    paymentWaitingBloc.init();
    super.initState();
  }

  //region Dispose
  @override
  void dispose() {
    imageCache.clear();
    paymentWaitingBloc.dispose();
    super.dispose();
  }
  //endregion

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),

      body: SafeArea(
          // child: const SizedBox()
          //
          //
          //     ///Todo Un-comment
        child: StreamBuilder<bool>(
          stream: paymentWaitingBloc.screenRefreshCtrl.stream,
          initialData: false,
          builder: (context, snapshot) {
            //If from UPI
            if(widget.paymentType == "upi"){
              return const UpiAndCardWaiting(isUpiWaiting: true,);
            }
            return WebView(
            // initialUrl: "https://google.com",
            javascriptMode: JavascriptMode.unrestricted,
            onWebViewCreated: (controller){
              paymentWaitingBloc.webViewController = controller;
              paymentWaitingBloc.webViewController.loadUrl(Uri.dataFromString(paymentWaitingBloc.bankWeb(),mimeType: 'text/html').toString());
            },
            // onProgress: (data){
            //   //print("Progress is $data");
            // },
            // onPageStarted: (url){
            //   //print("On Page Started $url");
            // },
            //   onPageFinished: (url){
            //
            //   },
            );
          }
        ),

      ),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar (){
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title:AppStrings.paymentOptions,
        isCartVisible: false,
        isMembershipVisible: false
    );
    // return buyerAppBar(
    //
    //     leadingIcon:SvgPicture.asset(
    //     AppImages.backButton,color: AppColors.appBlack,fit: BoxFit.fill),context: context,titleText:AppStrings.paymentOptions,drawerIconEnable:true,basketVisible: false,messageVisible: false,searchVisible: false, );
    //
  }
  //endregion


//region Payed Success
Widget successMessage(){
    return Lottie.network("https://assets3.lottiefiles.com/datafiles/Wv6eeBslW1APprw/data.json");
}
//endregion





   //region Loading screens
  // Widget body(){
  //     if(widget.paymentType == "upiId"){
  //       return Center(
  //         child: Column(
  //           mainAxisSize: MainAxisSize.max,
  //           mainAxisAlignment: MainAxisAlignment.spaceAround,
  //           crossAxisAlignment: CrossAxisAlignment.center,
  //           children: [
  //             SvgPicture.asset(AppImages.upiIdPaymentWaiting),
  //             Padding(
  //               padding: const EdgeInsets.symmetric(horizontal: 20),
  //               child: Text(AppStrings.upiIdPaymentWaiting,
  //                 textAlign: TextAlign.center,
  //                 style: TextStyle(
  //                 fontSize: 16,
  //                 fontFamily: "LatoRegular",
  //                 fontWeight: FontWeight.w400,
  //                 color: AppColors.appBlack,
  //               ),),
  //             ),
  //             Padding(
  //               padding: const EdgeInsets.symmetric(horizontal: 20),
  //               child: Column(
  //                 mainAxisSize: MainAxisSize.min,
  //                 mainAxisAlignment: MainAxisAlignment.center,
  //                 crossAxisAlignment: CrossAxisAlignment.center,
  //                 children: [
  //                   RotatedBox(
  //                     quarterTurns: 1,
  //
  //                       child: SvgPicture.asset(AppImages.threeDots,color: AppColors.lightGreen,height: 50,)),
  //                   verticalSizedBox(20),
  //                   Text(AppStrings.pleaseDoNotGoBack,
  //                     textAlign: TextAlign.center,
  //                     style: TextStyle(
  //                       fontSize: 14,
  //                       fontFamily: "LatoSemibold",
  //                       fontWeight: FontWeight.w600,
  //                       color: AppColors.appBlack2,
  //                     ),),
  //                 ],
  //               ),
  //             ),
  //
  //
  //
  //
  //           ],
  //         ),
  //       );
  //     }
  //     if(widget.paymentType == "card"||widget.paymentType == "bank"){
  //       return Center(
  //         child: Column(
  //           mainAxisSize: MainAxisSize.max,
  //           mainAxisAlignment: MainAxisAlignment.spaceAround,
  //           crossAxisAlignment: CrossAxisAlignment.center,
  //           children: [
  //             SvgPicture.asset(AppImages.upiIdPaymentWaiting),
  //             Padding(
  //               padding: const EdgeInsets.symmetric(horizontal: 20),
  //               child: Text(AppStrings.holdOn,
  //                 textAlign: TextAlign.center,
  //                 style: TextStyle(
  //                   fontSize: 16,
  //                   fontFamily: "LatoRegular",
  //                   fontWeight: FontWeight.w400,
  //                   color: AppColors.appBlack,
  //                 ),),
  //             ),
  //             Padding(
  //               padding: const EdgeInsets.symmetric(horizontal: 20),
  //               child: Text(AppStrings.pleaseDoNotGoBack,
  //                 textAlign: TextAlign.center,
  //                 style: TextStyle(
  //                   fontSize: 14,
  //                   fontFamily: "LatoSemibold",
  //                   fontWeight: FontWeight.w600,
  //                   color: AppColors.appBlack2,
  //                 ),),
  //             ),
  //
  //
  //
  //
  //           ],
  //         ),
  //       );
  //     }
  //     if(widget.paymentType == "upiApp"){
  //       return Center(
  //         child: Column(
  //           mainAxisSize: MainAxisSize.max,
  //           mainAxisAlignment: MainAxisAlignment.spaceAround,
  //           crossAxisAlignment: CrossAxisAlignment.center,
  //           children: [
  //             SvgPicture.asset(AppImages.upiIdPaymentWaiting),
  //             Padding(
  //               padding: const EdgeInsets.symmetric(horizontal: 20),
  //               child: Text(AppStrings.pleasePayUsingUpiApp,
  //                 textAlign: TextAlign.center,
  //                 style: TextStyle(
  //                   fontSize: 16,
  //                   fontFamily: "LatoRegular",
  //                   fontWeight: FontWeight.w400,
  //                   color: AppColors.appBlack,
  //                 ),),
  //             ),
  //             Padding(
  //               padding: const EdgeInsets.symmetric(horizontal: 20),
  //               child: Text(AppStrings.pleaseGoBackInitiate,
  //                 textAlign: TextAlign.center,
  //                 style: TextStyle(
  //                   fontSize: 14,
  //                   fontFamily: "LatoSemibold",
  //                   fontWeight: FontWeight.w600,
  //                   color: AppColors.appBlack2,
  //                 ),),
  //             ),
  //
  //
  //
  //
  //           ],
  //         ),
  //       );
  //     }
  //
  //
  //     return Container();
  //
  // }
//endregion



}
