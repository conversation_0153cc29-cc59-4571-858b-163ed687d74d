import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_in_progress/widget/return_tracking_details/return_tracking_details_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class Logistic extends StatefulWidget {
  final ReturnTrackingDetailsBloc returnTrackingDetailsBloc;
  const Logistic({Key? key, required this.returnTrackingDetailsBloc})
      : super(key: key);

  @override
  State<Logistic> createState() => _LogisticState();
}

class _LogisticState extends State<Logistic> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: SingleChildScrollView(
          child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///Logistics partner]
          AppTitleAndOptions(
            title: AppStrings.logisticsPartner,
            titleOption: SvgPicture.asset(AppImages.exclamation),
            option: AppCommonWidgets.dropDownOptions(
                onTap: () {
                  widget.returnTrackingDetailsBloc.onTapLogisticsPartners();
                },
                context: context,
                hintText: AppStrings.selectTheLogistic,
                value: widget
                    .returnTrackingDetailsBloc.logisticPartnerTextCtrl.text),
          ),

          verticalSizedBox(20),

          ///Tracking link
          AppTitleAndOptions(
            title: AppStrings.trackingLink,
            titleOption: SvgPicture.asset(AppImages.exclamation),
            option: AppTextFields.websiteTextField(
                context: context,
                textEditingController:
                    widget.returnTrackingDetailsBloc.trackingLinkTextCtrl,
                hintText: AppStrings.trackingLink,
                onChanged: () {
                  widget.returnTrackingDetailsBloc.checkUrlValidation();
                }),
          ),

          ///If url is in-valid
          widget.returnTrackingDetailsBloc.isUrlValid != null &&
                  !widget.returnTrackingDetailsBloc.isUrlValid!
              ? AppCommonWidgets.validAndInvalid(
                  buttonText: AppStrings.invalidUrl,
                  textColor: AppColors.red,
                )
              : const SizedBox(),

          ///If url is valid
          Visibility(
            visible: widget.returnTrackingDetailsBloc.isUrlValid != null &&
                widget.returnTrackingDetailsBloc.isUrlValid!,
            child: AppCommonWidgets.validAndInvalid(
                buttonText: AppStrings.viewTheLink,
                textColor: AppColors.brandBlack,
                isUnderLine: true,
                onTap: () {
                  CommonMethods.openAppWebView(
                      context: context,
                      webUrl: widget
                          .returnTrackingDetailsBloc.trackingLinkTextCtrl.text);
                }),
          ),

          // InkWell(
          //   onTap: (){
          //     CommonMethods.opeAppWebView(webUrl:widget.returnTrackingDetailsBloc.trackingLinkTextCtrl.text, context: context);
          //   },
          //   child: Container(
          //     margin: const EdgeInsets.only(top: 10),
          //
          //     alignment: Alignment.centerLeft,
          //     height: 20,
          //     child:Text(AppStrings.viewTheLink,style: AppTextStyle.smallTextRegular(textColor: AppColors.brandGreen),),
          //   ),
          // ),
          verticalSizedBox(20),

          ///Tracking Number
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "Tracking number",
                style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
              ),
              SvgPicture.asset(AppImages.exclamation)
            ],
          ),
          verticalSizedBox(13),
          colorFilledTextField(
            context: context,
            textFieldCtrl:
                widget.returnTrackingDetailsBloc.trackingNumberTextCtrl,
            hintText: "Tracking number",
            textFieldMaxLine: 1,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.done,
            // onChangeText: sellerOnBoardingBloc.onTextChange,
            regExp: AppConstants.intStringNoSpace,
            fieldTextCapitalization: TextCapitalization.none,
            maxCharacter: 50,
          ),
        ],
      )),
    );
  }

//region
}
