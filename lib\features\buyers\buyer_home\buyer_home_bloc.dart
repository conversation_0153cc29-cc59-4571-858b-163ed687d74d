import 'dart:async';
import 'dart:convert';

import 'package:app_settings/app_settings.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/update_pincode/update_pincode.dart';
import 'package:swadesic/features/buyers/buyer_home/welcome_to/welcome_to_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_screen.dart';
import 'package:swadesic/features/buyers/supported_stores/supported_stores_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/all_messages_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/messaging_and_requests_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/open_app_settings/open_app_settings.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/post/new.dart';
import 'package:swadesic/features/update_email/update_email.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_items_responses.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/notification_services/notification_service.dart';
import 'package:swadesic/services/seller_home_service/seller_home_service.dart';
import 'package:swadesic/services/shopping_cart_service/shopping_cart_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/services/user_profile/user_profile_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum BuyerHomeScreenState { Loading, Success, Failed }
enum PostalCodeState { Loading, Success, Failed }

class BuyerHomeBloc {
  // region Common Variables
  BuildContext context;
  bool recentlyVisitedActive = false;
  int recentlyVisitedItemCount = 20;
  late UserProfileService userProfileService;

  ///User Details
  late UserDetailsServices userDetailsServices;
  static GetUserDetailsResponse userDetailsResponse = GetUserDetailsResponse();
  late UserCreatedStoresDataModel userCreatedStoresDataModel;

  ///Crt item and cart detail
  late ShoppingCartServices shoppingCartServices;
  late GetCartItemResponses getCartItemResponses = GetCartItemResponses();
  late GetCartDetailsResponse getCartDetailsResponse;

  ///Location
  late PermissionStatus status;
  ///Get Store List
  late SellerHomeService sellerHomeService;
  static late StoreListResponse storeListResponse;
  ///Cart quantity data model
  late ShoppingCartQuantityDataModel shoppingCartQuantityDataModel;

  //Notification data model
  late UserOrStoreNotificationDataModel notificationDataModel;
  ///Logged in user info data model
  late LoggedInUserInfoDataModel loggedInUserInfoDataModel;

  ///Tab ctrl
  final TabController tabController;
  final bool isFromOnboardingFlow;


  // endregion



  //region Controller

  final screenRefreshCtrl = StreamController<bool>.broadcast();
  final postalCtrl = StreamController<PostalCodeState>.broadcast();
  final recentlyStoreCtrl = StreamController<bool>.broadcast();
  final buyerHomeCtrl = StreamController<BuyerHomeScreenState>.broadcast();
  final storeOnlineCtrl = StreamController<bool>.broadcast();
  final sliderCtrl = StreamController<int>.broadcast();
  final bottomCtrl = StreamController<int>.broadcast();
  final mobileNumberCtrl = StreamController<bool>.broadcast();
  final refreshTabCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  TextEditingController postalCodeTextCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  BuyerHomeBloc(this.context, this.tabController,this.isFromOnboardingFlow);
  // endregion

  // region Init
  init() async {
    shoppingCartQuantityDataModel = Provider.of<ShoppingCartQuantityDataModel>(context, listen: false);
    loggedInUserInfoDataModel =  Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    userCreatedStoresDataModel =  Provider.of<UserCreatedStoresDataModel>(context, listen: false);

    //Open welcome dialog if isFromOnboardingFlow is tree
    if(isFromOnboardingFlow){
      openWelcomeDialog();
    }
    ///Save home context
    AppConstants.currentSelectedTabContext = context;
    ///Shopping cart Service
    shoppingCartServices = ShoppingCartServices();

    // ///Store Visited service initialize
    // storeVisitService = StoreVisitService();

    ///User Profile service
    userProfileService = UserProfileService();

    ///User Detail Service
    userDetailsServices = UserDetailsServices();

    ///Get user info
    await getLoggedInUserDetail();
    ///Get cart items
    getCartItems();
    ///Seller home service
    sellerHomeService = SellerHomeService();
    ///Get store list created by user
    getStoreListCreatedByUser();
    ///Refresh home screen
    refreshBuyerHomeScreen();
    dataPrint();
    //Tab change listener
    tabController.addListener(() {
      refreshTabCtrl.sink.add(true);

    });
  }
// endregion

  //region Refresh buyer home screen
  void refreshBuyerHomeScreen(){
    buyerHomeCtrl.sink.add(BuyerHomeScreenState.Success);
  }
  //endregion

  //region Forgrou



  //region Get logged in user detail
  Future <void> getLoggedInUserDetail() async {
    // userDetailsResponse = await userDetailsServices.getLoggedInUserDetail();
// return;
    //region Try
    try {
      //selectedAddressRefreshCtrl.sink.add(SellerReturnWarrantyState.Loading);
      userDetailsResponse = await userDetailsServices.getLoggedInUserDetail(userReference: AppConstants.appData.userReference!);
      postalCodeTextCtrl.text = userDetailsResponse.userDetail!.pincode == null ? "" : userDetailsResponse.userDetail!.pincode!;
      mobileNumberCtrl.sink.add(true);
      ///Collect following user reference and save to global variable
      // for(var data in userDetailsResponse.following!){
      //   AppConstants.followingUserReferenceList.add(data.userReference!);
      // }
      ///Save logged in info to global and share pref
      saveUserInfoInGlobalAndSharePref();
      ///Add user info to logged in user data model
      loggedInUserInfoDataModel.setUserInfoResponse(data: userDetailsResponse.userDetail!);




      // ///Add global variable user pic url
      // AppConstants.bottomUserIcon = userDetailsResponse.userDetail!.icon??"";
      // //Refresh bottom navigation
      // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message!, context);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
  //endregion

  //
  // void launchUrlWithCallback() {
  //   final url = 'https://example.com/start?callback=https://yourapp.com/callback';
  //   html.window.open(url, '_blank'); // Open the URL in a new tab or window
  // }
  //
  //
  // void handleCallback() {
  //   // Parse the current URL to check for query parameters
  //   final uri = Uri.parse(html.window.location.href);
  //   final data = uri.queryParameters['data']; // Extract the "data" parameter
  //   if (data != null) {
  //     //print('Received data from external site: $data');
  //   }
  // }


  //region Get StoreList created by user
  void getStoreListCreatedByUser()async{
    //If static user then return
    if(CommonMethods().isStaticUser()){
      return;
    }
    try{
      storeListResponse = await sellerHomeService.getSellerStore();
      userCreatedStoresDataModel.addStoreList(storeListResponse: storeListResponse);


      if (userCreatedStoresDataModel.getStores != null) {
        //print("Store List from UserCreatedStoresDataModel:");
        //print(userCreatedStoresDataModel.getStores!.toJson()); // or access specific properties as needed
      } else {
        //print("No Store List available in UserCreatedStoresDataModel");
      }
    }
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
    catch(error){
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
//endregion


  void dataPrint(){
    userCreatedStoresDataModel.addListener(() {
      //print(userCreatedStoresDataModel.counter);
    });

  }




  ///Save user info in global variable and in share preference
  //region Save info in global variable and share preference
  void saveUserInfoInGlobalAndSharePref(){
    //Add user id
    AppConstants.appData.userId = userDetailsResponse.userDetail!.userid!;
    //Add user reference
    AppConstants.appData.userReference =  userDetailsResponse.userDetail!.userReference!;
    //Mark user view is true
    AppConstants.appData.isUserView = true;
    //Mark store view is false
    AppConstants.appData.isStoreView = false;
    //Save mobile number
    AppConstants.appData.mobileNumber = userDetailsResponse.userDetail!.phonenumber;
    //Save pin code
    AppConstants.appData.pinCode = userDetailsResponse.userDetail!.pincode;
    //Add all data to share pref
    AppDataService().addAppData();
  }
  //endregion



  //region Get Shopping cart Items
  getCartItems() async {
    //region Try
    try {
      //If from static store
      if(AppConstants.appData.userReference == AppConstants.staticUser){
        return;
      }
      ///Get cart item
      getCartItemResponses = await shoppingCartServices.getShoppingCartItems();
      //If cart item is not empty then call the api to get shopping cart detail
      //Also take out product reference and add to the data model
      if(getCartItemResponses.cartItemId!.isNotEmpty){
        //Get cart detail
        getCartDetailsResponse =  await shoppingCartServices.getCartDetail(getCartItemResponses.cartItemId!);
        //Clear product references from data model class
        shoppingCartQuantityDataModel.productReferenceList.clear();
        //Store loop
        for(var store in getCartDetailsResponse.cartStoreList!){
          //Product loop
          for(var product in store.cartProductList!){
            //If cart is not empty then add the quantity in data model
            ///Save cart quantity data to model
            shoppingCartQuantityDataModel.updateCartQuantity(productReference:product.productReference! );
          }

        }
      }
      else{
        //Clear product references from data model class
        shoppingCartQuantityDataModel.productReferenceList.clear();
      }





      // AppConstants.cartItemIdList.clear();
      // AppConstants.cartItemIdList.addAll(getCartItemResponses.cartItemId!);
      // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
      // screenRefreshCtrl.sink.add(true);
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.unableToFetchShoppingCartInfo, context);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.unableToFetchShoppingCartInfo, context);
      return;
    }
  }
  //endregion







  //region Go To Store You Follow
  void goToStoreYouFollow() {
    var screen = const SupportedStoresScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
    //     .then((value){
    //   init();
    // }
    // );
  }
  //endregion



  //region Open PinCode Dialog Box
  void pinCodeDialogBox() async {
    // Allow both signed-in and unsigned users to update pincode
    if (!CommonMethods().isStaticUser()) {
      await getLoggedInUserDetail();
    }
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(10), topLeft: Radius.circular(10))),
        builder: (context) {
          return SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: UpdatePinCode(buyerHomeBloc:this));
        }).then((value) {
      // if (value == null) return;
      // supportFilterModel = value;
      // applyFilter();
    });
    // showDialog(
    //     context: context,
    //     builder: (BuildContext context) {
    //       return AlertDialog(
    //         title:  Center(child: Text(AppStrings.deliveryPinCode,style: AppTextStyle.heading1Bold(textColor: AppColors.appBlack),)),
    //         titleTextStyle:  AppTextStyle.heading1Bold(textColor: AppColors.appBlack),
    //         content:UpdatePinCode(buyerHomeBloc: buyerHomeBloc,),
    //       );
    //     });
  }
  //endregion




  //region Send PinCode
  void sendPinCode() async {
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    try {
      bool pinValidation = CommonMethods().pinCodeValidation(postalCodeTextCtrl.text);
      //print(pinValidation);
      if (!pinValidation) {
        CommonMethods.toastMessage("Invalid postal code", context);
        return;
      }

      // If user is signed in, update pincode in backend
      if (!CommonMethods().isStaticUser()) {
        BuyerHomeBloc.userDetailsResponse.userDetail!.pincode = await userProfileService.addUserPinCode(postalCodeTextCtrl.text);
        //Update pincode in app constant
        AppConstants.appData.pinCode = BuyerHomeBloc.userDetailsResponse.userDetail!.pincode;
        //Save pin code in logged in user data model
        loggedInUserInfoDataModel.userDetail!.pincode = BuyerHomeBloc.userDetailsResponse.userDetail!.pincode;
        //Update data model ui
        loggedInUserInfoDataModel.updateUi();
      } else {
        // For unsigned users, just update the pincode locally
        AppConstants.appData.pinCode = postalCodeTextCtrl.text;
      }

      //Immediately update cache storage
      AppDataService().addAppData();
      Navigator.pop(context);
      CommonMethods.toastMessage(AppStrings.deliveryPinCodeUpdated, context);

      //Update the buy button to refresh in all loaded product
      for(var product in productDataModel.allProducts){
        product.isPinCodeChanged = true;
      }
      //Update ui
      productDataModel.updateUi();

      return;
    }on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message!, context);
      return;
    } catch (error) {
      buyerHomeCtrl.sink.add(BuyerHomeScreenState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }
  //endregion

  ///1
  //region Check Location Permission and take the current position
  determinePosition() async {

    //Check permission
    //  status = await Permission.location.request();
    status = await Permission.locationWhenInUse.request();
    //  status = await Permission.locationAlways.request();

    ///Granted
    if( status.isGranted){
      //print("granted");
      currentPosition();
    }
    ///Denied
    if ( status.isDenied) {
      status = await Permission.location.request();
      if (status.isDenied) {
        return Future.error('Location permissions are denied');
      }
    }
    ///Denied permanently
    if (status.isPermanentlyDenied) {
      openIfPermissionIsDenied(isLocationService: false);

      return Future.error('Location permissions are permanently denied, we cannot request permissions.');

    }

  }
  //endregion

  ///2
  //region Get current Position
  Future<void> currentPosition() async {

    //Check is location service is enable or not
    if(await Permission.location.serviceStatus.isDisabled){
      openIfPermissionIsDenied(isLocationService: true);
      return;
    }
    else{
      //Loading pin code
      postalCtrl.sink.add(PostalCodeState.Loading);
      Position position = await Geolocator.getCurrentPosition();
      //print(position);
      //print("Position from current position block: ${json.encode(position.toJson())}");
      getAddress(position);

    }

  }
  //endregion

  //region Get Address of the current Position
  void getAddress(Position latLong) async {

    // List<Placemark> addressList = await placemarkFromCoordinates(latLong.latitude , latLong.longitude);
    List<Placemark> addressList = await placemarkFromCoordinates(latLong.latitude, latLong.longitude);
    //print("State: ${addressList.first.administrativeArea}, City: ${addressList.first.locality},Postal Code: ${addressList.first.postalCode},");
    Placemark address = addressList.first;
    //print("${address.street},${address.subLocality},${address.locality},${address.postalCode},${address.country}");
    //Clear postal code field
    postalCodeTextCtrl.clear();
    //Add pin code to field
    postalCodeTextCtrl.text = addressList.first.postalCode!;
    //Close keyboard
    CommonMethods.closeKeyboard(context);
    //Success pin code
    postalCtrl.sink.add(PostalCodeState.Success);

  }
//endregion

  //region Go to Shopping Cart Screen
  gotoShoppingCart() {
    // AppConstants.userLevelPersistentTabController.jumpToTab(2);
    // AppConstants.bottomNavigationCtrl.sink.add(true);

    var screen = const ShoppingCartScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      screenRefreshCtrl.sink.add(true);
    });
  }
//endregion
// region Messaging screen
  // goToMessagingScreen() {
  //   var screen = const AllMessagesScreen();
  //   var route = MaterialPageRoute(builder: (context) => screen);
  //   Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  // }
//endregion

  //region Permission denied permanently
  void openIfPermissionIsDenied({required bool isLocationService}){
    CommonMethods.appDialogBox(
        context: context,
        widget:OpenAppSettings(
            onTap: (){
              isLocationService?AppSettings.openAppSettings(type: AppSettingsType.location).then((value) => determinePosition):AppSettings.openAppSettings().then((value) => determinePosition);
            }
        )
    );
  }
//endregion


  //region Welcome dialog
  Future<void> openWelcomeDialog()async{
    await Future.delayed(const Duration(milliseconds: 500));
    return  showDialog(

      context: context,

      builder: (_) => AppAnimatedDialog(child: WelcomeTo()),
    );
  }
  //endregion



//region Dispose
  void dispose() {
    imageCache.clear();
    recentlyStoreCtrl.close();
    buyerHomeCtrl.close();
    storeOnlineCtrl.close();
    sliderCtrl.close();
    bottomCtrl.close();
    tabController.dispose();
  }
//endregion

}