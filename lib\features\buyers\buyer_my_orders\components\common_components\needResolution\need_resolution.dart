import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/needResolution/need_resolution_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';

import '../../../../../../util/app_colors.dart';

class NeedResolution extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  final String escalationReason;
  final String? packageReference;
  final String title;
  final String subTitle;
  final String buttonText;

  const NeedResolution(
      {Key? key,
      required this.subOrderList,
      required this.buyerSubOrderBloc,
      required this.order,
      required this.escalationReason,
      this.packageReference,
      required this.title,
      required this.subTitle,
      required this.buttonText})
      : super(key: key);

  @override
  State<NeedResolution> createState() => _NeedResolutionState();
}

class _NeedResolutionState extends State<NeedResolution> {
  //region Bloc
  late NeedResolutionBloc needResolutionBloc;
  //endregion
  //region Init
  @override
  void initState() {
    needResolutionBloc = NeedResolutionBloc(
        context,
        widget.subOrderList,
        widget.buyerSubOrderBloc,
        widget.order,
        widget.escalationReason,
        widget.packageReference);
    needResolutionBloc.init();
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 25),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///Title
          Text(
            widget.title,
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          ),
          verticalSizedBox(20),

          ///Sub title
          AppTitleAndOptions(
            title: widget.subTitle,
            option: AppTextFields.allTextField(
              minLines: 5,
              maxLines: 5,
              maxEntry: 200,
              context: context,
              textEditingController: needResolutionBloc.notesTextCtrl,
              hintText: AppStrings.note,
            ),
          ),
          // AppCommonWidgets.orderTitleText(context: context, text: widget.subTitle,isVisibleExclamation: false),
          // colorFilledTextField(
          //   context: context,
          //   textFieldCtrl: needResolutionBloc.notesTextCtrl,
          //   hintText: "write..",
          //   hintFontSize: 14,
          //   textFieldMaxLine: 5,
          //   minLines: 5,
          //   keyboardType: TextInputType.text,
          //   textInputAction: TextInputAction.done,
          //   // onChangeText: sellerOnBoardingBloc.onTextChange,
          //   regExp: AppConstants.acceptAll,
          //   fieldTextCapitalization: TextCapitalization.sentences,
          //   maxCharacter: 200,
          // ),
          verticalSizedBox(10),
          Row(
            children: [
              Expanded(
                child: AppCommonWidgets.activeButton(
                    buttonName: widget.buttonText,
                    onTap: () {
                      needResolutionBloc.addNeedResolution();
                    }),
                // child: InkWell(
                //   onTap: (){
                //     needResolutionBloc.addNeedResolution();
                //   },
                //   child: Container(
                //     padding: const EdgeInsets.symmetric(vertical: 15,horizontal: 20),
                //     decoration: const BoxDecoration(
                //       color: AppColors.brandGreen,
                //       borderRadius: BorderRadius.all(Radius.circular(50)),
                //     ),
                //     child:  Center(
                //       child: Text(
                //         widget.buttonText,
                //         textAlign: TextAlign.center,
                //         style: AppTextStyle.button1Bold(textColor: AppColors.appWhite),
                //       ),
                //     ),
                //   ),
                // ),
              ),
            ],
          )
        ],
      ),
    );
  }
//endregion
}
