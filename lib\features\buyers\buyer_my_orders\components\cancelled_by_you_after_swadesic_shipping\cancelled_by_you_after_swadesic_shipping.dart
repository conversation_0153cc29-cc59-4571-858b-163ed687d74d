import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/cancelled_by_you_after_swadesic_shipping/cancelled_by_you_after_swadesic_shipping_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class CancelledByYouAfterSwadesicShippingScreen extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  const CancelledByYouAfterSwadesicShippingScreen(
      {Key? key,
      required this.subOrderList,
      required this.buyerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<CancelledByYouAfterSwadesicShippingScreen> createState() =>
      _CancelledByYouAfterSwadesicShippingScreenState();
}

class _CancelledByYouAfterSwadesicShippingScreenState
    extends State<CancelledByYouAfterSwadesicShippingScreen> {
  // region Bloc
  late CancelledByYouAfterShippingBloc cancelledByYouAfterSwadesicShippingBloc;

  // endregion

  // region Init
  @override
  void initState() {
    cancelledByYouAfterSwadesicShippingBloc = CancelledByYouAfterShippingBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.subOrderList);
    cancelledByYouAfterSwadesicShippingBloc.init();
    super.initState();
  }

  // endregion

  //region Dis update
  @override
  void didUpdateWidget(
      covariant CancelledByYouAfterSwadesicShippingScreen oldWidget) {
    cancelledByYouAfterSwadesicShippingBloc = CancelledByYouAfterShippingBloc(
        context, widget.buyerSubOrderBloc, widget.order, widget.subOrderList);
    cancelledByYouAfterSwadesicShippingBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Build

  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion

  //region Body
  Widget body() {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: cancelledByYouAfterSwadesicShippingBloc.groupNameList.length,
      itemBuilder: (context, index) {
        return cancelledByYouAfterShipping(
            groupName:
                cancelledByYouAfterSwadesicShippingBloc.groupNameList[index]);
      },
    );
  }
  //endregion

  //region Cancelled By You After Shipping
  Widget cancelledByYouAfterShipping({required String groupName}) {
    List<SubOrder> groupedSuborderList = [];

    ///Add all suborders to the suborder list as per the display package number
    groupedSuborderList = cancelledByYouAfterSwadesicShippingBloc.subOrderList
        .where((element) => element.displayPackageNumber == groupName)
        .toList();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: const BoxDecoration(
          color: AppColors.appWhite,
          border: Border(bottom: BorderSide(color: AppColors.lightStroke))),
      child: ExpandablePanel(
        //region Theme
        theme: const ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header:
            header(headerOrderList: groupedSuborderList, groupName: groupName),
        //endregion
        collapsed: onTapHowRefundAmountCalculated(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            onTapHowRefundAmountCalculated(),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: groupedSuborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        verticalSizedBox(10),
                        //Cancelled on
                        Text(
                            "Cancelled on: ${groupedSuborderList[index].cancelledDate == null ? '' : CommonMethods.convertStringDateTimeSlashFormat(groupedSuborderList[index].cancelledDate!)}",
                            maxLines: 2,
                            overflow: TextOverflow.visible,
                            textAlign: TextAlign.left,
                            style: AppTextStyle.heading3Medium(
                              textColor: AppColors.appBlack,
                            )),
                        verticalSizedBox(10),
                        //You will receive
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                "You will receive ₹ ${groupedSuborderList[index].refundDetails!.first.refundedAmount} as the refund amount",
                                maxLines: 2,
                                overflow: TextOverflow.visible,
                                textAlign: TextAlign.left,
                                style: AppTextStyle.contentHeading0(
                                    textColor: AppColors.appBlack),
                              ),
                            ),
                            SvgPicture.asset(
                              AppImages.exclamation,
                              height: 20,
                              color: AppColors.darkGray,
                            )
                          ],
                        ),
                        productInfoCard(
                            context: context,
                            subOrder: groupedSuborderList[index]),
                        //Cancel reason
                        //Reason
                        Text(
                          "${AppStrings.reason}: ${groupedSuborderList[index].cancellationReason ?? " "}",
                          textAlign: TextAlign.left,
                          style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack,
                          ),
                        ),

                        // Status for this product
                        Builder(builder: (context) {
                          // Check if this suborder has a secondary status
                          String? secondaryStatus;
                          if (groupedSuborderList[index]
                                      .secondarySuborderStatus !=
                                  null &&
                              groupedSuborderList[index]
                                  .secondarySuborderStatus!
                                  .isNotEmpty) {
                            secondaryStatus = groupedSuborderList[index]
                                .secondarySuborderStatus;
                          }

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Show status for this product
                              Container(
                                margin: const EdgeInsets.only(top: 10),
                                child: Text(
                                  "Status: ${groupedSuborderList[index].suborderStatus}",
                                  textAlign: TextAlign.left,
                                  style: AppTextStyle.contentHeading0(
                                      textColor: AppColors.brandBlack),
                                ),
                              ),

                              // Show return package details if status is RETURN_CONFIRMED
                              Visibility(
                                visible:
                                    secondaryStatus == 'RETURN_CONFIRMED' &&
                                        groupedSuborderList[index]
                                                .displayReturnPackageNumber !=
                                            null,
                                child: Container(
                                  margin: const EdgeInsets.only(top: 10),
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: AppColors.lightGreen2,
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Return Package: ${groupedSuborderList[index].displayReturnPackageNumber}",
                                        textAlign: TextAlign.left,
                                        style: AppTextStyle.contentHeading0(
                                            textColor: AppColors.appBlack),
                                      ),
                                      verticalSizedBox(5),
                                      if (groupedSuborderList[index]
                                              .returnConfirmationDate !=
                                          null)
                                        Text(
                                          "Return Confirmed on: ${groupedSuborderList[index].returnConfirmationDate}",
                                          textAlign: TextAlign.left,
                                          style: AppTextStyle.contentText0(
                                              textColor: AppColors.appBlack),
                                        ),
                                      verticalSizedBox(5),
                                      if (groupedSuborderList[index]
                                              .estimatedPickupDate !=
                                          null)
                                        Text(
                                          "Estimated Pickup: ${groupedSuborderList[index].estimatedPickupDate}",
                                          textAlign: TextAlign.left,
                                          style: AppTextStyle.contentText0(
                                              textColor: AppColors.appBlack),
                                        ),
                                      verticalSizedBox(5),
                                      if (groupedSuborderList[index]
                                              .returnTrackingNumber !=
                                          null)
                                        Text(
                                          "Tracking Number: ${groupedSuborderList[index].returnTrackingNumber}",
                                          textAlign: TextAlign.left,
                                          style: AppTextStyle.contentText0(
                                              textColor: AppColors.appBlack),
                                        ),
                                      verticalSizedBox(5),
                                      if (groupedSuborderList[index]
                                                  .returnTrackingLink !=
                                              null &&
                                          groupedSuborderList[index]
                                              .returnTrackingLink!
                                              .isNotEmpty)
                                        InkWell(
                                          onTap: () {
                                            CommonMethods.openUrl(
                                                url: groupedSuborderList[index]
                                                    .returnTrackingLink!);
                                          },
                                          child: Text(
                                            "Track Return",
                                            textAlign: TextAlign.left,
                                            style: AppTextStyle.contentText0(
                                                    textColor:
                                                        AppColors.brandBlack)
                                                .copyWith(
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          );
                        }),
                        verticalSizedBox(10),
                        //Divider
                        Visibility(
                          visible: groupedSuborderList.length - 1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header(
      {required List<SubOrder> headerOrderList, required String groupName}) {
    // Check if any suborder in this package has a secondary status
    String? secondaryStatus;
    if (cancelledByYouAfterSwadesicShippingBloc
        .isPartialCancellationForPackage(groupName)) {
      // Check all suborders in the order for this package
      if (cancelledByYouAfterSwadesicShippingBloc.store.subOrderList != null) {
        for (var suborder
            in cancelledByYouAfterSwadesicShippingBloc.store.subOrderList!) {
          if (suborder.displayPackageNumber == groupName &&
              suborder.secondarySuborderStatus != null &&
              suborder.secondarySuborderStatus!.isNotEmpty) {
            secondaryStatus = suborder.secondarySuborderStatus;
            break;
          }
        }
      }
    }

    // Create component title based on secondary status
    String componentTitle;
    if (cancelledByYouAfterSwadesicShippingBloc
            .isPartialCancellationForPackage(groupName) &&
        secondaryStatus != null &&
        secondaryStatus != "DELIVERY_IN_PROGRESS") {
      // Format: "Secondary status (generalised text) - package number"
      String readableStatus =
          CommonMethods.labelStatusToSentence(input: secondaryStatus) ??
              secondaryStatus;
      componentTitle =
          "$readableStatus - $groupName ${cancelledByYouAfterSwadesicShippingBloc.isPartialCancellationForPackage(groupName) ? '(Partial Cancel)' : '(Full Cancel)'}";
    } else {
      // Use the default title format
      componentTitle =
          "${AppStrings.cancelledByYouAfterShipping} :$groupName ${cancelledByYouAfterSwadesicShippingBloc.isPartialCancellationForPackage(groupName) ? '(Partial Cancel)' : '(Full Cancel)'}";
    }

    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.thumbUpIcon,
      componentName: componentTitle,
      suborderList: headerOrderList,
      isEstimateDeliveryShow: false,
      additionalWidgets: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Show current status right below the title
          Container(
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.only(bottom: 5),
            child: Text(
              "Current status: ${headerOrderList.first.suborderStatus}",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
            ),
          ),

          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.youCancelledThisOrderAfterShipping,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
          verticalSizedBox(5),
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.refundProcessWillStartAfterSellerReceives,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
            ),
          ),

          // Display package-specific cancellation message
          verticalSizedBox(10),
          Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: cancelledByYouAfterSwadesicShippingBloc
                      .isPartialCancellationForPackage(groupName)
                  ? AppColors.lightGreen2
                  : AppColors.orange.withOpacity(0.2),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Text(
              cancelledByYouAfterSwadesicShippingBloc
                  .getCancellationMessage(groupName),
              style: AppTextStyle.contentText0(
                  textColor: cancelledByYouAfterSwadesicShippingBloc
                          .isPartialCancellationForPackage(groupName)
                      ? AppColors.appBlack
                      : AppColors.orange),
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region On tap how refund calculated
  Widget onTapHowRefundAmountCalculated() {
    return BuyerMyOrderCommonWidgets.howRefundAmountCalculated(
        subOrderList: cancelledByYouAfterSwadesicShippingBloc.subOrderList,
        buyerSubOrderBloc:
            cancelledByYouAfterSwadesicShippingBloc.buyerSubOrderBloc);
  }

//endregion
}
