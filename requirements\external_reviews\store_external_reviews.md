# External Store Reviews
External reviews are reviews that added to a store by user on review request from a store. 

- Each user can add only one external review to a store.
- User needs a invite token to add external review.
- Store can create a external review request by tapping on "Create Store Review Request Link" option in store review



## APIs

### Check External Review exists
```bash
curl --location 'http://192.168.1.8:8000/content/check_external_store_review_request_exists/' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
    "store_reference": "S1744452089786",
    "user_identifier": "krishnakanth0143"
}'
```

Response:
success response:
```json
{
    "success": true,
    "message": "User already used the review request"
},
```
failure response:
```json
{
    "success": false,
    "message": "User does not have a used review request"
}
```

### Create External Review Request
```bash
curl --location 'http://192.168.1.8:8000/content/create_external_store_review_request/' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <token>' \
--data '{
    "store_reference": "S1744452089786",
    "user_identifier": "krishnakanth0143"
}'
```
user identifier can also have 
- "user_identifier": "+918801009165" (phone number)
- "user_identifier": "<EMAIL>" (email)


Response :
```json
{
    "message": "Existing review request found",
    "token": "ab6d2bbe-a5b4-4e10-ab6f-60456c98d89f",
    "expires_at": "2025-06-28T19:04:00.256862Z",
    "user_identifier": "ragnar_kratos"
}
```

### Add Comment
```bash
curl --location 'http://192.168.1.8:8000/graphdb/add_comment/' \
--header 'Authorization: Bearer <token>' \
--form 'parent_reference="S1744452089786"' \
--form 'comment_text="First store review"' \
--form 'entity_reference="U1749675054288"' \
--form 'comment_type="EXTERNAL_REVIEW"' \
--form 'rating_count="4"' \
--form 'external_review_token="dcdab192-7b89-4631-b82b-de3ca367d370"'
```
Response:
```json
{
    "message": "Comment added successfully",
    "reference": "CO202502261445347564"
}
```