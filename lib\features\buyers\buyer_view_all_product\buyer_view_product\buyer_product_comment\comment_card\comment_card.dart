import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/comment_card/comment_card_bloc.dart';
import 'package:swadesic/model/product_comment_response/product_all_comment_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';

class CommentList extends StatefulWidget {
  final BuyerProductCommentBloc buyerProductCommentBloc;
  final String storeReference;
  final String currentUserOrStore;
  final int? commentId;

  const CommentList(
      {Key? key,
      required this.buyerProductCommentBloc,
      required this.storeReference,
      required this.currentUserOrStore,
      this.commentId})
      : super(key: key);

  @override
  State<CommentList> createState() => _CommentListState();
}

class _CommentListState extends State<CommentList> {
  //region Bloc
  late CommentListBloc commentListBloc;
  //endregion
  //region Init
  @override
  void initState() {
    commentListBloc = CommentListBloc(
        context, widget.buyerProductCommentBloc, widget.commentId);
    commentListBloc.init();
    super.initState();
  }

  //endregion
  //region Did update
  @override
  void didUpdateWidget(covariant CommentList oldWidget) {
    setState(() {});
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: commentListBloc.commentRefreshCtrl.stream,
        builder: (context, snapshot) {
          return RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: widget.buyerProductCommentBloc.init,
            child: ListView.builder(
                // initialScrollIndex:commentListBloc.initialIndex,
                padding: const EdgeInsets.only(bottom: 20),
                itemCount:
                    widget.buyerProductCommentBloc.filteredComments.length,
                itemBuilder: (buildContext, parentCommentIndex) {
                  ///Add comment into a list string
                  //List<String> parentComment = widget.buyerProductCommentBloc.filteredComments[index].comments!.split(" ");
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //verticalSizedBox(5),
                      ///region Root Comments
                      //region Root comment
                      Container(
                        margin: const EdgeInsets.only(left: 5, right: 5),
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                            color: AppColors.appWhite,
                            // color: widget.buyerProductCommentBloc.filteredComments[parentCommentIndex].commentid == commentListBloc.selectedCommentId && commentListBloc.isCommentBackgroundColorVisible ?AppColors.lightGreen:AppColors.white,
                            boxShadow: widget
                                            .buyerProductCommentBloc
                                            .filteredComments[
                                                parentCommentIndex]
                                            .commentid ==
                                        commentListBloc.selectedCommentId &&
                                    commentListBloc
                                        .isCommentBackgroundColorVisible
                                ? AppColors.glowingShadowColor
                                : null),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              textScaleFactor: MediaQuery.textScaleFactorOf(
                                  AppConstants.globalNavigator.currentContext!),
                              text: TextSpan(
                                // style:AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
                                children: <TextSpan>[
                                  ///Store and user who added message
                                  TextSpan(
                                      style: widget
                                                  .buyerProductCommentBloc
                                                  .filteredComments[
                                                      parentCommentIndex]
                                                  .reference ==
                                              widget.storeReference
                                          ? AppTextStyle.contentText0(
                                              textColor: AppColors.brandBlack)
                                          : AppTextStyle.contentHeading0(
                                              textColor: AppColors.appBlack),
                                      // AppTextStyle.contentHeading0(textColor:widget.buyerProductCommentBloc.filteredComments[parentCommentIndex].reference==widget.storeReference?AppColors.brandGreen:AppColors.appBlack),

                                      text:
                                          "${widget.buyerProductCommentBloc.filteredComments[parentCommentIndex].username!} ",
                                      // style: TextStyle(fontFamily:AppConstants.rRegular, fontSize: 14, fontWeight: FontWeight.w700,
                                      //   color:widget.buyerProductCommentBloc.filteredComments[parentCommentIndex].reference==widget.storeReference?AppColors.brandGreen:AppColors.appBlack,
                                      // ),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          //print(widget.buyerProductCommentBloc.filteredComments[parentCommentIndex].username!);
                                          OnTapTag(
                                              context,
                                              widget
                                                  .buyerProductCommentBloc
                                                  .filteredComments[
                                                      parentCommentIndex]
                                                  .username!);
                                        }),

                                  ///Tagged user name and normal text
                                  TextSpan(
                                      style: AppTextStyle.contentText0(
                                          textColor: AppColors.appBlack),
                                      text: '',
                                      children: widget
                                          .buyerProductCommentBloc
                                          .filteredComments[parentCommentIndex]
                                          .comments!
                                          .split(" ")
                                          .map((value) {
                                        return value.startsWith('@') &&
                                                    value.length > 1 ||
                                                value.startsWith('!')
                                            ? TextSpan(
                                                text: ' $value',
                                                style:
                                                    AppTextStyle.contentText0(
                                                        textColor: AppColors
                                                            .writingBlack1),
                                                recognizer:
                                                    TapGestureRecognizer()
                                                      ..onTap = () {
                                                        //print(value);
                                                        // Tag(context,widget.buyerProductCommentBloc.filteredComments[parentCommentIndex].username!);

                                                        OnTapTag(
                                                            context,
                                                            value.replaceAll(
                                                                "@", ""));

                                                        // widget.buyerProductCommentBloc.goToProfile();
                                                      },
                                              )
                                            : TextSpan(
                                                text: ' $value',
                                                style:
                                                    AppTextStyle.contentText0(
                                                        textColor:
                                                            AppColors.appBlack),
                                              );
                                      }).toList()),
                                  // TextSpan(
                                  //   children: [
                                  //     TextSpan(
                                  //       text:  widget.buyerProductCommentBloc.filteredComments[index].comments,
                                  //       style: const TextStyle(
                                  //         fontFamily: "LatoRegular", fontSize: 14,
                                  //         color:AppColors.appBlack,
                                  //         fontWeight: FontWeight.w400,),
                                  //     ),
                                  //   ],
                                  // ),
                                ],
                              ),
                            ),
                            timeHelpful(
                                replyAndComments: widget.buyerProductCommentBloc
                                    .filteredComments[parentCommentIndex],
                                isFromChild: false

                                //endregion
                                ),
                            widget
                                        .buyerProductCommentBloc
                                        .filteredComments[parentCommentIndex]
                                        .commentType ==
                                    "review"
                                ? viewImages(widget.buyerProductCommentBloc
                                    .filteredComments[parentCommentIndex])
                                : const SizedBox(),
                          ],
                        ),
                      ),

                      ///Child Comment (Un comment)
                      //region Child comment
                      widget
                              .buyerProductCommentBloc
                              .filteredComments[parentCommentIndex]
                              .replies!
                              .isEmpty
                          ? Container()
                          : Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ///Replay list
                                ListView.builder(
                                  itemCount: widget
                                          .buyerProductCommentBloc
                                          .filteredComments[parentCommentIndex]
                                          .isReadMore
                                      ? 3
                                      : widget
                                          .buyerProductCommentBloc
                                          .filteredComments[parentCommentIndex]
                                          .replies!
                                          .length,
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemBuilder: (context, replayIndex) {
                                    return Container(
                                      //color: AppColors.yellow,
                                      // margin:const EdgeInsets.only(left: 20),
                                      // padding: const EdgeInsets.all(5),
                                      margin: const EdgeInsets.only(
                                          left: 20, right: 5),
                                      padding: const EdgeInsets.all(5),

                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          RichText(
                                            textScaleFactor:
                                                MediaQuery.textScaleFactorOf(
                                                    AppConstants.globalNavigator
                                                        .currentContext!),
                                            text: TextSpan(
                                              style:
                                                  DefaultTextStyle.of(context)
                                                      .style,
                                              children: <TextSpan>[
                                                ///Added message user name and admin
                                                TextSpan(
                                                    text:
                                                        "${widget.buyerProductCommentBloc.filteredComments[parentCommentIndex].replies![replayIndex].username!} ",
                                                    style: widget
                                                                .buyerProductCommentBloc
                                                                .filteredComments[
                                                                    parentCommentIndex]
                                                                .replies![
                                                                    replayIndex]
                                                                .reference ==
                                                            widget
                                                                .storeReference
                                                        ? AppTextStyle.contentText0(
                                                            textColor: AppColors
                                                                .brandBlack)
                                                        : AppTextStyle
                                                            .contentHeading0(
                                                                textColor:
                                                                    AppColors
                                                                        .appBlack),
                                                    // style: TextStyle(fontFamily: "LatoBold", fontSize: 14, fontWeight: FontWeight.w700,
                                                    //   color:widget.buyerProductCommentBloc.filteredComments[parentCommentIndex].replies![replayIndex].reference==widget.storeReference?AppColors.brandGreen:AppColors.appBlack,
                                                    //
                                                    // ),
                                                    recognizer:
                                                        TapGestureRecognizer()
                                                          ..onTap = () {
                                                            OnTapTag(
                                                                context,
                                                                widget
                                                                    .buyerProductCommentBloc
                                                                    .filteredComments[
                                                                        parentCommentIndex]
                                                                    .replies![
                                                                        replayIndex]
                                                                    .username!);
                                                          }),

                                                TextSpan(
                                                    text: '',
                                                    children: widget
                                                        .buyerProductCommentBloc
                                                        .filteredComments[
                                                            parentCommentIndex]
                                                        .replies![replayIndex]
                                                        .reply!
                                                        .split(" ")
                                                        .map((value) {
                                                      return value.startsWith(
                                                                      '@') &&
                                                                  value.length >
                                                                      1 ||
                                                              value.startsWith(
                                                                  '!')
                                                          ? TextSpan(
                                                              text: ' $value',
                                                              style: AppTextStyle
                                                                  .contentText0(
                                                                      textColor:
                                                                          AppColors
                                                                              .writingBlack1),
                                                              recognizer:
                                                                  TapGestureRecognizer()
                                                                    ..onTap =
                                                                        () {
                                                                      //print(value);
                                                                      // widget.buyerProductCommentBloc.goToProfile();
                                                                      OnTapTag(
                                                                          context,
                                                                          value.replaceAll(
                                                                              "@",
                                                                              ""));
                                                                    },
                                                            )
                                                          : TextSpan(
                                                              text: ' $value',
                                                              style: AppTextStyle
                                                                  .contentText0(
                                                                      textColor:
                                                                          AppColors
                                                                              .appBlack));
                                                    }).toList()),
                                              ],
                                            ),
                                            maxLines: 100,
                                          ),
                                          timeHelpful(
                                              replyAndComments: widget
                                                  .buyerProductCommentBloc
                                                  .filteredComments[
                                                      parentCommentIndex]
                                                  .replies![replayIndex],
                                              isFromChild: true),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                                Visibility(
                                  visible: widget
                                      .buyerProductCommentBloc
                                      .filteredComments[parentCommentIndex]
                                      .isReadMore,
                                  child: Container(
                                    margin: const EdgeInsets.only(left: 25),
                                    // height: 20,
                                    alignment: Alignment.centerLeft,

                                    child: CupertinoButton(
                                      padding: EdgeInsets.zero,
                                      onPressed: () {
                                        widget
                                            .buyerProductCommentBloc
                                            .filteredComments[
                                                parentCommentIndex]
                                            .isReadMore = false;
                                        widget.buyerProductCommentBloc
                                            .buyerProductCommentStateCtrl.sink
                                            .add(ProductCommentState.Success);
                                      },
                                      child: Text(
                                        AppStrings.viewMore,
                                        style: AppTextStyle.access0(
                                            textColor: AppColors.writingBlack1),
                                      ),
                                      // child: appText(AppStrings.viewMore,fontFamily: AppConstants.rRegular,
                                      //     color: AppColors.writingColor3,fontSize: 12,fontWeight: FontWeight.w700
                                      // ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                      //endregion
                      // verticalSizedBox(5)
                    ],
                  );
                }),
          );
        });
  }
//endregion

  //region Time Helpful and reply
  Widget timeHelpful(
      {required ReplyAndComments replyAndComments, required bool isFromChild}) {
    // //print(clap);
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        ///Rating, clap count, reply and time
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            ///Star and rating Number
            replyAndComments.review == 0.0
                ? const SizedBox()
                : Padding(
                    padding: const EdgeInsets.only(right: 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "${replyAndComments.review!.round()}",
                          style: AppTextStyle.smallText(
                              textColor: AppColors.writingBlack1),
                        ),
                        horizontalSizedBox(5),
                        SvgPicture.asset(
                          AppImages.star,
                          fit: BoxFit.cover,
                        ),

                        // const Icon(Icons.star,color: AppColors.yellow,size: 20,),
                      ],
                    ),
                  ),

            ///Comment Type Question
            replyAndComments.commentType == "question"
                ? Padding(
                    padding: const EdgeInsets.only(right: 10),
                    child: SvgPicture.asset(AppImages.commentQuestion),
                  )
                : const SizedBox(),

            ///Clap count
            Container(
              margin: const EdgeInsets.only(top: 5, bottom: 5, right: 5),
              child: Text(
                CommonMethods.singularPluralText(
                    item: replyAndComments.claps!,
                    singular: "clap",
                    plural: "claps"),
                style:
                    AppTextStyle.smallText(textColor: AppColors.writingBlack1),
              ),
            ),

            ///Reply
            InkWell(
              // padding: EdgeInsets.zero,
              onTap: () {
                widget.buyerProductCommentBloc.onTapReplay(
                    commentId: replyAndComments.commentid!,
                    userName: replyAndComments.username!);
              },
              child: Container(
                margin: const EdgeInsets.all(5),
                child: Text(AppStrings.reply,
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack1)),
              ),
            ),

            ///Time
            Container(
              margin: const EdgeInsets.all(5),
              child: Text(
                replyAndComments.commentedAt ?? replyAndComments.repliedAt!,
                style:
                    AppTextStyle.smallText(textColor: AppColors.writingBlack1),
              ),
            ),

            ///Three dots/Options
            InkWell(
                // padding: EdgeInsets.zero,
                onTap: replyAndComments.reference == widget.currentUserOrStore
                    ? () {
                        replyAndComments.replyid != null
                            ? widget.buyerProductCommentBloc
                                .childEditDeleteComment(
                                    childCommentId: replyAndComments.replyid!,
                                    childComment: replyAndComments.reply!)
                            : widget.buyerProductCommentBloc
                                .parentEditDeleteComment(
                                    replyAndComments.commentid!,
                                    replyAndComments.comments!,
                                    replyAndComments.commentType!);
                      }
                    : () {
                        widget.buyerProductCommentBloc.reportAndShareComment(
                            parentOrChildCommentId: replyAndComments.replyid ??
                                replyAndComments.commentid!,
                            isFromChild: isFromChild);
                      },
                child: Container(
                    margin: const EdgeInsets.all(5),
                    height: 10,
                    child: SvgPicture.asset(AppImages.commentOption,
                        fit: BoxFit.cover))),
          ],
        ),
        Expanded(child: horizontalSizedBox(10)),

        ///Clap icon
        InkWell(
            // padding: EdgeInsets.zero,
            onTap: () {
              widget.buyerProductCommentBloc
                  .onTapClap(replyAndComments: replyAndComments);
            },
            child: SizedBox(
                height: 16,
                width: 16,
                child: replyAndComments.clappedUsers!.contains(
                        AppConstants.appData.isUserView!
                            ? AppConstants.appData.userReference!
                            : AppConstants.appData.storeReference!)
                    ? SvgPicture.asset(
                        AppImages.clapTrue,
                        color: AppColors.yellow,
                      )
                    : SvgPicture.asset(AppImages.clapFalse))),
      ],
    );
  }

//endregion

  //region View Images
  Widget viewImages(ReplyAndComments comment) {
    return comment.commentReviewImage!.isEmpty
        ? const SizedBox()
        : Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        widget.buyerProductCommentBloc.onTapViewImage(comment);
                      },
                      child: Container(
                        // constraints: BoxConstraints(
                        //   maxHeight: double.infinity,
                        // ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 5),
                        decoration: const BoxDecoration(
                            color: AppColors.textFieldFill1,
                            borderRadius: BorderRadius.all(Radius.circular(5))),
                        child: Row(
                          children: [
                            Text(
                              comment.isExpand ? "Hide image" : "View Image",
                              style: AppTextStyle.contentText0(
                                  textColor: AppColors.appBlack),
                            ),
                            comment.isExpand
                                ? const Icon(
                                    Icons.keyboard_arrow_up_sharp,
                                    color: AppColors.brandBlack,
                                    size: 20,
                                  )
                                : const Icon(
                                    Icons.keyboard_arrow_down_sharp,
                                    color: AppColors.brandBlack,
                                    size: 20,
                                  )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                comment.isExpand
                    ? SizedBox(
                        height: 100,
                        child: ListView.builder(
                            itemCount: comment.commentReviewImage!.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () {
                                  widget.buyerProductCommentBloc
                                      .goToBuyerProductImageScreen(
                                          comment.commentReviewImage!);
                                },
                                child: Container(
                                  color: AppColors.textFieldFill1,
                                  height: 100,
                                  width: 100,
                                  margin:
                                      const EdgeInsets.only(right: 10, top: 5),
                                  child: extendedImage(
                                      comment.commentReviewImage![index]
                                          .reviewImage!,
                                      context,
                                      200,
                                      200,
                                      cache: true,
                                      fit: BoxFit.contain,
                                      imageHeight: 100),
                                ),
                              );
                            }),
                      )
                    : const SizedBox()
              ],
            ),
          );
  }
//endregion
}
