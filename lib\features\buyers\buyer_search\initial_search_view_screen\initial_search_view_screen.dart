// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:swadesic/features/buyers/buyer_search/initial_search_view_screen/initial_search_bloc.dart';
// import 'package:swadesic/util/app_colors.dart';
// import 'package:swadesic/util/app_images.dart';
// import 'package:swadesic/util/app_search_field/app_search_field.dart';
// import 'package:swadesic/util/app_strings.dart';
// import 'package:swadesic/util/app_text_style.dart';
// import 'package:swadesic/util/common_widgets.dart';
//
// class InitialSearchViewScreen extends StatefulWidget {
//
//   const InitialSearchViewScreen({Key? key}) : super(key: key);
//
//   @override
//   State<InitialSearchViewScreen> createState() => _InitialSearchViewScreenState();
// }
//
// class _InitialSearchViewScreenState extends State<InitialSearchViewScreen> {
//   //region Bloc
//   late InitialSearchBloc initialSearchBloc;
//   //endregion
//   //region Init
//   @override
//   void initState() {
//     initialSearchBloc = InitialSearchBloc(context);
//     initialSearchBloc.init();
//     super.initState();
//   }
//   //endregion
//   @override
//   Widget build(BuildContext context) {
//     return Hero(
//         tag: "initial search",
//         child: Scaffold(
//             backgroundColor: AppColors.appWhite,
//             body: SafeArea(child: Center(child: body()))));
//   }
//
//   //region Body
//   Widget body() {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 15),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.center,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//
//           Text(AppStrings.exploreAndExperience,
//             textAlign: TextAlign.center,
//             style: AppTextStyle.contentText0(textColor: AppColors.appBlack),),
//           Container(
//               decoration: const BoxDecoration(
//
//                   // color: Colors.white,
//                   boxShadow: [
//                     BoxShadow(
//                       color:AppColors.brandGreen,
//                       spreadRadius: 4,
//                       blurRadius: 10,
//                     ),
//                     BoxShadow(
//                       color: AppColors.brandGreen,
//                       spreadRadius: -4,
//                       blurRadius: 5,
//                     )
//                   ]),
//               margin: const EdgeInsets.only(top: 60, bottom: 20), height: 267, width: 264, child: SvgPicture.asset(AppImages.tree)),
//           useOurApp(),
//           searchFieldAndFilter(),
//         ],
//       ),
//     );
//   }
// //endregion
//
// //region Use our app
//   Widget useOurApp() {
//     return Container(
//       margin: const EdgeInsets.symmetric(vertical: 26),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.center,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//
//           Text(AppStrings.useOurApp,
//             textAlign: TextAlign.center,
//             style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
//           )
//
//         ],
//       ),
//     );
//   }
//
// //endregion
//
// //region Search field and filters
//   Widget searchFieldAndFilter() {
//     return Column(
//       children: [
//         Container(
//
//           margin: const EdgeInsets.only(top: 20,bottom: 10),
//           child: InkWell(
//             onTap: (){
//               initialSearchBloc.goToSearchScreen(index: 0);
//
//             },
//             child: AppSearchField(textEditingController: TextEditingController(),
//             isActive: false, hintText:AppStrings.search,
//             ),
//           ),
//         ),
//
//         Row(
//           mainAxisSize: MainAxisSize.max,
//           mainAxisAlignment: MainAxisAlignment.start,
//           children: [
//             Text("Search :",style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
//             horizontalSizedBox(20),
//             filterBox(data:AppStrings.stores,index: 1),
//             horizontalSizedBox(10),
//             filterBox(data: AppStrings.products,index: 2),
//             horizontalSizedBox(10),
//             filterBox(data: AppStrings.people,index: 3),
//           ],
//         )
//       ],
//     );
//   }
//
// //endregion
//
// //region Filter box
//   Widget filterBox({required int index,required String data}) {
//     return Row(
//       children: [
//         CupertinoButton(
//           padding: EdgeInsets.zero,
//           onPressed: (){
//             initialSearchBloc.goToSearchScreen(index: index);
//
//           },
//
//           child: Container(
//             padding: const EdgeInsets.symmetric(horizontal:15,vertical: 10),
//             alignment: Alignment.center,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(9),
//               color: AppColors.textFieldFill1,
//             ),
//             child: Text(data,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),),
//           ),
//         ),
//       ],
//     );
//   }
// //endregion
//
// }
