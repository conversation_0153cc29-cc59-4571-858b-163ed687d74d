import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:swadesic/util/app_constants.dart';
import 'dart:developer' as developer;

class ImagePreviewDialog extends StatefulWidget {
  final List<Map<String, dynamic>> imageAttachments;
  final int initialIndex;

  const ImagePreviewDialog({
    Key? key,
    required this.imageAttachments,
    required this.initialIndex,
  }) : super(key: key);

  @override
  _ImagePreviewDialogState createState() => _ImagePreviewDialogState();
}

class _ImagePreviewDialogState extends State<ImagePreviewDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  String _getImageUrl(Map<String, dynamic> attachment) {
    String fileUrl = attachment['url'] ?? '';

    // If the URL is a relative path (starts with '/'), prepend the base URL
    if (fileUrl.startsWith('/')) {
      // Use the newMessaging_baseUrl for file URLs
      fileUrl = AppConstants.newMessaging_baseUrl + fileUrl;

      developer.log(
        'Constructed image URL in preview: $fileUrl',
        name: 'ImagePreviewDialog'
      );
    }

    developer.log(
      'Image URL in preview: $fileUrl',
      name: 'ImagePreviewDialog'
    );

    return fileUrl;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.zero,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black.withOpacity(0.9),
        child: Stack(
          children: [
            // Image gallery
            PhotoViewGallery.builder(
              scrollPhysics: const BouncingScrollPhysics(),
              builder: (BuildContext context, int index) {
                return PhotoViewGalleryPageOptions(
                  imageProvider: NetworkImage(
                  _getImageUrl(widget.imageAttachments[index]),
                  headers: {
                    'Authorization': 'Bearer ${AppConstants.appData.newMessagingToken}',
                  },
                ),
                  initialScale: PhotoViewComputedScale.contained,
                  minScale: PhotoViewComputedScale.contained * 0.8,
                  maxScale: PhotoViewComputedScale.covered * 2,
                  heroAttributes: PhotoViewHeroAttributes(tag: 'image_${widget.imageAttachments[index]['url']}'),
                );
              },
              itemCount: widget.imageAttachments.length,
              loadingBuilder: (context, event) => Center(
                child: Container(
                  width: 30.0,
                  height: 30.0,
                  child: CircularProgressIndicator(
                    value: event == null
                        ? 0
                        : event.cumulativeBytesLoaded / (event.expectedTotalBytes ?? 1),
                  ),
                ),
              ),
              backgroundDecoration: BoxDecoration(
                color: Colors.transparent,
              ),
              pageController: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
            ),

            // Close button
            Positioned(
              top: 40,
              right: 20,
              child: IconButton(
                icon: Icon(Icons.close, color: Colors.white, size: 30),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ),

            // Image info
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                color: Colors.black.withOpacity(0.5),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      widget.imageAttachments[_currentIndex]['originalName'] ?? 'Image',
                      style: AppTextStyle.contentText0(
                        textColor: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4),
                    Text(
                      '${_currentIndex + 1} of ${widget.imageAttachments.length}',
                      style: AppTextStyle.contentText0(
                        textColor: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
