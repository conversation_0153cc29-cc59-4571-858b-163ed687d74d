import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/support/tickets_to_me/tickets_to_me_bloc.dart';
import 'package:swadesic/features/support/support_common_widgets.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class TicketsToMeScreen extends StatefulWidget {
  final String entityReference;
  
  const TicketsToMeScreen({
    Key? key,
    required this.entityReference,
  }) : super(key: key);

  @override
  TicketsToMeScreenState createState() => TicketsToMeScreenState();
}

class TicketsToMeScreenState extends State<TicketsToMeScreen>
    with AutomaticKeepAliveClientMixin<TicketsToMeScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  late TicketsToMeBloc ticketsToMeBloc;

  @override
  void initState() {
    super.initState();
    ticketsToMeBloc = TicketsToMeBloc(context, widget.entityReference);
    ticketsToMeBloc.init();
  }

  //region Dispose
  @override
  void dispose() {
    PaintingBinding.instance.imageCache.clear();
    imageCache.clear();
    ticketsToMeBloc.dispose();
    super.dispose();
  }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: StreamBuilder<TicketsToMeState>(
        stream: ticketsToMeBloc.ticketsToMeCtrl.stream,
        builder: (context, snapshot) {
          return GestureDetector(
            onTap: () {
              CommonMethods.closeKeyboard(context);
            },
            child: body(),
          );
        },
      ),
    );
  }

  // endregion

  Widget body() {
    return StreamBuilder<TicketsToMeState>(
      stream: ticketsToMeBloc.ticketsToMeCtrl.stream,
      initialData: TicketsToMeState.Loading,
      builder: (context, snapshot) {
        // Loading
        if (snapshot.data == TicketsToMeState.Loading) {
          return AppCommonWidgets.appCircularProgress();
        }
        
        // Success
        if (snapshot.data == TicketsToMeState.Success) {
          return Column(
            children: [
              searchField(),
              ticketsToMeBloc.finalFilteredTicketsList.isEmpty
                  ? Expanded(
                      child: RefreshIndicator(
                        color: AppColors.brandBlack,
                        onRefresh: () async {
                          await ticketsToMeBloc.init();
                        },
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: Container(
                            alignment: Alignment.center,
                            height: MediaQuery.of(context).size.width,
                            child: NoResult(message: AppStrings.noResults),
                          ),
                        ),
                      ),
                    )
                  : Expanded(child: ticketsList()),
            ],
          );
        }
        
        // Failed
        if (snapshot.data == TicketsToMeState.Failed) {
          return AppCommonWidgets.errorWidget(onTap: () {
            ticketsToMeBloc.init();
          });
        }
        
        return AppCommonWidgets.appCircularProgress();
      },
    );
  }

//region Search field
  Widget searchField() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Row(
        children: [
          Expanded(
            child: AppSearchField(
              textEditingController: ticketsToMeBloc.searchFieldTextCtrl,
              hintText: "Search tickets with id, keywords",
              onChangeText: (v) {
                ticketsToMeBloc.onSearch();
              },
              onTapSuffix: () {
                ticketsToMeBloc.onSearch();
              },
              isAutoFocus: false,
              isActive: true,
            ),
          ),
          horizontalSizedBox(10),
          InkWell(
              onTap: () {
                // Add filter functionality if needed
                // ticketsToMeBloc.onTapFilter();
              },
              child: SvgPicture.asset(AppImages.filter2))
        ],
      ),
    );
  }
//endregion

//region Tickets list
  Widget ticketsList() {
    return Scrollbar(
      child: RefreshIndicator(
        color: AppColors.brandBlack,
        onRefresh: () async {
          await ticketsToMeBloc.init();
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: ticketsToMeBloc.finalFilteredTicketsList.length,
            itemBuilder: (BuildContext context, int index) {
              return InkWell(
                onTap: () {
                  ticketsToMeBloc.goToTicketDetail(
                    ticketDetail: ticketsToMeBloc.finalFilteredTicketsList[index],
                    ticketId: ticketsToMeBloc.finalFilteredTicketsList[index].feedbackId!,
                  );
                },
                child: SupportScreenCommonWidgets.allFeedbackCard(
                  feedbackDetail: ticketsToMeBloc.finalFilteredTicketsList[index],
                  onTapVote: () {
                    ticketsToMeBloc.onTapUpVote(
                      rootFeedback: ticketsToMeBloc.finalFilteredTicketsList[index],
                    );
                  },
                  onTapRightArrow: () {
                    // Handle right arrow tap if needed
                  },
                ),
              );
            },
          ),
        ),
      ),
    );
  }

//endregion
}
