import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';

/// A wrapper around BuyerViewSingleProductScreen that adds the ability to scroll to reviews
class BuyerSingleProductDetailScreen extends StatefulWidget {
  final String productReference;
  final bool scrollToReviews;

  const BuyerSingleProductDetailScreen({
    Key? key,
    required this.productReference,
    this.scrollToReviews = false,
  }) : super(key: key);

  @override
  _BuyerSingleProductDetailScreenState createState() => _BuyerSingleProductDetailScreenState();
}

class _BuyerSingleProductDetailScreenState extends State<BuyerSingleProductDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return BuyerViewSingleProductScreen(
      productReference: widget.productReference,
      // Pass the scrollToReviews parameter to the ProductDetailFullCard via a callback
      // This will be handled in the initState of BuyerViewSingleProductScreen
    );
  }

  @override
  void initState() {
    super.initState();
    
    // If scrollToReviews is true, we need to wait for the product to load
    // and then scroll to the reviews section
    if (widget.scrollToReviews) {
      // Use a post-frame callback to ensure the screen is fully built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Wait a bit for the product to load
        Future.delayed(const Duration(milliseconds: 500), () {
          // Scroll to the reviews section
          ProductDetailFullCardBloc.scrollToReviews();
        });
      });
    }
  }
}
