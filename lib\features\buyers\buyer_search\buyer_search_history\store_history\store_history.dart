import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_history_screen_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/buyer_search_history_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/store_history/store_history_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/buyer_search_response/buyer_search_history_response.dart';
import 'package:swadesic/util/app_images.dart';

class StoreHistory extends StatefulWidget {
  final List<History> storeHistoryList;
  final BuyerSearchHistoryBloc buyerSearchHistoryBloc;
  final bool isAllView;

  const StoreHistory({Key? key, required this.storeHistoryList, required this.buyerSearchHistoryBloc, required this.isAllView}) : super(key: key);

  @override
  State<StoreHistory> createState() => _StoreHistoryState();
}

class _StoreHistoryState extends State<StoreHistory> {
  //region Bloc
  late StoreHistoryBloc storeHistoryBloc;

  //endregion
  //region Init
  @override
  void initState() {
    storeHistoryBloc = StoreHistoryBloc(context, widget.storeHistoryList, widget.buyerSearchHistoryBloc);
    storeHistoryBloc.init();
    // TODO: implement initState
    super.initState();
  }
  //endregion
  //region Dispose
  @override
  void dispose() {
    storeHistoryBloc.dispose();
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: storeHistoryBloc.refreshCtrl.stream,
        builder: (context, snapshot) {
          ///If there is no product then
          if (widget.storeHistoryList.isEmpty) {
            return const SizedBox();
          }

          return body();
        });
  }

  //region body
  Widget body() {
    return Column(
      children: [
        BuyerSearchCommonWidgets.title(data: "Stores"),
        //View all is true
        Visibility(
          visible: widget.isAllView,
          child: Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 15),
              child: ListView.builder(
                  itemCount: widget.isAllView?widget.storeHistoryList.length:widget.storeHistoryList.length > 3 ? storeHistoryBloc.itemCount : widget.storeHistoryList.length,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return BuyerSearchCommonWidgets.searchedDataCard(
                        customImageContainerType: CustomImageContainerType.store,
                        isStore: true,
                        verifiedWidget: VerifiedBadge(
                          width: 15,
                          height: 15,
                          subscriptionType: widget.storeHistoryList[index].subscriptionType,
                        ),
                        placeHolder: AppImages.storePlaceHolder,
                        context: context,
                        heading: widget.storeHistoryList[index].handle,
                        imageUrl: widget.storeHistoryList[index].icon,
                        title: widget.storeHistoryList[index].name,
                        // subTitle: widget.storeHistoryList[index].location!,
                        subTitle: "${widget.storeHistoryList[index].location}",

                        isCrossVisible: true,
                        onPressCross: () {
                          storeHistoryBloc.onPressCross(history: widget.storeHistoryList[index]);
                        },
                        onTapCard: () {
                          widget.buyerSearchHistoryBloc.goToSingleStoreScreen(storeReference: widget.storeHistoryList[index].searchItem!);
                        });
                  }),
            ),
          ),
        ),
        //View all is false
        Visibility(
          visible: !widget.isAllView,
          child: Padding(
            padding: const EdgeInsets.only(left: 15),
            child: ListView.builder(
                itemCount: widget.isAllView?widget.storeHistoryList.length:widget.storeHistoryList.length > 3 ? storeHistoryBloc.itemCount : widget.storeHistoryList.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return BuyerSearchCommonWidgets.searchedDataCard(
                      customImageContainerType: CustomImageContainerType.store,
                    isStore: true,
                      placeHolder: AppImages.storePlaceHolder,
                      context: context,
                      heading: widget.storeHistoryList[index].handle,
                      imageUrl: widget.storeHistoryList[index].icon,
                      title: widget.storeHistoryList[index].name,
                      subTitle: widget.storeHistoryList[index].location!,
                      isCrossVisible: true,
                      onPressCross: () {
                        storeHistoryBloc.onPressCross(history: widget.storeHistoryList[index]);
                      },
                      onTapCard: () {
                        widget.buyerSearchHistoryBloc.goToSingleStoreScreen(storeReference: widget.storeHistoryList[index].searchItem!);
                      });
                }),
          ),
        ),

        InkWell(
          onTap: () {
            storeHistoryBloc.onTapViewMore(isIncrease: true);
          },
          child: Visibility(
              visible: widget.isAllView?false:widget.storeHistoryList.length > storeHistoryBloc.itemCount,
              child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: "View more stores",isUnderline: true)),
        ),
      ],
    );
  }
//endregion
}
