import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_history_screen_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/buyer_search_history.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_initial_user_and_store_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/search_post_store_product_and_people/search_post_store_product_and_people_screen.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/search_result_count_data_model/search_result_count_data_model.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Buyer HomeScreen
class BuyerSearchScreen extends StatefulWidget {
  // final BuyerSearchHistoryResponse buyerSearchHistoryResponse;

  const BuyerSearchScreen({Key? key}) : super(key: key);

  @override
  _BuyerSearchScreenState createState() => _BuyerSearchScreenState();
}
// endregion

class _BuyerSearchScreenState extends State<BuyerSearchScreen> with TickerProviderStateMixin {
  // region Bloc and tab controller
  late BuyerSearchBloc buyerSearchBloc;
  late TabController tabController = TabController(
      length: 4, vsync: this, initialIndex: BuyerSearchBloc.searchScreenSelectedTab, animationDuration: const Duration(milliseconds: 0));
  late TabController initialTabCtrl = TabController(length: 2, vsync: this);

  // endregion

  // region Init
  @override
  void initState() {
    buyerSearchBloc = BuyerSearchBloc(context, tabController, initialTabCtrl);
    buyerSearchBloc.init();
    super.initState();
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    imageCache.clear();
    buyerSearchBloc.dispose();
    // TODO: implement dispose
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        //print("${didPop} Tssssssssssssssssssssssssssssssssss");
      },
      child: GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
        },
        child: Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                searchBar(),
                Expanded(
                  child: Stack(
                    children: [
                      StreamBuilder<bool>(
                          stream: buyerSearchBloc.screenRefreshCtrl.stream,
                          builder: (context, snapshot) {
                            return SafeArea(
                                child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // searchBar(),
                                StreamBuilder<BuyerSearchState>(
                                    stream: buyerSearchBloc.buyerSearchCtrl.stream,
                                    initialData: BuyerSearchState.Initial,
                                    builder: (context, snapshot) {
                                      // return Expanded(child: body());
                                      if (snapshot.data == BuyerSearchState.Initial) {
                                        return SearchInitialUserAndStoreScreen();
                                      }
                                      if (snapshot.data == BuyerSearchState.Loading) {
                                        // return const Center(
                                        //   child: CircularProgressIndicator(),
                                        // );
                                        return const SizedBox();
                                      }
                                      if (snapshot.data == BuyerSearchState.History) {
                                        return Expanded(
                                            child: BuyerSearchHistory(
                                          buyerSearchBloc: buyerSearchBloc,
                                        ));
                                      }
                                      if (snapshot.data == BuyerSearchState.Success) {
                                        return Expanded(child: body());
                                      }
                                      return Expanded(child: body());
                                    }),
                              ],
                            ));
                          }),
                      Positioned(top: 0, left: 0, right: 0, bottom: 0, child: searchProgress())
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // endregion

  //region Search progress
  Widget searchProgress() {
    return StreamBuilder<bool>(
        stream: buyerSearchBloc.searchProgressState.stream,
        initialData: false,
        builder: (context, snapshot) {
          //True
          if (snapshot.data!) {
            return AppCommonWidgets.appCircularProgress();
            // return Center(child: CircularProgressIndicator(color: AppColors.brandGreen,));
          }
          //False
          else {
            return const SizedBox();
          }
        });
  }

  //endregion

  //region Error
  Widget error() {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(AppImages.noHistoryIcon),
        verticalSizedBox(7.12),
        Text(
          AppStrings.yourSearchResultIsUnAvailable,
          style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0),
        ),
        verticalSizedBox(4.5),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppStrings.noWorry,
              style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
            ),
            horizontalSizedBox(3),
            SvgPicture.asset(AppImages.smileEmoji)
          ],
        ),
      ],
    ));
  }

//endregion

  //region Searchbar
  Widget searchBar() {
    return StreamBuilder<BuyerSearchState>(
        stream: buyerSearchBloc.buyerSearchCtrl.stream,
        initialData: BuyerSearchState.History,
        builder: (context, snapshot) {
          // if (snapshot.data == BuyerSearchState.Initial) {
          //   return const SizedBox();
          // }
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            child: AppSearchField(
              textEditingController: BuyerSearchBloc.searchTextEditingCtrl,
              focusNode: buyerSearchBloc.focusNode,
              isActive: true,
              hintText: AppStrings.search,
              onChangeText: (value) {
                buyerSearchBloc.onChangeTextField();
              },
              onTapSuffix: () {
                BuyerSearchBloc.searchTextEditingCtrl.clear();
                buyerSearchBloc.buyerSearchCtrl.sink.add(BuyerSearchState.Initial);
                CommonMethods.closeKeyboard(context);
              },
              onSubmit: () {
                buyerSearchBloc.getSearchResult(pinCode: buyerSearchBloc.pinCodeUsedToSearch);
              },
            ),
            // child: TextFormField(
            //   autofocus: true,
            //   controller: BuyerSearchBloc.searchTextEditingCtrl,
            //   onChanged: (value) {
            //     buyerSearchBloc.onChangeTextField();
            //   },
            //   maxLines: 1,
            //   style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack),
            //   decoration: InputDecoration(
            //     // prefixIcon: Icon(Icons.search,size: 30,color: AppColors.appBlack7,),
            //     // prefixIcon: Padding(
            //     //   padding: EdgeInsets.symmetric(horizontal: 11.73),
            //     //   child: SvgPicture.asset(
            //     //     AppImages.searchBarIcon,
            //     //     fit: BoxFit.contain,
            //     //     color: AppColors.appBlack7,
            //     //   ),
            //     // ),
            //     prefixIcon: Padding(
            //       padding: const EdgeInsets.symmetric(vertical: 10),
            //       child: SvgPicture.asset(
            //         AppImages.searchBarIcon,
            //         fit: BoxFit.contain,
            //         color: AppColors.appBlack7,
            //       ),
            //     ),
            //     suffix: InkWell(
            //       onTap: () {
            //         BuyerSearchBloc.searchTextEditingCtrl.clear();
            //         buyerSearchBloc.buyerSearchCtrl.sink.add(BuyerSearchState.History);
            //         CommonMethods.closeKeyboard(context);
            //       },
            //       child: SvgPicture.asset(
            //         AppImages.close,
            //         fit: BoxFit.contain,
            //         color: AppColors.appBlack7,
            //       ),
            //     ),
            //     filled: true,
            //
            //     // contentPadding: EdgeInsets.all(0),
            //
            //     fillColor: AppColors.lightestGrey,
            //
            //     isDense: true,
            //
            //     hintText: "search products, stores & your friends",
            //     hintStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: AppColors.appBlack.withOpacity(0.4)),
            //     focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(22), borderSide: BorderSide.none),
            //     enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(22), borderSide: BorderSide.none),
            //   ),
            // ),
          );
        });
  }

  //endregion

  // region Body
  Widget body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        filter(),
        Expanded(
          child: TabBarView(
            controller: buyerSearchBloc.tabController, // Assign the TabController to the TabBarView
            children: [

              // ListView(
              //   children: [
              //     SearchPostStoreProductAndPeopleScreen(entityType: EntityType.POST,limit: 3,isDetailView: false),
              //     SearchPostStoreProductAndPeopleScreen(entityType: EntityType.STORE,limit: 3,isDetailView: false),
              //     SearchPostStoreProductAndPeopleScreen(entityType: EntityType.PRODUCT,limit: 3,isDetailView: false),
              //     SearchPostStoreProductAndPeopleScreen(entityType: EntityType.USER,limit: 3,isDetailView: false),
              //   ],
              // ),
              SearchPostStoreProductAndPeopleScreen(entityType: EntityType.POST,limit: 30, buyerSearchBloc: buyerSearchBloc,),
              SearchPostStoreProductAndPeopleScreen(entityType: EntityType.STORE,limit: 30,buyerSearchBloc: buyerSearchBloc,),
              SearchPostStoreProductAndPeopleScreen(entityType: EntityType.PRODUCT,limit: 30,buyerSearchBloc: buyerSearchBloc,),
              SearchPostStoreProductAndPeopleScreen(entityType: EntityType.USER,limit: 30,buyerSearchBloc: buyerSearchBloc,),


              // allTabView(),
              // postTabView(),
              // storeTabView(),
              // productTabView(),
              // peopleTabView(),
            ],
          ),
        ),
        // StreamBuilder<bool>(
        //     stream: buyerSearchBloc.onChangeOptionCtrl.stream,
        //     builder: (context, snapshot) {
        //       var store = buyerSearchBloc.searchResponse.store!;
        //       var product = buyerSearchBloc.filteredProductList;
        //       var people = buyerSearchBloc.searchResponse.user!;
        //
        //       //All
        //       if (AppConstants.searchScreenSelectedTab == 0) {
        //         return Expanded(
        //           child: store.isEmpty && product.isEmpty && people.isEmpty
        //               ? noResults()
        //               : ListView(
        //                   padding: EdgeInsets.zero,
        //                   shrinkWrap: true,
        //                   children: [
        //                     // verticalSizedBox(20),
        //                     storeViewAllAll(),
        //                     productViewAllAll(),
        //                     peopleViewAllAll(),
        //                   ],
        //                 ),
        //         );
        //       }
        //       //Only Store
        //       if (AppConstants.searchScreenSelectedTab == 1) {
        //         return Expanded(child: storeProductPeople('Stores'));
        //       }
        //       //Only Product
        //       if (AppConstants.searchScreenSelectedTab == 2) {
        //         return Expanded(child: storeProductPeople('Products'));
        //       }
        //       //Only Store
        //       if (AppConstants.searchScreenSelectedTab == 3) {
        //         return Expanded(child: storeProductPeople('People'));
        //       }
        //       return Container();
        //     })
      ],
    );
  }

// endregion

  //region All tab view
  Widget allTabView() {
    return StreamBuilder<bool>(
        stream: buyerSearchBloc.onChangeOptionCtrl.stream,
        builder: (context, snapshot) {
          var store = buyerSearchBloc.searchResponse.store!;
          var product = buyerSearchBloc.filteredProductList;
          var people = buyerSearchBloc.searchResponse.user!;
          //All
          return store.isEmpty && product.isEmpty && people.isEmpty
              ? noResults()
              : ListView(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  children: [
                    // verticalSizedBox(20),
                    postViewAllAll(),
                    storeViewAllAll(),
                    productViewAllAll(),
                    peopleViewAllAll(),
                  ],
                );
        });
  }

  //endregion

  //region Post tab view
  Widget postTabView() {
    return Consumer<PostDataModel>(
      builder: (context, data, child) {
        // List<PostDetail> postList = data.allPostDetailList
        //     .where((element) => buyerSearchBloc.searchResponse.post!.any((e) => e.postReference == element.postReference))
        //     .toList();

        var post = buyerSearchBloc.searchResponse.post;

        //print("Total post in search is ${post!.length}");

        //No People
        if (post!.isEmpty) {
          return noResults();
        }
        return StreamBuilder<int>(
          stream: buyerSearchBloc.allViewMorePostCtrl.stream,
          initialData: buyerSearchBloc.allViewMorePostItemCount,
          builder: (context, snapshot) {
            return post!.isEmpty
                ? Container()
                :
                //region Stores
                Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          //Product Text
                          BuyerSearchCommonWidgets.title(
                            data: AppStrings.posts,
                          ),
                        ],
                      ),
                      Expanded(
                        child: ListView.builder(
                          physics: const AlwaysScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: post.length,
                          itemBuilder: (context, index) {
                            return PostCard(
                              postDetail: post[index],
                              onTapDelete: () {
                                buyerSearchBloc.confirmDelete(postDetail: post[index]);
                              },
                              onTapDrawer: () {
                                buyerSearchBloc.onTapDrawer(postDetail: post[index]);
                              },
                              onTapEdit: () {
                                buyerSearchBloc.goToEditPost(postDetail: post[index]);
                              },
                              onTapHeart: () {
                                buyerSearchBloc.onTapHeart(postDetail: post[index]);
                              },

                              onTapShare: () {
                                buyerSearchBloc.onTapShare(postDetail: post[index]);
                              },
                              onTapProfileImage: () {
                                buyerSearchBloc.onTapUserOrStoreIcon(reference: post[index].createdBy!.userOrStoreReference!);
                              },
                              onTapPost: () {
                                buyerSearchBloc.goToSinglePostView(postReference: post[index].postOrCommentReference!);
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  );
          },
        );
      },
    );
  }

  //endregion

  //region Store tab view
  Widget storeTabView() {
    var store = buyerSearchBloc.searchResponse.store;

    return StreamBuilder<bool>(
        stream: buyerSearchBloc.onChangeOptionCtrl.stream,
        builder: (context, snapshot) {
          //If store is empty
          if (store!.isEmpty) {
            return noResults();
          }
          // return Container(height: 100,width: 400,color: Colors.green,);
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  //Product Text
                  BuyerSearchCommonWidgets.title(
                    data: AppStrings.stores,
                  ),
                ],
              ),
              //region Stores View All
              //endregion

              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: store.length,
                  // itemCount: 1,
                  itemBuilder: (BuildContext, index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        InkWell(
                          onTap: () {
                            buyerSearchBloc.goToViewStoreScreen(store[index]);
                          },
                          child: BuyerSearchCommonWidgets.searchedDataCard(
                            customImageContainerType: CustomImageContainerType.store,
                            placeHolder: AppImages.storePlaceHolder,
                            context: context,
                            imageUrl: store[index].icon,
                            isStore: true,
                            heading: store[index].storehandle!,
                            title: store[index].storeName!,
                            subTitle: "${store[index].categoryName} • ${store[index].storeDetails!.first.location}",
                            isCrossVisible: false,
                            onTapCard: () {
                              buyerSearchBloc.goToViewStoreScreen(store[index]);
                            },
                          ),

                          // child: SizedBox(
                          //   height: 54,
                          //   child: Row(
                          //     mainAxisSize: MainAxisSize.max,
                          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //     crossAxisAlignment: CrossAxisAlignment.center,
                          //     children: [
                          //       ClipRRect(
                          //         borderRadius: BorderRadius.all(Radius.circular(12)),
                          //         child: SizedBox(
                          //             height: 54,
                          //             width: 54,
                          //             child: store[index].icon != null
                          //                 ? extendedImage(store[index].icon!, context, 100, 100, cache: true, fit: BoxFit.cover)
                          //                 : SvgPicture.asset(AppImages.noStoreLogo)),
                          //       ),
                          //       horizontalSizedBox(15),
                          //       Column(
                          //         mainAxisSize: MainAxisSize.max,
                          //         mainAxisAlignment: MainAxisAlignment.spaceAround,
                          //         crossAxisAlignment: CrossAxisAlignment.start,
                          //         children: [
                          //           Text(
                          //             "${store[index].storeName}",
                          //             style: const TextStyle(color: AppColors.appBlack, fontSize: 14, fontFamily: "LatoBold", fontWeight: FontWeight.w700),
                          //           ),
                          //           Text(
                          //             "${store[index].storehandle}",
                          //             style: TextStyle(color: AppColors.appBlack, fontSize: 12, fontFamily: "LatoRegular", fontWeight: FontWeight.w400),
                          //           ),
                          //           Text(
                          //             "${store[index].location}",
                          //             style: TextStyle(color: AppColors.darkGray, fontSize: 12, fontFamily: "LatoBold", fontWeight: FontWeight.w700),
                          //           ),
                          //         ],
                          //       ),
                          //       Expanded(child: horizontalSizedBox(0)),
                          //     ],
                          //   ),
                          // ),
                        ),
                      ],
                    );
                  },
                ),
              ),

              //verticalSizedBox(10),
            ],
          );
        });
  }

  //endregion

  //region Product tab view
  Widget productTabView() {
    var product = buyerSearchBloc.filteredProductList;

    return StreamBuilder<bool>(
        stream: buyerSearchBloc.onChangeOptionCtrl.stream,
        builder: (context, snapshot) {
          //No product
          if (product.isEmpty) {
            return noResults();
          }
          // return Container(height: 100,width: 400,color: Colors.green,);
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              //region Stores View All
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      //Product Text
                      BuyerSearchCommonWidgets.title(
                        data: AppStrings.products,
                      ),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 16),
                    child: InkWell(
                      onTap: () {
                        buyerSearchBloc.goToViewAllProducts();

                        // AppConstants.buyerSearchFilter = 'People';
                        // buyerSearchBloc.screenRefreshCtrl.sink.add(true);
                      },
                      child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: AppStrings.viewAll, isUnderline: true),
                    ),
                  ),
                  // product!.isEmpty
                  //     ? Container()
                  //     : InkWell(
                  //         onTap: () {
                  //           buyerSearchBloc.goToViewAllProducts();
                  //         },
                  //         child: Text(
                  //           AppStrings.viewAll,
                  //           style: TextStyle(
                  //               color: AppColors.brandGreen,
                  //               fontSize: 14,
                  //               fontFamily: "LatoRegular",
                  //               fontWeight: FontWeight.w400,
                  //               decoration: TextDecoration.underline),
                  //         ),
                  //       )
                ],
              ),
              //endregion

              Expanded(
                child: ListView.builder(
                  // physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  // itemCount: buyerSearchBloc.allViewMoreProductItemCount,
                  itemCount: product.length,
                  itemBuilder: (BuildContext, index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        BuyerSearchCommonWidgets.searchedDataCard(
                          customImageContainerType: CustomImageContainerType.product,
                          isProduct: true,
                          placeHolder: AppImages.productPlaceHolder,
                          context: context,
                          heading: "${product[index].brandName!} ${product[index].productName!}",
                          imageUrl: product[index].prodImages!.isEmpty
                              ? null
                              : product[index].prodImages != null
                                  ? product[index].prodImages!.first.productImage
                                  : null,
                          title: product[index].storehandle!,
                          isCrossVisible: false,
                          onTapCard: () {
                            buyerSearchBloc.goToViewProductScreen(index);
                          },
                        ),
                      ],
                    );
                  },
                ),
              ),

              //verticalSizedBox(10),
            ],
          );
        });
  }

  //endregion

  //region People tab view
  Widget peopleTabView() {
    var user = buyerSearchBloc.searchResponse.user;

    return StreamBuilder<bool>(
        stream: buyerSearchBloc.onChangeOptionCtrl.stream,
        builder: (context, snapshot) {
          //No People
          if (user!.isEmpty) {
            return noResults();
          }
          // return Container(height: 100,width: 400,color: Colors.green,);

          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              //region People View All
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  //Product Text
                  BuyerSearchCommonWidgets.title(
                    data: AppStrings.people,
                  ),
                ],
              ),
              //endregion

              Expanded(
                child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  // itemCount: buyerSearchBloc.allViewMoreProductItemCount,
                  itemCount: user.length,
                  itemBuilder: (BuildContext, index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        BuyerSearchCommonWidgets.searchedDataCard(
                          customImageContainerType: CustomImageContainerType.user,
                          isUser: true,
                          placeHolder: AppImages.userPlaceHolder,
                          context: context,
                          heading: "${user[index].firstName!} ${user[index].lastName ?? ""}",
                          imageUrl: user[index].icon,
                          title: user[index].displayName!,
                          isCrossVisible: false,
                          onTapCard: () {
                            buyerSearchBloc.goToUserProfileScreen(userReference: user[index].userReference!);
                          },
                        ),
                      ],
                    );
                  },
                ),
              ),

              //verticalSizedBox(10),
            ],
          );
        });
  }

  //endregion

//region Filter
  Widget filter() {
    return Consumer<SearchResultCountDataModel>(builder: (BuildContext context, SearchResultCountDataModel value, Widget? child) {
      return Padding(
        padding: const EdgeInsets.only(left: 15, right: 15, bottom: 15),
        child: StreamBuilder<bool>(
            stream: buyerSearchBloc.onChangeOptionCtrl.stream,
            builder: (context, snapshot) {
              return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ///All
                    ///Todo All tab
                    // BuyerSearchCommonWidgets.options(
                    //     text: AppStrings.all,
                    //     onPress: () {
                    //       buyerSearchBloc.onOptionChange(index: 0);
                    //     },
                    //     isSelected: BuyerSearchBloc.searchScreenSelectedTab == 0),

                    ///Post
                    BuyerSearchCommonWidgets.options(
                      context: context,
                        text: AppStrings.posts,
                        count: value.searchResultCountResponse!.posts ?? 0,
                        onPress: () {
                          buyerSearchBloc.onOptionChange(index: 0);
                        },
                        isSelected: BuyerSearchBloc.searchScreenSelectedTab == 0),

                    ///Store
                    BuyerSearchCommonWidgets.options(
                        context: context,
                        text: AppStrings.stores,
                        count: value.searchResultCountResponse!.stores ??0,

                        onPress: () {
                          buyerSearchBloc.onOptionChange(index: 1);
                        },
                        isSelected: BuyerSearchBloc.searchScreenSelectedTab == 1),

                    ///Product
                    BuyerSearchCommonWidgets.options(
                        context: context,
                        text: AppStrings.products,
                        count: value.searchResultCountResponse!.products ?? 0,

                        onPress: () {
                          buyerSearchBloc.onOptionChange(index: 2);
                        },
                        isSelected: BuyerSearchBloc.searchScreenSelectedTab == 2),

                    ///People
                    BuyerSearchCommonWidgets.options(
                        context: context,
                        text: AppStrings.people,
                        count: value.searchResultCountResponse!.people ??0,

                        onPress: () {
                          buyerSearchBloc.onOptionChange(index: 3);
                        },
                        isSelected: BuyerSearchBloc.searchScreenSelectedTab == 3),

                    ///Un-comment
                    // InkWell(
                    //     onTap: () {
                    //       buyerSearchBloc.onTapFilter();
                    //     },
                    //     child: SvgPicture.asset(AppImages.filter))

                    // //All
                    // Expanded(
                    //   child: InkWell(
                    //     onTap: () {
                    //       buyerSearchBloc.onOptionChange("all");
                    //     },
                    //     child: Container(
                    //         margin: const EdgeInsets.only(right: 5),
                    //         padding: const EdgeInsets.symmetric(vertical: 5),
                    //         decoration: BoxDecoration(
                    //             color: AppConstants.buyerSearchFilter == AppStrings.all ? AppColors.brandGreen : AppColors.lightWhite3,
                    //             borderRadius: BorderRadius.circular(60)),
                    //         child: Center(
                    //           child: Container(
                    //             alignment: Alignment.center,
                    //             height: 20,
                    //             child: appText(
                    //               AppStrings.all,
                    //               color: AppConstants.buyerSearchFilter == AppStrings.all ? AppColors.white : AppColors.writingColor2,
                    //               fontWeight: FontWeight.w400,
                    //               fontSize: 14,
                    //               fontFamily: AppConstants.rRegular,
                    //             ),
                    //           ),
                    //         )),
                    //   ),
                    // ),
                    // //Store
                    // Expanded(
                    //   child: InkWell(
                    //     onTap: () {
                    //       buyerSearchBloc.onOptionChange("stores");
                    //     },
                    //     child: Container(
                    //         margin: const EdgeInsets.only(right: 5),
                    //         padding: const EdgeInsets.symmetric(vertical: 5),
                    //         decoration: BoxDecoration(
                    //             color: AppConstants.buyerSearchFilter == AppStrings.stores ? AppColors.brandGreen : AppColors.lightWhite3,
                    //             borderRadius: BorderRadius.circular(60)),
                    //         child: Center(
                    //           child: Container(
                    //             alignment: Alignment.center,
                    //             height: 20,
                    //             child: appText(
                    //               AppStrings.stores,
                    //               color: AppConstants.buyerSearchFilter == AppStrings.stores ? AppColors.white : AppColors.writingColor2,
                    //               fontWeight: FontWeight.w400,
                    //               fontSize: 14,
                    //               fontFamily: AppConstants.rRegular,
                    //             ),
                    //           ),
                    //         )),
                    //   ),
                    // ),
                    // //product
                    // Expanded(
                    //   child: InkWell(
                    //     onTap: () {
                    //       buyerSearchBloc.onOptionChange("products");
                    //     },
                    //     child: Container(
                    //         margin: const EdgeInsets.only(right: 5),
                    //         padding: const EdgeInsets.symmetric(vertical: 5),
                    //         decoration: BoxDecoration(
                    //             color: AppConstants.buyerSearchFilter == AppStrings.products ? AppColors.brandGreen : AppColors.lightWhite3,
                    //             borderRadius: BorderRadius.circular(60)),
                    //         child: Center(
                    //           child: Container(
                    //             alignment: Alignment.center,
                    //             height: 20,
                    //             child: appText(
                    //               AppStrings.products,
                    //               color: AppConstants.buyerSearchFilter == AppStrings.products ? AppColors.white : AppColors.writingColor2,
                    //               fontWeight: FontWeight.w400,
                    //               fontSize: 14,
                    //               fontFamily: AppConstants.rRegular,
                    //             ),
                    //           ),
                    //         )),
                    //   ),
                    // ),
                    // //People
                    // Expanded(
                    //   child: InkWell(
                    //     onTap: () {
                    //       buyerSearchBloc.onOptionChange("people");
                    //     },
                    //     child: Container(
                    //         margin: const EdgeInsets.only(right: 5),
                    //         padding: const EdgeInsets.symmetric(vertical: 5),
                    //         decoration: BoxDecoration(
                    //             color: AppConstants.buyerSearchFilter == AppStrings.people ? AppColors.brandGreen : AppColors.lightWhite3,
                    //             borderRadius: BorderRadius.circular(60)),
                    //         child: Center(
                    //           child: Container(
                    //             alignment: Alignment.center,
                    //             height: 20,
                    //             child: appText(
                    //               AppStrings.people,
                    //               color: AppConstants.buyerSearchFilter == AppStrings.people ? AppColors.white : AppColors.writingColor2,
                    //               fontWeight: FontWeight.w400,
                    //               fontSize: 14,
                    //               fontFamily: AppConstants.rRegular,
                    //             ),
                    //           ),
                    //         )),
                    //   ),
                    // ),
                    // ///Un un-comment
                    // // SvgPicture.asset(AppImages.filter)
                  ],
                ),
              );
            }),
      );
    },


    );
  }

//endregion

  ///All Data
  //region All

  //region Post and view All (All)
  Widget postViewAllAll() {
    return Consumer<PostDataModel>(
      builder: (context, data, child) {
        // List<PostDetail> postList = data.allPostDetailList
        //     .where((element) => buyerSearchBloc.searchResponse.post!.any((e) => e.postReference == element.postReference))
        //     .toList();

        var post = buyerSearchBloc.searchResponse.post;

        //print("Total post in search is ${post!.length}");

        return StreamBuilder<int>(
          stream: buyerSearchBloc.allViewMorePostCtrl.stream,
          initialData: buyerSearchBloc.allViewMorePostItemCount,
          builder: (context, snapshot) {
            //print(snapshot.data);
            return post!.isEmpty
                ? Container()
                : Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      //region Stores View All
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          //Stores Text
                          BuyerSearchCommonWidgets.title(
                            data: AppStrings.posts,
                          ),
                          //View All
                          Padding(
                            padding: const EdgeInsets.only(right: 16),
                            child: InkWell(
                              onTap: () {
                                buyerSearchBloc.onOptionChange(index: 1);
                              },
                              child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: AppStrings.viewAll, isUnderline: true),
                            ),
                          ),
                        ],
                      ),

                      // ListView.builder(
                      //   physics: const NeverScrollableScrollPhysics(),
                      //   shrinkWrap: true,
                      //   padding: const EdgeInsets.symmetric(horizontal: 0),
                      //   itemCount: snapshot.data,
                      //   itemBuilder: (BuildContext, index) {
                      //     return Column(
                      //       mainAxisSize: MainAxisSize.min,
                      //       children: [ListView.builder(
                      //         physics: const NeverScrollableScrollPhysics(),
                      //         shrinkWrap: true,
                      //         itemCount: postList.length <= 3 ? postList.length : 3,
                      //         itemBuilder: (context, index) {
                      //           return PostCard(
                      //             postDetail: postList[index],
                      //             onTapDelete: () {
                      //               buyerSearchBloc.confirmDelete(postDetail: postList[index]);
                      //             },
                      //             onTapDrawer: () {
                      //               buyerSearchBloc.onTapDrawer(postDetail: postList[index]);
                      //             },
                      //             onTapEdit: () {
                      //               buyerSearchBloc.goToEditPost(postDetail: postList[index]);
                      //             },
                      //             onTapHeart: () {
                      //               buyerSearchBloc.onTapHeart(postDetail: postList[index]);
                      //             },
                      //             onTapReport: () {},
                      //             onTapShare: () {
                      //               buyerSearchBloc.onTapShare(postDetail: postList[index]);
                      //             },
                      //             onTapProfileImage: () {
                      //               buyerSearchBloc.onTapUserOrStoreIcon(reference: postList[index].storeOrUserReference!);
                      //             },
                      //             onTapPost: () {
                      //               buyerSearchBloc.goToSinglePostView(postReference: postList[index].postReference!);
                      //             },
                      //           );
                      //         },
                      //       ),
                      //
                      //       ],
                      //     );
                      //   },
                      // ),
                      ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount:snapshot.data,
                        itemBuilder: (context, index) {
                          return PostCard(
                            postDetail: post[index],
                            onTapDelete: () {
                              buyerSearchBloc.confirmDelete(postDetail: post[index]);
                            },
                            onTapDrawer: () {
                              buyerSearchBloc.onTapDrawer(postDetail: post[index]);
                            },
                            onTapEdit: () {
                              buyerSearchBloc.goToEditPost(postDetail: post[index]);
                            },
                            onTapHeart: () {
                              buyerSearchBloc.onTapHeart(postDetail: post[index]);
                            },
                            onTapShare: () {
                              buyerSearchBloc.onTapShare(postDetail: post[index]);
                            },
                            onTapProfileImage: () {
                              buyerSearchBloc.onTapUserOrStoreIcon(reference: post[index].createdBy!.userOrStoreReference!);
                            },
                            onTapPost: () {
                              buyerSearchBloc.goToSinglePostView(postReference: post[index].postOrCommentReference!);
                            },
                          );
                        },
                      ),

                      snapshot.data == post.length
                          ? Container()
                          : InkWell(
                              onTap: () {
                                buyerSearchBloc.allViewMorePost(3);
                              },
                              child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: "View more post", isUnderline: true)),
                    ],
                  );
          },
        );
      },
    );
  }

//endregion

//region Stores and view All (All)
  Widget storeViewAllAll() {
    var store = buyerSearchBloc.searchResponse.store;

    return StreamBuilder<int>(
        stream: buyerSearchBloc.allViewMoreStoreCtrl.stream,
        initialData: buyerSearchBloc.allViewMoreStoreItemCount,
        builder: (context, snapshot) {
          return store!.isEmpty
              ? Container()
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    //region Stores View All
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        //Stores Text
                        BuyerSearchCommonWidgets.title(
                          data: AppStrings.stores,
                        ),
                        //View All
                        Padding(
                          padding: const EdgeInsets.only(right: 16),
                          child: InkWell(
                            onTap: () {
                              buyerSearchBloc.onOptionChange(index: 2);
                            },
                            child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: AppStrings.viewAll, isUnderline: true),
                          ),
                        ),

                        // InkWell(
                        //   onTap: () {
                        //     AppConstants.buyerSearchFilter = 'stores';
                        //     buyerSearchBloc.screenRefreshCtrl.sink.add(true);
                        //   },
                        //   child: Text(
                        //     AppStrings.viewAll,
                        //     style: const TextStyle(
                        //         color: AppColors.brandGreen,
                        //         fontSize: 14,
                        //         fontFamily: "LatoRegular",
                        //         fontWeight: FontWeight.w400,
                        //         decoration: TextDecoration.underline),
                        //   ),
                        // )
                      ],
                    ),
                    //endregion

                    ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: snapshot.data,

                      // itemCount: 1,
                      itemBuilder: (BuildContext, index) {
                        // return Text('$index');
                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            BuyerSearchCommonWidgets.searchedDataCard(
                              customImageContainerType: CustomImageContainerType.store,
                              placeHolder: AppImages.storePlaceHolder,
                              imageUrl: store[index].icon,
                              context: context,
                              isStore: true,
                              heading: store[index].storehandle!,
                              title: store[index].storeName!,
                              subTitle: "${store[index].categoryName} • ${store[index].storeDetails!.first.location}",
                              isCrossVisible: false,
                              onTapCard: () {
                                buyerSearchBloc.goToViewStoreScreen(store[index]);
                              },
                            ),
                            // verticalSizedBox(10),
                          ],
                        );
                      },
                    ),

                    //verticalSizedBox(10),

                    snapshot.data == store.length
                        ? Container()
                        : InkWell(
                            onTap: () {
                              buyerSearchBloc.allViewMoreStore(3);
                            },
                            child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: "View more stores", isUnderline: true))
                  ],
                );
        });
  }

//endregion

//region Products and view All (All)
  Widget productViewAllAll() {
    var product = buyerSearchBloc.filteredProductList;
    return product.isEmpty
        ? Container()
        : StreamBuilder<int>(
            stream: buyerSearchBloc.allViewMoreProductsCtrl.stream,
            initialData: buyerSearchBloc.allViewMoreProductItemCount,
            builder: (context, snapshot) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  //If above one is empty then return container or a 30 size
                  buyerSearchBloc.searchResponse.store!.isNotEmpty ? verticalSizedBox(30) : const SizedBox(),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      //Product Text
                      BuyerSearchCommonWidgets.title(
                        data: AppStrings.products,
                      ),
                      //View All
                      Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: InkWell(
                          onTap: () {
                            buyerSearchBloc.onOptionChange(index: 3);

                            // buyerSearchBloc.goToViewAllProducts();
                          },
                          child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: AppStrings.viewAll, isUnderline: true),
                        ),
                      ),

                      // Text(
                      //   AppStrings.products,
                      //   style: TextStyle(color: AppColors.appBlack, fontSize: 14, fontFamily: "LatoRegular", fontWeight: FontWeight.w400),
                      // ),
                      // InkWell(
                      //   onTap: () {
                      //     buyerSearchBloc.goToViewAllProducts();
                      //   },
                      //   child: Text(
                      //     AppStrings.viewAll,
                      //     style: TextStyle(
                      //         color: AppColors.brandGreen,
                      //         fontSize: 14,
                      //         fontFamily: "LatoRegular",
                      //         fontWeight: FontWeight.w400,
                      //         decoration: TextDecoration.underline),
                      //   ),
                      // )
                    ],
                  ),
                  //endregion
                  ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    // itemCount: buyerSearchBloc.allViewMoreProductItemCount,
                    itemCount: snapshot.data,
                    itemBuilder: (context, index) {
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          BuyerSearchCommonWidgets.searchedDataCard(
                            customImageContainerType: CustomImageContainerType.product,
                            isProduct: true,
                            placeHolder: AppImages.productPlaceHolder,
                            context: context,
                            heading: "${product[index].brandName!} ${product[index].productName!}",
                            imageUrl: product[index].prodImages!.isEmpty ? null : product[index].prodImages![0].productImage!,
                            title: product[index].storehandle!,
                            isCrossVisible: false,
                            onTapCard: () {
                              buyerSearchBloc.goToViewProductScreen(index);
                            },
                          ),
                        ],
                      );
                    },
                  ),

                  // verticalSizedBox(20),

                  product.length == snapshot.data
                      ? Container()
                      : InkWell(
                          onTap: () {
                            buyerSearchBloc.allViewMoreProduct(3);
                          },
                          child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: "View more products", isUnderline: true))

                  // : InkWell(
                  //     onTap: () {
                  //       buyerSearchBloc.allViewMoreProduct(3);
                  //     },
                  //     child: Padding(
                  //       padding: EdgeInsets.only(left: 30),
                  //       child: Text(
                  //         AppStrings.viewMoreProduct,
                  //         style: TextStyle(
                  //             color: AppColors.brandGreen,
                  //             fontSize: 14,
                  //             fontFamily: "LatoRegular",
                  //             fontWeight: FontWeight.w400,
                  //             decoration: TextDecoration.underline),
                  //       ),
                  //     ),
                  //   )
                ],
              );
            });
  }

//endregion

//region People and view All (All)
  Widget peopleViewAllAll() {
    var user = buyerSearchBloc.searchResponse.user;
    return user!.isEmpty
        ? Container()
        : StreamBuilder<int>(
            stream: buyerSearchBloc.allViewMorePeopleCtrl.stream,
            initialData: buyerSearchBloc.allViewMorePeopleItemCount,
            builder: (context, snapshot) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  //If above one is empty then return container or a 30 size
                  buyerSearchBloc.searchResponse.product!.isNotEmpty ? verticalSizedBox(30) : const SizedBox(),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      //Product Text
                      BuyerSearchCommonWidgets.title(
                        data: AppStrings.people,
                      ),
                      //View All
                      Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: InkWell(
                          onTap: () {
                            buyerSearchBloc.onOptionChange(index: 3);
                          },
                          child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: AppStrings.viewAll, isUnderline: true),
                        ),
                      ),
                    ],
                  ),
                  //endregion
                  ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    // itemCount: buyerSearchBloc.allViewMoreProductItemCount,
                    itemCount: snapshot.data,
                    itemBuilder: (context, index) {
                      // return Text(index.toString());

                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          InkWell(
                            onTap: () {
                              buyerSearchBloc.goToUserProfileScreen(userReference: user[index].userReference!);
                              //buyerSearchBloc.goToViewProductScreen(index);

                              //buyerSearchBloc.goToViewStoreScreen(buyerSearchBloc.storeListResponse.data![index]);
                            },
                            child: BuyerSearchCommonWidgets.searchedDataCard(
                              customImageContainerType: CustomImageContainerType.user,
                              isUser: true,
                              placeHolder: AppImages.userPlaceHolder,
                              context: context,
                              heading: "${user[index].firstName!} ${user[index].lastName ?? ""}",
                              imageUrl: user[index].icon,
                              title: user[index].displayName!,
                              isCrossVisible: false,
                              onTapCard: () {
                                buyerSearchBloc.goToUserProfileScreen(userReference: user[index].userReference!);
                              },
                            ),
                            // child: SizedBox(
                            //   height: 54,
                            //   child: Row(
                            //     mainAxisSize: MainAxisSize.max,
                            //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            //     crossAxisAlignment: CrossAxisAlignment.center,
                            //     children: [
                            //       ClipRRect(
                            //         borderRadius: BorderRadius.all(Radius.circular(12)),
                            //         child: SizedBox(
                            //             height: 54,
                            //             width: 54,
                            //             // child: user[index].icon!.isEmpty?
                            //             // SvgPicture.asset(AppImages.noStoreLogo,fit: BoxFit.cover,):
                            //             // extendedImage(user[index].icon!, context, 100, 100,cache: true,fit: BoxFit.cover)),
                            //
                            //             child: SvgPicture.asset(
                            //               AppImages.noStoreLogo,
                            //               fit: BoxFit.cover,
                            //             )),
                            //       ),
                            //       horizontalSizedBox(15),
                            //       Column(
                            //         mainAxisSize: MainAxisSize.max,
                            //         mainAxisAlignment: MainAxisAlignment.spaceAround,
                            //         crossAxisAlignment: CrossAxisAlignment.start,
                            //         children: [
                            //           Text(
                            //             "${user[index].userName}",
                            //             style:
                            //                 TextStyle(color: AppColors.appBlack, fontSize: 14, fontFamily: "LatoBold", fontWeight: FontWeight.w700),
                            //           ),
                            //           Text(
                            //             "${user[index].userHandle}",
                            //             style: TextStyle(
                            //                 color: AppColors.appBlack, fontSize: 12, fontFamily: "LatoRegular", fontWeight: FontWeight.w400),
                            //           ),
                            //           Text(
                            //             "${user[index].userLocation}",
                            //             style: TextStyle(
                            //                 color: AppColors.darkGray, fontSize: 12, fontFamily: "LatoBold", fontWeight: FontWeight.w700),
                            //           ),
                            //         ],
                            //       ),
                            //       Expanded(child: horizontalSizedBox(0)),
                            //     ],
                            //   ),
                            // ),
                          ),
                        ],
                      );
                    },
                  ),

                  // verticalSizedBox(20),

                  user.length == snapshot.data
                      ? Container()
                      : InkWell(
                          onTap: () {
                            buyerSearchBloc.allViewMorePeople(3);
                          },
                          child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: "View more people", isUnderline: true))
                  // : InkWell(
                  //     onTap: () {
                  //       buyerSearchBloc.allViewMorePeople(3);
                  //     },
                  //     child: Padding(
                  //       padding: EdgeInsets.only(left: 30),
                  //       child: Text(
                  //         AppStrings.viewMoreProduct,
                  //         style: TextStyle(
                  //             color: AppColors.brandGreen,
                  //             fontSize: 14,
                  //             fontFamily: "LatoRegular",
                  //             fontWeight: FontWeight.w400,
                  //             decoration: TextDecoration.underline),
                  //       ),
                  //     ),
                  //   )
                ],
              );
            });
  }

//endregion

//endregion

  ///Store, Product and People

//region Stores, Products, People
//   Widget storeProductPeople() {
//     if (buyerSearchBloc.tabController.index == 1) {
//       var store = buyerSearchBloc.searchResponse.store;
//
//       //If store is empty
//       if (store!.isEmpty) {
//         return noResults();
//       }
//       // return Container(height: 100,width: 400,color: Colors.green,);
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             crossAxisAlignment: CrossAxisAlignment.center,
//             mainAxisSize: MainAxisSize.max,
//             children: [
//               //Product Text
//               BuyerSearchCommonWidgets.title(
//                 data: AppStrings.stores,
//               ),
//             ],
//           ),
//           //region Stores View All
//           //endregion
//
//           Expanded(
//             child: ListView.builder(
//               shrinkWrap: true,
//               padding: const EdgeInsets.symmetric(horizontal: 16),
//               itemCount: store.length,
//               // itemCount: 1,
//               itemBuilder: (BuildContext, index) {
//                 return Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     InkWell(
//                       onTap: () {
//                         buyerSearchBloc.goToViewStoreScreen(store[index]);
//                       },
//                       child: BuyerSearchCommonWidgets.searchedDataCard(
//                         placeHolder: AppImages.storePlaceHolder,
//                         context: context,
//                         heading: store[index].storeName!,
//                         imageUrl: store[index].icon!,
//                         title: store[index].storehandle!,
//                         subTitle: store[index].categoryName,
//                         isCrossVisible: false,
//                         onTapCard: () {
//                           buyerSearchBloc.goToViewStoreScreen(store[index]);
//                         },
//                         customImageContainerType: CustomImageContainerType.store,
//                       ),
//
//                       // child: SizedBox(
//                       //   height: 54,
//                       //   child: Row(
//                       //     mainAxisSize: MainAxisSize.max,
//                       //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       //     crossAxisAlignment: CrossAxisAlignment.center,
//                       //     children: [
//                       //       ClipRRect(
//                       //         borderRadius: BorderRadius.all(Radius.circular(12)),
//                       //         child: SizedBox(
//                       //             height: 54,
//                       //             width: 54,
//                       //             child: store[index].icon != null
//                       //                 ? extendedImage(store[index].icon!, context, 100, 100, cache: true, fit: BoxFit.cover)
//                       //                 : SvgPicture.asset(AppImages.noStoreLogo)),
//                       //       ),
//                       //       horizontalSizedBox(15),
//                       //       Column(
//                       //         mainAxisSize: MainAxisSize.max,
//                       //         mainAxisAlignment: MainAxisAlignment.spaceAround,
//                       //         crossAxisAlignment: CrossAxisAlignment.start,
//                       //         children: [
//                       //           Text(
//                       //             "${store[index].storeName}",
//                       //             style: const TextStyle(color: AppColors.appBlack, fontSize: 14, fontFamily: "LatoBold", fontWeight: FontWeight.w700),
//                       //           ),
//                       //           Text(
//                       //             "${store[index].storehandle}",
//                       //             style: TextStyle(color: AppColors.appBlack, fontSize: 12, fontFamily: "LatoRegular", fontWeight: FontWeight.w400),
//                       //           ),
//                       //           Text(
//                       //             "${store[index].location}",
//                       //             style: TextStyle(color: AppColors.darkGray, fontSize: 12, fontFamily: "LatoBold", fontWeight: FontWeight.w700),
//                       //           ),
//                       //         ],
//                       //       ),
//                       //       Expanded(child: horizontalSizedBox(0)),
//                       //     ],
//                       //   ),
//                       // ),
//                     ),
//                   ],
//                 );
//               },
//             ),
//           ),
//
//           //verticalSizedBox(10),
//         ],
//       );
//     }
//     if (buyerSearchBloc.tabController.index == 2) {
//       var product = buyerSearchBloc.filteredProductList;
//
//       //No product
//       if (product.isEmpty) {
//         return noResults();
//       }
//       // return Container(height: 100,width: 400,color: Colors.green,);
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           //region Stores View All
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             crossAxisAlignment: CrossAxisAlignment.center,
//             mainAxisSize: MainAxisSize.max,
//             children: [
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 mainAxisSize: MainAxisSize.max,
//                 children: [
//                   //Product Text
//                   BuyerSearchCommonWidgets.title(
//                     data: AppStrings.products,
//                   ),
//                 ],
//               ),
//               Padding(
//                 padding: const EdgeInsets.only(right: 16),
//                 child: InkWell(
//                   onTap: () {
//                     buyerSearchBloc.goToViewAllProducts();
//
//                     // AppConstants.buyerSearchFilter = 'People';
//                     // buyerSearchBloc.screenRefreshCtrl.sink.add(true);
//                   },
//                   child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: AppStrings.viewAll, isUnderline: true),
//                 ),
//               ),
//               // product!.isEmpty
//               //     ? Container()
//               //     : InkWell(
//               //         onTap: () {
//               //           buyerSearchBloc.goToViewAllProducts();
//               //         },
//               //         child: Text(
//               //           AppStrings.viewAll,
//               //           style: TextStyle(
//               //               color: AppColors.brandGreen,
//               //               fontSize: 14,
//               //               fontFamily: "LatoRegular",
//               //               fontWeight: FontWeight.w400,
//               //               decoration: TextDecoration.underline),
//               //         ),
//               //       )
//             ],
//           ),
//           //endregion
//
//           Expanded(
//             child: ListView.builder(
//               // physics: const NeverScrollableScrollPhysics(),
//               shrinkWrap: true,
//               padding: const EdgeInsets.symmetric(horizontal: 16),
//               // itemCount: buyerSearchBloc.allViewMoreProductItemCount,
//               itemCount: product.length,
//               itemBuilder: (BuildContext, index) {
//                 return Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     BuyerSearchCommonWidgets.searchedDataCard(
//                       customImageContainerType: CustomImageContainerType.product,
//                       placeHolder: AppImages.productPlaceHolder,
//                       context: context,
//                       heading: "${product[index].brandName!} ${product[index].productName!}",
//                       imageUrl: product[index].prodImages![0].productImage!,
//                       title: product[index].storehandle!,
//                       isCrossVisible: false,
//                       onTapCard: () {
//                         buyerSearchBloc.goToViewProductScreen(index);
//                       },
//                     ),
//                   ],
//                 );
//               },
//             ),
//           ),
//
//           //verticalSizedBox(10),
//         ],
//       );
//     }
//     if (buyerSearchBloc.tabController.index == 3) {
//       var user = buyerSearchBloc.searchResponse.user;
//
//       //No People
//       if (user!.isEmpty) {
//         return noResults();
//       }
//       // return Container(height: 100,width: 400,color: Colors.green,);
//
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           //region People View All
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             crossAxisAlignment: CrossAxisAlignment.center,
//             mainAxisSize: MainAxisSize.max,
//             children: [
//               //Product Text
//               BuyerSearchCommonWidgets.title(
//                 data: AppStrings.people,
//               ),
//             ],
//           ),
//           //endregion
//
//           Expanded(
//             child: ListView.builder(
//               physics: const NeverScrollableScrollPhysics(),
//               shrinkWrap: true,
//               padding: const EdgeInsets.symmetric(horizontal: 16),
//               // itemCount: buyerSearchBloc.allViewMoreProductItemCount,
//               itemCount: user.length,
//               itemBuilder: (BuildContext, index) {
//                 return Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     BuyerSearchCommonWidgets.searchedDataCard(
//                       customImageContainerType: CustomImageContainerType.user,
//                       placeHolder: AppImages.userPlaceHolder,
//                       context: context,
//                       heading: "${user[index].firstName!} ${user[index].lastName ?? ""}",
//                       imageUrl: user[index].icon,
//                       title: user[index].userName!,
//                       isCrossVisible: false,
//                       onTapCard: () {
//                         buyerSearchBloc.goToUserProfileScreen(userReference: user[index].userReference!);
//                       },
//                     ),
//                   ],
//                 );
//               },
//             ),
//           ),
//
//           //verticalSizedBox(10),
//         ],
//       );
//     }
//     return Container();
//   }

//endregion

//region No results
  Widget noResults() {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(AppImages.noSearchResultIcon),
        verticalSizedBox(7.12),
        Text(
          AppStrings.noMatchingSearchResult,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        ),
      ],
    ));
  }

//endregion
//
// //region Error
//   Widget error() {
//     return Center(
//         child: Column(
//       mainAxisSize: MainAxisSize.min,
//       mainAxisAlignment: MainAxisAlignment.center,
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         SvgPicture.asset(AppImages.exclamation),
//         verticalSizedBox(7.12),
//         Text(
//           AppStrings.noMatchingSearchResult,
//           style: AppTextStyle.heading2Bold(textColor: AppColors.writingColor2),
//         ),
//         verticalSizedBox(4.5),
//         Row(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Text(
//               AppStrings.noWorry,
//               style: AppTextStyle.heading4Regular(textColor: AppColors.writingColor3),
//             ),
//             horizontalSizedBox(3),
//             SvgPicture.asset(AppImages.smileEmoji)
//           ],
//         ),
//       ],
//     ));
//   }
// //endregion
}
