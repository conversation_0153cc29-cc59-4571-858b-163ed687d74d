import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/shopping_cart/available_to/available_to_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/selected_delivery_pincode/selected_delivery_pin_code_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Available to
class SelectedDeliveryPinCode extends StatefulWidget {
  final ShoppingCartBloc shoppingCartBloc;
  final SecureCheckoutBloc secureCheckoutBloc;
  const SelectedDeliveryPinCode({Key? key, required this.shoppingCartBloc, required this.secureCheckoutBloc}) : super(key: key);

  @override
  State<SelectedDeliveryPinCode> createState() => _SelectedDeliveryPinCodeState();
}
//endregion

class _SelectedDeliveryPinCodeState extends State<SelectedDeliveryPinCode> {
  //region Bloc
  late SelectedDeliveryPinCodeBloc selectedDeliveryPinCodeBloc;
  //endregion
  //region Init
  @override
  void initState() {
    selectedDeliveryPinCodeBloc = SelectedDeliveryPinCodeBloc(context,widget.shoppingCartBloc,widget.secureCheckoutBloc);
    selectedDeliveryPinCodeBloc.init();
    super.initState();
  }
  //endregion

  //region Did update widget
  @override
  void didUpdateWidget(covariant SelectedDeliveryPinCode oldWidget) {
    selectedDeliveryPinCodeBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion


  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: selectedDeliveryPinCodeBloc.availableToCtrl.stream,
        builder: (context, snapshot) {
          return body();
        }
    );
  }

  //region Body
  Widget body(){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: availableTo(),
    );
  }
  //endregion


  //region Available To Pin Code
  Widget availableTo() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 25),
          child: SizedBox(
            height: 32,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(AppStrings.selectedDeliveryPinCode,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
                horizontalSizedBox(20),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  width: 100,
                  decoration:
                  BoxDecoration(
                      color: AppColors.textFieldFill1,
                      borderRadius: const BorderRadius.all(Radius.circular(20)), border: Border.all(color: AppColors.textFieldFill1, width: 1)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: TextFormField(
                          enabled: false,
                          controller: selectedDeliveryPinCodeBloc.deliveryPinCode,
                          onChanged: (value){
                          },
                          keyboardType: TextInputType.number,
                          style:AppTextStyle.contentText0(textColor: AppColors.appBlack),
                          decoration: const InputDecoration(

                            contentPadding: EdgeInsets.zero,
                            isDense: true,
                            border: InputBorder.none,
                            // hintText: "110068",
                            // hintStyle: TextStyle(
                            //     fontFamily: "LatoRegular",
                            //     fontWeight: FontWeight.w400,
                            //     fontSize: 14,
                            //     color: AppColors.appBlack
                            //
                            // )
                          ),
                          inputFormatters: <TextInputFormatter>[
                            FilteringTextInputFormatter.allow(RegExp(AppConstants.onlyInt)),
                            LengthLimitingTextInputFormatter(6),


                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),


        //
        ///No address is saved
        Visibility(
            visible: selectedDeliveryPinCodeBloc.addNewAddress,
            child: noAddressIsSaved()),
        // ///No address is selected
        // Visibility(
        //     visible: selectedDeliveryPinCodeBloc.selectAddress,
        //     child: noAddressIsSelected())
      ],
    );
  }
//endregion


  //region Invalid pin code

  Widget invalidPinCode(){
    return Text(AppStrings.invalidPinCode,
      style: AppTextStyle.smallText(textColor: AppColors.red),
    );
  }
  //endregion


//region No address is saved
  Widget noAddressIsSaved(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(child:Text(AppStrings.noAddressIsSaved,
              style: AppTextStyle.smallText(textColor: AppColors.red),
            )
            ),
          ],
        ),
        verticalSizedBox(14),
        CupertinoButton(
          onPressed: (){},
          padding: EdgeInsets.zero,

          child: Container(
            decoration: BoxDecoration(color:Colors.transparent, borderRadius: BorderRadius.circular(100),

                border: Border.all(color: AppColors.appBlack,width: 1.2)

            ),
            padding: const EdgeInsets.symmetric(horizontal: 30,vertical: 11),
            child: InkWell(
              //padding: EdgeInsets.zero,
              onTap: () {
                selectedDeliveryPinCodeBloc.secureCheckoutBloc.goToCartAddress();
              },
              child: Text(
                  AppStrings.addNewAddress,
                  textAlign: TextAlign.center,
                  style:AppTextStyle.access0(textColor: AppColors.appBlack)
              ),
            ),
          ),
        ),
      ],
    );
  }
//endregion


//region No address is selected
  Widget noAddressIsSelected(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(child:Text(AppStrings.noAddressIsSelected,
              style: AppTextStyle.smallText(textColor: AppColors.red),
            )
            ),
          ],
        ),

        verticalSizedBox(14),
        CupertinoButton(
          onPressed: (){},
          padding: EdgeInsets.zero,

          child: Container(
            decoration: BoxDecoration(color:Colors.transparent, borderRadius: BorderRadius.circular(100),

                border: Border.all(color: AppColors.appBlack,width: 1.2)

            ),
            padding: const EdgeInsets.symmetric(horizontal: 30,vertical: 11),
            child: InkWell(
              //padding: EdgeInsets.zero,
              onTap: () {
                selectedDeliveryPinCodeBloc.secureCheckoutBloc.goToCartAddress();

                // availableToBloc.onTapAddAddress();
              },
              child: Text(
                  AppStrings.selectOrAdd,
                  textAlign: TextAlign.center,
                  style:AppTextStyle.access0(textColor: AppColors.appBlack)
              ),
            ),
          ),
        ),
      ],
    );
  }
//endregion

}
