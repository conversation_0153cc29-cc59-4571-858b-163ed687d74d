import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/order_history/order_history_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_order_details/seller_all_order_details_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


enum BuyerMyOrderState { Loading, Success, Failed, Empty }

class BuyerMyOrdersBloc {
  // region Common Variables
  BuildContext context;
  int indexToScroll = 0;
  late BuyerMyOrderServices buyerMyOrderServices;
  late GetOrderResponse buyerMyOrderResponse;
  late List<Order> searchedStore = [];
  List<String> selectedSubOrderNumberList = [];
  //Affected order
  final String? orderNumberFromNotification;
  // endregion

  // endregion


  //region Controller
  final dropDownCtrl = StreamController<bool>.broadcast();
  final buyerMyOrderState = StreamController<BuyerMyOrderState>.broadcast();
  // final expandableController  = ExpandableController(initialExpanded: false);
  final subOrderRefresh  =  StreamController<bool>.broadcast();
  final scrollCtrl = ScrollController();
  //endregion

  //region Text Controller
  final searchTextCtrl = TextEditingController();
  final deliveryPersonNameCtrl = TextEditingController();
  final deliveryPersonContactCtrl = TextEditingController();
  final trackingLinkCtrl = TextEditingController();
  final dateCtrl = TextEditingController();
  final cancelReasonTextCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  BuyerMyOrdersBloc(this.context, this.orderNumberFromNotification);
  // endregion

  // region Init
   init() {
    buyerMyOrderServices  = BuyerMyOrderServices();
    getBuyerMyOrders();
  }
// endregion

  //region Active and inactive drop down
  void dropDownChange(Order store){
    store.dropdownStatus = !store.dropdownStatus!;
    //searchedStore[userIndex].dropdownStatus = !searchedStore[userIndex].dropdownStatus!;
    //print(searchedStore[userIndex].dropdownStatus );
    dropDownCtrl.sink.add(true);
    buyerMyOrderState.sink.add(BuyerMyOrderState.Success);
  }
  //endregion





  //region On tap sub-order after multi-select active
  void onTapProduct(String subOrderNumber){
    //If selected sub order list is empty then return
    if(selectedSubOrderNumberList.isEmpty){
      return;
    }
    //If sub-sub order list contains the selected one then
    if(selectedSubOrderNumberList.contains(subOrderNumber)){
      selectedSubOrderNumberList.removeWhere((element) => element==subOrderNumber);
    }
    //Add the sub-order number to the sub-order list
    else{
      selectedSubOrderNumberList.add(subOrderNumber);
    }
    //Refresh Sub order UI
    subOrderRefresh.sink.add(true);

  }
  //endregion




  //region Get Buyer My Orders
  Future<void> getBuyerMyOrders() async {
    //region Try
    try {
      buyerMyOrderResponse = await buyerMyOrderServices.getBuyerMyOrder();
      // //print(buyerMyOrderResponse.storeList!.first.subOrderList!.first.storeContactInfo!.phoneNumber!.first);
      // await Future.delayed (Duration (seconds: 5));
      searchedStore.clear();
      searchedStore.addAll(buyerMyOrderResponse.orderList!);
      if(buyerMyOrderResponse.orderList!.isEmpty){
        buyerMyOrderState.sink.add(BuyerMyOrderState.Empty);
        return;
      }
      buyerMyOrderState.sink.add(BuyerMyOrderState.Success);
      ///Find the index where order number from notification
      getIndexFromOrderNumber();
    }
    //endregion
    on ApiErrorResponseMessage catch(error) {
      buyerMyOrderState.sink.add(BuyerMyOrderState.Failed);
      CommonMethods.toastMessage(error.message!, context);
    } catch (error) {
      buyerMyOrderState.sink.add(BuyerMyOrderState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
    }
  }
//endregion


  //region Get store info

  //endregion



  //region On change search text
  void onChangeSearchText(){
     //Clear all results
    searchedStore.clear();

    // for (var data in buyerMyOrderResponse.orderList!) {
    //   if (data.toString().toLowerCase().contains(searchTextCtrl.text.toLowerCase())) {
    //     searchedStore.add(data);
    //   }
    // }

    //If text field is end
    if(searchTextCtrl.text.isEmpty){
      searchedStore.addAll(buyerMyOrderResponse.orderList!);
      buyerMyOrderState.sink.add(BuyerMyOrderState.Success);
      return;

    }


    //Check order number
    for (var data in buyerMyOrderResponse.orderList!) {
      ///Order number filter
      if (data.orderNumber!.toLowerCase().contains(searchTextCtrl.text.toLowerCase())) {
        searchedStore.add(data);
      }

      ///Store name
      if (data.storeName!.toLowerCase().contains(searchTextCtrl.text.toLowerCase())) {
        searchedStore.add(data);
      }

      //Check Sub-order and product name
      ///Sub-order number
      for(var subOrder in data.subOrderList!){
        if (subOrder.suborderNumber!.toLowerCase().contains(searchTextCtrl.text.toLowerCase())) {
          searchedStore.add(data);
        }
      }
    }
    //Remove duplicate values
    searchedStore = searchedStore.toSet().toList();

    ///If no order found
    if(searchedStore.isEmpty){
      buyerMyOrderState.sink.add(BuyerMyOrderState.Empty);
      return;
    }
    //print(searchedStore.length);
    buyerMyOrderState.sink.add(BuyerMyOrderState.Success);

  }
  //endregion





  //region Index where order number from notification
  void getIndexFromOrderNumber(){
  for(int i = 0; i < buyerMyOrderResponse.orderList!.length; i++){
  if(buyerMyOrderResponse.orderList![i].orderNumber == orderNumberFromNotification){
    //Make drop down to true
    buyerMyOrderResponse.orderList![i].dropdownStatus = true;
    //Add index to scroll down
    indexToScroll = i;
    //print(i);
    buyerMyOrderState.sink.add(BuyerMyOrderState.Success);
    return;
  }
  }
}
//endregion






//region On tap order history
  goToOrderHistory({ required List<SubOrder> subOrderList}){
    var screen =  OrderHistoryScreen(subOrderList:subOrderList,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

//region Go to store
  goToStore({required String storeReference}){
    var screen =  BuyerViewStoreScreen(storeReference: storeReference,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region if from bottom navigation switch to home
  goToFirstBottomNavigation() {
    AppConstants.userPersistentTabController.jumpToTab(0);
    AppConstants.userPersistentTabController.jumpToTab(0);
    AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  }
//endregion



//region View Order details Bottom sheet
  customerAndOrderDetailsBottomSheet({required String orderNumber}){
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topRight: Radius.circular(20),topLeft: Radius.circular(20)),
        ),
        builder: (context) {
          return FractionallySizedBox(
            heightFactor: 0.8,
            child:Column(
              children:  [
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 20),
                  child: Text(
                    "Order details",
                    style: TextStyle(
                        fontFamily: "LatoSemibold",
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: AppColors.writingBlack),
                  ),
                ),
                Expanded(child:
                    SellerAllOrderDetailsScreen(orderNumber: orderNumber,)),
              ],
            ),
          );
        });



  }
//endregion


  //region On Tap Ask seller
  void onTapAskSeller({required String storeReference}) {
    var screen = BuyerViewStoreScreen(storeReference: storeReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
   }
//endregion


//region On tap order card
  void onTapOrderCard({required Order order}){
    var screen =  BuyerSubOrderScreen(orderNumber: order.orderNumber!,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // init();
    });
  }
//endregion

}
