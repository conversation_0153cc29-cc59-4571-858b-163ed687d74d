import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/buyer_and_seller_order_card/buyer_and_seller_order_card_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class BuyerAndSellerOrderCard extends StatefulWidget {
  final Order order;
  final bool isArrowVisible;
  final bool isSeller;
  final BuildContext buyerSellerOrderScreenContext;
  final String storeAndProfilePlaceHolder;
  final bool? isStoreVerified;
  final String orderSubtitleText;

  const BuyerAndSellerOrderCard(
      {Key? key,
      required this.order,
      this.isArrowVisible = true,
      this.isSeller = true,
      required this.buyerSellerOrderScreenContext,
      required this.storeAndProfilePlaceHolder,
      this.isStoreVerified,
      this.orderSubtitleText = ""})
      : super(key: key);

  @override
  State<BuyerAndSellerOrderCard> createState() =>
      _BuyerAndSellerOrderCardState();
}

class _BuyerAndSellerOrderCardState extends State<BuyerAndSellerOrderCard> {
  //region Bloc
  late BuyerAndSellerOrderCardBloc buyerAndSellerOrderCardBloc;

  //endregion
  //region Init
  @override
  void initState() {
    buyerAndSellerOrderCardBloc =
        BuyerAndSellerOrderCardBloc(context, widget.order, widget.isSeller);
    buyerAndSellerOrderCardBloc.init();
    super.initState();
  }

  //endregion

  //region Dis update
  @override
  void didUpdateWidget(covariant BuyerAndSellerOrderCard oldWidget) {
    buyerAndSellerOrderCardBloc =
        BuyerAndSellerOrderCardBloc(context, widget.order, widget.isSeller);
    buyerAndSellerOrderCardBloc.init();

    super.didUpdateWidget(oldWidget);
  }

  //endregion
  //region Did change
  @override
  void didChangeDependencies() {
    buyerAndSellerOrderCardBloc =
        BuyerAndSellerOrderCardBloc(context, widget.order, widget.isSeller);
    buyerAndSellerOrderCardBloc.init();
    super.didChangeDependencies();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    buyerAndSellerOrderCardBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Container(
        alignment: Alignment.centerLeft,
        // margin: const EdgeInsets.only(bottom: 10),
        padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
        decoration: BoxDecoration(
            border: Border(
                bottom:
                    BorderSide(color: AppColors.appBlack.withOpacity(0.1)))),
        child: body());
  }

  //region Body
  Widget body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Icon order number
        iconOrderAndPrice(),
        //Total item
        totalItemsAndSubOrder(),
        //Customer detail and order detail
        customerAndOrderDetail(),
        //Order detail and need help
        orderAndNeedHelp(),
      ],
    );
  }

//endregion

//region Icon,Order number and price
  Widget iconOrderAndPrice() {
    //Store or user image
    var storeUserImage = widget.isSeller
        ? widget.order.userIcon!
        : "${widget.order.storeIcon ?? widget.order.storeDetails!.storeImage}";
    //Store and user name
    var storeAndUserName = widget.isSeller
        ? widget.order.customerName!
        : " ${widget.order.storeName ?? widget.order.storeDetails!.storeName!}";
    return Container(
      padding: const EdgeInsets.only(bottom: 10),
      decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(color: AppColors.textFieldFill1))),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          //Icon
          InkWell(
            onTap: () {
              buyerAndSellerOrderCardBloc.onTapImage();
            },
            child: Container(
                margin: const EdgeInsets.symmetric(vertical: 2.5),
                child: CustomImageContainer(
                  height: 50,
                  width: 50,
                  imageUrl: storeUserImage,
                  imageType: !widget.isSeller
                      ? CustomImageContainerType.store
                      : CustomImageContainerType.user,
                )),
          ),

          const SizedBox(width: 10),

          //User name,store and order number
          Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                // height: 20,
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      storeAndUserName,
                      style: AppTextStyle.access1(
                        textColor: AppColors.appBlack,
                      ).copyWith(height: 0),
                    ),
                    VerifiedBadge(
                      width: 15,
                      height: 15,
                      subscriptionType: widget.order.subscriptionType,
                    ),
                  ],
                ),
              ),
              verticalSizedBox(5),
              //Order number
              InkWell(
                onTap: () {
                  CommonMethods.copyText(context, widget.order.orderNumber!);
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 1),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      color: AppColors.textFieldFill1,
                      borderRadius: BorderRadius.circular(60)),
                  child: Container(
                      // height: 20,
                      alignment: Alignment.center,
                      child: Text(
                        widget.order.orderNumber!,
                        style: AppTextStyle.smallText(
                          textColor: AppColors.writingBlack1,
                        ),
                      )),
                ),
              ),
            ],
          ),

          const Expanded(child: SizedBox(width: 10)),

          //Price and date time
          Container(
            margin: const EdgeInsets.symmetric(vertical: 3),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                //Price
                Container(
                  // height: 20,
                  alignment: Alignment.center,
                  child: Text(
                    "₹${buyerAndSellerOrderCardBloc.order.totalAmount}",
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.access1(textColor: AppColors.appBlack),
                  ),
                ),
                verticalSizedBox(5),
                //Date time
                Container(
                  // height: 20,
                  alignment: Alignment.center,
                  child: Text(
                    "${CommonMethods.dateTimeAmPm(date: widget.order.orderedDate!)[1]} ${CommonMethods.dateTimeAmPm(date: widget.order.orderedDate!)[2]}",
                    style: AppTextStyle.smallText(
                        textColor: AppColors.writingBlack1).copyWith(fontSize: 10),
                  ),
                )
              ],
            ),
          ),

          //Arrow
          Visibility(
              visible: widget.isArrowVisible,
              child: const Padding(
                  padding: EdgeInsets.only(left: 10),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: AppColors.brandBlack,
                    size: 18,
                  )))
        ],
      ),
    );
  }

//endregion

//region Total items and sub orders
  Widget totalItemsAndSubOrder() {
    //Waiting
    return Visibility(
      visible: true,
      child: StreamBuilder<bool>(
          stream: buyerAndSellerOrderCardBloc.cardRefreshCtrl.stream,
          initialData: true,
          builder: (context, snapshot) {
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              height: CommonMethods.textHeight(
                context: context,
                textStyle:
                    AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
              alignment: Alignment.centerLeft,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    CommonMethods.singularPluralText(
                      item: buyerAndSellerOrderCardBloc.order.subOrderList!
                          .map((e) => e.productQuantity!)
                          .reduce((value, element) => value + element),
                      singular: "item",
                      plural: "items",
                    ),
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack),
                  ),
                  horizontalSizedBox(15),
                  // Text("Suborders:",style:AppTextStyle.normalTextSemiBold(textColor: AppColors.writingBlack),),
                  // horizontalSizedBox(10),

                  ///Seller
                  Visibility(
                    visible: widget.isSeller,
                    child: Expanded(child: sellerOrdersText(widget.order.orderSubtitleText??"")),
                  ),

                  ///Buyer
                  Visibility(
                    visible: !widget.isSeller,
                    child: Expanded(child: buyerOrdersText(widget.order.orderSubtitleText??"")),
                  ),

                  ///Paid (Seller view)
                  Visibility(
                    visible: widget.isSeller,
                    child: AppToolTip(
                      message: "The buyer has paid for this order.",
                      toolTipWidget: Text(
                        "Prepaid",
                        style: AppTextStyle.access0(
                            textColor: AppColors.brandBlack),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
    );
  }

//endregion

  ///Seller
//region Customer and order detail
  Widget customerAndOrderDetail() {
    return Visibility(
      visible: widget.isSeller && !widget.isArrowVisible,
      child: Container(
        margin: const EdgeInsets.only(left: 0, right: 0, bottom: 10),
        child: Row(
          children: [
            Expanded(
                child: InkWell(
                    onTap: () {
                      buyerAndSellerOrderCardBloc.onTapCustomer();
                    },
                    child: button(buttonName: AppStrings.customerDetail))),
            horizontalSizedBox(10),
            Expanded(
                child: InkWell(
                    onTap: () {
                      buyerAndSellerOrderCardBloc.onTapOrderDetail();
                    },
                    child: button(buttonName: AppStrings.orderDetail))),
          ],
        ),
      ),
    );
  }

//endregion

  //region Seller orders text
  Widget sellerOrdersText(String orderSubtitleText) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        //print(constraints.maxWidth);
        var text = buyerAndSellerOrderCardBloc.sellerOrderText;
        if (orderSubtitleText.isNotEmpty) {
          text = orderSubtitleText;
        }
        if (constraints.maxWidth >=
            buyerAndSellerOrderCardBloc
                .textWidth(text)) {
          // Text fits within available space, no need to scroll
          return Text(
            text,
            maxLines: 1,
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          );
        } else {
          //Text scroll
          return AppCommonWidgets.scrollableText(
            text: text,
            textStyle: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          );
        }
      },
    );
  }

  //endregion

  ///Buyer
//region Order and need help
  Widget orderAndNeedHelp() {
    return Visibility(
      visible: !widget.isSeller && !widget.isArrowVisible,
      child: Container(
        margin: const EdgeInsets.only(left: 0, right: 0, bottom: 10),
        child: Row(
          children: [
            Expanded(
                child: InkWell(
                    onTap: () {
                      buyerAndSellerOrderCardBloc.onTapOrderDetail();
                    },
                    child: button(buttonName: AppStrings.orderDetail))),
            horizontalSizedBox(10),
            Expanded(
                child: InkWell(
                    onTap: () {
                      CommonMethods.reportAndSuggestion(context: context);
                    },
                    child: button(buttonName: AppStrings.needHelp))),
          ],
        ),
      ),
    );
  }

//endregion
  //region Buyer orders text
  Widget buyerOrdersText(String orderSubtitleText) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        var text = buyerAndSellerOrderCardBloc.buyerOrderText;
        if (orderSubtitleText.isNotEmpty) {
          text = orderSubtitleText;
        }
        if (constraints.maxWidth >=
            buyerAndSellerOrderCardBloc.textWidth(text)) {
          // Text fits within available space, no need to scroll
          return Text(
            text,
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          );
        } else {
          //Text scroll
          return AppCommonWidgets.scrollableText(
            text: text,
            textStyle: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          );
        }
      },
    );
  }

  //endregion

//region Buttons
  Widget button({required String buttonName}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
          borderRadius: BorderRadius.circular(100)),
      child: Container(
          // height: 20,
          alignment: Alignment.center,
          child: Text(
            buttonName,
            style: AppTextStyle.access1(textColor: AppColors.appBlack),
          )),
    );
  }
//endregion
}
