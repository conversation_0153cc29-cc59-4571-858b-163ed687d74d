/// Model class for order previews
class OrderPreview {
  final String orderNumber;
  final String suborderNumber;
  final String userReference;
  final String? userIcon;
  final String userName;
  final String storeReference;
  final String? storeIcon;
  final String storeName;
  final String? productPreviewImage;

  OrderPreview({
    required this.orderNumber,
    required this.suborderNumber,
    required this.userReference,
    this.userIcon,
    required this.userName,
    required this.storeReference,
    this.storeIcon,
    required this.storeName,
    this.productPreviewImage,
  });

  factory OrderPreview.fromJson(Map<String, dynamic> json) {
    return OrderPreview(
      orderNumber: json['orderNumber'] ?? '',
      suborderNumber: json['suborderNumber'] ?? '',
      userReference: json['userReference'] ?? '',
      userIcon: json['userIcon'],
      userName: json['userName'] ?? '',
      storeReference: json['storeReference'] ?? '',
      storeIcon: json['storeIcon'],
      storeName: json['storeName'] ?? '',
      productPreviewImage: json['productPreviewImage'],
    );
  }
}
