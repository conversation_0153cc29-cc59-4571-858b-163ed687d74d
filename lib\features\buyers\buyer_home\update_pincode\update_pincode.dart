import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class UpdatePinCode extends StatefulWidget {
  final BuyerHomeBloc buyerHomeBloc;
  const UpdatePinCode({Key? key, required this.buyerHomeBloc}) : super(key: key);

  @override
  State<UpdatePinCode> createState() => _UpdatePinCodeState();
}

class _UpdatePinCodeState extends State<UpdatePinCode> {
  @override
  Widget build(BuildContext context) {
    return body();
  }


  //region Body
  Widget body(){
    return  Container(
      margin: const EdgeInsets.symmetric(horizontal: 32,vertical: 34),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(AppImages.locationIcon,height: 48,width: 48,),
          Container(
              margin: const EdgeInsets.only(top: 23,bottom: 20),
              child: Text(AppStrings.deliveryPinCode,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),)),

          Padding(

            padding: const EdgeInsets.symmetric(horizontal: 80),
            child: AppTextFields.onlyNumberTextField(

              context: context,
              textAlign: TextAlign.center,
              maxEntry: 6,

              textEditingController: widget.buyerHomeBloc.postalCodeTextCtrl,
              hintText:  AppStrings.pinCode,
            ),
          ),
          verticalSizedBox(20),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Visibility(
                visible: !CommonMethods.isWeb(),
                child: Container(
                  margin: const EdgeInsets.only(right: 18),
                  child: StreamBuilder<PostalCodeState>(
                      stream: widget.buyerHomeBloc.postalCtrl.stream,

                      initialData: PostalCodeState.Success,
                      builder: (context, snapshot) {
                        //print(snapshot.data);
                        if(snapshot.data == PostalCodeState.Success){
                          return InkWell(
                            onTap: () {
                              widget.buyerHomeBloc.determinePosition();
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                              decoration: const BoxDecoration(
                                borderRadius: BorderRadius.all(Radius.circular(80)),
                                color: AppColors.textFieldFill1,
                              ),
                              child: Center(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(AppImages.location2,height: 20,width: 20,),
                                    horizontalSizedBox(10),
                                    Text(AppStrings.useGps,
                                      style: AppTextStyle.access0(textColor: AppColors.appBlack),
                                    )
                                    // appText(AppStrings.detectMyLocation,color: AppColors.appBlack,
                                    //   fontWeight:FontWeight.w700,
                                    //   fontSize: 14,
                                    //   fontFamily: AppConstants.rRegular,
                                    //
                                    //
                                    // )
                                  ],
                                ),
                              ),
                            ),
                          );
                        }

                        return const CircularProgressIndicator();


                      }
                  ),
                ),
              ),

              InkWell(
                onTap: (){
                  widget.buyerHomeBloc.sendPinCode();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 30, vertical:10),
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(80)),
                    color: AppColors.appBlack,
                  ),
                  child: Center(
                    child: Row(
                      children: [
                        Text(AppStrings.save,
                          style: AppTextStyle.access0(textColor: AppColors.appWhite),
                        )
                        // appText(AppStrings.detectMyLocation,color: AppColors.appBlack,
                        //   fontWeight:FontWeight.w700,
                        //   fontSize: 14,
                        //   fontFamily: AppConstants.rRegular,
                        //
                        //
                        // )
                      ],
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
//endregion

}