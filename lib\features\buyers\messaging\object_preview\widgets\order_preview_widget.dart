import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/messaging/object_preview/models/object_preview_models.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'dart:developer' as developer;

/// Widget to display order previews
class OrderPreviewWidget extends StatelessWidget {
  final OrderPreview order;
  final bool isMe;

  const OrderPreviewWidget({
    Key? key,
    required this.order,
    required this.isMe,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get the product image URL if available
    String? productImageUrl;
    if (order.productPreviewImage != null &&
        order.productPreviewImage!.isNotEmpty) {
      if (order.productPreviewImage!.startsWith('/')) {
        productImageUrl =
            '${AppConstants.baseUrl}${AppConstants.baseMediaUrl}${order.productPreviewImage!}';
      } else {
        productImageUrl =
            '${AppConstants.baseUrl}${AppConstants.baseMediaUrl}/${order.productPreviewImage!}';
      }
    }

    // Get the store icon URL
    String storeIconUrl = '';
    if (order.storeIcon != null && order.storeIcon!.isNotEmpty) {
      if (order.storeIcon!.startsWith('/')) {
        storeIconUrl =
            '${AppConstants.baseUrl}${AppConstants.baseMediaUrl}${order.storeIcon!}';
      } else {
        storeIconUrl =
            '${AppConstants.baseUrl}${AppConstants.baseMediaUrl}/${order.storeIcon!}';
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isMe
            ? AppColors.appRichBlack.withOpacity(0.9)
            : AppColors.textFieldFill2,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMe ? Colors.grey[800]! : Colors.grey[300]!,
          width: 0.5,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToOrderScreen(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with store info
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    // Store avatar
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.grey[300],
                      backgroundImage: storeIconUrl.isNotEmpty
                          ? NetworkImage(storeIconUrl)
                          : null,
                      child: storeIconUrl.isEmpty
                          ? Icon(
                              Icons.store,
                              color: Colors.grey[600],
                              size: 16,
                            )
                          : null,
                    ),
                    const SizedBox(width: 8),
                    // Store name
                    Expanded(
                      child: Text(
                        order.storeName,
                        style: AppTextStyle.access0(
                          textColor: isMe ? Colors.white : AppColors.appBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Order type indicator
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: isMe ? Colors.white24 : Colors.grey[300],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'ORDER',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: isMe ? Colors.white : Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Order details
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product image if available
                    if (productImageUrl != null)
                      Container(
                        height: 80,
                        width: 80,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            productImageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              developer.log(
                                'Error loading order product image: ${error.toString()}',
                                name: 'OrderPreviewWidget',
                                stackTrace: stackTrace,
                              );
                              return Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[500],
                                  size: 24,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    const SizedBox(width: 12),
                    // Order information
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Order number
                          Text(
                            'Order #${order.orderNumber}',
                            style: AppTextStyle.contentText0(
                              textColor:
                                  isMe ? Colors.white : AppColors.appBlack,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          // Suborder number
                          Text(
                            'Suborder: ${order.suborderNumber}',
                            style: AppTextStyle.smallTextRegular(
                              textColor:
                                  isMe ? Colors.white70 : Colors.grey[700]!,
                            ),
                          ),
                          const SizedBox(height: 8),
                          // View order button
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: isMe
                                  ? Colors.white24
                                  : AppColors.brandBlack.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Text(
                              'View Order Details',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color:
                                    isMe ? Colors.white : AppColors.brandBlack,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Navigate to the order detail screen
  void _navigateToOrderScreen(BuildContext context) {
    try {
      // Log the order numbers for debugging
      developer.log(
          '[INFO] _navigateToOrderScreen(): Order number: ${order.orderNumber}, Suborder number: ${order.suborderNumber}',
          name: 'OrderPreviewWidget');

      // Try to use the order number first, if it's valid
      String orderNumberToUse = '';

      // Check if order number is valid (should start with 'O')
      if (order.orderNumber.isNotEmpty && order.orderNumber.startsWith('O')) {
        orderNumberToUse = order.orderNumber;
      }
      // If not, try the suborder number
      else if (order.suborderNumber.isNotEmpty &&
          order.suborderNumber.startsWith('O')) {
        orderNumberToUse = order.suborderNumber;
      }
      // If both are invalid, show an error
      else {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Invalid order number format')));
        return;
      }

      developer.log(
          '[INFO] _navigateToOrderScreen(): Using order number: $orderNumberToUse',
          name: 'OrderPreviewWidget');

      // Navigate to order detail screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BuyerSubOrderScreen(
            orderNumber: orderNumberToUse,
          ),
        ),
      );
    } catch (e, stackTrace) {
      // Log any errors
      developer.log(
          '[ERROR] _navigateToOrderScreen(): Failed to navigate to order screen: ${e.toString()}',
          name: 'OrderPreviewWidget',
          stackTrace: stackTrace);

      // Show error message to user
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error opening order: ${e.toString()}')));
    }
  }
}
