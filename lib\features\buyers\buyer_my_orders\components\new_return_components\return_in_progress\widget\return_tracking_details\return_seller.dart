import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_in_progress/widget/return_tracking_details/return_tracking_details_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';

class Seller extends StatefulWidget {
  final ReturnTrackingDetailsBloc returnTrackingDetailsBloc;
  const Seller({Key? key, required this.returnTrackingDetailsBloc}) : super(key: key);

  @override
  State<Seller> createState() => _SellerState();
}

class _SellerState extends State<Seller> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        child:Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            AppTitleAndOptions(
              title: AppStrings.deliveryPersonDetail,
              titleOption: SvgPicture.asset(AppImages.exclamation),
              option: Column(
                children: [
                  AppTextFields.onlyStringWithSpaceTextField(
                    context: context,
                    maxEntry: 50,
                    textEditingController: widget.returnTrackingDetailsBloc.nameTextCtrl,
                    hintText:  AppStrings.deliveryPersonName,
                  ),
                  verticalSizedBox(10),
                  AppTextFields.mobileNumberTextField(
                    context: context,
                    textEditingController: widget.returnTrackingDetailsBloc.phoneNumberTextCtrl,
                    hintText:  AppStrings.phoneNumber,
                  ),
                ],
              ),
            ),
          ],
        )
    );
  }


  //region






}
