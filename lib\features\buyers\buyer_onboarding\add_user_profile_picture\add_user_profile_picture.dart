import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/add_user_profile_picture/add_user_profile_picture_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class AddUserProfilePicture extends StatefulWidget {
  final String userName;
  final String userReference;
  const AddUserProfilePicture(
      {super.key, required this.userName, required this.userReference});

  @override
  State<AddUserProfilePicture> createState() => _AddUserProfilePictureState();
}

class _AddUserProfilePictureState extends State<AddUserProfilePicture> {
  //region Bloc
  late AddUserProfilePictureBloc addUserProfilePictureBloc;
  //endregion
  //region Init
  @override
  void initState() {
    addUserProfilePictureBloc =
        AddUserProfilePictureBloc(context, widget.userReference);
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                    vertical: MediaQuery.of(context).size.height / 7),
                margin: const EdgeInsets.symmetric(horizontal: 20),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: AppColors.borderColor1)),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    imagePreview(),
                    userNameAndUpdateButton(),
                  ],
                ),
              ),
            ],
          ),
        ),
        Align(alignment: Alignment.bottomCenter, child: arrowAdnSkip())
      ],
    ));
  }

  //region Image preview
  Widget imagePreview() {
    return StreamBuilder<String>(
        stream: addUserProfilePictureBloc.userProfilePictureCtrl.stream,
        initialData: null,
        builder: (context, snapshot) {
          if (snapshot.data == null) {
            return SvgPicture.asset(
              AppImages.userPlaceHolder,
              height: 100,
              width: 100,
            );
          } else {
            return ClipRRect(
                borderRadius: BorderRadius.circular(200),
                child: Image.file(
                  File(snapshot.data!),
                  height: 100,
                  width: 100,
                  fit: BoxFit.cover,
                ));
          }
        });
  }
//endregion

//region User name and update profile picture
  Widget userNameAndUpdateButton() {
    return Container(
      margin: const EdgeInsets.only(top: 5),
      child: Column(
        children: [
          Text(
            widget.userName,
            style: AppTextStyle.settingHeading1(
                textColor: AppColors.writingBlack0),
          ),
          const SizedBox(
            height: 15,
          ),
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              addUserProfilePictureBloc.openGallery();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              decoration: BoxDecoration(
                  color: AppColors.brandBlack,
                  borderRadius: BorderRadius.circular(10)),
              child: Text(
                "Upload profile picture",
                style: AppTextStyle.access0(textColor: AppColors.appWhite),
              ),
            ),
          )
        ],
      ),
    );
  }
//endregion

//region Arrow and skip
  Widget arrowAdnSkip() {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          StreamBuilder<String>(
              stream: addUserProfilePictureBloc.userProfilePictureCtrl.stream,
              initialData: null,
              builder: (context, snapshot) {
                return Visibility(
                  visible: snapshot.data != null,
                  child: CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      addUserProfilePictureBloc.uploadProfilePic();
                    },
                    child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100),
                            color: AppColors.brandBlack),
                        child: SvgPicture.asset(AppImages.rightArrow)),
                  ),
                );
              }),
          const SizedBox(
            height: 30,
          ),
          CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                addUserProfilePictureBloc.goToFindYourFriend();
              },
              child: Text(
                "skip for later",
                style: AppTextStyle.access0(textColor: AppColors.disableBlack),
              ))
        ],
      ),
    );
  }
//endregion
}
