import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/buyer_cancelling_order/buyer_cancelling_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/shipping_in_progress/tracking_package/track_package.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ShippingInProgressBloc {
  // region Common Variables
  BuildContext context;
  late BuyerMyOrderServices buyerMyOrderServices;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  final List<SubOrder> subOrderList;
  List<String> groupNameList = [];


  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  final checkBoxCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller


  //endregion

  // region | Constructor |
  ShippingInProgressBloc(this.context, this.buyerSubOrderBloc, this.order, this.subOrderList);

  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    takeOutDisplayPackageNumbers();
  }

// endregion



  void takeOutDisplayPackageNumbers(){
    groupNameList.clear();
    for(var data in subOrderList){
      groupNameList.add(data.displayPackageNumber!);
    }
    ///Clear duplicates
    groupNameList=groupNameList.toSet().toList();
    //print("Package name length are ${groupNameList.length}");

  }
  //endregion

//region On tap track
void onTapTrack({required List<SubOrder> subOrders}){
    CommonMethods.appBottomSheet(screen: TrackPackage(suborderList: subOrders, buyerSubOrderBloc: buyerSubOrderBloc,order: order), context: context, bottomSheetName: AppStrings.trackThePackage);
}
//endregion

// region On tap Tracking detail
  Future onTapCancel({required List<SubOrder> subOrders}) {
    return CommonMethods.appBottomSheet(screen: BuyerCancellingOrderScreen(
      subOrderList: subOrders,
      store: order, buyerSubOrderBloc:buyerSubOrderBloc,
    ), context: context, bottomSheetName: AppStrings.cancellingTheOrder).then((value) {
      //Unselect all
      CommonMethods.subOrderSelectUnSelectAll(isSelectAll: false, subOrderList: subOrderList);
      //Refresh sub order screen.
      buyerSubOrderBloc.buyerSubOrderStateCtrl.sink.add(BuyerSubOrderState.Success);

    });
  }
// endregion

}
