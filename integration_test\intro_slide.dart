import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/app.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:url_strategy/url_strategy.dart';

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  // Set environment
  AppEnvironment.setEnv(Environment.dev);

  // Flutter binding ensure initialization
  //WidgetsFlutterBinding.ensureInitialized();

  // Firebase initialize
  if (kIsWeb) {
    await Firebase.initializeApp(
        options: FirebaseOptions(
          apiKey: "your_api_key",
          projectId: "your_project_id",
          messagingSenderId: "your_messaging_sender_id",
          appId: "your_app_id",
        ));
  }
  await Firebase.initializeApp();

  // Firebase cloud background messaging (Push notification)
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Crash analytics
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;


  // Remove # from web url
  setPathUrlStrategy();

  testWidgets('Open app and wait for 10 seconds', (WidgetTester tester) async {
    // Start your app
    await tester.pumpWidget(App());

    // Wait for 10 seconds
    await Future.delayed(Duration(seconds: 10));

    // Test finished
  });
}
@pragma('vm:entry-point')

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();

  //print("Handling a background message: ${message.notification!.title}");
}
