import 'dart:async';
import 'dart:core';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:rich_text_controller/rich_text_controller.dart';
import 'package:swadesic/features/buyers/buyer_add_image/buyer_add_image_screen.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_image_preview/buyer_image_preview_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/add_comment/add_comment.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/comment_filter/comment_filter.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/comment/comment_filter_model/comment_filter_model.dart';
import 'package:swadesic/model/deep_link_response/deep_link_response.dart';
import 'package:swadesic/model/product_comment_response/add_parent_comment_response.dart';
import 'package:swadesic/model/product_comment_response/product_all_comment_response.dart';
import 'package:swadesic/services/app_link_services/app_link_create_service.dart';
import 'package:swadesic/services/deep_link/deep_link.dart';
import 'package:swadesic/services/product_comment_services/product_comment_services.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

enum ProductCommentState { Loading, Success, Failed, Empty, FilterEmpty }

class BuyerProductCommentBloc {
  // region Common Variables
  BuildContext commentScreenContext;
  final String storeReference;
  late AddComment addComment;

  ///Get Product All Comment
  late ProductCommentServices productCommentServices;
  late ProductAllCommentResponse productAllCommentResponse;

  ///List of comments
  List<ReplyAndComments> filteredComments = [];

  ///Add Parent comment Response
  late AddParentCommentResponse addParentCommentResponse;

  ///Upload service
  late var uploadFileService = UploadFileService();
  // List<String> reviewImageList = [];
  List<CommentReviewImage>? commentReviewImage;
  bool isReviewEditActive = false;
  bool editReviewCommentHasImage = false;

  ///DeepLink
  late DeepLinkServices deepLinkServices;
  late DeepLinkCreateResponse deepLinkCreateResponse;

  final String? productRef;
  //final int? productId;
  // List<CommentResponcComments> comments = [];
  // bool isReplay = false;
  // String replayedUserName = "";
  // String replayedUserId = "";
  String commentType = "";
  int productRating = 5;
  bool isChildComment = false;
  bool isEditActive = false;
  bool sendAs = true;
  late int? replyCommentId;
  bool parentEdit = true;
  int parentChildCommentId = 0;
  bool emojiVisible = false;
  int clappedCount = 0;
  String clappedList = '';
  bool hasReviewAccess = false;

  CommentFilterModel commentFilterModel = CommentFilterModel();

  // var uuid = Uuid();

  // endregion

  //region Controller
  final screenRefreshCtrl = StreamController<bool>.broadcast();
  final emojiVisibleCtrl = StreamController<bool>.broadcast();
  final editSendVisibleCtrl = StreamController<bool>.broadcast();
  final replayCommentSendVisibleCtrl = StreamController<bool>.broadcast();
  final sendAsVisibleCtrl = StreamController<bool>.broadcast();
  final ratingVisibleCtrl = StreamController<bool>.broadcast();
  final buyerProductCommentStateCtrl =
      StreamController<ProductCommentState>.broadcast();
  final commentListCtrl = StreamController<bool>.broadcast();
  //endregion

  //region Color change Text editing controller
  late RichTextController commentTextFieldCtrl;
  Map<RegExp, TextStyle> pattern = {
    RegExp(AppConstants.atTag): const TextStyle(
        color: AppColors.brandBlack,
        fontFamily: AppConstants.rRegular,
        fontSize: 14,
        fontWeight: FontWeight.w400),
  };
  // TextEditingController commentTextFieldCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  BuyerProductCommentBloc(
      this.commentScreenContext, this.productRef, this.storeReference);

  // endregion

  // region Init
  Future init() async {
    ///initialize comment text ctrl
    initializeCommentTextCtrl();
    addComment = AddComment(this);

    productCommentServices = ProductCommentServices();
    deepLinkServices = DeepLinkServices();
    //Check review access
    checkReviewAccess();

    //Get all comments
    getProductAllCommentApiCall(productRef!);

    // comments.add(CommentResponcComments(
    //     comment: "No wonder why this is a growing trend lately", id: uuid.v4(), time: "2m", username: "rathod", replay: [
    //   CommentResponcCommentsReplay(
    //       username: "sushant ", time: "4m", comment:"true that")
    //
    //
    // ]));
    //
    // comments.add(CommentResponcComments(
    //     comment: "this looks very good on my new outfit I bought for next week’s party", id: uuid.v4(), time: "2m", username: "srijan", replay: []));
  }

// endregion

  //region On tap share comment
  void onTapShareComment(
      {required String productReference, required int commentId}) async {
    //Share
    // CommonMethods.share(AppLinkCreateService().createProductLink(productReference: productReference, commentId: commentId, storeReference:storeReference));
  }
  //endregion

  //region Check review access
  void checkReviewAccess() async {
    try {
      //Check is it from store view
      if (AppConstants.appData.storeReference != null) {
        hasReviewAccess = false;
        return;
      }

      ///Add review comment
      hasReviewAccess = await productCommentServices.checkReviewAccess(
          productReference: productRef!,
          reference: AppConstants.appData.userReference!);
      //Refresh send as
      sendAsVisibleCtrl.sink.add(false);
    } on ApiErrorResponseMessage {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          AppStrings.haveNotBought, commentScreenContext);

      return;
    } catch (error) {
      //print(error);

      CommonMethods.toastMessage(
          "Don't have review access", commentScreenContext);

      return;
    }
  }
  //endregion

  //region Initialize Comment Text editing controller
  void initializeCommentTextCtrl() {
    commentTextFieldCtrl = RichTextController(
        onMatch: (List<String> match) {
          pattern;
        },
        patternMatchMap: pattern);
  }

  //endregion

  //region Get Product All Comment
  getProductAllCommentApiCall(String productRef) async {
    //region Try
    try {
      //Clear filtered data
      filteredComments.clear();
      //Get all comment api call
      productAllCommentResponse =
          await productCommentServices.getProductComment(
              productRef: productRef,
              userReference: AppConstants.appData.userReference!);
      //Add all comments to filtered comment
      filteredComments.addAll(productAllCommentResponse.data!);
      //Read more flag
      isReadMoreFlag();
      //Empty
      if (productAllCommentResponse.data!.isEmpty) {
        buyerProductCommentStateCtrl.sink.add(ProductCommentState.Empty);
        return;
      }
      //Apply filter
      applyFilter();
      // buyerProductCommentStateCtrl.sink.add(ProductCommentState.Success);
    }
    //endregion
    on ApiErrorResponseMessage {
      buyerProductCommentStateCtrl.sink.add(ProductCommentState.Failed);
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);
      return;
    }
  }
  //endregion

  //region Apply filter
  applyFilter() {
    //Clear filtered data
    filteredComments.clear();

    ///If comment
    if (commentFilterModel.comment.isSelected) {
      filteredComments.addAll(productAllCommentResponse.data!
          .where((element) =>
              element.commentType == commentFilterModel.comment.status)
          .toList());
    }

    ///If question
    if (commentFilterModel.question.isSelected) {
      filteredComments.addAll(productAllCommentResponse.data!
          .where((element) =>
              element.commentType == commentFilterModel.question.status)
          .toList());
    }

    ///If review
    if (commentFilterModel.review.isSelected) {
      filteredComments.addAll(productAllCommentResponse.data!
          .where((element) =>
              element.commentType == commentFilterModel.review.status)
          .toList());
    }
    //If filtered data is empty
    if (filteredComments.isEmpty) {
      //Close bottom sheet
      return buyerProductCommentStateCtrl.sink
          .add(ProductCommentState.FilterEmpty);
    }
    //Refresh comment screen
    buyerProductCommentStateCtrl.sink.add(ProductCommentState.Success);
  }
  //endregion

  //region On tap reset
  resetFilter() {
    commentFilterModel.comment.isSelected = true;
    commentFilterModel.question.isSelected = true;
    commentFilterModel.review.isSelected = true;
    applyFilter();
  }
  //endregion

  //region Is read more
  void isReadMoreFlag() {
    for (var root in filteredComments) {
      if (root.replies!.length <= 3) {
        root.isReadMore = false;
      } else {
        root.isReadMore = true;
      }
      //print("Read more status is ${root.isReadMore }");
    }
  }
//endregion

  //region Add Parent Comment
  addParentCommentApiCall(String type) async {
    ///Check Comment field empty
    if (commentTextFieldCtrl.text.trim().isEmpty) {
      CommonMethods.toastMessage(
          AppStrings.emptyCommentCanNotPosted, commentScreenContext);
      return;
    }

    ///Check Comment field empty
    if (commentTextFieldCtrl.text.trim().isEmpty) {
      CommonMethods.toastMessage(
          AppStrings.emptyCommentCanNotPosted, commentScreenContext);
      return;
    }

    ///Close Keybord
    CommonMethods.closeKeyboard(commentScreenContext);

    ///Reset
    resetAllState();

    ///Comment Access check
    // if(BuyerHomeBloc.userDetailsResponse.userDetail!.addComment! != "1" && type == "comment" ){
    //   ///Clear text field
    //   commentTextFieldCtrl.clear();
    //   return CommonMethods.toastMessage(AppStrings.noAccess, commentScreenContext);
    // }

    ///Question Access check
    // if(BuyerHomeBloc.userDetailsResponse.userDetail!.addQuestion! != "1" && type == "question" ){
    //   ///Clear text field
    //   commentTextFieldCtrl.clear();
    //   return CommonMethods.toastMessage(AppStrings.noAccess, commentScreenContext);
    // }

    try {
      ///Close Keybord
      CommonMethods.closeKeyboard(commentScreenContext);

      ///Api call
      await productCommentServices.addParentComment(
          productRef!, commentTextFieldCtrl.text, type);

      ///Clear text field
      commentTextFieldCtrl.clear();

      ///Calling Get all comment Api
      getProductAllCommentApiCall(productRef!);
      buyerProductCommentStateCtrl.sink.add(ProductCommentState.Success);
    } on ApiErrorResponseMessage {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);

      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);

      return;
    }
  }
  //endregion

  //region Edit Parent Comment
  editParentComment(int parentChildCommentId) async {
    try {
      ///Check Comment field empty
      if (commentTextFieldCtrl.text.trim().isEmpty) {
        CommonMethods.toastMessage(
            AppStrings.emptyCommentCanNotPosted, commentScreenContext);
        return;
      }
      //print(parentChildCommentId);
      ///Close Keybord
      CommonMethods.closeKeyboard(commentScreenContext);

      ///Reset
      resetAllState();

      ///active send as mode
      sendAs = true;
      await productCommentServices.editParentComment(
          commentId: parentChildCommentId, comment: commentTextFieldCtrl.text);

      ///Clear text field
      commentTextFieldCtrl.clear();

      ///Calling Get all comment Api
      getProductAllCommentApiCall(productRef!);

      buyerProductCommentStateCtrl.sink.add(ProductCommentState.Success);
    } on ApiErrorResponseMessage catch (error) {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          error.message.toString(), commentScreenContext);

      return;
    } catch (error) {
      //print(error);
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);

      return;
    }
  }
  //endregion

  //region Edit Child Comment
  editChildComment(int parentChildCommentId) async {
    try {
      ///Check Comment field empty
      if (commentTextFieldCtrl.text.trim().isEmpty) {
        CommonMethods.toastMessage(
            AppStrings.emptyCommentCanNotPosted, commentScreenContext);
        return;
      }

      ///Close Keybord
      CommonMethods.closeKeyboard(commentScreenContext);
      //print(parentChildCommentId);
      ///Reset
      resetAllState();

      ///active send as mode
      sendAs = true;
      await productCommentServices.editChildComment(
          parentChildCommentId, commentTextFieldCtrl.text);

      ///Clear text field
      commentTextFieldCtrl.clear();

      ///Calling Get all comment Api
      getProductAllCommentApiCall(productRef!);

      buyerProductCommentStateCtrl.sink.add(ProductCommentState.Success);
    } on ApiErrorResponseMessage catch (error) {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          error.message.toString(), commentScreenContext);
    } catch (error) {
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);
    }
  }
  //endregion

  //region Delete Parent Comment
  void deleteParentComment(int commentId) async {
    //print(commentId);
    await productCommentServices.deleteParentComment(commentId);
    getProductAllCommentApiCall(productRef!);
  }
  //endregion

  //region Delete Child Comment
  void deleteChildComment(int childCommentId) async {
    //print(childCommentId);
    await productCommentServices.deleteChildComment(childCommentId);
    getProductAllCommentApiCall(productRef!);
  }
  //endregion

  //region On Change Comment Field
  void onChangeCommentField() {
    if (isReviewEditActive) {
      ///hide Send as Options
      sendAsVisibleCtrl.sink.add(false);

      ///Visible Star rating
      ratingVisibleCtrl.sink.add(true);
      return;
    }

    ///If reply button is tapped and Is Child comment is True
    if (isChildComment) {
      if (commentTextFieldCtrl.text.isNotEmpty) {
        ///Visible Send as Options
        sendAsVisibleCtrl.sink.add(false);

        ///Hide Edit Send Button
        editSendVisibleCtrl.sink.add(false);

        ///Hide Send as Options
        sendAsVisibleCtrl.sink.add(false);

        ///Hide Star rating
        ratingVisibleCtrl.sink.add(false);
      } else {
        resetAllState();

        ///active send as mode
        sendAs = true;
      }
    }
    if (sendAs) {
      if (commentTextFieldCtrl.text.isNotEmpty) {
        sendAsVisibleCtrl.sink.add(true);
      } else {
        resetAllState();
      }
    }

    if (isEditActive) {
      if (commentTextFieldCtrl.text.isNotEmpty) {
        ///Visible Send as Options
        sendAsVisibleCtrl.sink.add(false);

        ///Hide Edit Send Button
        editSendVisibleCtrl.sink.add(true);

        ///Hide Star rating
        ratingVisibleCtrl.sink.add(false);

        ///Hide reply Comment Send button
        replayCommentSendVisibleCtrl.sink.add(false);
      } else {
        resetAllState();

        ///active send as mode
        sendAs = true;
      }
    }
  }

  //endregion

  //region On Tap Review
  onTapReview(String type) {
    ///Comment Access check
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.addReview! != "1") {
      commentType = "";

      ///Clear text field
      commentTextFieldCtrl.clear();

      ///Visible Star Rating
      ratingVisibleCtrl.sink.add(false);

      ///Close Keybord
      CommonMethods.closeKeyboard(commentScreenContext);

      ///Refresh Review button color
      sendAsVisibleCtrl.sink.add(false);
      return CommonMethods.toastMessage(
          AppStrings.noAccess, commentScreenContext);
    }

    //reviewImageList.clear();
    // commentReviewImage!.clear();

    commentType = type;

    ///Visible Star Rating
    ratingVisibleCtrl.sink.add(true);

    ///Refresh Review button color
    sendAsVisibleCtrl.sink.add(true);
  }
  //endregion

  //region Add and edit  Review and rate product
  void reviewAndAddRatting() async {
    try {
      ///Close keyboard
      CommonMethods.closeKeyboard(commentScreenContext);

      ///If review comment is editing the this will called
      // if(isReviewEditActive){
      //   ///Edit Comment text and calling edit api
      //   await productCommentServices.editParentComment(commentId: parentChildCommentId, comment: commentTextFieldCtrl.text);
      //   ///Add star ratting
      //   await productCommentServices.productRating(productRating,parentChildCommentId);
      //   ///Upload image only if multipleSelectedImage has images.
      //   if(AppConstants.multipleSelectedImage.isNotEmpty){
      //     await addCommentImage(parentChildCommentId);
      //   }
      //   ///Clear All image from global variable
      //   AppConstants.multipleSelectedImage.clear();
      //   ///Reset All state
      //   resetAllState();
      //   ///Calling Get all comment Api
      //   await getProductAllCommentApiCall(productRef!);
      //   ///Clear comment field
      //   commentTextFieldCtrl.clear();
      //   ///Enable send as options
      //   sendAs=true;
      //   ///Close KeyBord
      //   CommonMethods.closeKeyboard(commentScreenContext);
      //   return;
      // }

      ///Add review comment
      addParentCommentResponse = await productCommentServices.addParentComment(
          productRef!, commentTextFieldCtrl.text, "review");

      ///Clear comment text field
      commentTextFieldCtrl.clear();

      ///Add star ratting
      await productCommentServices.productRating(
          productRating, addParentCommentResponse.data!.commentid!);

      ///Upload image only if multipleSelectedImage has images.
      if (AppConstants.multipleSelectedImage.isNotEmpty) {
        await addCommentImage(addParentCommentResponse.data!.commentid!);
      }

      ///Clear All image from global variable
      AppConstants.multipleSelectedImage.clear();

      ///Reset
      resetAllState();

      ///Close KeyBord
      CommonMethods.closeKeyboard(commentScreenContext);

      ///Calling Get all comment Api
      await getProductAllCommentApiCall(productRef!);
    } on ApiErrorResponseMessage {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          AppStrings.haveNotBought, commentScreenContext);

      return;
    } catch (error) {
      //print(error);

      CommonMethods.toastMessage(
          "Don't have review access", commentScreenContext);

      return;
    }
  }
  //endregion

  //region Go to Buyer Add Image Screen
  void goToBuyerAddImageScreen() {
    var screen = const BuyerAddImageScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(commentScreenContext, route).then((value) {
      screenRefreshCtrl.sink.add(true);
    });
  }
  //endregion

  //region On Tap Replay
  void onTapReplay({required int commentId, required String userName}) {
    ///Check comment
    // if(BuyerHomeBloc.userDetailsResponse.userDetail!.addComment! =="0"){
    //   CommonMethods.toastMessage(AppStrings.noAccess, commentScreenContext);
    //   return;
    // }
    ///Hide Edit Send Button
    editSendVisibleCtrl.sink.add(false);

    ///Store Comment ID
    replyCommentId = commentId;

    ///Mark As a child comment
    isChildComment = true;

    ///Hide send as option
    sendAs = false;

    ///Add User name to the Comment Text Field
    commentTextFieldCtrl.text = "@${userName.toLowerCase()} ";
    replayCommentSendVisibleCtrl.sink.add(true);

    ///Hide Send as Options
    sendAsVisibleCtrl.sink.add(false);

    ///Hide Star rating
    ratingVisibleCtrl.sink.add(false);

    ///Clear Comment Type
    commentType = "";
  }
  //endregion

//region Add Child Comment
  void addChildComment() async {
    try {
      ///Check Comment field empty
      if (commentTextFieldCtrl.text.trim().isEmpty) {
        CommonMethods.toastMessage(
            AppStrings.emptyCommentCanNotPosted, commentScreenContext);
        return;
      }

      ///Close Keybord
      CommonMethods.closeKeyboard(commentScreenContext);

      ///Reset
      resetAllState();

      ///active send as mode
      sendAs = true;
      await productCommentServices.addChildComment(
          replyCommentId!, commentTextFieldCtrl.text);

      ///Clear text field
      commentTextFieldCtrl.clear();

      ///Calling Get all comment Api
      getProductAllCommentApiCall(productRef!);
    } on ApiErrorResponseMessage catch (error) {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          error.message.toString(), commentScreenContext);
      return;
    } catch (error) {
      //print(error);
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);
      return;
    }
  }
//endregion

  //region Parent Edit Delete Dialog
  void parentEditDeleteComment(
      int parentCommentId, String parentComment, String commentType) {
    //print(commentType);
    //reviewImageList.clear();
    ratingVisibleCtrl.sink.add(false);
    //print("Parent child id is $parentCommentId");

    if (commentType == "review") {
      isReviewEditActive = true;
      var data = filteredComments
          .firstWhere((element) => element.commentid == parentCommentId);
      commentReviewImage = data.commentReviewImage;
      if (commentReviewImage!.isEmpty) {
        editReviewCommentHasImage = false;
      } else {
        editReviewCommentHasImage = true;
      }
    } else {
      isReviewEditActive = false;

      //reviewImageList.clear();
    }

    //print(reviewImageList.toString());

    showDialog(
        context: commentScreenContext,
        builder: (BuildContext context) {
          return AlertDialog(
              contentPadding: EdgeInsets.zero,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  //Edit
                  InkWell(
                    onTap: () {
                      ///Visible review options
                      commentType == "review"
                          ? ratingVisibleCtrl.sink.add(true)
                          : ratingVisibleCtrl.sink.add(false);

                      ///Add Parent Comment
                      parentChildCommentId = parentCommentId;

                      ///Enable parent comment edit flag
                      parentEdit = true;
                      isEditActive = true;

                      ///Hide send as mode
                      sendAs = false;
                      commentTextFieldCtrl.text = parentComment;
                      commentType == "review"
                          ? editSendVisibleCtrl.sink.add(false)
                          : editSendVisibleCtrl.sink.add(true);
                      Navigator.pop(context);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Text(
                        AppStrings.edit,
                        textAlign: TextAlign.left,
                        style: AppTextStyle.heading4Bold(
                            textColor: AppColors.appBlack),
                      ),
                    ),
                  ),
                  divider(),
                  //Delete
                  InkWell(
                    onTap: () {
                      deleteParentComment(parentCommentId);
                      Navigator.pop(context);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Text(
                        AppStrings.delete,
                        textAlign: TextAlign.left,
                        style: AppTextStyle.heading4Bold(
                            textColor: AppColors.appBlack),
                      ),
                    ),
                  ),
                  divider(),

                  Visibility(
                    visible: !isChildComment,
                    child: InkWell(
                      onTap: () {
                        //Close report dialog
                        Navigator.pop(context);
                        onTapShareComment(
                            commentId: parentCommentId,
                            productReference: productRef!);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: Text(
                          AppStrings.shareComment,
                          textAlign: TextAlign.left,
                          style: AppTextStyle.heading4Bold(
                              textColor: AppColors.appBlack),
                        ),
                      ),
                    ),
                  ),
                ],
              ));
        }).then((value) {
      // resetAllState();
    });
  }
  //endregion

  //region Child Edit Delete Dialog
  void childEditDeleteComment(
      {required int childCommentId, required String childComment}) {
    showDialog(
        context: commentScreenContext,
        builder: (BuildContext context) {
          return AlertDialog(
              contentPadding: EdgeInsets.zero,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  InkWell(
                    onTap: () {
                      ///Add Child Comment
                      parentChildCommentId = childCommentId;

                      ///Enable parent comment edit flag
                      parentEdit = false;
                      isEditActive = true;

                      ///Hide send as mode
                      sendAs = false;
                      commentTextFieldCtrl.text = childComment;
                      editSendVisibleCtrl.sink.add(true);
                      Navigator.pop(context);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Text(
                        AppStrings.edit,
                        textAlign: TextAlign.left,
                        style: AppTextStyle.heading4Bold(
                            textColor: AppColors.appBlack),
                      ),
                    ),
                  ),
                  divider(),
                  InkWell(
                    onTap: () {
                      deleteChildComment(childCommentId);
                      Navigator.pop(context);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Text(
                        AppStrings.delete,
                        textAlign: TextAlign.left,
                        style: AppTextStyle.heading4Bold(
                            textColor: AppColors.appBlack),
                      ),
                    ),
                  ),
                  divider(),
                ],
              ));
        });
  }
  //endregion

  //region Remove local image
  void removeLocalImage(int index) {
    AppConstants.multipleSelectedImage.removeAt(index);
    screenRefreshCtrl.sink.add(true);
  }
  //endregion

  //region Report and share
  void reportAndShareComment(
      {required int parentOrChildCommentId, required bool isFromChild}) {
    showDialog(
        context: commentScreenContext,
        builder: (BuildContext context) {
          return AlertDialog(
              contentPadding: EdgeInsets.zero,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  InkWell(
                    onTap: () {
                      //Close report dialog
                      Navigator.pop(context);
                      //print(parentOrChildCommentId.toString());
                      var screen = ReportScreen(
                        reference: parentOrChildCommentId.toString(),
                        isPostComment: true,
                      );
                      var route =
                          MaterialPageRoute(builder: (context) => screen);
                      Navigator.push(commentScreenContext, route);
                    },
                    child: Container(
                      // decoration: const BoxDecoration(
                      //   border: Border(
                      //     bottom: BorderSide(
                      //       color: AppColors.lightGray2,
                      //       width: 1.0
                      //     )
                      //   )
                      // ),
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Text(
                        AppStrings.report,
                        textAlign: TextAlign.left,
                        style: AppTextStyle.heading4Bold(
                            textColor: AppColors.appBlack),
                      ),
                    ),
                  ),
                  divider(),
                  Visibility(
                    visible: !isFromChild,
                    child: InkWell(
                      onTap: () {
                        //Close report dialog
                        Navigator.pop(context);
                        onTapShareComment(
                            commentId: parentOrChildCommentId,
                            productReference: productRef!);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: Text(
                          AppStrings.shareComment,
                          textAlign: TextAlign.left,
                          style: AppTextStyle.heading4Bold(
                              textColor: AppColors.appBlack),
                        ),
                      ),
                    ),
                  ),
                ],
              ));
        });
  }
  //endregion

//region Reset All State
  void resetAllState() {
    //print("empty");

    ///Remove Edit review image status to false
    editReviewCommentHasImage = false;

    ///Is review Edit message to false
    isReviewEditActive = false;

    ///Remove child comment tag
    isChildComment = false;

    ///Hide Send as Options
    sendAsVisibleCtrl.sink.add(false);

    ///Hide Star rating
    ratingVisibleCtrl.sink.add(false);

    ///Clear Comment Type
    commentType = "";

    ///Hide Send Button
    replayCommentSendVisibleCtrl.sink.add(false);

    ///Remove Edit Tag
    isEditActive = false;

    ///Hide Editing Send Button
    editSendVisibleCtrl.sink.add(false);

    ///Clear local images
    AppConstants.multipleSelectedImage.clear();
  }
//endregion

//region On Tap Emoji icon
  void onTapEmoji() {
    CommonMethods.closeKeyboard(commentScreenContext);
    emojiVisible = !emojiVisible;
    emojiVisibleCtrl.sink.add(emojiVisible);
  }
//endregion

//region On Select Emoji
  void onSelectEmoji(String selectedEmoji) {
    commentTextFieldCtrl.text = commentTextFieldCtrl.text + selectedEmoji;
  }
//endregion

  ///Clap
//region On tap clap
  void onTapClap({required ReplyAndComments replyAndComments}) async {
    ///Clap access
    // if(BuyerHomeBloc.userDetailsResponse.userDetail!.clapCommentQuestionReview! != "1"){
    //   ///Clear text field
    //   commentTextFieldCtrl.clear();
    //   return CommonMethods.toastMessage(AppStrings.noAccess, commentScreenContext);
    // }

    ///Api call
    try {
      ///Close Keyboard
      CommonMethods.closeKeyboard(commentScreenContext);

      ///Reset
      resetAllState();
      clappedList = '';
      clappedCount = 0;
      /*
      If user/store reference already in the clapped list.
       */
      if (replyAndComments.clappedUsers!.contains(
          AppConstants.appData.isUserView!
              ? AppConstants.appData.userReference
              : AppConstants.appData.storeReference)) {
        //Remove user reference from list
        replyAndComments.clappedUsers!.removeWhere((element) =>
            element ==
            (AppConstants.appData.isUserView!
                ? AppConstants.appData.userReference
                : AppConstants.appData.storeReference!));
      }
      /*
      Else add the user reference to the clapped user list.
       */
      else {
        //Add user reference from list
        replyAndComments.clappedUsers!.add(AppConstants.appData.isUserView!
            ? AppConstants.appData.userReference!
            : AppConstants.appData.storeReference!);
      }
      //Update the clap count by calculating length of the clapped user
      replyAndComments.claps = replyAndComments.clappedUsers!.length;
      //Success
      buyerProductCommentStateCtrl.sink.add(ProductCommentState.Success);

      ///Api call
      await productCommentServices.addRemoveClap(
          replyAndComments: replyAndComments);
    } on ApiErrorResponseMessage {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage,
          AppConstants.globalNavigator.currentContext);
      return;
    } catch (error) {
      //print(error);
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage,
          AppConstants.globalNavigator.currentContext);
      return;
    }
  }
//endregion

  /*
  * This method will make image visible
  * */
//region On Tap View Image
  void onTapViewImage(ReplyAndComments comments) {
    comments.isExpand = !comments.isExpand;
    screenRefreshCtrl.sink.add(true);
  }
//endregion

//region Add Comment Image
  addCommentImage(int commentId) async {
    try {
      for (int i = 0; i < AppConstants.multipleSelectedImage.length; i++) {
        await uploadFileService.addCommentImage(
            filePath: AppConstants.multipleSelectedImage[i].path,
            url: AppConstants.addCommentImage,
            fileNameWithExtension:
                AppConstants.multipleSelectedImage.first.name,
            commentId: commentId);
        //print(i);
      }
      AppConstants.multipleSelectedImage.clear();
    } on ApiErrorResponseMessage catch (error) {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          error.message.toString(), commentScreenContext);

      return;
    } catch (error) {
      //print(error);
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);

      return;
    }
  }
//endregion

//region Delete Comment Image
  deleteCommentImage(int imageId) async {
    try {
      for (var value in filteredComments) {
        value.commentReviewImage!
            .removeWhere((element) => element.reviewImageId == imageId);
        screenRefreshCtrl.sink.add(true);
      }

      productCommentServices.deleteCommentImage(imageId);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);
      return;
    } catch (error) {
      CommonMethods.toastMessage(
          AppStrings.commonErrorMessage, commentScreenContext);
      return;
    }
  }
//endregion

//region Go to Buyer Image Preview Screen
  void goToBuyerProductImageScreen(List<CommentReviewImage> productImage) {
    List<String> imageUrls = [];
    for (var data in productImage) {
      imageUrls.add(data.reviewImage!);
    }

    var screen = BuyerImagePreviewScreen(
      productImage: imageUrls,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route);
  }
//endregion

  //region Go to Seller profile
  void goToProfile() {
    var screen = UserProfileScreen(
      userReference: AppConstants.appData.userReference!,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(commentScreenContext, route).then((value) {
      init();
    });
  }
//endregion

//region On tap filter
  void onTapFilter({required BuyerProductCommentBloc buyerProductCommentBloc}) {
    //Close keyboard
    CommonMethods.closeKeyboard(commentScreenContext);
    showModalBottomSheet(
        context: commentScreenContext,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(20), topLeft: Radius.circular(20))),
        builder: (context) {
          return FractionallySizedBox(
              heightFactor: 0.8,
              child: CommentFilter(
                commentFilterModel: commentFilterModel,
                buyerProductCommentBloc: buyerProductCommentBloc,
              ));
        }).then((value) {
      // if (value == null) return;
      // supportFilterModel = value;
      // applyFilter();
    });
  }
//endregion

//region Dispose

//endregion
}
