import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class DeletedProductDialog extends StatefulWidget {
  final List<CartProduct> deletedCartProductList;
  final ShoppingCartBloc shoppingCartBloc;


  const DeletedProductDialog({super.key, required this.deletedCartProductList, required this.shoppingCartBloc});

  @override
  State<DeletedProductDialog> createState() => _DeletedProductDialogState();
}

class _DeletedProductDialogState extends State<DeletedProductDialog> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //Message
          Text(
            AppStrings.theseProductsInYourCartAreNoLongerAvailable,
            textAlign: TextAlign.center,
            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
          ),

          //Product icons
          productIcons(),
          //Action button
          actionButton(),
        ],
      ),
    );
  }

//endregion

//region Product icons
  Widget productIcons() {
    return Container(
      margin: const EdgeInsets.only(top: 30, bottom: 40),
      child: Wrap(
        alignment: WrapAlignment.center, // Aligns items in the center
        spacing: 10.0, // Spacing between items
        runSpacing: 10.0, // Spacing between rows
        children: widget.deletedCartProductList.map((item) {
          return Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(11)),
            height: 25,
            width: 25,
            child: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: (){
                //Go to single product view
                var screen = BuyerViewSingleProductScreen(productReference: item.productReference!,);
                var route = MaterialPageRoute(builder: (context) => screen);
                Navigator.push(context, route);
              },
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(11),
                  child: extendedImage(item.prodImages, context, 50, 50, imageHeight: 25, imageWidth: 25, customPlaceHolder: AppImages.productPlaceHolder)),
            ),
          );
        }).toList(),
      ),
    );
  }
//endregion

//region Action buttons
  Widget actionButton(){

    return Row(
      children: [
        //order later
        Expanded(
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: ()async{
              //Remove suborders from cart by cart id
              await widget.shoppingCartBloc.removeMultipleCartItemsApi(cartItemIdList: widget.deletedCartProductList.map((e) => e.cart!.cartItemId!).toList() );
              //Get cart
              widget.shoppingCartBloc.getCartItems();
              //Close
              Navigator.pop(context);

            },
            child: Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(80),
                color: AppColors.textFieldFill1,
              ),
              child: Text(AppStrings.removeFromCart,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.access0(textColor: AppColors.appBlack),),
            ),
          ),
        ),


      ],
    );
  }
//endregion

}

