import 'package:flutter_test/flutter_test.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';

void main() {
  group('API Response Variant Tests', () {
    test('should parse API response correctly and extract options from variants', () {
      // Simulate the API response structure
      final apiResponse = {
        "productid": 162,
        "product_reference": "P1751279515483215OEDG",
        "product_name": "Vaanar kutumb",
        "brand_name": "Vaanar",
        "mrp_price": 1,
        "selling_price": 1,
        "in_stock": 1,
        "product_variants": [
          {
            "variant_reference": "PV1751279516482179",
            "combinations": {
              "Type": "Non tailed",
              "Color": "Grey"
            },
            "mrp_price": 265,
            "selling_price": 200,
            "stock": 250,
            "is_active": true,
            "created_date": "2025-06-30T16:01:56.483523+05:30",
            "modified_date": "2025-06-30",
            "variant_version": "1.0.0"
          },
          {
            "variant_reference": "PV1751279516774737",
            "combinations": {
              "Type": "Tailed",
              "Color": "Black"
            },
            "mrp_price": 265,
            "selling_price": 200,
            "stock": 500,
            "is_active": true,
            "created_date": "2025-06-30T16:01:56.775977+05:30",
            "modified_date": "2025-06-30",
            "variant_version": "1.0.0"
          }
        ]
      };

      // Parse the product from API response
      final product = Product.fromJson(apiResponse);

      // Verify basic product data
      expect(product.productName, "Vaanar kutumb");
      expect(product.brandName, "Vaanar");
      expect(product.mrpPrice, 1); // Product-level price (should be overridden by variants)
      expect(product.sellingPrice, 1);

      // Verify variants are parsed correctly
      expect(product.variants, isNotNull);
      expect(product.variants!.length, 2);

      // Parse variants
      final variants = product.variants!
          .map((variantJson) => ProductVariant.fromJson(variantJson))
          .toList();

      // Test first variant
      final firstVariant = variants[0];
      expect(firstVariant.combinations['Type'], 'Non tailed');
      expect(firstVariant.combinations['Color'], 'Grey');
      expect(firstVariant.mrpPrice, 265);
      expect(firstVariant.sellingPrice, 200);
      expect(firstVariant.stock, 250);

      // Test second variant
      final secondVariant = variants[1];
      expect(secondVariant.combinations['Type'], 'Tailed');
      expect(secondVariant.combinations['Color'], 'Black');
      expect(secondVariant.mrpPrice, 265);
      expect(secondVariant.sellingPrice, 200);
      expect(secondVariant.stock, 500);
    });

    test('should extract options from variant combinations', () {
      final apiResponse = {
        "product_variants": [
          {
            "combinations": {
              "Type": "Non tailed",
              "Color": "Grey"
            },
            "mrp_price": 265,
            "selling_price": 200,
            "stock": 250,
          },
          {
            "combinations": {
              "Type": "Tailed",
              "Color": "Black"
            },
            "mrp_price": 265,
            "selling_price": 200,
            "stock": 500,
          },
          {
            "combinations": {
              "Type": "Tailed",
              "Color": "White"
            },
            "mrp_price": 300,
            "selling_price": 250,
            "stock": 100,
          }
        ]
      };

      final product = Product.fromJson(apiResponse);
      final variants = product.variants!
          .map((variantJson) => ProductVariant.fromJson(variantJson))
          .toList();

      // Simulate option extraction logic
      Map<String, Set<String>> optionsMap = {};
      
      for (final variant in variants) {
        variant.combinations.forEach((key, value) {
          if (!optionsMap.containsKey(key)) {
            optionsMap[key] = <String>{};
          }
          optionsMap[key]!.add(value);
        });
      }

      // Verify extracted options
      expect(optionsMap.containsKey('Type'), true);
      expect(optionsMap.containsKey('Color'), true);
      
      expect(optionsMap['Type']!.contains('Non tailed'), true);
      expect(optionsMap['Type']!.contains('Tailed'), true);
      expect(optionsMap['Type']!.length, 2);
      
      expect(optionsMap['Color']!.contains('Grey'), true);
      expect(optionsMap['Color']!.contains('Black'), true);
      expect(optionsMap['Color']!.contains('White'), true);
      expect(optionsMap['Color']!.length, 3);
    });

    test('should show variant pricing instead of product pricing', () {
      final apiResponse = {
        "product_name": "Test Product",
        "mrp_price": 1, // Product-level pricing (should be ignored)
        "selling_price": 1,
        "in_stock": 1,
        "product_variants": [
          {
            "combinations": {
              "Size": "M"
            },
            "mrp_price": 2000, // Variant pricing (should be used)
            "selling_price": 1500,
            "stock": 10,
          }
        ]
      };

      final product = Product.fromJson(apiResponse);
      final variant = ProductVariant.fromJson(product.variants!.first);

      // Product-level pricing should be ignored in favor of variant pricing
      expect(product.mrpPrice, 1); // Original product price
      expect(product.sellingPrice, 1);
      
      // Variant pricing should be used for display
      expect(variant.mrpPrice, 2000); // Actual price to display
      expect(variant.sellingPrice, 1500);
      expect(variant.stock, 10);
    });

    test('should handle discount calculation from variant data', () {
      final variant = ProductVariant(
        combinations: {'Type': 'Tailed', 'Color': 'Black'},
        mrpPrice: 265,
        sellingPrice: 200,
        stock: 500,
      );

      final discountPercentage = (((variant.mrpPrice - variant.sellingPrice) / variant.mrpPrice) * 100).round();
      expect(discountPercentage, 25); // (265-200)/265 * 100 ≈ 24.5% → 25%
    });

    test('should identify when product has multiple variants for selection', () {
      final singleVariantProduct = Product(
        variants: [
          {
            "combinations": {"Size": "M"},
            "mrp_price": 1000,
            "selling_price": 800,
            "stock": 10,
          }
        ]
      );

      final multiVariantProduct = Product(
        variants: [
          {
            "combinations": {"Size": "M", "Color": "Red"},
            "mrp_price": 1000,
            "selling_price": 800,
            "stock": 10,
          },
          {
            "combinations": {"Size": "L", "Color": "Blue"},
            "mrp_price": 1200,
            "selling_price": 900,
            "stock": 5,
          }
        ]
      );

      // Single variant shouldn't show variant selection UI
      final singleVariants = singleVariantProduct.variants!
          .map((v) => ProductVariant.fromJson(v))
          .where((v) => v.combinations.isNotEmpty)
          .toList();
      expect(singleVariants.length, 1);

      // Multiple variants should show variant selection UI
      final multiVariants = multiVariantProduct.variants!
          .map((v) => ProductVariant.fromJson(v))
          .where((v) => v.combinations.isNotEmpty)
          .toList();
      expect(multiVariants.length, 2);
    });
  });
}
