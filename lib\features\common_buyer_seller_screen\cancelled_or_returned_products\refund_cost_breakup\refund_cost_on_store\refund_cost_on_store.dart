import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/refund_cost_breakup/refund_cost_on_store/refund_cost_on_store_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/refund_cost_breakup/refund_cost_on_store/refund_cost_on_store_common_widgets.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/refund_amount_calculation_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class RefundCostOnStore extends StatefulWidget {
  final SubOrder suborder;
  final Order order;
  final List<RefundAmountCalculationDetail> refundAmountCalculationDetailList;
  const RefundCostOnStore({Key? key, required this.suborder, required this.order, required this.refundAmountCalculationDetailList,}) : super(key: key);

  @override
  State<RefundCostOnStore> createState() => _RefundCostOnStoreState();
}

class _RefundCostOnStoreState extends State<RefundCostOnStore> {
  //region Bloc
  late RefundCostOnStoreBloc refundCostOnStoreBloc;
  //endregion
  //region Init
  @override
  void initState() {
    //print(widget.refundAmountCalculationDetailList.length);
    refundCostOnStoreBloc = RefundCostOnStoreBloc(context,widget.suborder,widget.order,widget.refundAmountCalculationDetailList);
    super.initState();
  }
  //endregion
  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:(){
        //CommonMethods.closeKeyboard(context);
      },
      child:body(),
    );
  }

  // endregion



//region Body
  Widget body(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        //Title
       Container(
         color: AppColors.textFieldFill1,
         padding: const EdgeInsets.all(10),
         child: Row(
           mainAxisSize: MainAxisSize.max,
           mainAxisAlignment: MainAxisAlignment.spaceBetween,
           children: [
             Text(AppStrings.refundCostOnStore,style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),),
           SvgPicture.asset(AppImages.exclamation)
           ],
         ),
       ),
        detail(),


      ],
    );
  }
//endregion


//region Detail
Widget detail(){
    return Container(
      padding: const EdgeInsets.only(left: 17,right: 17,bottom: 20,top: 15),
      // margin: const EdgeInsets.symmetric(vertical: 15),
      child: ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          itemCount: refundCostOnStoreBloc.refundAmountCalculationDetailList.length,
          shrinkWrap: true,
          itemBuilder:(context,index){
        return RefundCostOnStoreCommonWidgets.info(title:refundCostOnStoreBloc.refundAmountCalculationDetailList[index].orderBreakupItemText!, price: refundCostOnStoreBloc.refundAmountCalculationDetailList[index].orderBreakupItemValue!);
      }),
      // child: ListView.builder(
      //   physics: const NeverScrollableScrollPhysics(),
      //   shrinkWrap: true,
      //
      // ),
    );
}
//endregion










}
