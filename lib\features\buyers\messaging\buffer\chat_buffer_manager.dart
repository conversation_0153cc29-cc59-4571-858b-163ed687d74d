import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:swadesic/models/chat_info.dart';
// import 'package:swadesic/models/message.dart';
import 'package:swadesic/features/buyers/messaging/buffer/message_repository.dart';
import 'package:http/http.dart' as http;
import 'dart:developer' as developer;

class ChatBufferManager {
  final Map<String, List<Message>> _messageBuffer = {};
  final Map<String, ChatInfo> _chatBuffer = {};
  final SharedPreferences _prefs;
  final String _messageBufferKey = 'message_buffer';
  final String _chatBufferKey = 'chat_buffer';

  ChatBufferManager(this._prefs);

  Future<void> addMessage(Message message) async {
    try {
      developer.log(
        '[ENTER] addMessage(): Adding message to buffer',
        name: 'ChatBufferManager'
      );

      final chatId = message.chat_id;
      if (!_messageBuffer.containsKey(chatId)) {
        _messageBuffer[chatId] = [];
      }

      // Add message to buffer
      _messageBuffer[chatId]!.add(message);

      // Sort messages by sequence number
      _messageBuffer[chatId]!.sort((a, b) => a.sequence_number.compareTo(b.sequence_number));

      // Save to SharedPreferences
      await _saveMessageBuffer();

      developer.log(
        '[EXIT] addMessage(): Message added successfully',
        name: 'ChatBufferManager'
      );
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] addMessage(): ${e.toString()}',
        name: 'ChatBufferManager',
        stackTrace: stackTrace
      );
      rethrow;
    }
  }

  Future<void> addChat(ChatInfo chat) async {
    try {
      developer.log(
        '[ENTER] addChat(): Adding chat to buffer',
        name: 'ChatBufferManager'
      );

      _chatBuffer[chat.chat_id] = chat;
      await _saveChatBuffer();

      developer.log(
        '[EXIT] addChat(): Chat added successfully',
        name: 'ChatBufferManager'
      );
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] addChat(): ${e.toString()}',
        name: 'ChatBufferManager',
        stackTrace: stackTrace
      );
      rethrow;
    }
  }

  Future<void> _saveMessageBuffer() async {
    try {
      final bufferData = _messageBuffer.map((chatId, messages) {
        return MapEntry(
          chatId,
          messages.map((msg) => msg.toJson()).toList(),
        );
      });

      await _prefs.setString(_messageBufferKey, jsonEncode(bufferData));
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] _saveMessageBuffer(): ${e.toString()}',
        name: 'ChatBufferManager',
        stackTrace: stackTrace
      );
      rethrow;
    }
  }

  Future<void> _saveChatBuffer() async {
    try {
      final bufferData = _chatBuffer.map((chatId, chat) {
        return MapEntry(chatId, chat.toJson());
      });

      await _prefs.setString(_chatBufferKey, jsonEncode(bufferData));
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] _saveChatBuffer(): ${e.toString()}',
        name: 'ChatBufferManager',
        stackTrace: stackTrace
      );
      rethrow;
    }
  }

  Future<void> loadFromStorage() async {
    try {
      developer.log(
        '[ENTER] loadFromStorage(): Loading buffers from storage',
        name: 'ChatBufferManager'
      );

      // Load message buffer
      final messageBufferJson = _prefs.getString(_messageBufferKey);
      if (messageBufferJson != null) {
        final data = jsonDecode(messageBufferJson) as Map<String, dynamic>;
        data.forEach((chatId, messages) {
          _messageBuffer[chatId] = (messages as List)
              .map((msg) => Message.fromJson(msg as Map<String, dynamic>))
              .toList();
        });
      }

      // Load chat buffer
      final chatBufferJson = _prefs.getString(_chatBufferKey);
      if (chatBufferJson != null) {
        final data = jsonDecode(chatBufferJson) as Map<String, dynamic>;
        data.forEach((chatId, chat) {
          _chatBuffer[chatId] = ChatInfo.fromJson(chat as Map<String, dynamic>);
        });
      }

      developer.log(
        '[EXIT] loadFromStorage(): Buffers loaded successfully',
        name: 'ChatBufferManager'
      );
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] loadFromStorage(): ${e.toString()}',
        name: 'ChatBufferManager',
        stackTrace: stackTrace
      );
      rethrow;
    }
  }

  Future<void> clearBuffers() async {
    try {
      developer.log(
        '[ENTER] clearBuffers(): Clearing all buffers',
        name: 'ChatBufferManager'
      );

      _messageBuffer.clear();
      _chatBuffer.clear();
      await _prefs.remove(_messageBufferKey);
      await _prefs.remove(_chatBufferKey);

      developer.log(
        '[EXIT] clearBuffers(): Buffers cleared successfully',
        name: 'ChatBufferManager'
      );
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] clearBuffers(): ${e.toString()}',
        name: 'ChatBufferManager',
        stackTrace: stackTrace
      );
      rethrow;
    }
  }

  List<Message> getMessagesForChat(String chatId) {
    return _messageBuffer[chatId] ?? [];
  }

  List<Message> getMessages(String chatId) {
    return getMessagesForChat(chatId);
  }

  Future<void> addMessages(String chatId, List<Message> messages) async {
    try {
      developer.log(
        '[ENTER] addMessages(): Adding multiple messages to buffer',
        name: 'ChatBufferManager'
      );

      for (final message in messages) {
        await addMessage(message);
      }

      developer.log(
        '[EXIT] addMessages(): Added ${messages.length} messages successfully',
        name: 'ChatBufferManager'
      );
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] addMessages(): ${e.toString()}',
        name: 'ChatBufferManager',
        stackTrace: stackTrace
      );
      rethrow;
    }
  }

  Future<void> addChats(List<ChatInfo> chats) async {
    try {
      developer.log(
        '[ENTER] addChats(): Adding multiple chats to buffer',
        name: 'ChatBufferManager'
      );

      for (final chat in chats) {
        await addChat(chat);
      }

      developer.log(
        '[EXIT] addChats(): Added ${chats.length} chats successfully',
        name: 'ChatBufferManager'
      );
    } catch (e, stackTrace) {
      developer.log(
        '[ERROR] addChats(): ${e.toString()}',
        name: 'ChatBufferManager',
        stackTrace: stackTrace
      );
      rethrow;
    }
  }

  Future<void> updateChat(ChatInfo chat) async {
    return addChat(chat); // Since addChat already handles updates
  }

  ChatInfo? getChatById(String chatId) {
    return _chatBuffer[chatId];
  }

  ChatInfo? getChatInfo(String chatId) {
    return getChatById(chatId);
  }

  List<ChatInfo> getAllChats() {
    return _chatBuffer.values.toList();
  }

  List<ChatInfo> getAllChatsFromBuffer() {
    return getAllChats();
  }

  Future<void> clearCache() async {
    return clearBuffers();
  }
}