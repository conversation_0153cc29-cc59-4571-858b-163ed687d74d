import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:swadesic/features/common_buyer_seller_screen/shipping_history/shipping_history_screen.dart';
import 'package:swadesic/features/mobile_number_otp/login_otp/resend_count_down/resend_count_down.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/additional_detail/addetional_detail.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/sellerNeedResolution/seller_need_resolution.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/shipping_in_progress/widget/update_delivery_status/update_delivery_status_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class UpdateDeliveryStatusScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  // final ShippingInProgressBloc shippingInProgressBloc;
  final SellerSubOrderBloc sellerSubOrderBloc;
  final bool isSeller;
  final Order order;
  final String packageNumber;

  const UpdateDeliveryStatusScreen(
      {Key? key,
      required this.suborderList,
      required this.sellerSubOrderBloc,
      required this.isSeller,
      required this.order,
      required this.packageNumber})
      : super(key: key);

  @override
  State<UpdateDeliveryStatusScreen> createState() =>
      _UpdateDeliveryStatusScreenState();
}

class _UpdateDeliveryStatusScreenState
    extends State<UpdateDeliveryStatusScreen> {
  // region Bloc
  late UpdateDeliveryStatusBloc updateDeliveryStatusBloc;

  // endregion

  // region Init
  @override
  void initState() {
    updateDeliveryStatusBloc = UpdateDeliveryStatusBloc(context, widget.order,
        widget.sellerSubOrderBloc, widget.suborderList, widget.packageNumber);
    updateDeliveryStatusBloc.init();
    super.initState();
  }
  // endregion

  //region Did update widget
  @override
  void didUpdateWidget(covariant UpdateDeliveryStatusScreen oldWidget) {
    updateDeliveryStatusBloc = UpdateDeliveryStatusBloc(context, widget.order,
        widget.sellerSubOrderBloc, widget.suborderList, widget.packageNumber);
    updateDeliveryStatusBloc.init();
    super.initState();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    updateDeliveryStatusBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Stack(
        children: [
          StreamBuilder<bool>(
              stream: updateDeliveryStatusBloc.bottomSheetRefresh.stream,
              builder: (context, snapshot) {
                return SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: SingleChildScrollView(
                      child: Column(mainAxisSize: MainAxisSize.min, children: [
                        // Padding(
                        //   padding: const EdgeInsets.symmetric(
                        //     horizontal: 20,
                        //   ),
                        //   child: Align(
                        //       alignment: Alignment.centerLeft,
                        //       child: appText(AppStrings.updateDeliveryStatus,
                        //           fontSize: 16, fontWeight: FontWeight.w600, color: AppColors.appBlack2, maxLine: 3, fontFamily: AppConstants.rRegular)),
                        // ),
                        // verticalSizedBox(20),
                        // Container(height: 100,width: 100,color: Colors.green,)
                        ShippingHistoryScreen(
                          dropDownTitle: "Delivery update history",
                          pNumber: widget.suborderList.first.packageNumber!,
                          orderNumber: widget.order.orderNumber!,
                        ),
                        verticalSizedBox(20),
                        deliveryEstimate(),
                        verticalSizedBox(20),
                        completeDelivery(),
                        verticalSizedBox(20),
                        selfAndLogistic(),
                        AppCommonWidgets.bottomListSpace(context: context),
                      ]),
                    ));
              }),
          StreamBuilder<UpdateDeliveryState>(
              stream: updateDeliveryStatusBloc.updateDeliveryCtrl.stream,
              builder: (context, snapshot) {
                if (snapshot.data == UpdateDeliveryState.Loading) {
                  return InkWell(
                    onTap: () {},
                    child: Container(
                      color: Colors.transparent,
                      height: double.infinity,
                      width: double.infinity,
                    ),
                  );
                }
                return const SizedBox();
              }),
        ],
      ),
    );
  }

//region Estimated delivery date
  Widget deliveryEstimate() {
    return StreamBuilder<bool>(
        stream: updateDeliveryStatusBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppTitleAndOptions(
                  title: AppStrings.estimatedDeliveryDate,
                  titleOption: SvgPicture.asset(AppImages.exclamation),
                  option: //Calender
                      Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          updateDeliveryStatusBloc.estimatedDeliveryDate,
                          style: AppTextStyle.settingText(
                              textColor: AppColors.appBlack),
                        ),
                        horizontalSizedBox(30),
                        InkWell(
                          onTap: () {
                            updateDeliveryStatusBloc.onTapCalender(
                                packageNumber:
                                    widget.suborderList.first.packageNumber!);
                          },
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: const BoxDecoration(
                                color: AppColors.textFieldFill1,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(10))),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SvgPicture.asset(
                                  AppImages.calender,
                                  color: AppColors.appBlack,
                                ),
                                horizontalSizedBox(10),
                                Text(
                                  AppStrings.updateDeliveryDate,
                                  style: AppTextStyle.access0(
                                      textColor: AppColors.appBlack),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                verticalSizedBox(13),
              ],
            ),
          );
        });
  }
//endregion

//region Complete delivery
  Widget completeDelivery() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.completeDelivery,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            // child: appText(AppStrings.completeDelivery,
            //     fontSize: 16, fontWeight: FontWeight.w600, color: AppColors.writingColor2, maxLine: 3, fontFamily: AppConstants.rRegular),
            //
          ),
        ),
        verticalSizedBox(10),

        ///Drop down
        InkWell(
          onTap: () {
            updateDeliveryStatusBloc.onTapProductList();
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 6.5),
            decoration: BoxDecoration(
                color: AppColors.lightestGrey2,
                borderRadius: BorderRadius.circular(7)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ///Drop down
                Container(
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      //Grand total
                      Expanded(
                          child: Text(
                        AppStrings.productListYouAreDeliver,
                        style: AppTextStyle.settingHeading1(
                            textColor: AppColors.appBlack),
                      )),
                      horizontalSizedBox(10),
                      updateDeliveryStatusBloc.isProductListVisible
                          ? RotatedBox(
                              quarterTurns: 3,
                              child: RotatedBox(
                                  quarterTurns: 4,
                                  child: SvgPicture.asset(AppImages.arrow3)),
                            )
                          : RotatedBox(
                              quarterTurns: 1,
                              child: SvgPicture.asset(AppImages.arrow3))
                    ],
                  ),
                ),

                ///List of product
                Visibility(
                    visible: updateDeliveryStatusBloc.isProductListVisible,
                    child: subOrderList()),

                ///Selected product count
                Visibility(
                  visible: !updateDeliveryStatusBloc.isProductListVisible,
                  child: Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      child: Text(
                        widget.suborderList.length == 1
                            ? "${widget.suborderList.length} suborder"
                            : "${widget.suborderList.length} suborders",
                        style: AppTextStyle.settingText(
                            textColor: AppColors.appBlack),
                      )),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
//endregion

//region Product List

//endregion

  //region Product list
  Widget productList2() {
    return StreamBuilder<bool>(
        stream: updateDeliveryStatusBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return Column(
            children: [
              ///Drop down
              InkWell(
                onTap: () {
                  updateDeliveryStatusBloc.onTapProductList();
                },
                child: Container(
                  color: AppColors.lightWhite3,
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      //Grand total
                      Expanded(
                          child: appText(AppStrings.productListYouAreDeliver,
                              color: AppColors.writingColor2,
                              fontWeight: FontWeight.w400,
                              fontFamily: "LatoBoldItalic",
                              fontSize: 14,
                              style: FontStyle.italic)),
                      horizontalSizedBox(10),
                      updateDeliveryStatusBloc.isProductListVisible
                          ? RotatedBox(
                              quarterTurns: 2,
                              child: SvgPicture.asset(AppImages.downArrow),
                            )
                          : SvgPicture.asset(AppImages.downArrow)
                    ],
                  ),
                ),
              ),
              verticalSizedBox(10),
              Visibility(
                  visible: updateDeliveryStatusBloc.isProductListVisible,
                  child: bottomSheetViewSubOrderList(
                      suborderList: widget.suborderList,
                      context: context,
                      subOrderStatus: widget.suborderList.first.suborderStatus))
            ],
          );
        });
  }
//endregion

  //region Sub orders list
  Widget subOrderList() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.suborderList.length,
        shrinkWrap: true,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppCommonWidgets.subOrderInfo(
                  isCheckBoxVisible: false,
                  subOrder: widget.suborderList[index],
                  onTap: () {
                    // widget.waitingForConfirmationBloc.onSelectSubOrder(widget.suborderList[index]);
                    // widget.waitingForConfirmationBloc.onSelectSubOrder(widget.suborderList[index]);
                  },
                  context: context),
              index == widget.suborderList.length - 1
                  ? const SizedBox()
                  : const Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                      child: Divider(
                        color: AppColors.lightGray,
                        height: 1,
                        thickness: 1,
                      ),
                    )
            ],
          );
        });
  }

//endregion

  //region Self and logistic
  Widget selfAndLogistic() {
    // Check if any suborder has deliveryBySwadesic as true
    bool isDeliveryBySwadesic = widget.suborderList
        .any((subOrder) => subOrder.deliveryBySwadesic ?? false);

    // Return empty container if delivery is by Swadesic
    if (isDeliveryBySwadesic) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        Visibility(visible: !widget.isSeller, child: logisticDelivery()),
        Visibility(visible: widget.isSeller, child: selfDelivery()),
        additionalDetails(),
      ],
    );
  }
  //endregion

//region Logistic delivery
  Widget logisticDelivery() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              sellerAllOrderActionButton(
                  buttonName: "Mark as Delivered",
                  onPress: () {
                    updateDeliveryStatusBloc
                        .markAsDelivered(widget.suborderList);
                  },
                  colors: AppColors.brandBlack,
                  textColor: AppColors.appWhite),
            ],
          ),
          verticalSizedBox(15),
          Row(
            children: [
              sellerAllOrderCancelButton(
                  buttonName: AppStrings.couldNotComplete,
                  onPress: () {
                    //Mark all suborder as true
                    CommonMethods.subOrderSelectUnSelectAll(
                        subOrderList: updateDeliveryStatusBloc.suborderList,
                        isSelectAll: true);
                    //Cancel
                    updateDeliveryStatusBloc.onTapCanNotComplete(
                        subOrderNumbers:
                            CommonMethods.sellerSelectedSubOrderNumberList(
                                updateDeliveryStatusBloc.suborderList));
                  }),
            ],
          ),
        ],
      ),
    );
  }
//endregion

//region Self delivery
  Widget selfDelivery() {
    return StreamBuilder<bool>(
        stream: updateDeliveryStatusBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                //Self delivery title

                AppTitleAndOptions(
                  title: AppStrings.selfDeliveryWillRequireOtp,
                  titleOption: SvgPicture.asset(AppImages.exclamation),
                  option: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //Send delivery otp nad field
                      updateDeliveryStatusBloc.sendOrderOtpStatus.isOtpSent &&
                              !updateDeliveryStatusBloc.isOtpExpired()
                          ? enterOtp()
                          : sendOtpButton(),
                      verticalSizedBox(20),
                      //Mark as delivery and cancel
                      Row(
                        children: [
                          Expanded(
                            child: AppCommonWidgets.activeButton(
                                buttonName: "Mark as Delivered",
                                buttonColor: updateDeliveryStatusBloc.isOtpValid
                                    ? AppColors.brandBlack
                                    : AppColors.textFieldFill1,
                                textColor: updateDeliveryStatusBloc.isOtpValid
                                    ? AppColors.appWhite
                                    : AppColors.borderColor1,
                                onTap: () {
                                  updateDeliveryStatusBloc.isOtpValid
                                      ? updateDeliveryStatusBloc
                                          .markAsDelivered(widget.suborderList)
                                      : CommonMethods.toastMessage(
                                          "Please confirm OTP", context);
                                }),
                          )

                          // sellerAllOrderActionButton(
                          //     buttonName: "Mark as Delivered",
                          //     onPress: () {
                          //       updateDeliveryStatusBloc.isOtpValid
                          //           ? updateDeliveryStatusBloc.markAsDelivered(widget.suborderList)
                          //           : CommonMethods.toastMessage("Please confirm OTP", context);
                          //     },
                          //     colors: updateDeliveryStatusBloc.isOtpValid ? AppColors.brandGreen : AppColors.inActiveGreen,
                          //     textColor: AppColors.appWhite),
                        ],
                      ),
                      verticalSizedBox(20),
                      Row(
                        children: [
                          Expanded(
                            child: AppCommonWidgets.inActiveButton(
                                buttonName: AppStrings.couldNotComplete,
                                onTap: () {
                                  //Mark all suborder as true
                                  CommonMethods.subOrderSelectUnSelectAll(
                                      subOrderList:
                                          updateDeliveryStatusBloc.suborderList,
                                      isSelectAll: true);
                                  //Cancel
                                  updateDeliveryStatusBloc.onTapCanNotComplete(
                                      subOrderNumbers: CommonMethods
                                          .sellerSelectedSubOrderNumberList(
                                              updateDeliveryStatusBloc
                                                  .suborderList));
                                }),
                          ),

                          // sellerAllOrderCancelButton(buttonName:AppStrings.couldNotComplete, onPress: () {
                          //   //Mark all suborder as true
                          //   CommonMethods.subOrderSelectUnSelectAll(subOrderList: updateDeliveryStatusBloc.suborderList,isSelectAll: true);
                          //   //Cancel
                          //   updateDeliveryStatusBloc.onTapCanNotComplete(subOrderNumbers: CommonMethods.sellerSelectedSubOrderNumberList(updateDeliveryStatusBloc.suborderList)) ;
                          //
                          // }),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }
//endregion

  //region Enter otp
  Widget enterOtp() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: PinCodeTextField(
                  //auto
                  controller: updateDeliveryStatusBloc.otpCtrl,
                  length: 4,
                  onChanged: (String value) {
                    //If otp text is less then 4 then disable the mark as delivery button
                    if (updateDeliveryStatusBloc.otpCtrl.text.length < 4) {
                      updateDeliveryStatusBloc.isOtpValid = false;
                      updateDeliveryStatusBloc.bottomSheetRefresh.sink
                          .add(true);
                    }
                    // mobileNumberOtpBloc. otpText = value;
                  },
                  onCompleted: (value) {
                    updateDeliveryStatusBloc.verifyOtp();
                  },
                  inputFormatters: <TextInputFormatter>[
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4)
                  ],
                  appContext: context,
                  autoDismissKeyboard: true,
                  animationType: AnimationType.none,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.box,
                    // Use box shape for rounded rectangles
                    borderRadius: BorderRadius.circular(10),
                    // Rounded corners
                    fieldHeight: 60,
                    // Set height
                    fieldWidth: 50,
                    // Set width to be slightly narrower
                    activeColor: Colors.transparent,
                    // No border when active, matches your design
                    inactiveColor: Colors.transparent,
                    // No border when inactive
                    selectedColor: Colors.transparent,
                    // No border when selected
                    activeFillColor: AppColors.textFieldFill1,
                    // Background color when active
                    inactiveFillColor: AppColors.textFieldFill1,
                    // Background color when inactive
                    selectedFillColor:
                        AppColors.textFieldFill1, // Background color when selected
                  ),
                  cursorColor: AppColors.appBlack,
                  backgroundColor: Colors.transparent,
                  keyboardType: TextInputType.number,
                  enableActiveFill: true,
                  // Enables the fill for each box
                  textStyle: AppTextStyle.access1(textColor: AppColors.appBlack),
            ),),
            const SizedBox(
              width: 140,
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ResendCountDown(
              isResendMobileOtp: true,
              onTap: () {
                updateDeliveryStatusBloc.otpCtrl.clear();
                updateDeliveryStatusBloc.sendOtp(
                    packageNumber: widget.packageNumber);
              },
            ),
          ],
        ),
        otpHasBeenSent(),
      ],
    );
  }
  //endregion

  //region Send otp button
  Widget sendOtpButton() {
    return AppCommonWidgets.activeButton(
        buttonName: "Send delivery OTP",
        buttonColor: AppColors.brandBlack,
        onTap: () {
          updateDeliveryStatusBloc.otpCtrl.clear();
          updateDeliveryStatusBloc.sendOtp(packageNumber: widget.packageNumber);
        });
    // return  CupertinoButton(
    //   padding: EdgeInsets.zero,
    //   onPressed: () {
    //     updateDeliveryStatusBloc.otpCtrl.clear();
    //     updateDeliveryStatusBloc.sendOtp(packageNumber: widget.packageNumber);
    //   },
    //
    //   // child: Container(
    //   //   padding: const EdgeInsets.all(10),
    //   //   decoration: const BoxDecoration(color: AppColors.brandGreen, borderRadius: BorderRadius.all(Radius.circular(10))),
    //   //   child: Text("Send delivery OTP",style: AppTextStyle.access0(textColor: AppColors.appWhite),),
    //   // ),
    // );
  }
  //endregion

  //region Can't deliver

//region Additional detail
  Widget additionalDetails() {
    return Visibility(
        visible: updateDeliveryStatusBloc.isCanNotDelivery,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            //Issue
            Container(
              margin: const EdgeInsets.only(top: 30, bottom: 20),
              padding: const EdgeInsets.symmetric(vertical: 20),
              alignment: Alignment.center,
              child: Text(
                AppStrings.issueWithCompletingDelivery,
                style: AppTextStyle.sectionHeading(
                    textColor: AppColors.writingBlack),
              ),
            ),
            AdditionalDetails(
              order: updateDeliveryStatusBloc.order,
              sellerSubOrderBloc: updateDeliveryStatusBloc.sellerSubOrderBloc,
              subOrderList: updateDeliveryStatusBloc.suborderList,
            ),
            needResolution(),
          ],
        ));
  }
//endregion

//region Need resolution
  Widget needResolution() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SellerNeedResolution(
        order: updateDeliveryStatusBloc.order,
        sellerSubOrderBloc: updateDeliveryStatusBloc.sellerSubOrderBloc,
        escalationReason: AppStrings.sellerUnableToDeliver,
        suborderList: updateDeliveryStatusBloc.suborderList,
      ),
    );
  }
//endregion

//region Otp has been sent
  Widget otpHasBeenSent() {
    return StreamBuilder<UpdateDeliveryState>(
        stream: updateDeliveryStatusBloc.updateDeliveryCtrl.stream,
        initialData: UpdateDeliveryState.Loading,
        builder: (context, snapshot) {
          //If success
          // if (snapshot.data == UpdateDeliveryState.Success) {
            return Container(
              alignment: Alignment.centerLeft,
              child: RichText(
                textScaleFactor: MediaQuery.textScaleFactorOf(
                    AppConstants.globalNavigator.currentContext!),
                maxLines: 5,
                overflow: TextOverflow.ellipsis,
                text: TextSpan(
                  style: AppTextStyle.heading3Regular(
                      textColor: AppColors.brandBlack),
                  children: [
                    TextSpan(
                      style: AppTextStyle.contentHeading0(
                          textColor: AppColors.brandBlack),
                      text:
                          "Delivery OTP has been sent to Buyer's Swadesic App. Ask them to check their Notifications.",
                    ),
                    // WidgetSpan(child: Text(productName,
                    // maxLines: 1,
                    // ))
                  ],
                ),
              ),
              // child: Text(
              //     AppStrings.deliveryOtpHasBeenSent,
              //   maxLines: 3,
              //   style: AppTextStyle.heading3regular(textColor: AppColors.brandGreen),
              // ),
            );
          // }
          // return const SizedBox();
        });
  }
//endregion
}
