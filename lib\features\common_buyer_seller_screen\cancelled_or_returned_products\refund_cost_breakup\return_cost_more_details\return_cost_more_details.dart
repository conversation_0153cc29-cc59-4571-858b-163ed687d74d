import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/refund_cost_breakup/return_cost_more_details/return_cost_more_details_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/cancelled_or_returned_products/refund_cost_breakup/return_cost_more_details/return_cost_more_details_common_widgets.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/refund_amount_calculation_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class RefundCostMoreDetails extends StatefulWidget {
  final SubOrder suborder;
  final Order order;
  final List<RefundAmountCalculationDetail> requestAndSuborderStatus;
  final List<RefundAmountCalculationDetail> productAndPayment;
  final List<RefundAmountCalculationDetail> storeRefundPolicy;

  const RefundCostMoreDetails({Key? key, required this.suborder, required this.order, required this.requestAndSuborderStatus, required this.productAndPayment, required this.storeRefundPolicy,}) : super(key: key);

  @override
  State<RefundCostMoreDetails> createState() => _RefundCostMoreDetailsState();
}

class _RefundCostMoreDetailsState extends State<RefundCostMoreDetails> {
  //region Bloc
  late ReturnCostMoreDetailsBloc returnCostMoreDetailsBloc;
  //endregion
  //region Init
  @override
  void initState() {
    returnCostMoreDetailsBloc = ReturnCostMoreDetailsBloc(context,widget.suborder,widget.order,widget.requestAndSuborderStatus,widget.productAndPayment,widget.storeRefundPolicy);
    super.initState();
  }
  //endregion
  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:(){
        //CommonMethods.closeKeyboard(context);
      },
      child:body(),
    );
  }

  // endregion



//region Body
  Widget body(){
    return Container(
      color: AppColors.textFieldFill1,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //Title
          Container(
            color: AppColors.textFieldFill1,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            child: Text(AppStrings.moreDetails,style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),),
          ),
          details(),


        ],
      ),
    );
  }
//endregion


//region Details
Widget details(){
    return Container(
  padding: const EdgeInsets.only(left: 17,right: 17,bottom: 20,top: 15),

      child: Column(
        children: [
          requestAndSubOrderStatus(),
          verticalSizedBox(10),
          productAndPayment(),
          verticalSizedBox(10),
          storeRefundPolicy(),

        ],
      ),
  );
}
//endregion


//region Request and sub order status
Widget requestAndSubOrderStatus(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(AppStrings.requestAndSubOrder,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),
        verticalSizedBox(12),
        ListView.builder(
          padding: EdgeInsets.zero,
            itemCount: returnCostMoreDetailsBloc.requestAndSuborderStatus.length,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context,index){
          return ReturnCostMoreDetailsCommonWidgets.info(title:"${returnCostMoreDetailsBloc.requestAndSuborderStatus[index].orderBreakupItemText}", value:"${returnCostMoreDetailsBloc.requestAndSuborderStatus[index].orderBreakupItemValue}");

        }),
      ],
    );
}
//endregion


//region Product and payment
  Widget productAndPayment(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(AppStrings.productAndPayment,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),
        verticalSizedBox(12),
        ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: returnCostMoreDetailsBloc.productAndPayment.length,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context,index){

              //If last index then show rupee symbol in value
              if(returnCostMoreDetailsBloc.productAndPayment.length-1 == index){
                return ReturnCostMoreDetailsCommonWidgets.info(title:"${returnCostMoreDetailsBloc.productAndPayment[index].orderBreakupItemText}", value:"${returnCostMoreDetailsBloc.productAndPayment[index].orderBreakupItemValue}");
              }

              return ReturnCostMoreDetailsCommonWidgets.info(title:"${returnCostMoreDetailsBloc.productAndPayment[index].orderBreakupItemText}", value:"${returnCostMoreDetailsBloc.productAndPayment[index].orderBreakupItemValue}");

            }),
      ],
    );
  }
//endregion


//region Store refund policy
  Widget storeRefundPolicy(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(AppStrings.storeRefundPolicy,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),
        verticalSizedBox(12),
        ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: returnCostMoreDetailsBloc.storeRefundPolicy.length,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context,index){
              return ReturnCostMoreDetailsCommonWidgets.info(title:"${returnCostMoreDetailsBloc.storeRefundPolicy[index].orderBreakupItemText}", value:"${returnCostMoreDetailsBloc.storeRefundPolicy[index].orderBreakupItemValue}");

            }),
      ],
    );
  }
//endregion












}
