import 'package:swadesic/util/app_constants.dart';

class AppStrings {
  //region Splash Screen
  static String splashText = "A nation is as strong as an individual is.";
  static String appName = "Swadesic";
  static String aNation = "A nation is as strong as its \nindividual is.";

  //endregion

  //region Mobile number Screen
  static String supportShop =
      "Support-Shop-Create \nSmall & local business of India";
  static String createShopDiscuss =
      "Create | Shop | Chat\nSmall businesses of India";
  static String mobileNumber = "Mobile number";
  static String shareUsYourMobileNumber = "Share us your mobile number";
  static String completeOnBoarding = "Complete Onboarding";
  static String letsGetToKnowYou = "Let's get to know you!";
  static String emailAddress = "Email address";
  static String shareUsYourEmail = "Share us your email address";
  static String sendOtp = "Send OTP";

  //endregion
  // region OTP Screen
  static String paymentIsSecure = "Payments are securely \nmanaged by Swadesic";
  static String otp = "OTP";
  static String mobileOtp = "Enter OTP received on Mobile";
  static String enterMobileOtp = "Enter your Mobile OTP";
  static String enterYourEmailOtp = "Enter your Email OTP";
  static String emailOtp = "Enter OTP received on Email";
  static String getIn = "Get in >";
  static String connectWithGoogle = "Connect with Google account";
  static String getStarted = "Get started";
  static String continueWithEmail = "or continue with Email";
  static String resend = "resend OTP";
  static String resendButton = "Resend OTP";
  static String Continue = "Continue";

  //endregion

  //region Hint text
  static String hintTextSearchVith =
      "Search with order #, products, stores etc..,";

  //endregion

  //region Seller Home Screen

  static String youHaveNot =
      "You haven’t viewed any stores yet.\nSearch and explore small businesses of Bharat";
  static String noResults = "No results";
  static String noTicketsYet = "No tickets Yet";
  static String noOneIsHere = "No one is here";
  static String youHaveNotFollowed =
      "You haven’t followed or supported anyone yet. \nExplore Swadesic";
  static String yourStoreOpen = "Your store is open for orders";
  static String yourStoreClose = "Your store is closed for orders";
  static String viewStore = "View store";
  static String searchStore = "Search stores";
  static String searchProducts = "Search products";
  static String searchPeople = "Search people";
  static String accounts = "Accounts";
  static String goToSupport = "Go to support";
  static String personal = "Personal";
  static String newVersionApp = "New version of Swadesic is available!";
  static String feed = "Feed";
  static String tapToInstall = "Tap to install ";
  static String verify = "Verify";
  static String business = "Business";
  static String individual = "Individual";
  static String needGst = "Needs GSTIN and national-wide sales is available";
  static String needIndividual =
      "Needs Individual PAN and state-wide sales is available";
  static String howStoreHelpYou = "how stores help you sell?";
  static String createNewStore = "Create a new store";
  static String shareStoreLink = "Share store link";
  static String inviteYourFriendsAndCustomers =
      "Invite your friends and customers to the Store";
  // static String shareProfile = "Share profile";
  static String shareApp = "Share app";
  static String shareProfile = "Share profile";
  static String shareStore = "Share store";
  static String share = "Share";
  static String home = "Home";
  static String community = "Community";
  static String yourStoreIsCurrentlyOpen = "Your store is currently open.";
  static String yourStoreIsCurrentlyClosed = "Your store is currently closed.";
  static String storeProfile = "Store profile";
  static String settings = "Settings";
  static String myPlan = "My Plan";
  static String autoWithdrawalEnabled = "Auto Withdrawal Enabled";
  static String autoWithdrawalDisable = "Auto Withdrawal Disabled";
  static String yourEarningWillBeAuto =
      "Your earnings will be automatically withdrawn once they exceed ₹500.";
  static String deActivateAndDeleteStore = "Deactivate or delete my store";
  static String exitSurvey = "Exit survey";
  static String changeProfilePicture = "change profile picture";
  static String appAndSecurity = "App & Security";
  static String downloadApp = "Download app";
  static String targetGender = "Targeted gender";
  static String notOpenForOrderNow = "Not open for orders now";
  static String letsMakeGreatAgain = "Let’s make Bharat great again";
  static String thisIsSellerExclusive =
      "This is a seller exclusive app version \nreleased to onboard sellers \nwho are early adopters. \nThis app may be unstable as it is \nstill an early roll-out. \nFeel free to provide your \nfeedback in Support";
  static String pleaseProvideDeliveryOrReturnSetting =
      "Please provide the delivery or return settings information for this product; it appears to be missing";
  static String theSelectionWillSaveTheSameSettings =
      "This selection will save the same settings for both your store and products as well";
  static String noteTheLabelsAreGiving =
      "Note: The labels are given primarily from people running the stores, but you may report if you find discrepancy in their disclosure";
  static String swadeshiMadeLabel = "Swadeshi Made label";
  static String swadeshiBrandLabel = "Swadeshi Brand label";
  static String swadeshiOwnedLabel = "Swadeshi Owned label";
  static String explanation = "Explanation";
  static String congratulationYourStoreHasBeenCreated =
      "Congratulations! Your store has been created successfully.";
  static String congratulationYourStoreIsLiveNow =
      "Congratulations! Your store is live right now - happy selling! 😊";
  static String yourReturnSettingForThisProductIsSaved =
      "Your return settings for this product is saved";
  static String yourLabelsForThisProductIsSaved =
      "Your Swadeshi labels for this product is saved";
  static String yourLabelsForThisStoreIsSaved =
      "Your Swadeshi labels for this store is saved";
  static String yourReturnSettingForThisStoreIsSaved =
      "Your return settings for this store is saved";
  static String yourDeliverySettingForThisProductIsSaved =
      "Your delivery settings for this product is saved";
  static String yourDeliverySettingForThisStoreIsSaved =
      "Your delivery settings for this store is saved";
  static String fontSizeChangeMessage =
      "Welcome to our app! We believe in giving you the power to personalize your experience, and that's why we've introduced a brand-new feature – Font Size Customization. With this feature, you can tailor the font size to your liking, ensuring that the app's content is comfortable and easy to read for you. Whether you prefer larger text for enhanced readability or a more compact font for a sleek look, it's all at your fingertips. Simply access the 'Font Size' option in the settings menu, and you'll have the freedom to adjust the text size to your heart's content. However, please keep in mind that when the text sizes are at extremes, it can break some elements in the app and may not look good. We're excited to provide you with a more personalized and accessible app experience!\n\nDisclaimer: When the text sizes are at extremes, it can break some elements in the app and may not look good.";
  static String noTransactionResultFound = "No transactions results found";
  static String noResultsFound = "No results found";
  static String pleaseEnterValidAmount = "Please enter valid amount";
  static String pleaseCheckYourAccountBalance =
      "Please check your account balance";
  static String yourStoreWillBeCompletelyDeleted =
      "Your store will be completely deleted from the app and if you want to create a store you should do it from scratch and upload products again."
      "\n\nTrust score & seller level will reset to default values"
      "\n\nData related to orders & payments will be stored for consumer & legal purposes."
      "\nYou can’t proceed this step without an clearing your Account balance.";
  static String closingTheStoreWillMakeThe =
      "Closing the store will make the store visible, allowing people to support but doesn’t take orders until you open the store again.";
  static String noContentYet = "No content yet";
  static String soonYouCanSee = "Soon you can see public reviews here";
  static String noMoreData = "You have reached end of the search results";
  static String speakWithCustomerToResolve =
      "Speak with your customer to resolve the issue to avoid a bad customer experience with your brand.";
  static String goToActivateCheckList = "Go to Activation checklist";
  static String selectedBusinessCategory = "Select business category";
  static String selectedBusinessType = "Select business type";
  static String selectBusinessType = "Select business type";
  static String selectCategory = "Select category";
  static String viewDeliveryLocations = "View delivery locations";
  static String selectProfession = "Select profession";
  static String searchCity = "Select city";
  static String selectLogisticPartner = "Select logistic partner";
  static String orderingOnSwadesicIsCommingSoon =
      "Swadesic ordering is soon coming. Meanwhile, build audience & customer engagement around your brand";
  static String selectVpa = "Select VPA";
  static String feeResponsibility = "Fee responsibility";
  static String pleaseRateTheProductFirst = "Please rate the product first";
  static String addYourReview = "Add your review";
  static String thisGroupNameIsAlready =
      "This group name is already used in this order. Add a different one.";
  static String deliveryEstimate = "Delivery estimate:";
  static String closestEstimateDeliveryDate =
      "Closest estimate delivery date: ";
  static String closestReturnEstimateDeliveryDate =
      "Closest estimate return date: ";
  static String estimatedReturnPickupDate = "Estimated return pickup date: ";
  static String estimatedReturnDeliveryDate =
      "Estimated return delivery date: ";
  static String returnedOn = "Returned on: ";
  static String estimatedPickupDate = "Estimated pickup date: ";
  static String deliveredSuccessfully = "Delivered successfully:";
  static String deliveredOn = "Delivered on:";
  static String delivered = "delivered";
  static String reasonToCancel = "Reason to cancel (sent to seller)";
  static String storeWillReceiveTheOrderWhenThePayment =
      "Store will receive the order when the payment is success.";
  static String reason = "Reason";
  static String typeOfVerification = "Type of verification";
  static String autoCancelledAfter36Hour = "Auto cancelled after 36 hours";
  static String addImages = "Add images";
  static String inviteCode = "Invite code";
  static String victory =
      "\"Victory or defeat is not important, but the fight itself is everything.\" Netaji Subhas Chandra Bose";

  static String invalideOtp = "Invalid OTP";
  static String invalidPinCode = "Invalid pin code";
  static String rateTheProduct = "Rate the product and send";
  static String app = "App";
  static String cancelledOrderHasNoFee = "Cancelled orders, has no fee";
  static String noFeeForOrderBelow =
      "No fee for orders below ₹500 in cart value";
  static String normalOrders = "Normal orders, charged with flat fee ₹50";
  static String cancelTheOrder = "Cancel the Order & Issue Refund";
  static String confirmTheOrderAgain =
      "Confirm the Order Again to Ship Again";
  static String getAnUpdateTitle = "Get an update";
  static String add = "Add";
  static String addressAdded = "Address added";
  static String addressUpdated = "Address updated";
  static String phoneAndEmailCanNotEmpty = "Phone and email can't be empty";
  static String phoneNumberCanNotEmpty = "Phone number can't be empty";
  static String remove = "Remove";
  static String addSellerNote = "Add seller note";
  static String appVersion = "App version";
  static String shopWithCare = "Shop with care";
  static String showAnyways = "Show anyways";
  static String continueAnyway = "Continue";
  static String thisStoreIsNotYetPublic =
      "This new store is not yet fully public till they get their first 5 orders or 30 supporters. Browse and Buy with Caution as they might be testing. Report if you spot anything unusual.";
  static String updateTheLatestApp = "Update to the latest app\n(recommended)";
  static String appIsUpToDate = "App is up to date.";
  static String editProfile = "Edit profile";
  static String personalInformation = "Personal information";
  static String withdraw = "Withdraw";
  // static String delayInDelivery = "Delay in delivery";
  static String escalationSuccessful =
      "Escalation successful, you will receive escalation ID in notifications.";
  static String newOrders = "New orders";
  static String waitingOrders = "Waiting orders";
  static String returnsAndCancel = "Returns & Cancel";
  static String sellerCanNOtCompleteDelivery = "Seller can't complete delivery";
  static String suggest = "Suggest";
  static String addProduct = "Add product";
  static String completeTrustCenterToAddProducts =
      "Complete trust center to add products";
  static String addAProduct = "Add a product";
  static String addAStory = "Add a story";
  static String addAPost = "Add a post";
  static String editProduct = "Edit products";
  static String editDetails = "Edit details";
  static String noteProductsCancelledOrreturn =
      "Note: Products cancelled or returned are included in Missed revenue and not in waiting to be released or In process sections ";
  static String guides = "Guides";
  static String invoice = "Invoice";
  static String viewInvoice = "View invoice";
  static String deleteProducts = "Delete products";
  static String createExternalProductReviewLink = "Create external product review link";
  static String deleteProduct = "Delete product";
  static String hiddenProduct = "Hidden products";
  static String editStore = "Edit store";
  static String manageProduct = "Manage products";
  static String notifications = "Notifications";
  static String createPost = "Create post";
  static String editPost = "Edit post";
  static String theSubOrderIsRemoved =
      'The suborder is removed from shipping group and moved to "Confirmed, not yet shipped". Please ship it quickly';
  static String markAllAsRead = "Mark all as read";
  static String markAsDelivered = "Mark as Delivered";
  static String orderIsConfirmed = "Order is confirmed";
  static String ordersAreConfirmed = "Orders are confirmed";
  static String markAsShipped = "Mark as Shipped";
  static String allOrders = "All orders";
  static String noOrderProvidedByCustomer = "No notes provided by customer";
  static String orderHistory = "Order history";
  static String productOrderHistory = "Product order history";
  static String followersCustomers = "Followers \n& Customers";
  static String speakWithYourCustomer =
      "Speak with your customer and clear things out 🙂";
  static String changeOrderStatus =
      "Change order status to any status from cancel to delivered";
  static String trustCenter = "Trust Center";
  static String updateEmail = "Update email";
  static String speakWithStoreAndUnderstand =
      "Speak with the store and understand the product return process if they don’t reach out in 24 hours from acceptance.";
  static String swadesicIntendsToBring =
      "Swadesic intends to bring transparency between customers and stores. Trust, but verify.";
  static String changeOrderStatusTo = "Change order status to";
  static String weRecommendYouToSpeakWithStore =
      "We recommend you to speak with store and clear out any issues before reaching out to Swadesic support";
  static String chnageOrderStatusButton = "Change order status";
  static String refundWillBeShortly = "Refund will be shortly initiated";
  static String accountBalance = "Account balance";
  static String failedToFetch = "Unable to fetch";
  static String accountBalanceAndRewards = "Account balance & Rewards";
  static String bankAccount = "Bank accounts";
  static String sendToBank = "Send to Bank";
  static String manageBankAccounts = "Manage Bank accounts";
  static String unlocked = "Unlocked";
  static String speakWithStore = "Speak with Store";
  static String inProcess = "In process";
  static String lifeTime = "Lifetime revenue";
  static String refundCostWillBeReducedFromTheRefundAmount =
      "Refund cost will be reduced from the refund amount \nRefund cost :\n• Transaction fee charged by payment gateway (usually 2% if any)\n• Delivery fee if product is shipped before you request cancel";
  static String missed = "Missed revenue";
  static String pleaseEnterValidGst = "Please enter a valid gst number";
  static String requestedVerification = "Requested Verification";
  static String verifiedBySwadesic = "Verified by Swadesic";
  static String pleaseEnterValidPan = "Please enter a valid pan number";
  static String pleaseAddSignature =
      "Please add your signature or upload from gallery.";
  static String paymentFromOrders = "Payments from orders";
  static String yourUpiAppReceivedANotification =
      "Your UPI app received a notification from us.\nPlease pay there, then proceed back.\nWe will wait.";
  static String sortAndFilter = "Sort and filter";
  // static String pleasePayUsingUpiApp = "Please pay using your UPI app and come back. We will wait.";
  static String waitingToRelease = "Waiting to release";
  static String contact = "Contact";
  static String swadeshiLabel = "Swadeshi labels";
  static String swadeshiOwned = "Swadeshi Owned";
  static String swadeshiOwnedLable = "Swadeshi Owned label";
  static String swadeshiLabelsHelpConsumersUnderstand =
      "Swadeshi labels help consumers understand and make an informed decision on how Swadeshi the product, brand or the business is.";
  static String document = "Document";
  static String theCoreIntention =
      "The core intention of Swadesic is to create a compelling eco system for Swadeshi brands & support a self-reliant Bharat (Atmanirbhar Bharat) in long run. ";
  static String paste = "Paste";
  static String pleaseEnterValidUrl = "Please enter valid url";
  static String pleaseAddSwadeshiLabelForThisProduct =
      "Please add Swadeshi labels of this product";
  static String storeLocation = "Store location";
  static String name = "Name";
  static String referralCode = "Referral code";
  static String deactivateTheStore = "Deactivate the store";
  static String closeTheStore = "Close the store";
  static String deleteTheStore = "Delete the store";
  static String title = "Title";
  static String selfDeliveryWillRequireOtp =
      "Self delivery will require OTP verification from customer.";
  static String selectProductDeliveryMethod = "Select product delivery method";
  static String groupName = "Group name";
  static String pleaseWaitWhileWeAreFetchingDeliveryLocation =
      "Please wait while we retrieve all the locations in Bharat.";
  static String gstVerification = "GSTIN verification";
  static String idVerification = "ID Verification";
  static String unableToLoadThis = "Unable to load this";
  static String gstIn = "GSTIN";
  static String addANewBankAccount = "Add a new bank account";
  static String firstName = "First name";
  static String inRupees = "in rupees";
  static String pleaseBeAsDetailedAsPossible =
      "Please be as detailed as possible. What did you expect and what happened instead?";
  static String giveUsABrief = "Give us a brief with keywords";
  static String pkg = "PKG";
  static String returnReason = "Return reason";
  static String lastName = "Last name";
  static String diaplayName = "Display name";
  static String aboutYou = "About you";
  static String websiteLinks = "Website link";
  static String userName = "Username";
  static String phoneNumber = "Phone Number";
  static String phone = "Phone";
  static String sellersWithoutAVerifiedGst =
      "Sellers without a verified GSTIN can only do sales in their own state (as mentioned in Trust center). Store would still get national-wide visibility and customers across Bharat can contact you.";
  static String email = "Email";
  static String address = "Address";
  static String pleaseAddYourGst =
      "Please add your GST in 'Trust center' to deliver all over the India";
  static String documentHintText = "Eg: GST, Incorporation, licenses etc..,";
  static String city = "City";
  static String profession = "Profession";
  static String category = "Category";
  static String selectBusinessCategory = "Select business category";
  static String pleaseCompleteContactAndLocation =
      "Please complete contact and location";
  static String selectTheCategory = "Select the category";
  static String pinCode = "Pincode";
  static String enterPickupPinCode = "Enter pickup pincode";
  static String state = "State";
  static String bringBusiness =
      "Bring your business online with Swadesic \nat 0% commissions*";
  static String zeroCommissions = "0% commissions*";
  static String orderConfirmed = "order confirmed";
  static String confirmed = "confirmed";
  static String orderCancelled = "order cancelled";
  static String orderGrouped = "order grouped";
  static String trackingDetailUpdated = "Tracking detail updated";
  static String tapOnTheProduct =
      "Tap on the products to see their entire history";
  static String noSearchResultFount = "No search result found";
  static String noStoreFound = "No store found";
  static String noProductFound = "No product found";
  static String noPeopleFound = "No people found";
  static String amountCreditedAgainst = "Amount credited against an order";
  static String thisStoreIsNotreceivingOrder =
      "This store is not receiving orders at the moment";
  static String amountDebitAgainst = "Amount debited against an order";
  static String escalateToSwdesicForReturn = "Escalate to Swadesic for return";
  static String needResolutionRageTheTicketForRefund =
      "Need resolution. Raise ticket for refund";
  static String needResolutionDelayInDelivery =
      "Need resolution. Delay in delivery";
  static String issueWithRefundAmount = "Issue with refund amount";
  static String withdrawalRequest = "Withdrawal request";
  static String couldNotComplete = "Can't complete delivery";
  static String cancelledOrReturnedProducts = "Cancelled or Returned products";
  static String refundCostBreakup = "Refund cost breakup";
  static String deliveryServiceBy = "Delivery service by";
  static String deliveryPersonDetail = "Delivery person details";
  static String additionalNotes = "Additional notes";
  static String additionalNotesOnThis = "Additional notes on this";
  static String trackingNumberAndDetail = "Tracking number & details";
  static String inCaseOfDelay = "In case of delay, tap here";
  static String deliveryDateUpdated = "Delivery dated updated";
  static String acceptReturn = "Accept Return";
  static String returnPickupDateUpdated = "Return pickup date updated";

  //region Inventory Options
  static String deleteVariant = "Delete Variant";
  static String areYouSureDeleteVariant = "Are you sure you want to delete this variant? This action cannot be undone.";
  static String variantDeletedSuccessfully = "Variant deleted successfully";
  static String failedToDeleteVariant = "Failed to delete variant";
  static String noVariantsAdded = "No variants added yet";
  static String pleaseAddProductOptionsFirst = "Please add product options first";
  static String optionWithThisNameAlreadyExists = "Option with this name already exists";
  static String thisVariantAlreadyExists = "This variant already exists";
  //endregion

  //endregion

  //region Seller Profile Screen
  static String favouriteStore = "Favourite stores";
  static String recentStore = "Recent stores";
  static String recentlyVisited = "Recently visited stores";
  static String myWishList = "My wishlist";
  static String myOrders = "My orders";
  static String myPayment = "My payments";
  static String myReviews = "My reviews";
  static String whatsNext = "What’s next?";
  static String suggestions = "Suggestions";

  //endregion

  //region Membership Screen
  static String membership = "Membership";
  static String thatIsStrange =
      "That's strange that we \nsuggestions couldn't find any phone number.";
  static String weArePulling =
      "We are pulling your friend's \n numbers, please wait.";
  static String pleaseSendInviteUsing =
      "Please send invite using phone number.";

//endregion

  //region Tool tip messages
  static String updateDeliverDate =
      "Your delivery date has passed.\nKindly provide an updated timeline \nfor your deliverable.";
  static String updateReturnStatus = "Update return status";
  static String returnTrackingDetail = "Return tracking detail";
  static String returnStatus = "Return status";
  static String selectReturnStatus = "Select return status";
  static String trackingDetails = "Tracking details";
  static String returnInProgress = "Return in progress";
  static const String update = "Update";
  static String targetedGenderIsForCuration =
      "Target gender is used for recommendations \nand will not limit your product's visibility \nwhen searched or in explore sections";

  //endregion

  //region Add product Screen
  static String createStore = "Create store";
  static String accountMyOrders = "Accounts- My stores";
  static String next = "Next";
  static String findUsingContact = "Find using contacts";
  static String brandName = "Brand Name";
  static String brandNameHint = "Brand name";
  static String viewTheLink = "View the link";
  static String invalidUrl = "Invalid url";
  static String enterValidEmailAddress = "Enter valid email address";

  static String productName = "Product Name";
  static String productCategoryTitle = "Product category";
  static String productSlug = "Product slug";
  static String productSlugHint =
      "Your identifier in lower case for product url";
  static String productCode = "Product code";
  static String productPolicies = "Product policies";
  static String buyOnSwadesicConsiderations = "Buy on Swadesic considerations";
  static String productCodeHint = "Your identifier for product";
  static String productCategoryHint =
      "Add category/sub-category/specific category";
  static String productNameHint =
      "Product name with all useful information in 2 lines ";

  static String productDescription = "Product description";
  static String productDescriptionHint = "Add a detailed product description";

  static String promotionLink = "Product demo or promotion link";
  static String promotionLinkHint =
      "provide a link that provides more details- preferably video";

  static String inStock = "In stock";
  static String partialRefundForCustomer =
      "Partial refund for customers.\n • Delivery fee refunded if product not shipped.\n • Transaction fee responsibility and delivery fee of shipped product falls on either by seller or customer based on settings.";
  static String refundInFullForCustomer =
      "Refunds in full for customers.\n • Delivery fee refunded if product not shipped.\n • Transaction fee responsibility and delivery fee of shipped product falls on either by seller or customer based on settings.";
  static String inStockHint = "in stock quantity";
  static String refundInFullForCustomerStoreCovers =
      "Refunds in full for customers. Store covers transaction fee when an order is auto-cancelled, reflected as a deduction in account balance.";
  static String partialRefundForCustomerText =
      "Partial refund for customer. Customer refund amount will be deducted with transaction fee when an unconfirmed order is cancelled by buyer. ";
  static String notesCanNotBeEmpty = "Notes can't be empty";
  static String note = "Note";
  static String whenYouAreOrderingMultiple =
      "When you are ordering from multiple stores, your order request is split into separate orders and sent to the stores. You can track them in My orders using order numbers";
  static String refundInFullForCancelledBeforeConfirm =
      "Refunds in full for customers. Store covers transaction fee when an unconfirmed order is cancelled by seller , reflected as a deduction in account balance.";

  static String pricing = "Pricing";
  static String why = "why?";
  static String mrp = "MRP";
  static String yourAnswareHelpUs =
      "Your answers help us prioritize our offerings";
  static String thisHelpUsPersonalize =
      "This helps us personalize the products & content";
  static String skip = "Skip";
  static String actualPrice = "Actual price(MRP)";
  static String pleaseCompleteTrustCenterDetail =
      "Please complete trust center details first";
  static String sellingPrice = "Selling price";
  static String individualPanIsKept =
      "Individual PAN is kept private from your customers";
  static String seller = "Seller";
  static String sellerOrSwadesicShipping = "Seller or Swadesic shipping";
  static String returnBySeller = "Return by seller";
  static String logisticsPartner = "Logistics partner";
  static String swadesicShipping = "Swadesic Shipping";
  static String youReceive = "you receive";
  static String whitelabelCharge = "Swadesic charges you 0% commissions now";
  static String oneUnitSellAt = "1 unit of this product sells at ₹";
  static String discount = "Discount: ";
  static String unlockToEditGstIn = "Unlock to edit GSTIN";
  static String setReturnSettings = "Set Return settings";
  static String tagStories = "Tag Stories";
  static String setDeliverySettings = "Set Fulfillment settings";
  static String addSwadesicLabels = "Add Swadeshi labels";
  static String inventoryOptions = "Inventory options";
  static String inventoryOptionsUpdated = "Inventory options updated successfully";
  static String doesThisProductHaveOptions = "Does this product have options? (like Size, Pack, etc...)";
  static String productOptions = "Product Options";
  static String productVariants = "Product Variants";
  static String noOptionsAddedYet = "No options added yet";
  static String noVariantsAddedYet = "No variants added yet";
  static String addAnOption = "Add an Option";
  static String editOption = "Edit Option";
  static String createAVariant = "Create a Variant";
  static String editVariant = "Edit Variant";
  static String optionName = "Option name";
  static String optionValues = "Option values";
  static String enterOptionValue = "Enter option value";
  static String selectProductOptions = "Select product options";
  static String inStockQuantity = "In stock quantity";
  static String addVariant = "Add Variant";
  static String updateVariant = "Update Variant";
  // static String pleaseAddProductOptionsFirst = "Please add product options first";
  // static String optionWithThisNameAlreadyExists = "Option with this name already exists";
  // static String thisVariantAlreadyExists = "This variant already exists";
  static String pleaseEnterOptionName = "Please enter option name";
  static String pleaseAddAtLeastOneOptionValue = "Please add at least one option value";
  static String pleaseSelectOption = "Please select";
  static String pleaseEnterValidStockQuantity = "Please enter valid stock quantity";
  static String pleaseEnterValidMRP = "Please enter valid MRP";
  static String pleaseEnterValidSellingPrice = "Please enter valid selling price";
  static String failedToLoadVariants = "Failed to load variants";
  static String oneUnitOfThisProductSellsAt = "1 unit of this product sells at ₹";
  static String areYouSure = "Are you sure about this ?";
  static String freeDeliveryConditions = "Free delivery conditions";
  static String hashTags = "Hashtags (add upto 20) ";
  static String optional = "(optional)";
  static String logout = "Logout";
  static String contactPermissionDisclaimer =
      "By allowing access to your contacts, you can easily connect with friends and family who are already on Swadesic, discover their stores, and stay updated with their latest products";
  static String needResolutionMarkNotDelivered =
      "Need resolution. Mark Not delivered";
  static String yes = "Yes";
  static String no = "No";
  static String getBackToApp = "Cancel, get back to the app";
  static String accept = "Accept";
  static String yesLogout = "Yes, logout";
  static String denied = "Denied";
  static String cancel = "Cancel";
  static String later = "Later";
  static String cancelled = "cancelled";
  static String returned = "returned";
  static String noReturn = "No return";
  static String noReturnAvailableOnThisProduct =
      "No return available on this product";
  static String notAccepted = "Not accepted";
  static String returnButton = "Return";
  static String df = "DF";
  static String cancelAll = "Cancel all";
  static String confirm = "Confirm";
  static String confirmAll = "Confirm all";
  static String confirmThisAlone = "Confirm this alone";
  static String removeFromThisShippingGroup = "Remove from this shipping group";
  static String cancelThisAlone = "Cancel this alone";
  static String confirmAllSubOrder = "Confirm all";
  static String groupToShip = "Group to ship";
  static String grouped = "grouped";
  static String ship = "Ship";
  static String affiliateCommissionCanNotBeEmpty =
      "Affiliate commission can not be empty";
  static String affiliateCommissionMustBeGreaterThanZero =
      "Affiliate commission must be greater than 0";
  static String affiliateCommissionShouldBeLessThanSellingPrice =
      "Affiliate commission should be less than selling price";
  static String productSlugCanNotBeEmpty = "Product slug can not be empty";
  //endregion

  //region Add Product Preview Screen
  static String publish = "Publish";
  static String buyNow = "Buy now";
  static String buyAtStore = "Buy at store";
  static String refreshForUpdate = "Refresh for update";
  static String switchToBuyer = "Switch to buyer";
  static String viewDetails = "View details";
  static String moreDetails = "More details";
  static String showHide = "show/hide";
  static String returnPolicy = "Return policy";
  static String returnPickupBy = "Return pickup by";
  static String refundIfCancellationOrReturn =
      "Refund if cancellation or return";
  static String returnWindow = "Return window";
  static String returnCondition = "Return condition";
  static String ifSellerCancelsOrAutoCancels =
      "If seller cancels or Auto-cancels";
  static String deliveryBy = "Delivery by";
  static String delivery = "Delivery";
  static String deliverBy = "Deliver by";
  static String deliveryPartner = "Delivery partner";
  static String returnCostPaidBy = "Return cost paid by";
  static String productVersion = "Product version";
  static String createdOn = "Created on";
  static String productAddedOn = "Product added on";
  static String productPreview = "Product Preview";
  static String createATicket = "Create a ticket";
  static String pastTickets = "Past tickets";
  static String commentHint = "write a comment, review or a question";
  static String comments = "Comments";
  static String comment = "Comment";
  static String likes = "Likes";
  static String productComment = "Product comments";
  static String noCommentYet = "No comments yet";
  static String noReviewsYet = "No reviews yet";
  static String addAReview = "Add a review";
  static String viewMore = "View more replies";
  static String doNotShy = "Don’t be shy! add yours ";
  static String askSellerAbout = "Got a Question? Ask the Store";
  static String post = "post";
  static String posts = "Posts";
  static String postTitle = "Post";
  static String postButton = "Post";
  static String creditedToAccountBalanceOn = "Credited to Account balance on";
  static String needMoreDetails = "Need more details?";
  static String askSeller = "Ask seller about this product";
  static String send = "send";
  static String sendNow = "Send Now";
  static String sendButton = "Send";
  static String sendAmount = "Send";
  static String addTheAccount = "Add the Account";
  static String previousProduct = "previous product";
  static String nextProduct = "next product";
  static String edit = "Edit";
  static String delete = "Delete";
  static String deleteAccount = "Delete account";

  //endregion

  //region Buyer Home
  static String supportedStores = "Supported stores";
  static String findYourCustomersOnSwadesic = "Find your customers on Swadesic";
  static String findYourFriendsOnSwadesic = "Find your friends on Swadesic";
  static String inviteForAnAtmaNirvar = "Invite for an Atmanirbhar Bharat";
  static String findFriendsAndTheirStores =
      "Find friends and their stores on Swadesic";
  static String findYourFriendsAndCustomers =
      "Find your Friends and Customers to the Store";
  static String supportAndSuggestion = "Support and suggestion";
  static String suggestAnIdea = "Suggest an idea";
  static String exploreStores = "Explore stores";
  static String swadesicMembership = "Swadesic Membership";
  static String swadesicSupport = "Swadesic Support ";
  static String detectMyLocation = "Detect my location";
  static String useGps = "Use GPS";

  //endregion

  //region Delivery settings
  static List<String> timeToPrepare = <String>[
    "within a day",
    "within two day",
    "within three day",
    "within four day",
    "within five day",
    "within six day",
    "within seven day",
    "within eight day",
    "within nine day",
    "within ten day",
    "within eleven day",
    "within twelve day",
  ];
  //endregion

  //region Buyer Search Screen
  static String stores = "Stores";
  static String products = "Products";
  static String people = "People";
  static String viewAll = "View all";
  static String viewMoreStores = "view more stores";
  static String viewMoreProduct = "view more product";
  static String viewMorePeople = "view more people";

  //endregion

  //region Buyer View Products
  static String searchResults = "search results: ";
  static String search = "Search";
  static String startChat = "start a chat with stores & people";
  static String searchOrderProduct =
      "Search with order #, products, stores etc..,";
  static String searchRecentlyVisited = "search recently visited stores";
  static String helpful = "helpful";
  static String reply = "reply";
  static String searchSupportedStore = "Search supported stores";
  static String searchVisitedStore = "Search visited stores";

  //endregion

  //region Buyer product Share
  static String writeAMessage = "write a message..";
  static String writeAComment = "write a comment, question..";
  static String hideComments = "hide comments";

  //endregion

  //region Buyer View Store Screen
  static String followers = "followers";
  static String followersCap = "Followers";
  static String follow = "Follow";
  static String following = "Following";
  static String sales = "sales";
  static String sale = "sale";
  static String reviewLowerCase = "review";
  static String reviewsLowerCase = "reviews";
  static String saleLowerCase = "sale";
  static String salesLowerCase = "sales";
  static String chnageDeliveryPinCode = "Change delivery pin code ";
  static String accountLinkedTo = "Account linked to";
  static String postAs = "post as";
  static String returnAndRefundSettings = "Return and refund settings";
  static String youWillAlsoSeeStores =
      "You will also see stores created by your contacts below";
  static String customers = "customers";
  static String knowMore = "know more";
  static String appreciateYour = "Appreciate your efforts!! ";
  static String sellerConfirmedOrder = "Seller confirmed order(s)";
  static String productWillSoonBe = "Product will soon be packed and shipped";
  static String sellerInitiatedPickup = "Seller initiated pickup";
  static String productWillSoonBeShipped =
      "Your product is being packed and soon be shipped.";
  static String sellerCancelledThisOrderAfterConfirmation =
      "Seller cancelled this order after confirmation. ";
  static String youCancelledThisOrderBeforeShipping =
      "You cancelled this order before shipping.";
  static String youCancelledThisOrderAfterShipping =
      "You cancelled this order after product is shipped.";
  static String refundProcessWillStart = "Refund process will start shortly. ";
  static String hereYouCanSeeProductsSeller =
      "Here you can see products with different return statuses. Keep it up to date and give your customer a best experience and avoid bad brand reputation";
  static String hereYouCanSeeProductsBuyer =
      "Here you can see products with different return statuses. All seller updates can be seen here. Feel free to reach out to Swadesic support if needed.";
  static String refundProcessWillStartAfterSellerReceives =
      "Refund process will start after seller receives the product from delivery services. If it is delivered to you, we will pickup a schedule.";
  static String refundProcessWillStartOurApology =
      "Refund process will start shortly. Our appologies, seller will be penalized for the disaapointment.";
  static String sorryForTheDisappointment =
      "Sorry for the disappointment, seller didn’t confirm the below order(s)";
  static String fullRefundProcess = "Full refund process will start shortly. ";
  static String youCancelledTheBelowOrder = "You cancelled the below order(s)";
  static String customerCancelledTheBelowOrder =
      "Customer cancelled the below order(s).";
  static String yourProductReached =
      "Hurray! Your product(s) reached your customer!!\nYour payment will be initiated after return period.";
  static String customersLove =
      "Customers love faster deliveries!\nPack the product(s) and start delivery";
  static String scheduledPickup =
      "Please prepare the package as per the mentioned dimensions and stick labels on the package before Pickup time";
  static String youStartedShipping =
      "You started shipping. Please do timely updates for keeping your customers informed and happy";
  static String locations = "locations";
  static String belowProductsGotAutomaticallyCancelled =
      "Below Product(s) got automatically cancelled because seller didn't respond to order in 36 hours";
  static String shippingStarted = "Shipping started";
  static String trustScore = "trust score";
  static String deliveryOtpHasBeenSent =
      "Delivery OTP has been sent to Buyer's delivery contact, please ask OTP from Buyer.";
  static String support = "Support";
  static String help = "Help";
  static String loading = "Loading..";
  static String switchAccount = "Switch account";
  static String userReward = "User rewards";
  static String feedbackItem = "Feedback item";
  static String supporter = "supporter";
  static String supporting = "Supporting";
  static String thanksForLettingUsKnow =
      "Thanks for letting us know. We'll get back to you if required";
  static String customerDetail = "Customer details";
  static String orderDetail = "Order details";
  static String supporters = "supporters";
  static String returnReasonCanNotBeEmpty = "Return reason can't be empty";
  static String message = "Message";
  static String showLessButtonText = "Show less";
  static String trust = "Trust";
  static String dashboard = "Dashboard";
  static String moveToWishList = "Move to wishlist";
  static String copyProductLink = "Copy product link";
  static String copyPostLink = "Copy post link";
  static String copyLink = "Copy link";
  static String deletePost = "Delete post";
  static String shareTheProduct = "Share the product";
  static String sendAdMessage = "Send as message";
  static String reportTheProduct = "Report the product";
  static String reportThePost = "Report the post";
  static String completeCheckList =
      "Complete the checklist to go live and start selling.";
  static String completeStoreActivation = "Complete Store Activation";
  static String activationChecklistToGoLive = "Activation checklist to Go live";
  static String storeActivation = "Store Activation";
  static String completeCheckListToActivateStore =
      "Complete the checklist to go live.";
  static String pleaseAddAtLeast3Products = "Please add at least 3 products";
  static String pleaseCompleteTrustCenterDetails =
      "Please complete trust center details";
  static String pleaseCompleteTheCheckList =
      "Please complete the checklist first";
  static String pleaseActivateYourStore = "Please activate your store first";
  static String goLiveMakeYour = "Go live - Make your store public";
  static String pleaseAddYourStoreAndDelivery =
      "Please add your store delivery and return setting.";

  //endregion

  //region Shopping Cart Screen
  static String shoppingCart = "Shopping cart";
  static String savedAddress = "Saved address";
  static String storeName = "Store name: ";
  static String storeCoverImage = "Store cover image";
  static String noAddressSelected = "No addresss is selected for delivery";
  static String storeNameText = "Store name";
  static String storeHandle = "Store handle";
  static String lowerCaseOnlyAllowed = "lower case only";
  static String availableTo = "available to";
  static String refundPolicy = "refund policy";
  static String howMuchCanITrust = "How much can I trust this seller? ";
  static String checkStores = "Check store’s ";
  static String quantity = "quantity :";
  static String removeFromCart = "Remove from cart";
  static String paymentDoneBy = "Payments are securely managed by Swadesic";
  static String orderPaymentDetails = "Order payment details";
  static String cartTotal = "Cart totals";
  static String shippingFees = "shipping fees";
  static String total = "Total";
  static String youCanEnableOrdering =
      "You can enable ordering once you get verified";
  static String theStoreIsNotActiveYet = "The store is not active yet.";
  static String unableToLoadOrderDetails = "Unable to load order details";
  static String okay = "Okay";
  static String goBackToHome = "Go back to home";
  static String orderAgain = "Order again";
  static String sellerNote = "Add seller note";
  static String sellerNotes = "Seller note";
  static String deliveryNotes = "Delivery notes";
  static String addressDetails = "Address details";
  static String savedAddresses = "Saved addresses";
  static String deliveryAddress = "Delivery address ";
  static String save = "save";
  static String saveChanges = "Save changes";
  static String saved = "Saved";
  static String discard = "Discard";
  static String disclaimer = "Disclaimer";
  static String addNewAddress = "Add a new address";
  static String selectOrAdd = "Select or Add an address";
  static String viewSelectAndAddAddress = "View, select and add addresses";
  static String completeTrustCenter = "Complete trust center";
  static String addDeliveryNote = "Add delivery note";
  static String secureCheckout = "Secure checkout";
  static String selectDeliveryAddressAndAddressAndPayment =
      "Select Delivery Address & Payment";
  static String security = "Security";
  static String weGotYourBack =
      "We've got your back, we saved your notes and settings along with your product";

  //endregion
  ///Escalation reason
  //region Escalation reasons
  static const sellerUnableToDeliver = "[CREATEDBY:SELLER]UNABLE_TO_DELIVER";
  static const sellerRefundOnHoldEscalationReason =
      "[CREATEDBY:SELLER]POST_REFUND_HOLD";
  static const buyerRefundOnHoldEscalationReason =
      "[CREATEDBY:BUYER]REFUND_ON_HOLD";
  static const delayInDelivery = "[CREATEDBY:BUYER]DELAY_IN_DELIVERY";
  static const buyerDidNotReceivedEscalationReason =
      "[CREATEDBY:BUYER]DID_NOT_RECEIVED_PRODUCT";

  //endregion

  //region Completed Order Screen
  static String orderPlacedSuccessfully = "Yay!! Order placed successfully.";
  static String weWillInformSeller =
      "We will inform seller about your order  - Swadesic";
  static String thankYouForSupporting =
      "Thank you for supporting small businesses of ";
  static String bharat = "Bharat! ";
  static String threeHand = "🙏🙏🙏";
  static String thankYouForYourOrder = "Thank you for your order";
  static String waitingButDoNotWorry = "Waiting, but don’t worry.";
  static String weWillUpdateYou =
      "We will update you as soon as we know the status";
  static String orderCancelledAndWeDoNot =
      "Order cancelled and we didn’t expect this.";
  static String sorryForTheTnconvenience =
      "Sorry for the inconvenience. Please try after sometime.";

  static String appreciateYourTrust = "Appreciate your trust in ";
  static String whitelabel = "Swadesic";
  static String deliveryPinCode = "Delivery pincode";
  static String selectedDeliveryPinCode = "Selected delivery pincode";
  static String orderID = "Order ID";
  static String orderdateTime = "Order datetime";
  static String releaseDate = "Release date(s)";
  static String deliveryDate = "Delivery date";
  static String inProgress = "Inprogress";
  static String haveQuestions = "Have questions? ";
  static String questions = "Questions";
  static String question = "Question";
  static String review = "Review";
  static String receiveDiscount =
      "Receive new product updates, discounts & offers by following and adding them to your favorites";
  static String goToHome = "Go to whitelabel home";
  static String cancelEdit = "cancel/edit order";
  static String viewOrderDetails = "View order details";
  static String orderDetails = "Order details";
  static String needHelp = "Need help?";
  static String viewMyOrder = "View My orders";
  static String howDeliveryWork = "How delivery works?";

  //endregion

  //region Trust Center Screen
  static String report = "Report";
  static String findYourCustomer = "Find your customers on Swadesic";
  static String openInBrowser = "Open in browser";
  static String shareComment = "Share comment";
  static String additionalDetails = "Additional details";
  static String whitelabelTrustScore = "Swadesic trust score";
  static String whitelabelSellerLevel = "Swadesic seller level";
  static String howTrustSellerLevelCalculated =
      "How trust score & seller level is calculated?";
  static String howToIncrease = "How to increase trust score?";
  static String buildCustomerTrust =
      "Build customer trust by disclosing your location. For online-only businesses, select the approximate location of your registered business or where you reside.";
  static String enhanceBrand =
      "Enhance brand trust by being accessible. Share relevant details with store visitors while avoiding personal contacts.";
  static String beingAvailable =
      "Being available help buyers perceive your brand more trustworthy.\nThese details will be shared to visitors of your stores. Make sure you avoid sharing personal contacts";
  static String businessBySharing =
      "Bring in more credibility to your business by sharing your company documents that can be shared";
  static String letCustomerKnow =
      "Let customers know where you are from. If you operate only online, select approximated location of registered business or where you live";
  static String contactInformation = "Contact information";
  static String butWeRequest =
      "But we request you not to share any documents that are private to your business. Please reach out to your legal team incase of doubt.";
  static String location = "Location";
  static String documentsSubmitted = "Documents";
  static String incorporation = "Incorporation";
  static String yourInfoShowsEveryOne =
      "Your information on trust center will be shown to everyone who visits your store. Make sure you add details that are not personal for your privacy.";
  static String gstFiling = "GST filing";
  static String license = "License";
  static String history = "History";
  static String howToIncreaseTrustScore =
      "Every store starts with Trust score ‘50’ and Seller level ‘Silver’."
      "\n\nIncrease your trust score by adding more information about your businsess."
      "\n\nScore and level will increase as you do more business and establish trust among your customers & followers."
      "\n\nTrust score ranges from 0 to 100Seller level ranges from Silver to Diamond";
  static String addMoreInfo = "Add more trust by adding more information";
  static String moreInfoEqualMoreTrust = "More information equals more trust";
  static String brandAreBuiltOnTrust =
      "Brands are built on trust and thrives on transparency";
  static String ensurePrivacy =
      "Ensure privacy: Trust Center information is shown to all store visitors. Only share non-personal, non-confidential information in the Trust Center";

  // static String productFake = "Products are fake but listed as genuine";
  // static String issuesWithOrder = "Issues with an order placed with the store";
  // static String sellerSpamming = "Seller is spamming me or someone I know";
  // static String pretendingAs = "Store is pretending as some other brand";
  // static String sellerIsAFraud = "Seller is a fraudster";
  // static String shouldNotBeThere = "Store has content that should not be there on Swadesic";
  // static String documentsNotGenuine = "Documents submitted are not genuine";
  // static String contactInfoWrong = "Contact information is wrong";
  // static String other = "other";

  static String pleaseWriteDetails = "Please write us the details";
  static String submit = "Submit";

  //endregion

  //region Seller Settings Screen
  static String searchSettings = "Search settings";

  //endregion

  //region Store you followed
  static String youAreNotFollowing =
      "You aren’t supporting any stores yet. \nSearch and explore small businesses of  India";
  static String youAreNotVisited =
      "You haven't visited any stores yet. \nSearch and explore small businesses of  India";
  static String editStoreLogo = "Edit store logo";

  //endregion

  //region Store Profile Screen
  static String enterStoreName = "Enter your store name";
  static String enterStoreAddress = "Enter store address";
  static String enterStoreLocation = "Enter Store location";
  static String areaOfStoreOperation = "Area of store operations";
  static String addDocumentName = "Add document name";
  static String existingDocument = "Existing documents";
  static String enterStoreHandle = "Enter your storehandle";
  static String storeHandleAvailable = "Your storehandle is available";
  static String businessCategory = "Business category";
  static String aboutYourBusiness = "About your business";
  static String howCool =
      "Say how cool your brand is and how special your products are...";
  static String websiteLinkOptional = "Website link (optional)";
  static String yourWebsiteAndOthers =
      "Your website, instagram page, youtube link etc";
  static String selectAndEdit = "Select and edit products";

  //endregion

  static String announcements = "Announcements";
  static String whatIsStoreName = "Name of the Store";
  static String chooseAStoreHandle = "Choose a storehandle ";
  static String enterYourStoreHandle = "Enter your store handle";
  static String enterYourTestStoreHandle = "enter your test store handle";
  static String youCanEdit = "(you can change this later)";
  static String aboutBusinessDummyData =
      "We are a India’s cosmetics for women and we used to be a small business 5 years back. Nykaa thrives by being an online e-commerce seller.";
  static String chooseYourBusiness = "Choose your business category";
  static String readTerms = "*read terms & conditions";
  static String termsAndCondition = "Terms and conditions";
  static String privacyAgreement = "Privacy agreement";
  static String governmentMandates =
      "Government mandates GSTIN for nationwide sales. Swadesic provides nationwide visibility to all sellers, but without GSTIN, sales are limited to the state level. Complete verification to receive orders.";
  static String storeHandleIsNotAvailable =
      "Your store handle is not available";
  static String userNameIsNotAvailable = "Your user name is not available";
  static String storeHandleIsEmpty = "Store handle is empty";
  static String sellingPriceShouldbe = "Selling price should be less than MRP";
  static String storeHandleIsAvailable = "your store handle is available";
  static String howDoYou = "How do you describe your business?";
  static String noNotificationYet = "No notification yet";
  static String noProducts = "No products";
  static String weCouldNotFound =
      "We couldn’t found what or whom you asked for";
  static String notSeen = "NOT_SEEN";
  static String pleaseRecheck = "Please recheck";
  static String seen = "SEEN";
  static String exploreAndExperience =
      "Explore and experience the diversity of \n Bharatiya products";
  static String explore = "Explore";
  static String useOurApp =
      "Use our app's search! It's like sitting under 'Kalpa Vruksham' (wish-fulfilling tree)";
  static String itsLike =
      "It's like sitting under 'Kalpa Vruksham' (wish-fulfilling tree) ";
  static String createNationalBrand =
      "Create a national brand for your business";
  static String buildTrust =
      "Build trust around your brand \nand loyal customer base !!";
  static String writeAboutBusiness = "Write about your business";
  static String websiteLink = "Website link (optional)";
  static String productsUnderReturn = "Products under return";
  static String yourLinks = "your website, instagram page, youtube link etc";
  static String enterPhoneNumber = "enter phone number";
  static String enterYourGstIn = "Enter your GSTIN";
  static String enterYourPan = "Enter your PAN";
  static String nameOnPan = "Name as it is mentioned on PAN";
  static String enterEmail = "Enter email address";
  static String yourFeedbackIsGreatly = "Your feedback is greatly appreciated!";
  static String enterValidNumber = "Enter valid number";
  static String pleaseAddAtLeaseOneContactInfo =
      "Please add at least one contact info.";
  static String valid = "valid";
  static String invalid = "invalid";
  static String pleaseEnterValidUpi = "Please enter valid UPI ";
  static String enterValidUserName = "Enter a new username";
  static String pleaseSelectAtLeaseOneRole = "Please select at least one role.";
  static String letTheWorldKnow =
      "Let the world know who you are, \nand your products !!";
  static String shareYourLink =
      "Share your store link to the world and get orders";
  static String makeInBharat = "Let’s Make Bharat Glorious Again";
  static String other = "Other";
  static String issueWithCompletingDelivery = "Issue with completing delivery";

  static const String copy = "Copy";
  static const String openLink = "Open link";
  static const String call = "Call";
  static const String copiedToClipBoard = "Copied to Clipboard";
  static String productCategory = "Product category & preference";
  static String productMapCategory = "Product map (category & preference)";
  static String addProductImage = "Add Product Images";
  static String productBelongsTo = "Select category your product belong to";
  static String productMap = "Product map";
  static String subCategory = "Sub category";
  static String subOrders = "suborders";
  static String subOrder = "suborder";
  static String item = "item";
  static String items = "items";
  static String customerCategory = "Choose the customer category";
  static String productImage = "Product Images";
  static String addQuantity = "Add Quantity";
  static String addVariantOld = "Add variants (size, colour)";
  static String productDetails = "Product details";
  static String aboutTheProduct = "About the product";
  static String anyOtherDetails = "Any other details";
  static String returnAndRefundPolicy = "Return & Refund policy";
  static String returnWindowsClosesBy = "Return window closes by";
  static String theProductDoesNotSupportReturn =
      "This product doesn’t support return ";
  static String returnAndWarranty = "Return & warranty";
  static String cancellationAndReturn = "Cancellation and return settings";
  static String returnSettings = "Return settings";
  static String returnPreferences = "Return preferences";
  static String refundPreferences = "Refund preferences";
  static String fullRefundAlwaysAppreciated =
      "While customers prefer full refunds, it's important to consider the cost for sustainable finances. Strike a balance and choose wisely.";
  static String mrpSelling = "MRP & Selling Price";
  static String takePhoto = "Take a photo ";
  static String uploadImageFromPhone = "Upload images from your phone";
  static String addFromPreviousProduct = "Add from previous products";
  static String requestVerification = "Request verification";
  static String unlockToChangeGstin = "Unlock to change GSTIN";
  static String unlockToChangePan = "Unlock to change PAN";
  static String unlockToChangeSignature = "Unlock to change signature";
  static String unlockToChange = "Unlock to change";
  static String addSignature = "Add signature";
  static String female = "Female";
  static String male = "Male";
  static String kids = "Kids";
  static String all = "All";
  static String links = "links";
  static String link = "link";
  static String done = "Done";
  static String minimumQuantity = "Minimum quantity customer can buy";
  static String maximumQuantity = "Maximum quantity customer can buy";
  static String multipleVariant = "Does the product has multiple variants?";
  static String totalProductStock = "Total products in stock";
  static String thanksCompletingEmail =
      "Thanks for completing email verification";
  static String deliveryPinCodeUpdated = "Delivery pin code updated";
  static String whatsNew = "What's new";
  static String basicInfo = "Basic Store Onboarding";
  static String saveAsStoreDefault = "Save as store default";
  static String toProvideAReliableExprience =
      "To provide a reliable experience to your customers, complete the store onboarding";
  static String made = "Product";
  static String complete = "Complete";
  static String goLiveNow = "Go live now";
  static String brand = "Brand";
  static String owned = "Business";
  static String noSettingFound = "No setting found";
  static String publishYpurProductToSeeDetails =
      "Publish your product to see details";
  static String productIsPublished = "Product is published";
  static String changeAppFonts = "Change app fonts";
  static String change = "Change";
  static String youCanOnlyOrderFromOtherStoreAfterOfficialLaunch =
      "You can only test-order from your own stores in Swadesic private. Ordering from stores created by others is available from the preview launch.";
  static String appFontSize = "App font size";
  static String freeDelivery = "Free Delivery";
  static String weHaveSavedYourReturnSettingsNowAddProductDetail =
      "We've saved your return settings. Now, you can proceed to add your product details.";
  static String beloPreferencesAreFetchedFromStore =
      "Below preferences are fetched from your store settings";
  static String pleaseEnterReturnDays =
      "Please enter the number of days for returns to be accepted for your product";
  static String pleaseAddDeliveryAndReturn =
      "Please add delivery and return settings";
  static String sellerPays = "Seller pays";
  static String customerPays = "Customer pays";
  static String contactDetails = "Contact details";
  static String manageFeeResponsibility = "Manage fee responsibility";
  static String manageStoreSubscription = "Manage Store Subscriptions";
  static String manageSubscription = "Manage Subscriptions";
  static String dragAndReorder = "Drag and reorder images";
  static String previousImages = "Previously added images";
  static String imageAdded = " images added";
  static String feelFree = "Feel free to add more details";

  //region Brand name Screen Strings
  static const addBrandName = "Add the brand name";
  static const addReturnCondition = "Add return condition";
  static const conditionToAcceptReturn = "Conditions to accept return";
  static const mentionYourReturnCondition =
      "Mention your return accepting conditions";
  static const enterReturnCondition = "Enter return condition";
  static const selectTheLogistic = "Select the logistics partner you use";
  static const standardFeeCanNot = "Standard fee can't be empty or 0";
  static const completeVerification = "Complete Verification";
  static const selectAPrimaryAccount = "Select a primary account";
  static const primaryAccount = "Primary account";
  static const byJoiningViaAnInvite =
      "By joining via an invite, you'll receive ₹50 worth of Infinity Points for purchases and you'll receive ₹200 worth of Infinity Points if you're a business when you create a store & verify within 10 days from joining.";
  static const ourMeter = "Order meter";
  static const incorrectGoogleAccount =
      "Incorrect Google account. Please use a Google account that starts with ";
  static const highPriority = "High priority";
  static const thatsAllWeHave = "That's all we got for now. ";
  static const heyThere =
      "Hey there! 👋 Check out Swadesic - a community based marketplace to discover, support & shop Swadeshi products; Engage with Swadeshi brands and their stories. I've been using it and thought it could be helpful for you too! Let's support our indian small businesses together.\nhttps://play.google.com/store/apps/details?id=com.sociallyx.swadesic";
  static const youDoNotHaveSufficientFund =
      "You do not have sufficient funds to withdraw.";
  static const youCanWithdrawUpTo = "You can withdraw up to ₹";
  static const excludingTheReserverdAmountForCancellations =
      ", excluding the reserved amount for cancellations and return charges.";
  static const yourWithdrawalRequest =
      "Your withdrawal request has been submitted";
  static const deviceIsRooted =
      "Uh oh! It looks like the app has detected that your device might be rooted or that it's running on an emulator. This can sometimes cause compatibility issues.";
  static const thereAreNoMoreContactToFollow =
      "There are no more contacts to follow";
  static const thereAreNoMoreStoreToFollow =
      "There are no more stores to support";
  static const thereAreNoMoreContactOrStoreToFollow =
      "There are no more contacts or stores to support";
  static const posted = "Posted";
  static const syncingContacts = "Syncing contacts";
  static const deletedContact = "Deleted contact";
  static const yourPostIsSent = "Your post is sent";
  static const yourCommentIsSent = "Your comment is sent";
  static const posting = "Posting...";
  static const youHaveReachTheEnd = "You have reached the end of your feed";
  static const moreOnSwadesic = "More on Swadesic";
  static const whatsOnSwadesic = "What's on Swadesic";
  static const noMoreFeedsToLoad = "No more feeds to load";
  static const productHasBeenAddedToYourCart =
      "Product has been added to your cart";
  static const postUpdated = "Post updated";
  static const outOfStock = "Out of stock";
  static const productIsOutOfStock =
      "Product is currently out of stock"; // You will be notified when it becomes available.
  static const showLess = "show less";
  static const less = "less";
  static const showMore = "show more";
  static const more = "more";
  static const onePLaceToFind =
      "One place to find and join discussions related to your store and products. Answer questions and get a pulse of customer sentiment here.";
  static const yourUserProfileWillDeleteSoon =
      "Your user profile is scheduled for deletion soon.";
  static const returnOrdersManagedBy = "Return pickup";
  static const needHelpFromSwadesic = "Need help from Swadesic";
  static const releaseRefund = "Release Refund";
  static const refundAmountIsInHold = "Refund amount is in in hold";
  static const amountIsReleased = "Amount is released";
  static const areYouSureWantsToLogout = "Are you sure you want to logout ?";
  static const completeEmailVerification = "Complete Email Verification";
  static const areYouSureWantsToDelete =
      "Are you sure you want to delete this?";
  static const youWillLooseAllTheDetails =
      "You will loose all the details if you go back";
  static const doYouReallyWantsToDelete =
      "Do you really want to delete your product? Remember that this action cannot be reversed.";
  static const yourChangesIsNotYetSaved =
      "Your changes are not yet saved.\nDo you want to save changes ? ";
  static const youWillLoose = "You will loose all the details if you go back.";
  static const areYouSureWantToUnGroup =
      "Are you sure you want to take this product out of this group?";
  static const areYouWantToStartShipAgain =
      "Are you sure you want to start ship again?";
  static const applyingTheseSettingsAsTheDefault =
      "Applying these settings as defaults. Any existing products with default settings would contain these changes from now on.";
  static const speakWithCustomerAndShare =
      "Speak with your customer and share the product return process.";
  static const noProductYet = "No products yet";
  static const areYouSureWantToHold =
      "Are you sure want to hold refund amount?";
  static const areYouSureWantToReleaseHoldingAmount =
      "Are you sure want to release holding amount?";
  static const pleaseComeBack = "Please come back";
  static const addProductAndStartSelling = "Add products & start selling";
  static const noHistoryYet = "No history yet";
  static const goToLatestVersion = "View current product";
  static const goToCart = "Go to cart";
  static const gotIt = "Got it";
  static const addToCart = "Add to cart";
  static const findWhatYouLove = "Find what you love! Search! ";
  static const requestAndSubOrder = "Request & suborder status";
  static const productAndPayment = "Product & payment";
  static const pan = "Pan";
  static const storeRefundPolicy = "Store refund policy";
  static const showRefundCalculation = "show refund calculation";
  static const refundCostOnStore = "Refund cost on store";
  static const startYourStoreAt = "Start your store at 0% commissions*";
  static const resetToDefaultStoreSettings = "Reset to default store settings";
  static const pleaseAddReturnDays = "Please add return days";
  static const autoCancelledOrders = "Auto-cancelled orders";
  static const buyerCancellWatingOrder = "Buyer cancels waiting order";
  static const sellerCancelledWaitingOrder = "Seller cancels waiting order";
  static const verifiedAndLocked = "Verified and locked";
  static const unVerifiedAndRequested =
      "Unverified and requested for verification";
  static const verified = "Verified";
  static const showBreakup = "Show breakup";
  static const hideBreakup = "Hide breakup";
  static const recentSearches = "Recent searches";
  static const recent = "Recent";
  static const clear = "Clear";
  static const clearAll = "Clear all";
  static const notShow = "not show";
  static const popular = "Most popular";
  static const order = "order";
  static const orders = "orders";

  //endregion
  //region Product name Screen Strings
  static const addProductName = "Add the product name";

  //endregion

//region Product Map Strings
  static const searchSubCategory = "search sub category";

//endregion

  //region User On Boarding
  static const allPaymentSecure =
      "All payments are securely managed by Swadesic";
  static const followNext = "Follow and next";
  //endregion
  //region Seller Store Delivery Settings
  static const deliverySettings = "Fulfillment settings";
  static const selfDeliveryByStore = "Self-delivery by store";
  static const deliveryPreferences = "Delivery preferences";
  static const deliveryByLogistics = "Delivery by logistics partner";
  static const deliveryServiceLocation = "Delivery service locations";
  static const timeToPrepareAndShipPackage = "Time to prepare & ship package";
  static const fromConfirmation = "from confirmation";
  static const deliveryFeeType = "Delivery fee type";
  static const deliveryFeeMethod = "Delivery fee method";
  static const deliveryPersonDetails = "Delivery personnel details";
  static const defaultLogisticsPartner = "Default logistics partner";
  static const fromDateOfOrder = "from date of order";
  static const timeToDeliverTheOrder =
      "Time to deliver the order (rough estimate)";
  static const productDeliveryMethod = "Select delivery method";
  static const sellerHaveTheFlexibilityToSell =
      "Sellers have the flexibility to sell anywhere they want in their own options.";
  static const youCanCustomizeDeliverySettings =
      "You can customize delivery settings for the product below. ";
  static const belowDetailWillBeShare =
      "Below details will be shared only after you accept the order";

  //endregion
  static const returnProductMakesCustomer =
      "Returnable products make customers to trust the brand";
  static const theseSettingsAre =
      "These settings are considered as default settings. You can customize any of these during product catalogue.";
  static const youCanCustomizeReturn =
      "You can customize cancellation and return settings for the product below.";
  static const youCanCustomizeDelivery =
      "You can customize delivery settings for the product below.";
  static const customersSeeTrust =
      "Customers see trust in products with return option and place order";
  static const returnDetail = "Return details";
  static const returnType = "Choose return type";
  static const customerSeeTrustInBrands =
      "Customers see trust in brands that offer flexible return options and more likely to place an order";
  static const giveFullRefund =
      "Give full refund to my customer. I will pay for return costs";
  static const letCustomerPay = "Let my customer pay for return costs";
  static const shouldWeDo = "Should we do full refund to your customer?";
  static const example =
      "Example: Say your customer got free delivery because he order more than 5 items and if returns few products and make the products ordered less than 5, we will charge delivery fee and deduct it from refund amount";
  static const onReturn =
      "On return, should we re-evaluate free delivery promotions of that order and adjust delivery refund amount?";
  static const returnCost =
      "Return costs: Transaction fees 3%, Delivery fee if any.";
  static const ifYouAreUnableToDeliver =
      "If you are unable to deliver, there can be multiple reasons like, we suggest you to contact customer and resolve. Alternatively, you can change status to cancel, start shipping etc..,";
  //region  Seller Return and Warranty

  //endregion
  //region Seller Delivery
  static const deliveryPersonName = "Delivery person name";
  static const returnPersonName = "Return person name";
  static const deliveryPersonContact = "Delivery person contact number";
  static const trackingNumber = "Tracking number";
  static const trackingLink = "Tracking link";

  //endregion

  //region Delete or deactivate screen
  static const ifYouWant =
      "If you want to take a break, choose closing the store for orders.";
  static const again = "again.";

  static const deActivatingYourStore =
      "Deactivating your store will not make it visible to anybody even with search but all your product details will be saved and can be activated again anytime you like.\nDeactivating impacts your store’s trust score if the gap is too long.";
  static const openTheStore = "Open the store";
  static const deActivateMyStore = "Deactivate my store";
  static const activateMyStore = "Activate my store";
  static const deleteMyStore = "Delete my store";
  static const accountDeletion = "Account deletion";
  static const pleaseBeAsDetailed =
      "Please be as detailed as possible. What did you expect and what happened instead?";
  static const subMitAndDelete = "Submit and delete my account";
  static const description = "Description";
  static const addMoreDetail = "Add more details";
  static const addressDeleted = "Address deleted successfully";
  static const newResponse = "New response";
  static const messaging = "Messaging";
  static const requests = "Requests";
  static const stockUpdated = "Stock updated";

  //endregion

  ///Error and message
  static const commonErrorMessage =
      "There seems to be a problem. Please wait & try again.";
  static const unableToLoadTransaction = "Unable to load transaction";
  static const unableToSaveStory = "Unable to save story";
  static const unableToLoadBalance = "Unable to load reward balance";
  static const unableToLoadInvitedCount = "Unable to load invited count";
  static const unableToFetchAvailableLocations =
      "Unable to fetch available locations";
  static const unableToLoadInvitedUserAndStore =
      "Unable to load invited user and stores";
  static const timeOutMessage =
      "This took longer than expected. Please try again.";
  static const noContactsInPhone = "There are no contacts in your phone";
  static const unableToFetchRecommendedUsers =
      "Unable to fetch recommended users";
  static const noMoreRecommendedUsersAreThere =
      "No more recommended users are there";
  static const noMoreRecommendedStoresAreThere =
      "No more recommended stores are there";
  static const unableToFetchRecommendedStore =
      "Unable to fetch recommended stores";
  static const unableToFetchRecommendedProducts =
      "Unable to fetch recommended products";
  static const unableToFetchLikes = "Unable to fetch likes";
  static const unableToFetch = "Unable to fetch";
  static const enterValidPinCode = "Enter valid pin code";
  static const noMorePostToLoad = "No more posts to load";
  static const youCantAddMoreThen = "You can't add more than";
  static const thoughtsFieldCanNotEmpty = "Thoughts field can't be empty ";
  static const emptyPostCanNotBeAdded = "Empty post can't be added";
  static const emptyCommentCanNotBeAdded = "Empty comment can't be added";
  static const previewSuggests =
      "Preview suggests that Swadesic is only for limited users with limited features";
  static const noImageIsSelected = "No image is selected";
  static const thisTestStoreDoesNotBelongsToYou =
      "This test store for the product does not belong to you. Please purchase products from the test store that you have created.";
  static const noPostFound = "No post found";
  static const noRepostFound = "No reposts found";
  static const looksLikeThereAreNoPost =
      "Looks like there are no posts here yet";
  static const noRepostYet = "No reposts yet";
  static const thisFeatureIsCommingSoon = "This feature is coming soon.";
  static const openSettingToAllowPermission =
      "Open setting to allow contact permission";
  static const allowContactPermission = "Allow contact permission";
  static const noContactDetailsAvailablePleaseAddInTheTrustCenter =
      "No contact details available. Please add in the Trust center";
  static const noContactDetailsAvailable = "No contact details available.";
  static const unlockAndSeamless =
      "Unlock a seamless experience! Download our app for complete access to exclusive features and content";
  static const orderLater = "Order later";
  static const pleaseSelectAddressOf = "Please select address of ";
  static const someOfTheProductAreNotAble =
      "Some of the products are not able to deliver to your selected address.";
  static const theseProductsInYourCartAreNoLongerAvailable =
      "These products in your cart are no longer available.";
  static const thisProductIsNoLongerAvailable =
      "This products is no longer available.";
  static const thisStoreIsNoLongerAvailable =
      "This store is no longer available.";
  static const noLongerAvailable = "No longer available";
  static const notDeliverable = "Not deliverable to the selected pincode";
  static const unableToUpdateEstimateDeliveryDate =
      "Unable to date estimate delivery date";
  static const unableToLoadOrders = "Unable to load orders";
  static const unableToAddNewAddress = "Unable to add new address";
  static const unableToEditAddress = "Unable to edit address";
  static const storesForTheseProductsAreNotAcceptingOrdersRightNow =
      "Stores for these products aren't accepting orders right now. Move them to your wish-list?";
  static const unableToLoadGrandTotal = "Unable to load Grand total";
  static const storeAreDeleted =
      "Some of the products in your cart are no longer available.";
  static const unableToUpdateStocks = "Unable to update stocks";
  static const noInternet =
      "Please check your internet connection and try again.";
  static const firstNameCanNotBeEmpty = "First name can't be empty";
  static const unableToLoadRecentlyVisitedStores =
      "Unable to load recently visited stores";
  static const activateYourStoreFirstToStartShareProduct =
      "Activate your store first to start sharing your products. Your store is currently inactive.";
  static const unableToFetchShoppingCartInfo =
      "Unable to fetch shopping crt info";
  static const unableToFetchCustomerInfo =
      "Unable to fetch customer information";
  static const unableToFetchAccountInfo = "Unable to fetch account information";
  static const unableToFetchSubOrders = "Unable to fetch subOrders";
  static const unableToCheckStoreHandle = "Unable to check store handle";
  static const unableToCheckProductSlug = "Unable to check product slug";
  static const unableToCheckProductCode = "Unable to check product code";
  static const unableToCheckUserName = "Unable to check user name";
  static const estimatedDaysCanNotSmallerThenPrepareTime =
      "Your estimated delivery days can't be equal to or shorter than the time required for preparation.";
  static const emptyCommentCanNotPosted =
      "Empty comment, review or a question can't be posted";
  static const noMatchingResult = "No matching results found";
  static const unableToLoad = "Unable to load";
  static const notLoaded = "Not loaded";
  static const unableToLoadStoreInfo = "Unable to load store information";
  static const unableToLoadCart = "Unable to load cart";
  static const unableToLoadProducts = "Unable to load products";
  static const unableToLoadUserInfo = "Unable to load user info";
  // static const unableToLoadUserInfo = "Unable to load user info";
  static String aMinimumOf10Days =
      "A minimum of 10 days return period is recommended";
  static String aMaximumOf7Days =
      "A return period of up to 7 days is recommended.";
  static String enterValidReturnPeriod = "Enter valid return period";

  static const unableToOpenComment = "Unable to open comment";
  static const storeHandleCanNotBeEmpty = "Store handle can't be empty";
  static const userNameCanNotBeEmpty = "User name can't be empty";
  static const displayNameCanNotBeEmpty = "Display name can't be empty";
  static const invalidLink = "Invalid link";
  static const storeNameCanNotBeEmpty = "Store name can't be empty";
  static const pleaseSelectBusinessCategory = "Please select business category";
  static const pleaseAddTheStoreLogo = "Please add the store logo.";
  static const pleaseSelectBusinessType = "Please select business type";
  static const aboutYourBusinessCanNotEmpty =
      "About your business can't be empty";
  static const brandNameCanNotBeEmpty = "Brand name can't be empty";
  static const productNameCanNotBeEmpty = "Product name can't be empty";
  static const productCategoryCanNotBeEmpty = "Product category can't be empty";
  static const productDescriptionCanNotBeEmpty =
      "Product description can't be empty";
  static const descriptionCanNotBeEmpty = "Description can't be empty";
  static const stockQuantityCanNotBeEmptyOrZero =
      "In stock quantity can't 0 or empty";
  static const mrpCanNotBeEmptyOrZero = "MRP can't 0 or empty";
  static const sellingPriceCanNotBeEmptyOrZero =
      "Selling price can't 0 or empty";
  static const pleaseAddAtLeaseOneProductImage =
      "Please add at least one product image";
  static const sellingPriceShouldNotBeMoreThenMRP =
      "Selling price should not be more than MRP";
  static const pleaseEnterValidPromotionLink =
      "Please enter valid Promotion link";

  static const productImageUnableToAdd = "Product image unable to add.";
  static const yourSearchHistory =
      "Your search history is currently unavailable";
  static const yourSearch = "Your search is currently unavailable";
  static const yourSearchResultIsUnAvailable =
      "Your search result is currently unavailable";
  static const noWorry = "No worries, you find it next time";
  static const noMatchingSearchResult = "No matching search results";
  static const noHistoryFound = "No history";
  static const refundAmountCalculationIsNotAvailable =
      "Refund amount calculation is not available.";
  static const noAccess = "You don't have access";
  static const userNameNotAvailable = "user name not available";
  static const beforeStoreActivationStoreLink =
      "Before store activation, store link is not active";
  static const available = "available";
  static const availablePayout = "Available Payout";
  static const swadesicSubscriptions = "Swadesic Subscriptions";
  static const availability = "Availability";
  static const unAvailable = "unavailable";
  static const userNameAvailable = "username available";
  static const usernameUnavailable = "username unavailable";

  static const noAccessViewComment =
      "You don't have access to write but view comment";
  static const usernameAlreadyExist = "User name already exist";
  static const haveNotBought = "You haven't bought this product";
  static const fieldEmpty = "Some fields are missing";
  static const cancelReasonCanNot = "Cancel reason can't be empty";
  static const cancelReason = "Cancel reason";
  static const profileUpdateSuccessfully = "Profile update successfully";
  static String storeUpdateSuccessfully = "Store update successfully";
  static const invalidReferralCode = "Invalid referral code";
  static const yourCartIsEmpty = "Your cart is empty";
  static const noOrderYet = "No orders yet ";
  // static const pleaseEnterValidUrl = "Please enter valid url";
  static const noResultFound = "No results found";
  static const permissionDenied = "Permission denied";
  static const private = "Private";
  static const somethingWentWrong = "Something went wrong";
  static const notAbleToOpen = "Not able to open";
  static const noMoreMemberInviteLeft = "You don't have member invite left";
  static const noMoreSellerInviteLeft = "You don't have seller invite left";
  static const pleaseSelectDelivery = "Please select a delivery address";
  static const noMatchingResultInRecentVisited =
      "No matching results in your \nrecently visited stores";
  static const youHaveNotViewedAnyStore =
      "You haven’t viewed any stores yet.\nSearch and explore small businesses of Bharat";
  static const noMatchingResultInSupported =
      "No matching results in your \nsupported stores";
  static const noMatchingResultInRecentlyVisited =
      "No matching results in your \nrecently visited stores";
  static const noAddressIsSaved =
      "No address is saved for this pincode.\nAdd a new address of this pincode or change to new pincode";
  static const noAddressIsSelected =
      "No address is selected, please select/add an address of selected pin code";
  static const noCommentFound = "No comments";
  static const trustCenterSetupIncompleteMessage =
      "Complete Trust Center to select Delivery locations.";
  static const youHaveNotPurchaseThis =
      "You haven't purchase this product yet. Please purchase first";
  static const pleaseSelectAProduct = "Please select any product to continue";
  static const thereAreNoLinksToShare = "There are no links to share";
  static const canNotEditOrDelete = "Can't edit or delete";

  static String sorryThatYouAre = "Sorry that you are here, but what happened?";
  static String weWillInquire =
      "We will inquire into the issue as soon as possible and take necessary action. \nPlease select a report category. If you need more help, please write us at support.";
  static String thankYouForYourAction =
      "Thank you for your action. We will inquire into the issue as soons as possible and take necessary action.";
  static String yourStoreIsDeleted = "Your store is successfully deleted.";
  static String pleaseSelectPrepareAndDelivery =
      "Please select prepare and delivery time";
  static String enterValidDelivery =
      "Enter valid delivery person name and number.";
  static String selectDeliveryLocation = "Select delivery location";
  static String accountNumberDoesNotMatch = "Account number does not match";
  static String withdrawRequested = "Withdrawal request successfully processed";
  static String yourVerificationWillCompleteInNext =
      "Verification will be completed within 36 hours, after which you can receive orders. Meanwhile, you can go live to showcase your store, products, and posts to Swadesic users";

  ///Dialog box message
  static const pasteInviteCode = "Paste your invite code.";
  static const openSettingAndTurnOn =
      "Open app setting and turn on permission.";
  static const openAppSettings = "Open app setting";

  //region Payment Screen
  static const paymentOptions = "Payment options";
  static const status = "Status";
  static const orderStatus = "Order status";
  static const payUsing = "Pay using";
  static const creditCard = "CREDIT CARDS";
  static const debitCard = "DEBIT CARDS";
  static const addManageCards = "add/manage cards";
  static const upi = "UPI";
  static const upiApps = "UPI Apps";
  static const vpa = "VPA";
  static const withUpiId = "With UPI ID";
  static const netBanking = "NET BANKING";
  static const pleaseDoNotGoBack =
      "Please don’t go back unless there is a problem";
  static const pleaseGoBackInitiate =
      "Please go back and reinitiate the payment if you\n couldn’t make the payment. ";
  static const pleasePayUsingUpiApp =
      "Please pay using your UPI app and come back.\nWe will wait.";
  static const holdOn =
      "Hold on while we take you to a site where you can enter your OTP to complete the transaction.";
  static const upiIdPaymentWaiting =
      "Your UPI app received a notification from us.\n Please pay there, then proceed back.\n We will wait.";
  static const unableToOpenUpi =
      "Unable to open UPI app. Please try using another payment method.";
  // static const support = "Support";
  // static const supporting = "Supporting";
  static const howDoYouLike = "How do you like our app?";
//endregion

//region Seller all order Details

  static const prepareTime = "Select prepare time";
  static const deliveryTime = "Select delivery time";
  static const grandTotal = "Grand total";
  static const totalPaid = "Total paid";
  static const totalProductCost = "Total product cost";
  static const breakup = "Breakup";
  static const shippingFee = "Shipping fees";
  static const promotions = "Promotions";
  static const productsOrdered = "Products ordered";
  static const amountToBeReceived = "Total amount to be received";
  static const deliveryFee = "Delivery fee";
  static const amountToBePaid = "Amount to be paid";
  static const price = "Price";
  static const waitingForConfirmation = "Waiting for confirmation";
  static const cancelledByYouBeforeShipping =
      "Cancelled by you, before shipping";
  static const cancelledByYouAfterShipping = "Cancelled by you, after shipping";
  static const sellerCancelledConfirmedOrder =
      "Seller cancelled confirmed orders";
  static const notConfirmAndCancelled = "Not confirmed and cancelled";
  static const sellerCancelledBeforeConfirm =
      "Seller cancelled, before confirmation";
  static const autoCancelled = "Auto cancelled";
  static const customerCancelledBeforeShipping =
      "Customer cancelled, before shipping";
  static const customerCancelledAfterShipping =
      "Customer cancelled, after shipping";
  static const sellerCancelledAfterConfirm =
      "Seller cancelled, after confirmation";
  static const shippingInProgress = "Shipping in progress";
  static const scheduledForPickup = "Scheduled for pickup";
  static const returns = "Returns";
  static const deliveredSuccessFully = "Delivered successfully";
  static const deliveredComponentStatus = "Delivered";
  static const timesUpOrderAutoCancelShortly =
      "Time’s up! Order auto-cancels shortly";
  static const autoCancelledIfNotConfirmed = "Auto-cancels if not confirmed";
  static const autoCancelAndFull = "Auto-cancels & full refund if unconfirmed";
  static const ifYouWantToUpdate =
      "If you want to update estimated delivery dates for a specific product, select it and update separately.";
  static const youCanGroup =
      "You can group your products and ship them in different packages. You can add as many packages as you wish.";
  static const selectUnSelect = "Select or unselect from the below list";
  static const productList = "Products list";
  static const refundCancelRequest = "Refund cancel request";
  static const estimatedDeliveryDate = "Estimated delivery date";
  static const updateStock = "Update stock";
  static const updateInventory = "Update Inventory";
  static const mobileNumberUpdated = "Mobile number updated";
  static const updateDeliveryDate = "Update delivery date";
  static const addShipping = "Add shipping and tracking details for this group";
  static const comingSoon = "Coming soon";
  static const commentComingSoon = "Stay Excited! Comments On Their Way.";
  static const commentCanNotEmpty = "Comment can't be empty";
  static const repostComingSoon = "Stay Excited! Repost On Their Way.";
  static const addPickupDetailsForThisGroup =
      "Add pickup details for this group";
  static const confirmOrCancel = "Confirm or Cancel";
  static const shipOrCancel = "Ship or cancel";
  static const startShippingAll = "Start shipping all";
  static const addPhotos = "Add photos";
  static const addGifVideo = "Add GIF, video, voice, poll etc..,";
  static const downloadTheApp = "Download the App for Smoother Experience";
  static const spamMessage =
      "Please use the app responsibly to ensure a positive experience for all users.";
  static const addLocation = "Add Location";
  static const youCanNotCreateMoreThen =
      "You can't create more than 5 stores in this plan. ";
  static const infinityPointsOrFlashPoint =
      "1 Flash point equals 1 Indian Rupee in the Swadesic ecosystem.";
  static const oneInfinityPoints =
      "1 Infinity point redeems to 1 Indian rupee in Swadesic eco-system";
  static const tagStoreProductMember = "Tag Stores, Products & Members";
  static const tagStorePeople = "Tag influencer & review page";
  static const promoteProduct = "Promote product (earn affiliate commission)";
  static const startShipping = "Start shipping";
  static const seeWhySwadesicMatters = "See Why Swadeshi Matters!";
  static const visitWebsite = "Visit Website";
  static const appSupport = "App Support";
  static const startShippingSeparately = "Start shipping separately";
  static const trackingDetail = "Tracking details";
  static const cancelOrder = "Cancel order(s)";
  static const returnUpdateHistory = "Return update history";
  static const howEasy = "How easy it is to process returns?";
  static const helpUs = "Help us better Swadesic for Bharat";
  static const updateDeliveryStatus = "Update delivery status";
  static const prepareForPickup = "Prepare for pickup";
  static const trackPickup = "Track pickup";
  static const holdRefundHeading = "Hold refund";
  static const callTheCustomer = "Call the customer";
  static const rateTheReturn = "Rate the return";
  static const completeDelivery = "Complete delivery ";
  static const completeReturn = "Complete return";
  static const productListYouAreDeliver =
      "Product list you are completing delivery for (in this group) - ";
  static const productListYouAreReturned =
      "Product list you are completing return for (in this group) - ";
  static const initiateReturn = "Initiate returns";
  static const sendPickupOtp = "Send pickup OTP";
  static const cancellingTheOrder = "Cancelling the order ";
  static const trackThePackage = "Track the package";
  static const deliveryUpdateHistory = "Delivery update history";
  static const confirmedNotYetShipped = "Confirmed, not yet shipped";
  static const scheduledForShipping = "Scheduled for shipping";
  // static const shippingStarted = "Shipping started";
  static const productListInGroup = "Product list (in this group) - ";
  static const refundType = "Refund type";
  static const issuedRefundAmount = "Issued Refund amount";
  static const fullRefundPartialRefund = "Full refund/Partial refund";
  static const refundAmountCalculation = "Refund amount calculation";
  static const rateReview = "Rate & review";
  static const buyAs = "Buy as";
  static const pleaseExplainWhyYouWantsToHoldRefund =
      "Please explain why you want to hold the refund amount?";
  static const productNotDelivered = "Product not delivered";
  static const refundAmountIsOnHold = "Refund amount is on hold";
  static const productReturnAndEscalate = "Product return & Escalate";
  static const notDelivered = "Not delivered";
  // static const notDeliveredEscalationReason = "Not delivered";
  static const productReturn = "Product return";
  static const notDeliveredButton = "Not delivered";
  static const retry = "Retry";
  static const returnText = "Return";
  static const howRefundAmountIsCalculated = "How refund amount is calculated?";
  static const reviewAdded = "Review added";
  static const returnProduct = "Return product";
  static const ifYouFeelAfter =
      "If you feel after the discussion, the issue is not resolved. Create an escalation ticket tagging Store & Swadesic dispute resolution team";
  static const inThisProcess =
      "In this process -\n●Understand Refund breakup\n●check return conditions\n●Place return";
  static const detailedExplanation = "Detailed explanation of the issue";
  static const refundAmount =
      "Refund amount gets credited to the same channel from which you paid for the order";
  static const returnAreNatural =
      "Returns are a natural part of ecommerce and customer expects and prefers there free return policy in any store they shop online. ";
  static const ifYouHaveIssues =
      "If you have issues with product returned by your customer, we recommend you to have a dialogue with them and discuss the concerns. ";
  static const ifYouThink =
      "If you think the product is damaged/changed and strictly want to hold refund. Please proceed and raise dispute. We will call up for investigation and contact you both after you speak with your customer.";
  static const cancellingOrder =
      "Cancelling orders have a negative impact on the small business owners & Swadesic eco-system. Cancel wisely.\n\nAdditionally, there will adjustments made in your refund amount if there are any free delivery & other promotions involved. Details below. ";
  static const aDeliveryNote =
      "A delivery note is a document that accompanies a shipment of goods. It provides a list of the products and quantity of the goods included in the delivery. ";
  static const possibleReason =
      "Possible reasons:\n● Somebody must have received your product behalf of you at the delivery location\n● There can be delay in delivery but seller must have updated before \n\nWe recommend you to speak with the seller and clear this out. ";
  static const possibleReasonForDelay =
      "Possible reasons for delay:\n ● Delay in delivery services\n ● Late updates into the App by the Seller";
  static const ifYouFeel =
      "If you feel after the discussion, the issue is not resolved. Tap below. A ticket will be handled by Swadesic dispute resolution team";

  // Create Store screen
  static const createPreviewStore = "Create a Preview Store";



  // static const List<String>logisticPartners = <String>[
  //   "UPS",
  //   "DHL",
  //   "Kuehne + Nagel",
  //   "XPO Logistics",
  //   "GXO Logistics",
  //   "Yellow Corp",
  // ];

  static const pleaseSelectOrders = "Please select orders";
  static const pleaseAddYourReview = "Please add your review";
  static const cancelledAndReturned = "Cancelled  or Returned orders";
//endregion

//region Edit user
  static const thisUserNameTaken =
      "This user name is already taken, try something else";
  static const thisUserNameIsAvailable = "your user name is available";
  //endregion

//region App Filter
  static const filter = "Filter";
  static const sort = "Sort";
  static const earliestFirst = "Earliest first";
  static const oldestFirst = "Oldest first";
  static const lowestFirst = "Lowest first";
  static const highestFirst = "Highest first";
  static const filterOrders = "Orders";
  static const amount = "Amount";
  static const amountToBeSent = "Amount to be sent";
  static const accountNickName = "Account name (nickname)";
  static const accountHolderName = "Account holder name";
  static const additionRequire =
      "Addition requires OTP verification (expires in 5 minutes)";
  static const transactions = "Transactions";
  static const dateCreated = "Date created";
  static const upVote = "Upvote";
  static const reset = "reset";
  static const apply = "Apply";
  static const time = "Time";
  static const statusType = "Status type";
  static const transactionType = "Transaction type";
  static const statusTypeList = [
    "Added to account balance",
    "Order in process",
    "Nothing to be added"
  ];
  // static const transactionTypeList = [
  //   "Successful withdrawl requests",
  //   "Failed withdrawl requests",
  //   "Credits against orders",
  //   "Debits against return costs",
  //   "Promotions"
  // ];
  static const timeFilter = [
    "This Month",
    "Last Month",
    "Last 3 months",
    "All time"
  ];
//endregion

//region Transaction type flags
  static const credited = "CREDITED";
  static const debited = "DEBITED";
  static const withdrawal = "WITHDRAWAL";
  static const success = "SUCCESS";
  static const pending = "PENDING";
  static const successMessage = "Success";
  static const pendingMessage = "Pending";
  static const failure = "FAILURE";
  static const transactionPending = "PENDING";
  static const failMessage = "Failed";

  // static const success = "SUCCESS";

//endregion

//region Withdraw status
  static const bankDetail = "Bank Details";
  static const bankName = "Bank name";
  static const bankBranch = "Bank branch";
  static const accountNumber = "Account number";
  static const reEnterAccountNumber = "Re-enter account number";
  static const accountName = "Account name";
  static const transactionDetail = "Transaction Details";
  static const transactionAmount = "Transaction amount";
  static const referenceNumber = "Reference number";
  static const ifscCode = "IFSC code of bank branch";
  static const bankAccountName = "Bank account name";
  static const transactionDate = "Transaction date";
  static const transactionStatus = "Status";

//endregion

//region Verification badge text
  static const verifiedAsBusiness = "Verified as Business";
  static const verifiedAsIndependentSeller = "Verified as Independent Seller";
  static const verificationRequestedAsBusiness =
      "Verification requested as Business";
  static const verificationRequestedAsIndependentSeller =
      "Verification requested as Independent Seller";
  static const unverifiedSeller = "Unverified";
//endregion

//region Orders
  static const getAnUpdateFromYourSeller =
      "Get an update from your seller if refund is delayed";
  static const getAnUpdate = "Get an update";
  static const toReturnProduct = "To return product";
  static const speakWithSellerShareTheReason =
      "Speak with seller \n● Share the reason you want to return the product - wrong product, damaged etc..,\n● Proceed with instructions provided by seller \n\nWe recommend you to speak with the seller and clear this out.";

//endregion

//region No internet
  static const weAreStillConnected =
      "We’re still connected by emotion.\nListen to our AI generated poems..";
//endregion
  //region Categories
  static const List<String> buyerCategoryList = [
    "Accounts",
    "Comments",
    "Edit profile",
    "Fraud",
    "Home",
    "Notifications",
    "Onboarding",
    "Order confirmation",
    "Orders/Cancel flow",
    "Orders/Delivery flow",
    "Orders/return flow",
    "Others",
    "Payments",
    "Product page",
    "Scam",
    "Search",
    "Security",
    "Settings",
    "Shopping cart",
    "Splash screen",
    "Store page",
    "Support",
    "Technical Issues",
    "Trust center",
    "User profile"
  ];
  static const List<String> sellerCategoryList = [
    "Account balance",
    "Add products",
    "Delivery settings",
    "Edit profile",
    "Fraud",
    "GST verification",
    "Home",
    "Notifications",
    "Orders/Cancel flow",
    "Orders/Delivery flow",
    "Orders/return flow",
    "Others",
    "Product page",
    "Reporting",
    "Return and refund settings",
    "Scam",
    "Security",
    "Settings",
    "Store Deactivation & Deletion",
    "Store profile",
    "Support",
    "Technical Issues",
    "Trust center"
  ];

  //endregion
//region On internet
  static String screenAvailableOnly =
      "Screens available only with internet. We'll resume when connection is restored.";

  //endregion
//region Poem list
  static const List<String> poemList = [
    "In Swadesic's heart, Bharat's stories reside, A tapestry of cultural heritage we'll forever guide. With reverence and love, we'll pave the way, For a prosperous Bharat, where dreams will sway.",
    "In Swadesic's heart, Bharat's stories reside, A tapestry of cultural heritage we'll forever guide. With reverence and love, we'll pave the way, For a prosperous Bharat, where dreams will sway.",
    "In Swadesic's heart, Bharat's stories reside, A tapestry of cultural heritage we'll forever guide. With reverence and love, we'll pave the way, For a prosperous Bharat, where dreams will sway.",
    "In Swadesic's heart, Bharat's stories reside, A tapestry of cultural heritage we'll forever guide. With reverence and love, we'll pave the way, For a prosperous Bharat, where dreams will sway.",
    "In Swadesic's heart, Bharat's stories reside, A tapestry of cultural heritage we'll forever guide. With reverence and love, we'll pave the way, For a prosperous Bharat, where dreams will sway.",
  ];
//endregion

//region Report reason

  ///Store
  static List<String> storeReportReason = [
    "Store is spamming me or someone I know",
    "Store is pretending as some other brand(mention other store's handle or profile link in the details)",
    "Store is run by a fraudster",
    "Store has content that should not be there on Swadesic - Nudity, Sexual, Fraudulent, Scam, Abusive, Violent content etc..",
    "Store trust center information is wrong -  contact, location & identification etc..",
    "Store has fake products but listed as genuine",
    "Store is not responding to my placed orders or returns",
    "Store has fake reviews for many products",
    "Store shares personal or sensitive information without consent."
  ];

  ///Product
  static List<String> productReportReason = [
    "Product content quality is too low",
    "Product has fake rating & reviews",
    "Product details are not genuine",
    "Product is copywrited to my brand",
    " Product has content that should not be there on Swadesic - Nudity, Sexual, Fraudulent, Scam, Abusive, Violent content etc..",
    "Product is fake but listed as genuine",
    "Product is in wrong category",
    "Product demo link doesn't work or misled",
    "Product images are not relevant, fake etc..",
    "Product shares personal or sensitive information without consent."
  ];

  ///Comment
  static List<String> commentReportReason = [
    "Comment is spam",
    "Content should not be there on Swadesic - Nudity, Sexual, Fraudulent, Scam, Abusive, Violent content etc.., ",
    "Content spreads misinformation or false claims.",
    "Content shares personal or sensitive information without consent."
  ];

  ///User
  static List<String> userReportReason = [
    "User is spamming me or someone I know",
    "User is pretending as some other person(mention other person's username or profile link in the details)",
    "User is a fraudster",
    "User has content that should not be there on Swadesic - Nudity, Sexual, Fraudulent, Scam, Abusive, Violent content etc..",
    "User has fake reviews for many products.",
    "User shares personal or sensitive information without consent."
  ];
//endregion

  ///Labels
//region Labels

//region Hero tags
//   static const speakWithSellerShareTheReason = "storeAndProduct";

//endregion

//region Rewords and invitees
  static const reward = "Rewards";
  static const myInvitees = "My invitees";
  static const affiliateProgram = "Affiliate Program";
  static const invitees = "Invitees";
//endregion

  //region Add story
  static const storyHasBeenSaved = "Story saved successfully";

  //endregion

  //region Refund preferences store and product specific strings
  static String defaultFeeResponsibility = "Default fee responsibility";
  static String scenario = "Scenario";
  static String transactionFeeAndTDS = "Transaction Fee + TDS";
  static String transactionFeeAndTDSValue = "Transaction Fee + TDS";
  static String deliveryFeeAndTDSValue = "Delivery Fee";
  static String buyerCancels = "Buyer cancels";
  static String buyerPays = "Buyer pays";
  static String refundIfNotShipped = "Refunded if not shipped";
  static String storeCancels = "Store cancels";
  static String storePays = "Store pays";
  static String refundedToBuyer = "Refunded to buyer";
  static String autoCancel = "Auto-cancel";
  static String buyerReturns = "Buyer returns";
  static String notRefundedAlreadyShipped = "Not refunded (already shipped)";
  static String noteTransactionFeeText =
      "Note:\n1. TDS: 1% of amount paid by buyer (always)\n2. Handling Fee: Not charged on any cancellation or return";
  static String refundCoverageSettings =
      "Refund Coverage Settings (Coming soon)";
  static String chooseWhichFeesToCover =
      "Choose which fees you want to cover to offer better refunds to buyers. These costs will be deducted from your account balance.";
  static String coverTransactionFeeAndTDS =
      "Cover Transaction Fee + TDS on buyer-initiated cancellations";
  static String buyerGetsFullRefund =
      "Buyer gets full refund (always shipping if shipped)";
  static String coverDeliveryFeeEvenAfterShipping =
      "Cover Delivery Fee even after shipping";
  static String buyerGetsFullDeliveryRefund =
      "Buyer gets full delivery refund even if item was shipped";
  static String doYouWantTheseCoverageSettings =
      "Do you want these coverage settings?";
  static String yesCoverage = "Yes";
  static String noCoverage = "No";

  // Product specific strings
  static String onlyLimitedOptionsAvailable =
      "Only limited options available now. All options will soon be available.";
  static String manageFeeResponsibilityProduct = "Manage fee responsibility";
  static String feeResponsibilityProduct = "Fee responsibility";
  static String customerPaysDropdown = "customer pays";
  static String partialRefundForCustomersProduct =
      "Partial refund for customers.";
  static String deliveryFeeRefundedIfNotShipped =
      "• Delivery fee refunded if product not shipped.";
  static String transactionFeeResponsibilityAndDeliveryFee =
      "• Transaction fee responsibility and delivery fee of shipped product falls on either by seller or customer based on settings.";
  static String autoCancelledOrdersProduct = "Auto-cancelled orders";
  static String sellerPaysProduct = "seller pays";
  static String refundsInFullForCustomersStoreCovers =
      "Refunds in full for customers. Store covers transaction fee when an order is auto-cancelled, reflected as a deduction in account balance.";
  static String sellerCancelsWaitingOrderProduct =
      "Seller cancels waiting order";
  static String refundsInFullForCustomersSellerCovers =
      "Refunds in full for customers. Store covers transaction fee when an unconfirmed order is cancelled by seller , reflected as a deduction in account balance.";
  static String buyerCancelsWaitingOrderProduct = "Buyer cancels waiting order";
  static String customerPaysProduct = "customer pays";
  static String partialRefundForCustomerDeductedWithTransactionFee =
      "Partial refund for customer. Customer refund amount will be deducted with transaction fee when an unconfirmed order is cancelled by buyer.";
  static String feeResponsibilityProductSection = "Fee responsibility";
  static String deductionsCustomerSideRefundAmounts =
      "Deductions to be done on customer side will be made in refund amounts and deductions on store side will be deducted from Store Account balance.";
  static String transactionFeeProduct = "Transaction fee";
  static String whoeverCancelsPays = "whoever cancels pays";
  static String ifBuyerCancelsFeeDeductedFromRefund =
      "• If Buyer Cancels: Fee deducted from buyer's refund.";
  static String ifStoreCancelsOrAutoCancelsFeeDeductedFromStore =
      "• If Store Cancels or Order Auto-Cancels: Fee deducted from store account.";
  static String ifReturnHappensStoreBearsFee =
      "• If Return Happens: Store bears the fee.";
  static String deliveryFeeProduct = "Delivery fee";
  static String notRefundedAfterShipping = "Not refunded after shipping";
  static String ifCustomerCancelsAfterShippingInitiated =
      "• If customer cancels after shipping initiated, then delivery fee is deducted from customer. If cancellation happens before shipping, delivery fee would be refunded.";
  static String inCaseOfReturnsDeliveryFeeNotRefunded =
      "• In case of returns, delivery fee is not refunded as shipping is already made.";
  static String handlingFeeProduct = "Handling fee";
  static String noChargeApplied = "No charge is applied";
  static String swadesicDoesntChargeAnyFee =
      "Swadesic doesn't charge any fee on orders that are cancelled or returned";
  static String tdsProduct = "TDS";
  static String onePercentOfAmountPaid = "1% of Amount paid";
  static String onePercentOfAmountPaidByCustomer =
      "1% of amount paid by customer will still be considered even during cancellations & returns.";

  // Return pickup strings
  static String returnPickupResponsibility = "Return pickup responsibility";
  static String returnsArePrimarilyStoreResponsibility =
      "Returns are primarily store responsibility. Swadesic does automatic return process initiation if opt Swadesic shipping during delivery.";

  // Buyer refund policy strings
  static String buyerRefundPolicy = "Refund policy";
  static String buyerFullRefundBasedOnConditions =
      "Full refund based on conditions";
  static String buyerIfSellerCancelsOrAutoCancels =
      "If seller cancels or auto-cancels";
  static String buyerFullRefund = "Full refund";
  static String buyerIfBuyerCancels = "If buyer cancels";
  static String buyerPartialRefund = "Partial refund";
  static String buyerTransactionFeeTDSDeducted =
      "Transaction fee + TDS (1%) deducted by payment gateway (typically 2.36%) will be reduced from the refund amount";
  static String buyerDeliveryFeeRefundedOnlyIfShippingNotStarted =
      "Delivery fee refunded only if shipping hasn't started";
  static String buyerIfBuyerReturnTheProduct = "If buyer return the product";
  static String buyerDeliveryFeeNotRefundedSinceAlreadyShipped =
      "Delivery fee not refunded (since already shipped)";
  //endregion

//region App share message
  static String userInviteeMessage =
      "Hey!\nI'm using Swadesic, a platform supporting Swadeshi products and small businesses. It's a community where you can find great alternatives and help build a self-reliant Bharat—truly a strong boost for the vocal for local movement!\n\nYou can instantly start supporting small businesses across India and shop safely while platform manages payments, backed by a refund guarantee. Sellers can set up stores for free, start receiving orders, and build a loyal customer community";
  static String storeInviteeMessage =
      "Hey!\nAs someone who runs a store on Swadesic, I've found a platform that genuinely supports Swadeshi products and small businesses. It's a community where you can find great alternatives and help build a self-reliant Bharat—truly a strong boost for the vocal for local movement!\n\nYou can instantly start supporting small businesses across India and shop safely while platform manages payments, backed by a refund guarantee. Sellers can set up stores for free, start receiving orders, and build a loyal customer community";
  static String otherSharingAProductMessage =
      "Check out this product! \nI found this on Swadesic, a Swadeshi Marketplace Community. Know more here:\n";
  static String ownerSharingAProductMessage =
      "Check out my product!\nI’ve got this on my store at Swadesic, a community supporting local brands. Check it out here:\n";
  static String otherSharingAStoreMessage =
      "Check out this amazing store!\nI found this store on Swadesic, supporting Swadeshi products and small businesses. Explore their store & products here:\n";
  static String ownerSharingAStoreMessage =
      "Check out my store!\nI’m excited to share my store on Swadesic with you, where you can find great Swadeshi products. Check it out:\n";

  //endregion

  //region Parse json
//region Parse json
  static void fromJson(Map<String, dynamic> json) {
    userInviteeMessage = json['user_invitee_message'] ?? userInviteeMessage;
    storeInviteeMessage = json['store_invitee_message'] ?? storeInviteeMessage;
    otherSharingAProductMessage =
        json['other_sharing_a_product_message'] ?? otherSharingAProductMessage;
    ownerSharingAProductMessage =
        json['owner_sharing_a_product_message'] ?? ownerSharingAProductMessage;
    otherSharingAStoreMessage =
        json['other_sharing_a_store_message'] ?? otherSharingAStoreMessage;
    ownerSharingAStoreMessage =
        json['owner_sharing_a_store_message'] ?? ownerSharingAStoreMessage;
  }
//endregion

  //endregion
  // static void fromJson(Map<String, dynamic> json) {
  //   welcomeMessage = json['welcomeMessage'] ?? '';
  // }
}
