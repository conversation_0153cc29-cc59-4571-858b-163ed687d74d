import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:swadesic/features/buyers/buyer_home/banner/banner_action.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/banner_response/banner_response.dart';
import 'package:swadesic/services/banner_services/banner_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/store_user_product_navigation/store_user_product_navigation.dart';

class BannerBloc {
  // region Common Variables
  BuildContext context;

  int currentPage = 0;
  ///Banner service and response
  late BannerResponse bannerResponse  = BannerResponse();
   late Timer timer;
  late List<BannerInfo> bannerList = [];
  final bool isFromFeed;


  // endregion
  //region Page View Controller
  PageController imageSliderPageCtrl = PageController();
  //endregion


  //region Controller

  final bannerCtrl = StreamController<List<BannerInfo>>.broadcast();
  //endregion

  // region | Constructor |
  BannerBloc(this.context, this.isFromFeed);
  // endregion

  // region Init
  void init() async{
     await getBanner();
  }
// endregion
  //region Get recently banner
  Future<void> getBanner({bool isAutoSlide = true}) async {
    try {
      //Api call
      bannerResponse = await BannerService().getBanner();
      //Filter feed banners
      await filterBanners();
      //Auto slide
    isAutoSlide?autoSlide():null;
      //Broadcast list of banners
      !bannerCtrl.isClosed?bannerCtrl.sink.add(bannerList):null;

    } on ApiErrorResponseMessage catch(error) {
      !bannerCtrl.isClosed?bannerCtrl.sink.add([]):null;
      return;
    } catch (error) {
      !bannerCtrl.isClosed?bannerCtrl.sink.add([]):null;
      return;
    }
  }
//endregion

  //region Filer banners
  Future<void> filterBanners()async{
    //If user
    if(AppConstants.appData.isUserView!){
      //If banner
      if(isFromFeed){
        bannerList = bannerResponse.bannerInfoList!.where((element) => element.currentUserType == "USER" && element.bannerLocation == "FEED").toList();
      }
      //Else Home
      else{
        bannerList = bannerResponse.bannerInfoList!.where((element) => element.currentUserType == "USER" && element.bannerLocation == "HOME").toList();
      }

    }
    //If seller
    if(AppConstants.appData.isStoreView!){
      //If banner
      if(isFromFeed){
        bannerList = bannerResponse.bannerInfoList!.where((element) => element.currentUserType == "STORE" && element.bannerLocation == "FEED").toList();
      }
      //Else Home
      else{
        bannerList = bannerResponse.bannerInfoList!.where((element) => element.currentUserType == "STORE" && element.bannerLocation == "HOME").toList();
      }

    }
  }
  //endregion



  //region Auto slide
  void autoSlide()async{

    //If banner list is empty or item is 1
    if(bannerList.isEmpty){
      return;
    }
    timer = Timer.periodic(const Duration(seconds: 5), (Timer timer) {
      //If api data is empty
      if(bannerList.isEmpty){
        return;
      }
      if (currentPage < bannerList.length - 1) {
        currentPage++;
      } else {
        currentPage = 0;
      }
      imageSliderPageCtrl.animateToPage(
        currentPage,
        duration: const Duration(milliseconds: 800),
        curve: Curves.ease,
      );
    });
  }
  //endregion


  //region On tap banner
  void onTapBanner({required BannerInfo bannerInfo})async{
    BannerAction(context,bannerInfo);
  }
  //endregion

  //region On Change Slider
  void onChangeSlider(int index) {
    currentPage = index;
    bannerCtrl.sink.add(bannerList);
  }
//endregion


//region Dispose
  void dispose() {
    imageCache.clear();
    bannerCtrl.close();
    timer.cancel();
    imageSliderPageCtrl.dispose();
  }
//endregion

}
