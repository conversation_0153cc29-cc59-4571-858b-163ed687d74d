import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/user_reward_and_invitees/user_reward/balance_and_invite_code/balance_and_invite_code.dart';
import 'package:swadesic/features/buyers/user_reward_and_invitees/user_reward/reward_transations/reward_transactions.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';

class UserRewardScreen extends StatefulWidget {
  const UserRewardScreen({super.key});

  @override
  State<UserRewardScreen> createState() => _UserRewardScreenState();
}

class _UserRewardScreenState extends State<UserRewardScreen> with AutomaticKeepAliveClientMixin{

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    return SafeArea(child: body());
  }


  //region Body
Widget body(){
    return ListView(
      shrinkWrap: true,
      children: [
        BalanceAndInviteCode(),
        RewardTransactions(),
      ],
    );
}
//endregion


}
