import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/user_address_services/user_address_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

enum ChangeItHereState { Loading, Success, Failed, Empty }

class ChangeItHereBloc {
  // region Common Variables
  BuildContext context;
  bool isChangeFieldVisible = false;

  ///User Address Service
  late UserAddressService userAddressService;
  final SecureCheckoutBloc secureCheckoutBloc;
  // endregion

  //region Controller
  final refreshCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text ctrl
  final TextEditingController changeNumberTextCtrl = TextEditingController();
  final FocusNode numberFocus = FocusNode();
  //endregion

  // region | Constructor |
  ChangeItHereBloc(this.context, this.secureCheckoutBloc);

  // endregion

  // region Init
  init() async {
    userAddressService = UserAddressService();
  }

// endregion

//region On tap change
  void onTapChange() {
    //Visible field
    isChangeFieldVisible = true;
    //Refresh
    refreshCtrl.sink.add(true);
    //Add number to the field
    changeNumberTextCtrl.text = secureCheckoutBloc.selectedAddress.phoneNumber!;
    pinCodeDialogBox();
  }
//endregion

  //region Open PinCode Dialog Box
  void pinCodeDialogBox() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
              title: const Center(child: Text("Update phone number")),
              titleTextStyle: const TextStyle(
                  color: AppColors.writingColor2,
                  fontSize: 16,
                  fontFamily: "LatoSemiBold",
                  fontWeight: FontWeight.w600),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: double.infinity,
                    margin: const EdgeInsets.only(top: 10),
                    padding: const EdgeInsets.all(10),
                    decoration: const BoxDecoration(
                        color: AppColors.lightStroke,
                        borderRadius: BorderRadius.all(Radius.circular(20))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: TextFormField(
                            maxLines: 1,
                            minLines: 1,
                            controller: changeNumberTextCtrl,
                            // onChanged: (value) {
                            //   secureCheckoutBloc.cartDetailsResponse.deliveryNotes = value;
                            // },
                            keyboardType: TextInputType.number,
                            style: const TextStyle(
                                fontFamily: "LatoRegular",
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: AppColors.appBlack),
                            decoration: InputDecoration(
                                contentPadding: EdgeInsets.zero,
                                isDense: true,
                                border: InputBorder.none,
                                hintText: AppStrings.mobileNumber,
                                hintStyle: const TextStyle(
                                    fontFamily: "LatoSemiBold",
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                    color: AppColors.writingColor2)),
                            inputFormatters: <TextInputFormatter>[
                              FilteringTextInputFormatter.allow(
                                  RegExp(AppConstants.onlyInt)),
                              LengthLimitingTextInputFormatter(10),
                            ],
                          ),
                        ),
                        SvgPicture.asset(
                          AppImages.editIcon,
                          fit: BoxFit.fill,
                          color: AppColors.writingColor2,
                        )
                      ],
                    ),
                  ),
                  verticalSizedBox(20),
                  CupertinoButton(
                      borderRadius: const BorderRadius.all(Radius.circular(50)),
                      color: AppColors.brandBlack,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: const SizedBox(
                        width: double.infinity,
                        child: Center(
                          child: Text(
                            AppStrings.update,
                            style: TextStyle(
                                fontFamily: "LatoSemiBold",
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                                color: AppColors.appWhite),
                          ),
                        ),
                      ),
                      onPressed: () {
                        editAddressApiCall();
                        // secureCheckoutBloc.sellerNote();
                        // secureCheckoutBloc.orderCreate();
                      }),
                ],
              ));
        }).then((value) {
      changeNumberTextCtrl.clear();
    });
  }
//endregion

  //region Edit Address Api call
  void editAddressApiCall() async {
    //region Try
    try {
      if (changeNumberTextCtrl.text.isEmpty ||
          changeNumberTextCtrl.text.length < 10) {
        CommonMethods.toastMessage(AppStrings.enterValidNumber, context);
        return;
      }
      await userAddressService.editAddress(
        address: secureCheckoutBloc.selectedAddress.address!,
        city: secureCheckoutBloc.selectedAddress.city!,
        pinCode: secureCheckoutBloc.selectedAddress.pincode!,
        state: secureCheckoutBloc.selectedAddress.state!,
        name: secureCheckoutBloc.selectedAddress.name!,
        defaultAddress: false,
        mobileNumber: changeNumberTextCtrl.text,
        userAddressId: secureCheckoutBloc.selectedAddress.useraddressid!,
      );
      //Updated data to selected address
      secureCheckoutBloc.selectedAddress.phoneNumber =
          changeNumberTextCtrl.text;

      //Close dialog box
      Navigator.of(context, rootNavigator: true).pop();
      //Mobile number updated
      CommonMethods.toastMessage(AppStrings.mobileNumberUpdated, context);
      //Get address
      secureCheckoutBloc.getAddress();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
      return;
      //print(error.message);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);

      return;
    }
  }

//endregion

//region Dispose
  void dispose() {
    refreshCtrl.close();
  }
//endregion
}
