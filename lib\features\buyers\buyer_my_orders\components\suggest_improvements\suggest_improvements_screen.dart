import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/suggest_improvements/suggest_improvements_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class BuyerSuggestImprovementsScreen extends StatefulWidget {
  final List<SubOrder> subOrderList;
  const BuyerSuggestImprovementsScreen({Key? key, required this.subOrderList}) : super(key: key);

  @override
  State<BuyerSuggestImprovementsScreen> createState() => _BuyerSuggestImprovementsScreenState();
}

class _BuyerSuggestImprovementsScreenState extends State<BuyerSuggestImprovementsScreen> {

  // region Bloc
  late BuyerSuggestImprovementBloc buyerSuggestImprovementBloc;

  // endregion

  // region Init
  @override
  void initState() {
    buyerSuggestImprovementBloc = BuyerSuggestImprovementBloc(context);
    buyerSuggestImprovementBloc.init();
    super.initState();
  }

  // endregion



  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: buyerSuggestImprovementBloc.bottomSheetRefresh.stream,
        builder: (context, snapshot) {
          return SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: SingleChildScrollView(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      howEasy(),
                      verticalSizedBox(20),
                      whatCanWeDo(),
                      verticalSizedBox(10),
                      whatDidYouLike(),
                      verticalSizedBox(10),
                      problemYouAreFacing(),
                      verticalSizedBox(10),
                      overAllExperience(),



                    ]
                ),
              )
          );
        }
    );
  }



  //region How easy
  Widget howEasy(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        appText("How easy it is to process returns?",fontSize:16,fontWeight: FontWeight.w400,color: AppColors.appBlack,maxLine:1,fontFamily:AppConstants.rRegular,opacity: 0.7,textAlign: TextAlign.start ),
        verticalSizedBox(20),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 25),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.lightStroke)
                ),
              ),
              Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.lightStroke)
                ),
              ),
              Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.lightStroke)
                ),
              ),
              Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.lightStroke)
                ),
              ),
              Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.lightStroke)
                ),
              ),

            ],
          ),
        ),
        verticalSizedBox(15),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              appText("Not easy",fontSize:14,fontWeight: FontWeight.w400,color: AppColors.appBlack,maxLine:1,fontFamily:AppConstants.rRegular,textAlign: TextAlign.start ),
              verticalSizedBox(15),
              appText("Easy",fontSize:14,fontWeight: FontWeight.w400,color: AppColors.appBlack,maxLine:1,fontFamily:AppConstants.rRegular,textAlign: TextAlign.start ),

            ],
          ),
        )

      ],
    );
  }
  //endregion

  //region What can we do
  Widget whatCanWeDo(){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [

          appText("What can we do to improve your experience?",fontSize:16,fontWeight: FontWeight.w400,color: AppColors.appBlack,maxLine:1,fontFamily:AppConstants.rRegular,opacity: 0.7  ),
          verticalSizedBox(10),
          colorFilledTextField(
            context: context,
            textFieldCtrl:TextEditingController(),
            hintText: "write..",
            hintFontFamily: AppConstants.rRegular,
            textFieldMaxLine: 5,
            hintFontSize: 14,
            hintTextColor: AppColors.writingColor3,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.done,
            // onChangeText: sellerOnBoardingBloc.onTextChange,
            regExp:AppConstants.acceptAll,
            fieldTextCapitalization: TextCapitalization.words,
            maxCharacter: 100,
          ),
        ],
      ),
    );
  }
//endregion


  //region What did you like
  Widget whatDidYouLike(){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          appText("What did you like the most?",fontSize:16,fontWeight: FontWeight.w400,color: AppColors.appBlack,maxLine:1,fontFamily:AppConstants.rRegular,opacity: 0.7  ),
          verticalSizedBox(10),
          colorFilledTextField(
            context: context,
            textFieldCtrl:TextEditingController(),
            hintText: "write..",
            textFieldMaxLine: 5,
            hintFontFamily: AppConstants.rRegular,
            hintFontSize: 14,
            hintTextColor: AppColors.writingColor3,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.done,
            // onChangeText: sellerOnBoardingBloc.onTextChange,
            regExp:AppConstants.acceptAll,
            fieldTextCapitalization: TextCapitalization.words,
            maxCharacter: 100,
          ),
        ],
      ),
    );
  }
//endregion


  //region Problem you are facing
  Widget problemYouAreFacing(){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          appText("Problems you are facing in your business right now, we will see if we can help.",fontSize:16,fontWeight: FontWeight.w400,color: AppColors.appBlack,maxLine:2,fontFamily:AppConstants.rRegular,opacity: 0.7 ),
          verticalSizedBox(10),
          colorFilledTextField(
            context: context,
            textFieldCtrl:TextEditingController(),
            hintText: "write..",
            textFieldMaxLine: 5,
            hintFontSize: 14,
            hintFontFamily: AppConstants.rRegular,

            hintTextColor: AppColors.writingColor3,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.done,
            // onChangeText: sellerOnBoardingBloc.onTextChange,
            regExp:AppConstants.acceptAll,
            fieldTextCapitalization: TextCapitalization.words,
            maxCharacter: 100,
          ),
        ],
      ),
    );
  }
//endregion



  //region Over all experience
  Widget overAllExperience(){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          appText("Over all experience",fontSize:16,fontWeight: FontWeight.w400,color: AppColors.appBlack,maxLine:2,fontFamily:AppConstants.rRegular,opacity: 0.7 ),
          verticalSizedBox(10),
          Container(
            width: double.infinity,
            alignment: Alignment.center,
            child: RatingBar.builder(
              initialRating: 5,
              minRating: 1,
              direction: Axis.horizontal,
              glowColor: AppColors.yellow,
              unratedColor: AppColors.lightGray,
              allowHalfRating: false,
              itemCount: 5,
              itemPadding: const EdgeInsets.symmetric(horizontal: 5.0),
              itemBuilder: (context, _) => const Icon(
                Icons.star,
                color: Colors.amber,
              ),
              onRatingUpdate: (rating) {
                //buyerProductCommentBloc.productRating = rating.round();
                // //print(buyerProductCommentBloc.productRating);
              },
            ),
          ),
          verticalSizedBox(10),
          colorFilledTextField(
            context: context,
            textFieldCtrl:TextEditingController(),
            hintText: "write..",
            textFieldMaxLine: 5,
            hintFontSize: 14,
            hintFontFamily: AppConstants.rRegular,

            hintTextColor: AppColors.writingColor3,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.done,
            // onChangeText: sellerOnBoardingBloc.onTextChange,
            regExp:AppConstants.acceptAll,
            fieldTextCapitalization: TextCapitalization.words,
            maxCharacter: 100,
          ),
        ],
      ),
    );
  }
//endregion

}
