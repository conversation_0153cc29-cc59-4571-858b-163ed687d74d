import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_email_field/app_email_field_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class AppEmailField extends StatefulWidget {
  final BuildContext context;
  final TextEditingController textEditingController;
  final String hintText;
  final int minLines;
  final int maxLines;
  final bool enabled;
  final dynamic onEditingComplete;
  final dynamic onChanged;
  final dynamic onSaved;
  final Function(bool?) isValid;

  const AppEmailField({super.key,
    this.minLines = 1,
    this.maxLines = 1,
    this.enabled = true,
    required this.context, required this.textEditingController, required this.hintText, this.onEditingComplete, this.onChanged, this.onSaved, required this.isValid});

  @override
  State<AppEmailField> createState() => _AppEmailFieldState();
}



class _AppEmailFieldState extends State<AppEmailField> {
  //region Bloc
  late AppEmailFieldBloc appEmailFieldBloc;
  //endregion

  //region Init
  @override
  void initState() {
    appEmailFieldBloc = AppEmailFieldBloc(context,widget.isValid,widget.textEditingController);
    super.initState();
  }
  //endregion


  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream:appEmailFieldBloc.refreshCtrl.stream,
      builder: (context, snapshot) {
        return TextFormField(
          enabled: widget.enabled,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          textCapitalization: TextCapitalization.none,
          textAlign: TextAlign.start,
          keyboardType: TextInputType.emailAddress,
          controller: widget.textEditingController,
          inputFormatters: [FilteringTextInputFormatter.deny(RegExp(r'\s'))],
          onEditingComplete: () {
            CommonMethods.closeKeyboard(context);
            widget.onEditingComplete == null ? const SizedBox() : widget.onEditingComplete();
          },
          onChanged: (value) {
            widget.onChanged == null ? Container() : widget.onChanged();
            appEmailFieldBloc.onChangeEmail();
          },
          onSaved: (value) {
            widget.onSaved();
          },
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
          decoration: AppTextFields.appInputDecoration(

              hintText: widget.hintText,
              invalidMessage: "Invalid email",
              isValid: appEmailFieldBloc.isEmailValid??false,
              validMessage:null,
              visibleValidInvalid: appEmailFieldBloc.isEmailValid != null),
        );
      }
    );
  }
}
