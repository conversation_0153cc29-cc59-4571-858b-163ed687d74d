import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_order_common_widget.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/return_in_progress/return_in_progress_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ReturnInProgress extends StatefulWidget {
  final List<SubOrder> subOrderList;
  final BuyerMyOrdersBloc buyerMyOrdersBloc;
  final Order store;
  const ReturnInProgress(
      {Key? key,
      required this.subOrderList,
      required this.buyerMyOrdersBloc,
      required this.store})
      : super(key: key);

  @override
  State<ReturnInProgress> createState() => _ReturnInProgressState();
}

class _ReturnInProgressState extends State<ReturnInProgress> {
  // region Bloc
  late ReturnInProgressBloc returnInProgressBloc;

  // endregion

  // region Init
  @override
  void initState() {
    returnInProgressBloc =
        ReturnInProgressBloc(context, widget.store, widget.buyerMyOrdersBloc);
    returnInProgressBloc.init();
    super.initState();
  }

  // endregion

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.textFieldFill1,
      child: ExpandablePanel(
        theme: const ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: false,
          tapHeaderToExpand: true,
          tapBodyToExpand: false,

          //useInkWell: false,
          iconPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          //iconColor: Colors.green
        ),
        //Waiting for confirmation
        ///Header
        header: Container(
          padding: const EdgeInsets.all(10),
          color: AppColors.textFieldFill1,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(AppImages.thumbUpIcon),
              horizontalSizedBox(10),
              const Text(
                "Return picked, shipping in progress",
                style: TextStyle(
                  fontFamily: "LatoSemibold",
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.darkGray,
                ),
              ),
              Expanded(child: horizontalSizedBox(10)),
            ],
          ),
        ),

        ///Collapsed
        collapsed: messageRefund(),

        ///Expanded
        expanded: Container(
          color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              messageRefund(),

              //Sub-order list
              Container(
                color: AppColors.appWhite,
                child: ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    shrinkWrap: true,
                    itemCount: widget.subOrderList.length,
                    itemBuilder: (BuildContext, index) {
                      return Column(
                        children: [
                          //Divider
                          Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 10),
                              child: divider(),
                            ),
                          ),
                          //Select and color change
                          Container(
                            decoration: const BoxDecoration(
                                color: AppColors.appWhite,
                                //color: AppColors.green3,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(10))),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                //Return picked on 24-01-2021
                                const Text(
                                  "Return picked on 24-01-2021",
                                  style: TextStyle(
                                    fontFamily: "LatoSemibold",
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.appBlack,
                                  ),
                                ),
                                //You will receive ₹ 450 as the refund amount
                                Container(
                                  padding: const EdgeInsets.only(
                                      bottom: 10, top: 10),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Expanded(
                                        child: Text(
                                          "You will receive ₹ 450 as the refund amount",
                                          //overflow: TextOverflow.visible,
                                          maxLines: 1,
                                          style: TextStyle(
                                            fontFamily: "LatoBold",
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            color: AppColors.appBlack,
                                          ),
                                        ),
                                      ),
                                      SvgPicture.asset(AppImages.exclamation)
                                    ],
                                  ),
                                ),

                                buyerSubOrderCard(
                                  context: context,
                                  subOrder: widget.subOrderList[index],
                                ),
                                //Your reason: Reason mentioned
                                const Text(
                                  "Return reason: Reason mentioned",
                                  textAlign: TextAlign.start,
                                  style: TextStyle(
                                      fontFamily: "LatoRegular",
                                      fontWeight: FontWeight.w400,
                                      fontSize: 14,
                                      color: AppColors.appBlack),
                                ),
                                Row(
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        returnInProgressBloc.onTapTracking(
                                            widget.subOrderList,
                                            widget.buyerMyOrdersBloc);
                                      },
                                      child: Container(
                                        margin: const EdgeInsets.only(top: 10),
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 10, horizontal: 10),
                                        decoration: BoxDecoration(
                                            color: AppColors.appWhite,
                                            borderRadius:
                                                const BorderRadius.all(
                                                    Radius.circular(10)),
                                            border: Border.all(
                                                color:
                                                    AppColors.inActiveGreen)),
                                        child: const Center(
                                          child: Text(
                                            "Track return status ",
                                            style: TextStyle(
                                                fontFamily: "LatoRegular",
                                                fontWeight: FontWeight.w400,
                                                fontSize: 14,
                                                color: AppColors.appBlack),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    }),
              ),
            ],
          ),
        ),
      ),
    );
  }

//region Message and refund amount calculate
  Widget messageRefund() {
    return Container(
      color: AppColors.appWhite,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //Refund process will start after seller receives the product from delivery services
          Container(
            padding: const EdgeInsets.all(10),
            color: AppColors.appWhite,
            child: const Text(
              "Refund process will start after seller receives the product from delivery services",
              //overflow: TextOverflow.visible,
              maxLines: 2,
              style: TextStyle(
                fontFamily: "LatoBold",
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: AppColors.brandBlack,
              ),
            ),
          ),
          //Tip
          tip("Know how returns & refunds work"),

          const Padding(
            padding: EdgeInsets.only(top: 10, left: 10, right: 10),
            child: Row(
              children: [
                //howRefundAmountCalculated(widget.subOrderList, context)
              ],
            ),
          )
        ],
      ),
    );
  }
//endregion
}
