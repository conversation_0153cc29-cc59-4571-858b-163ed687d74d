import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_detail_card.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affilite_program_transaction.dart';
import 'package:swadesic/features/widgets/app_container/app_container.dart';
import 'package:swadesic/features/widgets/invite_more_frends/invite_more_friends.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class AffiliateProgramScreen extends StatefulWidget {
  const AffiliateProgramScreen({super.key});

  @override
  State<AffiliateProgramScreen> createState() => _AffiliateProgramScreenState();
}

class _AffiliateProgramScreenState extends State<AffiliateProgramScreen> {
  //region Bloc
  late AffiliateProgramBloc affiliateProgramBloc;
  //endregion

  //region Init
  @override
  void initState() {
    affiliateProgramBloc = AffiliateProgramBloc(context);
    affiliateProgramBloc.init();
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<AffiliateProgramState>(
        stream: affiliateProgramBloc.affiliateProgramStateCtrl.stream,
        initialData: AffiliateProgramState.Loading,
        builder: (context, snapshot) {
          // Loading
          if (snapshot.data == AffiliateProgramState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }

          //Success
          if (snapshot.data == AffiliateProgramState.Success) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  availablePayout(),
                  const SizedBox(
                    height: 10,
                  ),
                  inviteCodeAndManageBank(),
                  manageBank(),
                  Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      child: AffiliateProgramDetailCard(
                          affiliateProgramBloc: affiliateProgramBloc)),
                  Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      child: const InviteMoreFriends()),
                  AffiliateProgramTransaction(
                    affiliateProgramBloc: affiliateProgramBloc,
                  )
                ],
              ),
            );
          }

          //Failed
          return AppCommonWidgets.errorWidget(onTap: () {
            affiliateProgramBloc.getInviteCodeAndTransaction();
          });
        });
  }
//endregion

//region Available payout
  Widget availablePayout() {
    return AppBorderContainer(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
      child: Container(
        padding: const EdgeInsets.only(bottom: 10, left: 10, right: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppStrings.availablePayout,
                  style: AppTextStyle.sectionHeading(
                      textColor: AppColors.appBlack),
                ),
                const SizedBox(
                  height: 10,
                ),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Text(
                    "₹${affiliateProgramBloc.affiliateProgramResponse.userAffiliateBalance}",
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.w600,
                      fontFamily: "RobotoRegular",
                      color: AppColors.appBlack,
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
//endregion

//region Invite code and manage bank
  Widget inviteCodeAndManageBank() {
    return AppBorderContainer(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          children: [
            Image.asset(
              AppImages.begIcon,
              height: 18,
            ),
            const SizedBox(
              width: 5,
            ),
            Text(
              AppStrings.inviteCode,
              style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
            ),
            Expanded(
                child: const SizedBox(
              width: 10,
            )),
            Text("${affiliateProgramBloc.inviteCode}",
                style: AppTextStyle.sectionHeading(
                    textColor: AppColors.brandBlack)),
            const SizedBox(
              width: 15,
            ),
            InkWell(
                onTap: () {
                  CommonMethods.copyText(context,
                      "${AppStrings.userInviteeMessage}\n${AppConstants.domainName}?ref=${affiliateProgramBloc.inviteCode}");
                },
                child: Image.asset(
                  AppImages.copy,
                  height: 22,
                )),
            const SizedBox(
              width: 15,
            ),
            InkWell(
                onTap: () {
                  CommonMethods.share(
                      "${AppStrings.userInviteeMessage}\n${AppConstants.domainName}?ref=${affiliateProgramBloc.inviteCode}");
                },
                child: Image.asset(
                  AppImages.shareThreeLine,
                  height: 22,
                )),
            const SizedBox(
              width: 15,
            ),
          ],
        ));
  }
//endregion

//region Manage bank
  Widget manageBank() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Left text: Manage bank accounts
          InkWell(
            onTap: () {
              affiliateProgramBloc.goToBankAccount();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Text('Manage bank accounts',
                  style: AppTextStyle.access0(
                      textColor: AppColors.appBlack, isUnderline: true)),
            ),
          ),

          InkWell(
            onTap: () {
              affiliateProgramBloc.openPayOutEligibilityDialog();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Text(
                  "${affiliateProgramBloc.affiliateProgramResponse.isAutoPayEnable! ? "Auto-payout enabled" : "Not eligible for payout"}",
                  style: AppTextStyle.smallText(
                      textColor: affiliateProgramBloc
                              .affiliateProgramResponse.isAutoPayEnable!
                          ? AppColors.brandBlack
                          : AppColors.orange,
                      isUnderline: !affiliateProgramBloc
                          .affiliateProgramResponse.isAutoPayEnable!)),
            ),
          ),
        ],
      ),
    );
  }
//endregion
}
