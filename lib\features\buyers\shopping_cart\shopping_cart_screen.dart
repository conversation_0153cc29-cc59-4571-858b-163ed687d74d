import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/shopping_cart/available_to/available_to.dart';
import 'package:swadesic/features/buyers/shopping_cart/change_it_here/change_it_here.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_screen.dart';

// import 'package:hashtagable/widgets/hashtag_text_field.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_common_widgets.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_price/shopping_cart_price.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_field_style.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

// region Buyer Store Screen
class ShoppingCartScreen extends StatefulWidget {
  //final bool? snowBackButton;
  //late _ShoppingCartScreenState timeSheetListScreenState;
  const ShoppingCartScreen({Key? key}) : super(key: key);

  //ShoppingCartScreen({Key? key}) : super(key: key);

  //#region Region - createState
  @override
  _ShoppingCartScreenState createState() => _ShoppingCartScreenState();

//   //#endregion
//
//   //#region Region - Refresh TimeSheet
//   refreshTimeSheetList() {
//     timeSheetListScreenState.refresh();
//   }
// //#endregion
//
//
// // @override
// // _BuyerHomeScreenState createState() {
// //   //print("Visitre");
// //   return _BuyerHomeScreenState();
// //
// // }
}

// endregion
class _ShoppingCartScreenState extends State<ShoppingCartScreen> {
  // region Bloc
  late ShoppingCartBloc shoppingCartBloc;

  // endregion

  // region Init
  @override
  void initState() {
    shoppingCartBloc = ShoppingCartBloc(context);
    shoppingCartBloc.init();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
        },
        child: StreamBuilder<bool>(
            stream: AppConstants.bottomNavigationRefreshCtrl.stream,
            builder: (context, snapshot) {
              return WillPopScope(
                onWillPop: () async {
                  shoppingCartBloc.showWeSavedYourNote();
                  return false;
                },
                child: Scaffold(
                  backgroundColor: AppColors.appWhite,
                  appBar: appBar(),
                  resizeToAvoidBottomInset: true,
                  body: SafeArea(
                      // child: Container()
                      child: body()),
                ),
              );
            }));
  }

  // endregion
//
  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        onTapLeading: () {
          shoppingCartBloc.showWeSavedYourNote();
        },
        context: context,
        isCustomTitle: false,
        title: AppStrings.shoppingCart,
        isCartVisible: false,
        isMembershipVisible: false);
  }

// endregion

  // region Body
  Widget body() {
    return StreamBuilder<CartDetailState>(
        initialData: CartDetailState.Loading,
        stream: shoppingCartBloc.cartDetailStateCtrl.stream,
        builder: (context, snapshot) {
          //
          if (snapshot.data == CartDetailState.Empty) {
            return RefreshIndicator(
              color: AppColors.brandBlack,
              onRefresh: () async {
                await shoppingCartBloc.init();
              },
              child: ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  AvailableTo(shoppingCartBloc: shoppingCartBloc),
                  SizedBox(
                    height: MediaQuery.of(context).size.width / 2,
                  ),
                  SvgPicture.asset(AppImages.emptyCart),
                  Text(
                    AppStrings.yourCartIsEmpty,
                    textAlign: TextAlign.center,
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack1),
                  )
                ],
              ),
            );

            return Center(
              child: AppCommonWidgets.emptyResponseText(
                  emptyMessage: AppStrings.yourCartIsEmpty),
            );
          }
          if (snapshot.data == CartDetailState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          if (snapshot.data == CartDetailState.Failed) {
            return AppCommonWidgets.errorWidget(
                errorMessage: AppStrings.unableToLoadCart,
                onTap: () {
                  shoppingCartBloc.init();
                });
          }
          //Success
          return RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              await shoppingCartBloc.init();
            },
            child: Consumer<ShoppingCartQuantityDataModel>(
              builder: (BuildContext context,
                  ShoppingCartQuantityDataModel value, Widget? child) {
                if (value.productReferenceList.isEmpty) {
                  return RefreshIndicator(
                    color: AppColors.brandBlack,
                    onRefresh: () async {
                      await shoppingCartBloc.init();
                    },
                    child: ListView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: [
                        AvailableTo(shoppingCartBloc: shoppingCartBloc),
                        SizedBox(
                          height: MediaQuery.of(context).size.width / 2,
                        ),
                        SvgPicture.asset(AppImages.emptyCart),
                        Text(
                          AppStrings.yourCartIsEmpty,
                          textAlign: TextAlign.center,
                          style: AppTextStyle.contentText0(
                              textColor: AppColors.writingBlack1),
                        )
                      ],
                    ),
                  );
                }

                return SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    //shrinkWrap: true,
                    children: [
                      AvailableTo(shoppingCartBloc: shoppingCartBloc),
                      verticalSizedBox(10),
                      cart(),
                      //
                      verticalSizedBox(24),
                      paymentSecurelyDone(),
                      verticalSizedBox(20),
                      shoppingCartPrice(),
                      // verticalSizedBox(10),
                      //addSellerNote(),
                      //verticalSizedBox(30),
                      //offer(),
                      verticalSizedBox(10),
                      selectDeliveryAndPayment()
                      // deliveryAddressDetails(),
                      // //verticalSizedBox(20),
                      // addViewMoreAddress(),
                      // verticalSizedBox(20),
                      // // addDeliveryNote(),
                      // verticalSizedBox(20),
                      // secureCheckout(),
                      // verticalSizedBox(100),
                    ],
                  ),
                );
              },
            ),
          );
        });
  }

  // endregion

  //region Cart Items
  Widget cart() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: shoppingCartBloc.cartDetailsResponse.cartStoreList!.length,
        itemBuilder: (context, storeIndex) {
          //print(shoppingCartBloc.cartDetailsResponse.cartStoreList!.length);
          return ShoppingCartCommonWidgets.storeDropDOwn(
              cartStore: shoppingCartBloc
                  .cartDetailsResponse.cartStoreList![storeIndex],
              context: context,
              onTapDropDown: () {
                shoppingCartBloc.onTapDropDown(
                    cartStore: shoppingCartBloc
                        .cartDetailsResponse.cartStoreList![storeIndex]);
              },
              shoppingCartBloc: shoppingCartBloc);
        });
  }

  //endregion

//region Payment Are Securely Done
  Widget paymentSecurelyDone() {
    return Container(
      color: AppColors.brandBlack,
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Center(
        child: Text(
          AppStrings.paymentDoneBy,
          style: AppTextStyle.contentHeading0(textColor: AppColors.appWhite),
        ),
      ),
    );
  }

//endregion

//region Shopping cart price
  Widget shoppingCartPrice() {
    return StreamBuilder<CartPriceState>(
        stream: shoppingCartBloc.cartPriceStateCtrl.stream,
        initialData: CartPriceState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == CartPriceState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          if (snapshot.data == CartPriceState.Success) {
            return ShoppingCartPrice(
              getCartPriceResponse: shoppingCartBloc.getCartPriceResponse,
            );
          }
          return AppCommonWidgets.errorWidget(
              errorMessage: AppStrings.unableToLoadGrandTotal,
              onTap: () {
                shoppingCartBloc.getShoppingCartPrice();
              });
        });
  }

//endregion

//region Select delivery and payment
  Widget selectDeliveryAndPayment() {
    return StreamBuilder<CartPriceState>(
        stream: shoppingCartBloc.cartPriceStateCtrl.stream,
        builder: (context, snapshot) {
          return Container(
            margin: const EdgeInsets.all(20),
            child: CupertinoButton(
                borderRadius: const BorderRadius.all(Radius.circular(50)),
                color: AppColors.brandBlack,
                padding: const EdgeInsets.symmetric(vertical: 17.5),
                onPressed: snapshot.data == CartPriceState.Success
                    ? () {
                        // shoppingCartBloc.sellerNote();
                        shoppingCartBloc.goToSecureCheckout();
                      }
                    : null,
                child: SizedBox(
                  width: double.infinity,
                  child: Center(
                    child: Text(
                      AppStrings.selectDeliveryAddressAndAddressAndPayment,
                      style:
                          AppTextStyle.access1(textColor: AppColors.appWhite),
                    ),
                  ),
                )),
          );
        });
  }

//endregion

///////////////////////////////////////////////////////////
//region Not in use
// //region Delivery Address Details
//   Widget deliveryAddressDetails() {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 17),
//       child: Text(
//         AppStrings.deliveryAddress,
//         style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
//       ),
//     );
//   }
//
// //endregion
//
//   //region Selected Delivery Address
//   Widget deliveryAddress() {
//     return StreamBuilder<ShoppingAddressState>(
//         stream: shoppingCartBloc.selectedAddressRefreshCtrl.stream,
//         initialData: ShoppingAddressState.Loading,
//         builder: (context, snapshot) {
//           //print(snapshot.data);
//           return shoppingCartBloc.selectedAddress.name == null
//               ? Container()
//               : Padding(
//                   padding: const EdgeInsets.only(top: 17,left: 17,right: 17),
//                   child: Container(
//                     alignment: Alignment.centerLeft,
//                     width: double.infinity,
//                     padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
//                     decoration: BoxDecoration(
//                         color: AppColors.textFieldFill1,
//                         borderRadius: const BorderRadius.all(Radius.circular(10)),
//                         border: Border.all(color:AppColors.textFieldFill1)),
//                     child: Column(
//                       mainAxisSize: MainAxisSize.min,
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           //shoppingCartBloc.selectedAddressModel.name!??
//                           shoppingCartBloc.selectedAddress.name!,
//                           maxLines: 1,
//                           style: AppTextStyle.access0(textColor: AppColors.appBlack),
//                           // style: const TextStyle(fontFamily: "LatoSemiBold", fontWeight: FontWeight.w600, fontSize: 14, color: AppColors.appBlack),
//                         ),
//                         verticalSizedBox(10),
//                         Text(
//                           "${shoppingCartBloc.selectedAddress.address},${shoppingCartBloc.selectedAddress.city},${shoppingCartBloc.selectedAddress.state},${shoppingCartBloc.selectedAddress.pincode}",
//                           style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
//                         ),
//                         verticalSizedBox(20),
//                         InkWell(
//                           onTap: (){
//                             shoppingCartBloc.goToCartAddress();
//                             // shoppingCartBloc.onTapAddViewAddress();
//                           },
//                           child:  Text("change",
//                             style:AppTextStyle.access0(textColor: AppColors.appBlack),),
//                         )
//                       ],
//                     ),
//                   ),
//                 );
//
//           ///
//           // if (snapshot.data == ShoppingAddressState.Success) {
//           //   return shoppingCartBloc.selectedAddressModel.name == null
//           //       ? Container()
//           //       : Padding(
//           //           padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
//           //           child: Container(
//           //             alignment: Alignment.centerLeft,
//           //             width: double.infinity,
//           //             padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
//           //             decoration: BoxDecoration(
//           //                 color: AppColors.white,
//           //                 borderRadius: const BorderRadius.all(Radius.circular(10)),
//           //                 border: Border.all(color: AppColors.lightWhite2)),
//           //             child: Column(
//           //               mainAxisSize: MainAxisSize.min,
//           //               mainAxisAlignment: MainAxisAlignment.center,
//           //               crossAxisAlignment: CrossAxisAlignment.start,
//           //               children: [
//           //                 Text(
//           //                   //shoppingCartBloc.selectedAddressModel.name!??
//           //                   shoppingCartBloc.selectedAddressModel.name!,
//           //                   maxLines: 1,
//           //                   style: const TextStyle(fontFamily: "LatoSemiBold", fontWeight: FontWeight.w600, fontSize: 14, color: AppColors.appBlack),
//           //                 ),
//           //                 verticalSizedBox(10),
//           //                 Text(
//           //                   "${shoppingCartBloc.selectedAddressModel.address},${shoppingCartBloc.selectedAddressModel.city},${shoppingCartBloc.selectedAddressModel.state},${shoppingCartBloc.selectedAddressModel.pincode}",
//           //                   maxLines: 1,
//           //                   style: const TextStyle(fontFamily: "LatoRegular", fontWeight: FontWeight.w400, fontSize: 12, color: AppColors.appBlack),
//           //                 ),
//           //               ],
//           //             ),
//           //           ),
//           //         );
//           // }
//           // return Container();
//         });
//   }
//
//   //endregion
//
//
// //region Add and  View More address
//   Widget addViewMoreAddress() {
//     return StreamBuilder<ShoppingAddressState>(
//         stream: shoppingCartBloc.selectedAddressRefreshCtrl.stream,
//         builder: (context, snapshot) {
//           return shoppingCartBloc.selectedAddress.useraddressid==null
//               ? Padding(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 17,
//                   ),
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min,
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       // Padding(
//                       //   padding: const EdgeInsets.symmetric(vertical: 10),
//                       //   child: Row(
//                       //     children: [
//                       //       AppCommonWidgets.emptyResponseText(emptyMessage: AppStrings.noAddressSelected),
//                       //       Expanded(child: horizontalSizedBox(10))
//                       //     ],
//                       //   ),
//                       // ),
//                       CupertinoButton(
//                         padding: EdgeInsets.zero,
//                         onPressed: (){
//                           shoppingCartBloc.onTapAddViewAddress();
//                         },
//                         child: Container(
//                           margin: const EdgeInsets.only(top: 17),
//                           alignment: Alignment.center,
//                           width: double.infinity,
//                           decoration: BoxDecoration(color: AppColors.appWhite, borderRadius: BorderRadius.circular(100),
//                           border: Border.all(color: AppColors.appBlack,width: 1.2)
//                           ),
//                           padding: const EdgeInsets.symmetric(vertical: 13,horizontal: 20),
//                           child: Text(
//                             AppStrings.viewSelectAndAddAddress,
//                             overflow: TextOverflow.ellipsis,
//                             style:AppTextStyle.access0(textColor: AppColors.appBlack)
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 )
//               : Column(
//                   mainAxisSize: MainAxisSize.min,
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     deliveryAddress(),
//                     //Delivery notes
//                     addDeliveryNotes(),
//                     // Padding(
//                     //   padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
//                     //   child: Row(
//                     //     children: [
//                     //       InkWell(
//                     //         //padding: EdgeInsets.zero,
//                     //         onTap: () {
//                     //           shoppingCartBloc.onTapAddViewAddress();
//                     //         },
//                     //         child: Text(
//                     //           AppStrings.viewSelectAndAddAddress,
//                     //           style: const TextStyle(
//                     //               decoration: TextDecoration.underline,
//                     //               fontFamily: "LatoBold",
//                     //               fontWeight: FontWeight.w700,
//                     //               fontSize: 12,
//                     //               color: AppColors.writingColor2),
//                     //         ),
//                     //       ),
//                     //       const Expanded(child: SizedBox())
//                     //     ],
//                     //   ),
//                     // ),
//                     // verticalSizedBox(20),
//
//                     Container(
//                       margin: const EdgeInsets.only(bottom: 40,left: 17,right: 17),
//                       child: Text(
//                         "Delivery note will be sent to all stores in the cart",
//                         style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
//                       ),
//                     ),
//
//                     Container(
//                         margin: const EdgeInsets.symmetric(horizontal: 17,),
//                         child: Text(AppStrings.contactDetails,style: AppTextStyle.contentHeading0(textColor:AppColors.appBlack),)),
//
//                     ChangeItHere(shoppingCartBloc: shoppingCartBloc,)
//                   ],
//                 );
//         });
//   }
//
// //endregion
//
// //region Add Delivery Note
//
//    Widget addDeliveryNotes(){
//     //If delivery notes are empty and text field visibility is false
//     if(shoppingCartBloc.cartDetailsResponse.deliveryNotes.isEmpty && !shoppingCartBloc.cartDetailsResponse.isDeliveryNotesTextFieldVisible){
//       return CupertinoButton(
//         padding: EdgeInsets.zero,
//         onPressed: (){
//           shoppingCartBloc.cartDetailsResponse.isDeliveryNotesTextFieldVisible = true;
//           //Refresh screen
//           shoppingCartBloc.shoppingCartCtrl.sink.add(ShoppingCartState.Success);
//         },
//         child: Container(
//           padding: const EdgeInsets.symmetric(horizontal: 15,vertical: 10),
//           decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(7),
//               color: AppColors.textFieldFill1
//           ),
//           margin: const EdgeInsets.symmetric(horizontal: 17,vertical: 11),
//           child: Text(AppStrings.addDeliveryNote,style: AppTextStyle.access0(textColor: AppColors.appBlack),),
//         ),
//       );
//     }
//     //If field visible is true
//     if(shoppingCartBloc.cartDetailsResponse.isDeliveryNotesTextFieldVisible){
//       final TextEditingController notesTextCtrl = TextEditingController(text:shoppingCartBloc.cartDetailsResponse.deliveryNotes );
//       return Container(
//         margin: const EdgeInsets.symmetric(horizontal: 17,vertical: 11),
//         child: Stack(
//           children: [
//             TextFormField(
//               minLines: 5,
//               maxLines: 5,
//               controller:notesTextCtrl ,
//               style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
//               keyboardType:TextInputType.text ,
//               textCapitalization:TextCapitalization.sentences ,
//               decoration: InputDecoration(
//                   isDense: true,
//                   hintStyle: AppTextStyle.hintText(textColor: AppColors.writingBlack1),
//                   fillColor: AppColors.textFieldFill1, // Specify the desired internal color
//                   filled: true,
//                   hintText: AppStrings.addDeliveryNote,
//                   contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 16.0),
//                   border:  AppTextFieldStyle.filledColored(fieldColors: AppColors.textFieldFill1),
//                   focusedBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.textFieldFill1),
//                   enabledBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.textFieldFill1),
//                   disabledBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.textFieldFill1),
//                   focusedErrorBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.textFieldFill1),
//                   errorBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.textFieldFill1)
//
//               ),
//             ),
//             Positioned(
//                 bottom: 12,right: 11,
//                 child: InkWell(
//                   onTap: (){
//                     //If field is null
//                     if(notesTextCtrl.text.isEmpty){
//                       //Mark field visible to false
//                       shoppingCartBloc.cartDetailsResponse.isDeliveryNotesTextFieldVisible = false;
//                       //Refresh screen
//                       shoppingCartBloc.shoppingCartCtrl.sink.add(ShoppingCartState.Success);
//
//                     }
//                     //Add text to the seller notes.
//                     shoppingCartBloc.cartDetailsResponse.deliveryNotes = notesTextCtrl.text;
//                     //Mark field visible to false
//                     shoppingCartBloc.cartDetailsResponse.isDeliveryNotesTextFieldVisible = false;
//                     //Save notes to cache memory
//                     shoppingCartBloc.saveMessageToSharePref();
//                     //Refresh screen
//                     shoppingCartBloc.shoppingCartCtrl.sink.add(ShoppingCartState.Success);
//
//                     //print(shoppingCartBloc.cartDetailsResponse.deliveryNotes);
//
//                   },
//                   child: Container(
//                     padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 5),
//                     decoration: BoxDecoration(
//                         color: AppColors.textFieldFill1,
//                         borderRadius: BorderRadius.circular(8),
//                         border: Border.all(color: AppColors.appBlack,width: 1)
//                     ),
//                     child:Text(AppStrings.add,style: AppTextStyle.button2Bold(textColor: AppColors.appBlack),),
//                   ),
//                 ))
//           ],
//         ),
//       );
//     }
//     //If seller notes is not empty and field visible is false
//     if(shoppingCartBloc.cartDetailsResponse.deliveryNotes.isNotEmpty && !shoppingCartBloc.cartDetailsResponse.isDeliveryNotesTextFieldVisible){
//       return InkWell(
//         onTap: (){
//           //
//           shoppingCartBloc.cartDetailsResponse.isDeliveryNotesTextFieldVisible = true;
//           //Refresh screen
//           shoppingCartBloc.shoppingCartCtrl.sink.add(ShoppingCartState.Success);
//
//
//         },
//         child: Container(
//           width: double.infinity,
//           padding: const EdgeInsets.all(20),
//           margin: const EdgeInsets.symmetric(horizontal: 17,vertical: 11),
//
//           decoration: BoxDecoration(
//               color: AppColors.textFieldFill1,
//               borderRadius: BorderRadius.circular(10)
//           ),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             mainAxisAlignment: MainAxisAlignment.center,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(AppStrings.deliveryNotes,style: AppTextStyle.access0(textColor: AppColors.appBlack),),
//               verticalSizedBox(10),
//               Text(shoppingCartBloc.cartDetailsResponse.deliveryNotes,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),),
//
//
//             ],
//           ),
//
//         ),
//       );
//     }
//
//     return  Container();
//   }
//
//
// //   Widget addDeliveryNote() {
// //
// //     return Padding(
// //       padding: const EdgeInsets.symmetric(horizontal: 20),
// //       child: Container(
// //         width: double.infinity,
// //         padding: const EdgeInsets.all(10),
// //         decoration: const BoxDecoration(color: AppColors.lightWhite2, borderRadius: BorderRadius.all(Radius.circular(20))),
// //         child: Row(
// //           mainAxisAlignment: MainAxisAlignment.start,
// //           crossAxisAlignment: CrossAxisAlignment.center,
// //           mainAxisSize: MainAxisSize.min,
// //           children: [
// //             Expanded(
// //               child: TextFormField(
// //                 maxLines: 3,
// //                 minLines: 1,
// //                 controller:TextEditingController(text: shoppingCartBloc.cartDetailsResponse.deliveryNotes),
// //                 onChanged: (value) {
// //                   shoppingCartBloc.cartDetailsResponse.deliveryNotes = value;
// //                 },
// //                 keyboardType: TextInputType.multiline,
// //                 textCapitalization:TextCapitalization.sentences ,
// //
// //
// //                 style: TextStyle(fontFamily: "LatoRegular", fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack),
// //                 decoration: InputDecoration(
// //                     contentPadding: EdgeInsets.zero,
// //                     isDense: true,
// //                     border: InputBorder.none,
// //                     hintText: AppStrings.addDeliveryNote,
// //                     hintStyle: TextStyle(fontFamily: "LatoSemiBold", fontWeight: FontWeight.w600, fontSize: 14, color: AppColors.writingColor2)),
// //               ),
// //             ),
// //             SvgPicture.asset(
// //               AppImages.editIcon,
// //               fit: BoxFit.fill,
// //               color: AppColors.writingColor2,
// //             )
// //           ],
// //         ),
// //       ),
// //     );
// //   }
//
// //endregion

//endregion
  ///
//region Add Seller Note
//   Widget addSellerNote(OrderGroup orderGroup) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       child: Container(
//         width: double.infinity,
//         padding: EdgeInsets.all(10),
//         decoration: BoxDecoration(color: AppColors.lightWhite2, borderRadius: BorderRadius.all(Radius.circular(20))),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.start,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Expanded(
//                 child: TextFormField(
//               minLines: 1,
//               maxLines: 5,
//               // decoratedStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.brandGreen, fontFamily: AppConstants.rRegular),
//               // basicStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack, fontFamily: AppConstants.rRegular),
//               // decorateAtSign: true,
//               onChanged: (text) {
//                 orderGroup.sellerNote = text;
//               },
//               textCapitalization: TextCapitalization.sentences,
//               decoration: InputDecoration(
//                 contentPadding: EdgeInsets.zero,
//                 isDense: true,
//                 border: InputBorder.none,
//                 hintText: AppStrings.sellerNote,
//                 hintStyle: const TextStyle(fontFamily: "LatoSemiBold", fontWeight: FontWeight.w600, fontSize: 14, color: AppColors.writingColor2),
//               ),
//
//               // child: TextFormField(
//               //   controller: shoppingCartBloc.deliveryNoteTextCtrl,
//               //   maxLines: 3,
//               //   minLines: 1,
//               //   keyboardType: TextInputType.multiline,
//               //   onChanged: (value){
//               //     orderGroup.sellerNote = value;
//               //   },
//               //   style: TextStyle(
//               //       fontFamily: "LatoRegular",
//               //       fontSize: 14,
//               //       fontWeight: FontWeight.w400,
//               //       color: AppColors.appBlack),
//               //   decoration: InputDecoration(
//               //     contentPadding: EdgeInsets.zero,
//               //     isDense: true,
//               //     border: InputBorder.none,
//               //     hintText: AppStrings.sellerNote,
//               //     hintStyle: TextStyle(
//               //         fontFamily: "LatoSemiBold",
//               //         fontWeight: FontWeight.w600,
//               //         fontSize: 14,
//               //         color: AppColors.appBlack2
//               //     )
//               //   ),
//               // ),
//             )),
//             SvgPicture.asset(
//               AppImages.editIcon,
//               fit: BoxFit.fill,
//               color: AppColors.writingColor2,
//             )
//           ],
//         ),
//       ),
//     );
//   }
//endregion
}
