import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart' as web;
import 'package:swadesic/features/buyers/buyer_payment/razor_pay_payment/razor_pay_payment_wating/razor_pay_payment_waiting_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_payment_options_responses/create_order_and_initiate_payment_response.dart';
import 'package:swadesic/services/buyer_payment_services/buyer_payment_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
///Todo uncomment this for web app
// import 'dart:js' as js;

class RazorPayPayment {
  //region Common variable
  // Razorpay razorpay = Razorpay();
  web.Razorpay razorpayWeb = web.Razorpay();
  final String? paymentUrlInWeb;
  final BuildContext context;
  final CreateOrderAndInitiatePaymentResponse createOrderAndInitiatePaymentResponse;

  //endregion

  //region Constructor
  RazorPayPayment({required this.createOrderAndInitiatePaymentResponse, required this.context, this.paymentUrlInWeb}) {
    initiatePayment(context: context);
  }

  //endregion

  //region Initiate payment
  Future<void> initiatePayment({required BuildContext context}) async {
    //region Try
    try {
      //print("Web url is $paymentUrlInWeb");

      //If web then open in web
      //Add domain as "lol.swadesic.com"
      // if (kIsWeb) {
      //   CommonMethods.openUrl(url: "${AppConstants.domainName}$paymentUrlInWeb");
      //   // final url = paymentUrlInWeb;
      //   // if (url != null) {
      //   //   html.window.open(url, '_blank');
      //   //   setupUrlListener();
      //   // }
      //   return;
      // }

      //Fetch initiate payment response data
      // initiatePaymentResponse = await BuyerPaymentServices().initiatePayment(orderNumber: orderNumber, deliveryFee: deliveryFee, cartTotal: cartTotal, totalAmount: totalAmount);
      //Add all data in options
      Map<String, dynamic> options = {
        'key': createOrderAndInitiatePaymentResponse.paymentDetails!.key,
        'amount': createOrderAndInitiatePaymentResponse.paymentDetails!.amount,
        'name': createOrderAndInitiatePaymentResponse.paymentDetails!.name,
        'description': createOrderAndInitiatePaymentResponse.paymentDetails!.description,
        "order_id": createOrderAndInitiatePaymentResponse.paymentDetails!.orderId,
        'retry': {'enabled': true, 'max_count': 6},
        'send_sms_hash': true,
        'theme': {'color': createOrderAndInitiatePaymentResponse.paymentDetails!.theme!.color},
        'prefill': {'contact': createOrderAndInitiatePaymentResponse.paymentDetails!.prefill!.contact, 'email': createOrderAndInitiatePaymentResponse.paymentDetails!.prefill!.email},
        // 'external': {
        //   'wallets': ['paytm']
        // }
        "config": {
          "display": {
            // "hide": [
            //   {"method": 'paylater'},
            //   {"method": 'wallet'},
            //   {"method": 'emi'}
            //
            // ]
          }
        }
      };
      //Initiate razor pay
      initiateRazorPay(options: options);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(error.message!, context);
      }
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    }
  }

  //endregion



  // void setupUrlListener() {
  //   html.window.onPopState.listen((event) {
  //     handleCallback(); // Call your function on URL change
  //   });
  // }
  //
  // void handleCallback() {
  //   final uri = Uri.parse(html.window.location.href);
  //   final data = uri.queryParameters['data'];
  //   if (data != null) {
  //     //print('Received data from external site: $data');
  //   }
  // }



  //region Initiate Razor pay
  void initiateRazorPay({required Map<String, dynamic> options}) {


    // razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, handleExternalWalletSelected);
    //If web


      // razorpayWeb.on(Razorpay.EVENT_PAYMENT_ERROR, handlePaymentErrorResponse);
      // razorpayWeb.on(Razorpay.EVENT_PAYMENT_SUCCESS, handlePaymentSuccessResponse);

    ///Web
    if(kIsWeb){
      openRazorpayCheckoutForWeb();

      // razorpayWeb.on(
      //   web.Razorpay.EVENT_PAYMENT_ERROR,
      //   handlePaymentErrorResponse,
      // );
      // razorpayWeb.on(
      //   web.Razorpay.EVENT_PAYMENT_SUCCESS,
      //   handlePaymentSuccessResponse,
      // );
      // // razorpayWeb.on(
      // //   Razorpay.EVENT_EXTERNAL_WALLET,
      // //   handleExternalWalletSelected,
      // // );
      //
      // razorpayWeb.open(options);
    }
    ///Mobile
    else{
      razorpayWeb.on(
        web.Razorpay.EVENT_PAYMENT_ERROR,
        handlePaymentErrorResponse,
      );
      razorpayWeb.on(
        web.Razorpay.EVENT_PAYMENT_SUCCESS,
        handlePaymentSuccessResponse,
      );
      razorpayWeb.open(options);
    }
  }
  //endregion




  //region Web razor pay checkout
  void openRazorpayCheckoutForWeb() {
    ///Todo uncomment this for web app

    // Define the Razorpay options
    // final options = js.JsObject.jsify({
    //   'key': createOrderAndInitiatePaymentResponse.paymentDetails!.key,
    //   'amount': createOrderAndInitiatePaymentResponse.paymentDetails!.amount,
    //   'name': createOrderAndInitiatePaymentResponse.paymentDetails!.name,
    //   'description': createOrderAndInitiatePaymentResponse.paymentDetails!.description,
    //   "order_id": createOrderAndInitiatePaymentResponse.paymentDetails!.orderId,
    //   'retry': {'enabled': true, 'max_count': 6},
    //   'send_sms_hash': true,
    //   'theme': {'color': createOrderAndInitiatePaymentResponse.paymentDetails!.theme!.color},
    //   'prefill': {
    //     'contact': createOrderAndInitiatePaymentResponse.paymentDetails!.prefill!.contact,
    //     'email': createOrderAndInitiatePaymentResponse.paymentDetails!.prefill!.email,
    //   },
    //   'config': {
    //     'display': {
    //       // 'hide': [
    //       //   {'method': 'paylater'},
    //       //   {'method': 'wallet'},
    //       //   {'method': 'emi'},
    //       // ],
    //     },
    //   },
    //   // Success handler
    //   'handler': (response) {
    //     //print('Payment Success!');
    //     //print('Payment ID: ${response["razorpay_payment_id"]}');
    //     //print('Order ID: ${response["razorpay_order_id"]}');
    //     //print('Signature: ${response["razorpay_signature"]}');
    //
    //     // Trigger further actions like server-side verification here
    //     handlePaymentSuccessResponse(
    //         web.PaymentSuccessResponse(
    //           response["razorpay_payment_id"],
    //             response["razorpay_order_id"],
    //             response["razorpay_signature"],
    //           null
    //         )
    //
    //         // orderId:,paymentId:,signature:response["razorpay_signature"]
    //     );
    //   },
    //   // Failure or cancellation handler
    //   'modal': {
    //     'ondismiss': () {
    //       //print('Payment Failed or Cancelled');
    //
    //       // Trigger additional actions on failure
    //       handlePaymentErrorResponse();
    //     },
    //   },
    // });
    //
    // // Initialize Razorpay checkout and open it
    // final razorpay = js.context.callMethod('Razorpay', [options]);
    // razorpay.callMethod('open');
  }
  //endregion


  //region Payment Success
  void handlePaymentSuccessResponse(web.PaymentSuccessResponse response) {
    /*
    * Payment Success Response contains three values:
    * 1. Order ID
    * 2. Payment ID
    * 3. Signature
    * */
    //print(response.signature);
    goToPaymentStatusScreen(
      razorPayPaymentId: response.paymentId!,
      razorPayOrderId: response.orderId!,
      razorPaySignature: response.signature!,
    );
  }

  //endregion

//region Payment Failed
  void handlePaymentErrorResponse() {
    /*
    * PaymentFailureResponse contains three values:
    * 1. Error Code
    * 2. Error Description
    * 3. Metadata
    * */
    // showAlertDialog(context, "Payment Failed", "Code: ${response.code}\nDescription: ${response.message}\nMetadata:${response.error.toString()}");
   // Navigator.pop(context);
    goToPaymentStatusScreen(
      razorPayPaymentId: "",
      razorPayOrderId: "",
      razorPaySignature: "",
    );
  }

//endregion

//region Go to Buyer payment Status Screen
  void goToPaymentStatusScreen({
    required String razorPayPaymentId,
    required String razorPayOrderId,
    required String razorPaySignature,
  }) {
    var screen = RazorPayPaymentWaitingScreen(
      orderNumber: createOrderAndInitiatePaymentResponse.paymentDetails!.notes!.orderRequestNumber!,
      grandTotal: createOrderAndInitiatePaymentResponse.paymentDetails!.amount!.toString(),
      razorPayOrderId: razorPayOrderId,
      razorPayPaymentId: razorPayPaymentId,
      razorPaySignature: razorPaySignature,
      transactionId: createOrderAndInitiatePaymentResponse.paymentDetails!.notes!.txnToken!,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route).then((value) {
    //
    // });
    Navigator.pushReplacement(context, route).then((value) {});
  }
//endregion
}
