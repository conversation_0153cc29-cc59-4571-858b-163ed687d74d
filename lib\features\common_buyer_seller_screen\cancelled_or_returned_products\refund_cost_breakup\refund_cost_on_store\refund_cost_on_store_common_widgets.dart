import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class RefundCostOnStoreCommonWidgets{



  //region Info
  static Widget info({required String title,required String price}){
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(title,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),
          // horizontalSizedBox(10),
          // SvgPicture.asset(AppImages.exclamation),
          Expanded(child: horizontalSizedBox(10)),
          Text(price,style: AppTextStyle.settingText(textColor: AppColors.appBlack),),


        ],
      ),
    );
  }
  //endregion

}