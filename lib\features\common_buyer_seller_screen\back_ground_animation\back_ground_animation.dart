import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:swadesic/util/app_constants.dart';

class BackGroundAnimation extends StatefulWidget {
  const BackGroundAnimation({super.key});

  @override
  _BackGroundAnimationState createState() => _BackGroundAnimationState();
}

class _BackGroundAnimationState extends State<BackGroundAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<List<Color>> gradients;
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 2), // Adjust the duration as needed
    )..addListener(() {
      setState(() {});
    });

    // gradients = [
    //   [Colors.red, Colors.orange, Colors.yellow],
    //   [Colors.blue, Colors.green, Colors.purple],
    //   [Colors.pink, Colors.deepPurple, Colors.indigo],
    //   [Colors.cyan, Colors.teal, Colors.blue],
    // ];

    //Good
    gradients = [
      [const Color(0x7723FF4C), Colors.greenAccent, Colors.white],
      [Colors.white, Colors.greenAccent, const Color(0x7723FF4C)],

      [Colors.white, Colors.greenAccent, const Color(0x7723FF4C)],
      [const Color(0x7723FF4C), Colors.greenAccent, Colors.white],
      // [Colors.green, Colors.teal, Colors.lightGreenAccent],
      // [Colors.greenAccent,const Color(0x77E194FF), Colors.teal],
      // [Colors.white, Colors.lightGreenAccent, const Color(0x77E194FF)],
    ];

    // Color(0x3310c057),
    // Color(0x2c3ee07f),
    // Color(0x3302d256)

    //  gradients = [
    //   [Colors.lightGreen, Colors.greenAccent, Colors.white],
    //    [Colors.white, Colors.greenAccent, Colors.lightGreen],
    //    [Colors.lightGreen, Colors.greenAccent, Colors.white],
    //    [Colors.white, Colors.greenAccent, Colors.lightGreen],
    // ];


    _startAnimation();
  }

  void _startAnimation() {
    _controller.forward().whenComplete(() {
      setState(() {
        currentIndex = (currentIndex + 1) % gradients.length;
        _controller.reset();
        _startAnimation();
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AnimatedContainer(
          duration: const Duration(seconds: 1),
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: gradients[currentIndex],
              stops: const [0.0, 0.3, 1.0], // 0% -> 30% -> 100%
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            // gradient: LinearGradient(
            //   colors: gradients[currentIndex],
            //   begin: Alignment.bottomLeft,
            //   end: Alignment.topRight,
            // ),
          ),
        ),
        Positioned.fill(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20,sigmaY: 30),
            child: const SizedBox(),
          ),
        ),
      ],
    );
  }
}
