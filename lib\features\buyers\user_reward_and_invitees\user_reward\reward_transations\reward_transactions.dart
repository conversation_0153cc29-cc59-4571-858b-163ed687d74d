import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:readmore/readmore.dart';
import 'package:swadesic/features/buyers/user_reward_and_invitees/user_reward/reward_transations/reward_transaction_bloc.dart';
import 'package:swadesic/features/widgets/reward_transaction_card/reward_transaction_card.dart';
import 'package:swadesic/features/widgets/title_and_filter/title_and_filter.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/onTapTag/onTapTag.dart';

class RewardTransactions extends StatefulWidget {
  const RewardTransactions({super.key});

  @override
  State<RewardTransactions> createState() => _RewardTransactionsState();
}

class _RewardTransactionsState extends State<RewardTransactions> {
  //region Bloc
  late RewardTransactionBloc rewardTransactionBloc;
  //endregion

  //region Init
  @override
  void initState() {
    rewardTransactionBloc = RewardTransactionBloc(context);
    rewardTransactionBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    rewardTransactionBloc.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<RewardTransactionState>(
        stream: rewardTransactionBloc.rewardTransactionCtrl.stream,
        initialData: RewardTransactionState.Loading,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data == RewardTransactionState.Loading) {
            return Container(
              height: MediaQuery.of(context).size.width,
              alignment: Alignment.center,
              child: AppCommonWidgets.appCircularProgress(),
            );
          }

          //Success
          if (snapshot.data == RewardTransactionState.Success) {
            return Column(
              children: [
                TitleAndFilter(
                    title: AppStrings.transactions, onTapFilter: () {}),
                const SizedBox(
                  height: 10,
                ),
                transactions(),
              ],
            );
          }
          //Failed
          return AppCommonWidgets.errorWidget(
              errorMessage: AppStrings.unableToLoadTransaction,
              height: MediaQuery.of(context).size.width,
              onTap: () {
                rewardTransactionBloc.init();
              });
        });
  }

  //region Transactions
  Widget transactions() {
    return ListView.builder(
        itemCount: rewardTransactionBloc.rewardTransactionResponse.data!.length,
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) {
          return RewardTransactionCard(
            title: ReadMoreText(
              rewardTransactionBloc
                  .rewardTransactionResponse.data![index].transactionTitle!,
              trimMode: TrimMode.Line,
              isExpandable: true,
              trimLines: 1,
              colorClickableText: Colors.pink,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              lessStyle: AppTextStyle.contentHeading0(
                  textColor: AppColors.writingBlack1),
              moreStyle: AppTextStyle.contentHeading0(
                  textColor: AppColors.writingBlack1),
              trimLength: 1,
              trimCollapsedText: AppStrings.more,
              trimExpandedText: " ${AppStrings.less}",
              textAlign: TextAlign.start,
              annotations: [
                // Annotation(
                //   regExp: RegExp(r'#([a-zA-Z0-9_]+)'),
                //   spanBuilder: ({required String text, TextStyle? textStyle}) => TextSpan(
                //     text: text,
                //     style: textStyle?.copyWith(color: Colors.blue),
                //   ),
                // ),
                //User name or handle
                Annotation(
                  regExp: RegExp(r"@[a-zA-Z0-9_]+(?:'s\s+[a-zA-Z0-9\s]+)?"),
                  spanBuilder: ({required String text, TextStyle? textStyle}) =>
                      TextSpan(
                    text: text,
                    style: textStyle?.copyWith(color: AppColors.brandGreen),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // Extract the username from the tapped text
                        final userName = text.substring(1);
                        OnTapTag(context, userName);
                        //print(userName); // Print the username
                      },
                  ),
                ),
              ],
            ),

            // Text(rewardTransactionBloc.rewardTransactionResponse.data![index].transactionTitle!,
            //   style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
            date: DateFormat('yyyy-MM-dd hh:mm a').format(DateTime.parse(
                    rewardTransactionBloc
                        .rewardTransactionResponse.data![index].createdDate!)
                .toLocal()),
            isSuccess: rewardTransactionBloc
                    .rewardTransactionResponse.data![index].rewardStatus ==
                "SUCCESS",
            price: rewardTransactionBloc.rewardTransactionResponse.data![index]
                        .transactionType! ==
                    TransactionEnum.CREDIT.name
                ? rewardTransactionBloc
                    .rewardTransactionResponse.data![index].receivedValue!
                : rewardTransactionBloc
                    .rewardTransactionResponse.data![index].sentValue!,
            reference: rewardTransactionBloc
                .rewardTransactionResponse.data![index].rewardReference!,
            isIncoming: rewardTransactionBloc
                    .rewardTransactionResponse.data![index].transactionType! ==
                TransactionEnum.CREDIT.name,
          );
        });
  }
//endregion

//   //region Transactions
// Widget transactions(){
//   return Column(
//     children: [
//       RewardTransactionCard(
//         title: Text("Earned Discount for Purchase",style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
//         date: '2022-10-25 11:50 PM',
//         isSuccess: true,
//         price: '200',
//         reference: 'O234567890123456',
//         isIncoming: true,
//       ),
//       RewardTransactionCard(
//         title: Text("Earned Discount for Purchase",style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
//         date: '2022-10-25 11:50 PM',
//         isSuccess: false,
//         price: '200',
//         reference: 'O234567890123456',
//         isIncoming: false,
//       ),
//       RewardTransactionCard(
//         title: Row(
//           children: [
//             Text("@sudhanshu.trivedi :",style: AppTextStyle.contentHeading0(textColor: AppColors.brandGreen),),
//             Text("Onboarding",style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
//           ],
//         ),
//         date: '2022-10-25 11:50 PM',
//         isSuccess: false,
//         price: '200',
//         reference: '+91 88291 73742',
//         isIncoming: false,
//       ),
//     ],
//   );
//   }
// //endregion
}
