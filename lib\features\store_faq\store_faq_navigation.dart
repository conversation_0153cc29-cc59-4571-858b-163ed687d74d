import 'package:flutter/material.dart';
import 'package:swadesic/features/store_faq/store_faq_screen.dart';
import 'package:swadesic/util/app_constants.dart';

class StoreFaqNavigation {
  // Navigate to Store FAQ screen
  static void navigateToStoreFaqScreen(
    BuildContext context, {
    required String storeReference,
    String? storeName,
    bool isStoreOwner = false,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoreFaqScreen(
          storeReference: storeReference,
          storeName: storeName,
          isStoreOwner: isStoreOwner,
        ),
      ),
    );
  }

  // Navigate to Store FAQ screen with specific category
  static void navigateToStoreFaqCategory(
    BuildContext context, {
    required String storeReference,
    required String categoryKey,
    String? storeName,
    bool isStoreOwner = false,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoreFaqScreen(
          storeReference: storeReference,
          storeName: storeName,
          initialCategoryKey: categoryKey,
          isStoreOwner: isStoreOwner,
        ),
      ),
    );
  }

  // Navigate to Store FAQ screen with specific question expanded
  static void navigateToStoreFaqQuestion(
    BuildContext context, {
    required String storeReference,
    required String categoryKey,
    required String questionKey,
    String? storeName,
    bool isStoreOwner = false,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoreFaqScreen(
          storeReference: storeReference,
          storeName: storeName,
          initialCategoryKey: categoryKey,
          initialQuestionKey: questionKey,
          isStoreOwner: isStoreOwner,
        ),
      ),
    );
  }

  // Generate Store FAQ category link
  static String generateCategoryLink(String storeReference, String categoryKey) {
    return '${AppConstants.domainName}store/$storeReference/faq?category=$categoryKey';
  }

  // Generate Store FAQ question link
  static String generateQuestionLink(
    String storeReference,
    String categoryKey,
    String questionKey,
  ) {
    return '${AppConstants.domainName}store/$storeReference/faq?category=$categoryKey&question=$questionKey';
  }

  // Parse Store FAQ link and navigate
  static void handleStoreFaqLink(
    BuildContext context,
    Uri uri, {
    String? storeName,
    bool isStoreOwner = false,
  }) {
    // Extract store reference from path
    final pathSegments = uri.pathSegments;
    if (pathSegments.length < 2 || pathSegments[0] != 'store') {
      return;
    }

    final storeReference = pathSegments[1];
    final categoryKey = uri.queryParameters['category'];
    final questionKey = uri.queryParameters['question'];

    if (categoryKey != null && questionKey != null) {
      // Navigate to specific question
      navigateToStoreFaqQuestion(
        context,
        storeReference: storeReference,
        categoryKey: categoryKey,
        questionKey: questionKey,
        storeName: storeName,
        isStoreOwner: isStoreOwner,
      );
    } else if (categoryKey != null) {
      // Navigate to specific category
      navigateToStoreFaqCategory(
        context,
        storeReference: storeReference,
        categoryKey: categoryKey,
        storeName: storeName,
        isStoreOwner: isStoreOwner,
      );
    } else {
      // Navigate to general Store FAQ screen
      navigateToStoreFaqScreen(
        context,
        storeReference: storeReference,
        storeName: storeName,
        isStoreOwner: isStoreOwner,
      );
    }
  }

  // Check if URL is a store FAQ link
  static bool isStoreFaqLink(Uri uri) {
    final pathSegments = uri.pathSegments;
    return pathSegments.length >= 3 &&
           pathSegments[0] == 'store' &&
           pathSegments[2] == 'faq';
  }

  // Extract store reference from FAQ URL
  static String? extractStoreReference(Uri uri) {
    final pathSegments = uri.pathSegments;
    if (pathSegments.length >= 2 && pathSegments[0] == 'store') {
      return pathSegments[1];
    }
    return null;
  }

  // Generate store FAQ base URL
  static String generateStoreFaqBaseUrl(String storeReference) {
    return '${AppConstants.domainName}store/$storeReference/faq';
  }

  // Create shareable store FAQ link with custom message
  static Map<String, String> createShareableLink({
    required String storeReference,
    required String storeName,
    String? categoryKey,
    String? questionKey,
    String? categoryName,
    String? questionText,
  }) {
    String url;
    String message;

    if (categoryKey != null && questionKey != null && questionText != null) {
      // Share specific question
      url = generateQuestionLink(storeReference, categoryKey, questionKey);
      message = "Found this helpful FAQ: \"$questionText\" from $storeName on Swadesic!";
    } else if (categoryKey != null && categoryName != null) {
      // Share category
      url = generateCategoryLink(storeReference, categoryKey);
      message = "Check out these $categoryName FAQs from $storeName on Swadesic!";
    } else {
      // Share general store FAQ
      url = generateStoreFaqBaseUrl(storeReference);
      message = "Check out the FAQs from $storeName on Swadesic!";
    }

    return {
      'url': url,
      'message': message,
    };
  }
}
