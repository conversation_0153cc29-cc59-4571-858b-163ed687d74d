import 'dart:ui' show lerpDouble;
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:expandable/expandable.dart';
import 'package:swadesic/features/store_faq/store_faq_bloc.dart';
import 'package:swadesic/features/store_faq/add_edit_faq_bottom_sheet.dart';
import 'package:swadesic/features/store_faq/store_faq_navigation.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/faq/faq_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/app_enums.dart';

class StoreFaqScreen extends StatefulWidget {
  final String storeReference;
  final String? storeName;
  final String? initialCategoryKey;
  final String? initialQuestionKey;
  final bool isStoreOwner;

  const StoreFaqScreen({
    Key? key,
    required this.storeReference,
    this.storeName,
    this.initialCategoryKey,
    this.initialQuestionKey,
    this.isStoreOwner = false,
  }) : super(key: key);

  @override
  State<StoreFaqScreen> createState() => _StoreFaqScreenState();
}

class _StoreFaqScreenState extends State<StoreFaqScreen> {
  late StoreFaqBloc storeFaqBloc;
  ScrollController _scrollController = ScrollController();
  bool _isReorderingMode = false;

  @override
  void initState() {
    super.initState();
    storeFaqBloc = StoreFaqBloc();
    _initializeBloc();
  }

  void _initializeBloc() {
    storeFaqBloc.init(
      storeReference: widget.storeReference,
      initialCategoryKey: widget.initialCategoryKey,
      initialQuestionKey: widget.initialQuestionKey,
      isStoreOwner: widget.isStoreOwner,
    );
  }

  @override
  void dispose() {
    storeFaqBloc.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: AppBar(
        title: Text(
          widget.storeName != null ? 'Know about @${widget.storeName}' : 'Know about Store',
          style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
        ),
        backgroundColor: AppColors.appWhite,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.appBlack),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          if (_isReorderingMode && widget.isStoreOwner)
            IconButton(
              icon: const Icon(Icons.check, color: AppColors.appBlack),
              onPressed: () {
                setState(() {
                  _isReorderingMode = false;
                });
              },
              tooltip: 'Exit reordering mode',
            ),
          IconButton(
            icon: const Icon(Icons.share, color: AppColors.appBlack),
            onPressed: _shareCurrentCategory,
          ),
        ],
      ),
      body: StreamBuilder<StoreFaqScreenState>(
        stream: storeFaqBloc.stateStream,
        initialData: StoreFaqScreenState.Initial,
        builder: (context, snapshot) {
          if (snapshot.data == StoreFaqScreenState.Loading) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColors.brandBlack,
              ),
            );
          }

          if (snapshot.data == StoreFaqScreenState.Failed) {
            return AppCommonWidgets.errorWidget(
              errorMessage: 'Failed to load store FAQ data',
              onTap: () {
                _initializeBloc();
              },
            );
          }

          if (snapshot.data == StoreFaqScreenState.Success) {
            // Scroll to specific question if deep linking
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _scrollToQuestionIfNeeded();
            });

            return GestureDetector(
              onTap: () {
                // Exit reordering mode when tapping outside
                if (_isReorderingMode) {
                  setState(() {
                    _isReorderingMode = false;
                  });
                }
              },
              child: Column(
                children: [
                  // _buildCategoryTabs(),
                  Expanded(
                    child: _buildFaqList(),
                  ),
                ],
              ),
            );
          }

          return const SizedBox();
        },
      ),
      floatingActionButton: widget.isStoreOwner ? _buildFloatingActionButton() : null,
    );
  }

  Widget _buildCategoryTabs() {
    if (storeFaqBloc.storeFaqDataModel.getStoreFaqCategories.isEmpty) {
      return const SizedBox();
    }

    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: StreamBuilder<int>(
        stream: storeFaqBloc.selectedCategoryStream,
        initialData: storeFaqBloc.selectedCategoryIndex,
        builder: (context, snapshot) {
          return ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: storeFaqBloc.storeFaqDataModel.getStoreFaqCategories.length,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            itemBuilder: (context, index) {
              final isSelected = index == (snapshot.data ?? 0);
              final category = storeFaqBloc.storeFaqDataModel.getStoreFaqCategories[index];
              
              return GestureDetector(
                onTap: () => storeFaqBloc.changeCategory(index),
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                  height: 32,
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.appBlack : AppColors.appWhite,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isSelected ? AppColors.appBlack : AppColors.borderColor1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      category.name,
                      style: AppTextStyle.access0(
                        textColor: isSelected ? AppColors.appWhite : AppColors.appBlack,
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildFaqList() {
    // Check if categories are loaded
    if (storeFaqBloc.storeFaqDataModel.getStoreFaqCategories.isEmpty) {
      return Center(
        child: Text(
          'Nothing Added Yet',
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        ),
      );
    }

    if (storeFaqBloc.selectedCategoryIndex >=
        storeFaqBloc.storeFaqDataModel.getStoreFaqCategories.length) {
      return Center(
        child: Text(
          'Nothing Added Yet',
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        ),
      );
    }

    final currentCategory = storeFaqBloc
        .storeFaqDataModel.getStoreFaqCategories[storeFaqBloc.selectedCategoryIndex];

    if (currentCategory.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Nothing Added Yet',
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
            if (widget.isStoreOwner) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _showAddFaqBottomSheet,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.brandBlack,
                  foregroundColor: AppColors.appWhite,
                ),
                child: const Text('Add First FAQ'),
              ),
            ],
          ],
        ),
      );
    }

    return StreamBuilder<int>(
      stream: storeFaqBloc.selectedCategoryStream,
      initialData: storeFaqBloc.selectedCategoryIndex,
      builder: (context, snapshot) {
        final category = storeFaqBloc
            .storeFaqDataModel.getStoreFaqCategories[snapshot.data ?? 0];

        if (widget.isStoreOwner && _isReorderingMode) {
          // Reordering mode - show ReorderableListView
          return ReorderableListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: category.items.length,
            onReorder: _onReorder,
            proxyDecorator: (child, index, animation) {
              // Custom decoration for dragged item with rounded corners
              return AnimatedBuilder(
                animation: animation,
                builder: (BuildContext context, Widget? child) {
                  final double animValue = Curves.easeInOut.transform(animation.value);
                  final double elevation = lerpDouble(0, 6, animValue)!;
                  final double scale = lerpDouble(1, 1.02, animValue)!;
                  return Transform.scale(
                    scale: scale,
                    child: Material(
                      elevation: elevation,
                      borderRadius: BorderRadius.circular(10), // Rounded corners
                      child: child,
                    ),
                  );
                },
                child: child,
              );
            },
            itemBuilder: (context, index) {
              final faqItem = category.items[index];
              return _buildFaqItem(faqItem, index, category.categoryKey);
            },
          );
        } else {
          // Normal mode - show ListView.separated for both store owner and buyer
          return ListView.separated(
            controller: _scrollController,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: category.items.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final faqItem = category.items[index];
              return _buildFaqItem(faqItem, index, category.categoryKey);
            },
          );
        }
      },
    );
  }

  Widget _buildFaqItem(StoreFaqItem faqItem, int index, String categoryKey) {
    Widget faqContent = _buildFaqItemContent(faqItem, categoryKey);

    if (widget.isStoreOwner && !_isReorderingMode) {
      // Store owner in normal mode - add long press detection and slidable
      faqContent = GestureDetector(
        onLongPress: () {
          setState(() {
            _isReorderingMode = true;
          });
          // Show a snackbar to indicate reordering mode
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Reordering mode activated. Tap outside to exit.'),
              duration: const Duration(seconds: 2),
              action: SnackBarAction(
                label: 'Exit',
                onPressed: () {
                  setState(() {
                    _isReorderingMode = false;
                  });
                },
              ),
            ),
          );
        },
        child: Slidable(
          key: ValueKey(faqItem.itemKey),
          endActionPane: ActionPane(
            motion: const DrawerMotion(),
            children: [
              Theme(
                data: Theme.of(context).copyWith(
                  textTheme: Theme.of(context).textTheme.copyWith(
                    labelLarge: AppTextStyle.smallText(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                ),
                child: SlidableAction(
                  onPressed: (_) => _editFaqItem(faqItem),
                  backgroundColor: AppColors.textFieldFill1,
                  foregroundColor: AppColors.appBlack,
                  icon: Icons.edit,
                  label: 'Edit',
                ),
              ),
              Theme(
                data: Theme.of(context).copyWith(
                  textTheme: Theme.of(context).textTheme.copyWith(
                    labelLarge: AppTextStyle.smallText(
                      textColor: AppColors.red,
                    ),
                  ),
                ),
                child: SlidableAction(
                  onPressed: (_) => _deleteFaqItem(faqItem),
                  backgroundColor: AppColors.red.withOpacity(0.05),
                  foregroundColor: AppColors.red,
                  icon: Icons.delete,
                  label: 'Delete',
                ),
              ),
            ],
          ),
          child: faqContent,
        ),
      );
    } else if (widget.isStoreOwner && _isReorderingMode) {
      // Store owner in reordering mode - just return content for ReorderableListView
      faqContent = faqContent;
    }

    return Container(
      key: ValueKey('store_faq_item_${faqItem.itemKey}'),
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: faqContent,
    );
  }

  Widget _buildFaqItemContent(StoreFaqItem faqItem, String categoryKey) {
    return ExpandablePanel(
      theme: const ExpandableThemeData(
        animationDuration: Duration(milliseconds: 300),
        iconPlacement: ExpandablePanelIconPlacement.right,
        tapBodyToCollapse: true,
        tapHeaderToExpand: true,
        tapBodyToExpand: true,
        iconPadding: EdgeInsets.zero,
        headerAlignment: ExpandablePanelHeaderAlignment.center,
        iconSize: 24,
        iconColor: AppColors.appBlack,
      ),
      controller: ExpandableController(initialExpanded: faqItem.isExpanded),
      header: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            if (widget.isStoreOwner && _isReorderingMode)
              const Icon(
                Icons.drag_handle,
                color: AppColors.writingBlack1,
                size: 20,
              ),
            if (widget.isStoreOwner && _isReorderingMode) const SizedBox(width: 8),
            Expanded(
              child: Text(
                faqItem.question,
                style: AppTextStyle.access0(
                  textColor: AppColors.appBlack,
                ),
              ),
            ),
            IconButton(
              icon: const Icon(
                Icons.share,
                size: 20,
                color: AppColors.appBlack,
              ),
              onPressed: () => _shareQuestion(categoryKey, faqItem),
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          ],
        ),
      ),
      collapsed: const SizedBox(),
      expanded: Padding(
        padding: const EdgeInsets.only(bottom: 16, top: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              faqItem.answer,
              style: AppTextStyle.contentText0(
                textColor: AppColors.writingBlack2,
              ),
            ),
            if (faqItem.itemImages.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildFaqImages(faqItem.itemImages),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFaqImages(List<String> images) {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(right: 8),
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderColor1),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                images[index],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: AppColors.textFieldFill1,
                    child: const Icon(
                      Icons.image_not_supported,
                      color: AppColors.writingBlack1,
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _showAddFaqBottomSheet,
      backgroundColor: AppColors.brandBlack,
      child: const Icon(Icons.add, color: AppColors.appWhite),
    );
  }

  // Event handlers
  void _onReorder(int oldIndex, int newIndex) {
    if (!widget.isStoreOwner) return;

    final currentCategory = storeFaqBloc.getCurrentCategory();
    if (currentCategory == null || oldIndex >= currentCategory.items.length) return;

    // Adjust newIndex if moving down
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }

    // Get the item before reordering for API call
    final item = currentCategory.items[oldIndex];

    // Update local state immediately for instant UI feedback
    storeFaqBloc.reorderFaqItemsLocally(
      categoryKey: currentCategory.categoryKey,
      oldIndex: oldIndex,
      newIndex: newIndex,
    );

    // Call API to reorder (this will happen in background)
    storeFaqBloc.reorderFaqItems(
      itemKey: item.itemKey,
      newOrder: newIndex + 1, // API expects 1-based ordering
    );

    // Exit reordering mode after reordering
    setState(() {
      _isReorderingMode = false;
    });
  }

  void _showAddFaqBottomSheet() {
    if (!widget.isStoreOwner) return;

    // Check if store can add more FAQ items (limit of 10)
    if (!storeFaqBloc.canAddMoreFaqItems()) {
      CommonMethods.toastMessage(
        'You can only have up to 10 FAQ items per store (${storeFaqBloc.getCurrentFaqItemsCount()}/10)',
        context,
      );
      return;
    }

    CommonMethods.accessBottomSheet(
      screen: AddEditFaqBottomSheet(
        onSave: (categoryName, question, answer) async {
          final success = await storeFaqBloc.addFaqItem(
            categoryName: categoryName,
            question: question,
            answer: answer,
          );

          if (mounted) {
            if (success) {
              Navigator.pop(context);
              CommonMethods.toastMessage('FAQ item added successfully', context);
            } else {
              // Check if it failed due to limit
              if (!storeFaqBloc.canAddMoreFaqItems()) {
                CommonMethods.toastMessage('FAQ limit reached (10 items maximum)', context);
              } else {
                CommonMethods.toastMessage('Failed to add FAQ item', context);
              }
            }
          }
        },
      ),
      context: context,
    );
  }

  void _editFaqItem(StoreFaqItem faqItem) {
    if (!widget.isStoreOwner) return;

    final currentCategory = storeFaqBloc.getCurrentCategory();
    if (currentCategory == null) return;

    CommonMethods.accessBottomSheet(
      screen: AddEditFaqBottomSheet(
        isEdit: true,
        initialCategoryName: currentCategory.name,
        initialQuestion: faqItem.question,
        initialAnswer: faqItem.answer,
        onSave: (categoryName, question, answer) async {
          final success = await storeFaqBloc.editFaqItem(
            itemKey: faqItem.itemKey,
            question: question,
            answer: answer,
          );

          if (mounted) {
            if (success) {
              Navigator.pop(context);
              CommonMethods.toastMessage('FAQ item updated successfully', context);
            } else {
              CommonMethods.toastMessage('Failed to update FAQ item', context);
            }
          }
        },
      ),
      context: context,
    );
  }

  void _deleteFaqItem(StoreFaqItem faqItem) {
    if (!widget.isStoreOwner) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete FAQ Item'),
          content: const Text('Are you sure you want to delete this FAQ item?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                final success = await storeFaqBloc.deleteFaqItem(faqItem.itemKey);

                if (mounted) {
                  if (success) {
                    CommonMethods.toastMessage('FAQ item deleted successfully', context);
                  } else {
                    CommonMethods.toastMessage('Failed to delete FAQ item', context);
                  }
                }
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _scrollToQuestionIfNeeded() {
    if (widget.initialQuestionKey == null) return;

    // Find the question in the current category
    final currentCategory = storeFaqBloc.getCurrentCategory();
    if (currentCategory == null) return;

    final questionIndex = currentCategory.items.indexWhere(
      (item) => item.itemKey == widget.initialQuestionKey,
    );

    if (questionIndex != -1) {
      // Scroll to the question with animation
      Future.delayed(const Duration(milliseconds: 300), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            questionIndex * 80.0, // Approximate item height
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  // Share current category
  void _shareCurrentCategory() {
    final currentCategory = storeFaqBloc.getCurrentCategory();
    if (currentCategory == null) return;

    final categoryUrl = StoreFaqNavigation.generateCategoryLink(
      widget.storeReference,
      currentCategory.categoryKey,
    );

    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: categoryUrl,
        imageLink: null,
        imageType: CustomImageContainerType.store,
        entityType: EntityType.STORE,
        message: "Check out these ${currentCategory.name} FAQs from ${widget.storeName ?? 'this store'} on Swadesic!",
      ),
      context: context,
    );
  }

  // Share specific question
  void _shareQuestion(String categoryKey, StoreFaqItem faqItem) {
    final questionUrl = StoreFaqNavigation.generateQuestionLink(
      widget.storeReference,
      categoryKey,
      faqItem.itemKey,
    );

    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: questionUrl,
        imageLink: null,
        imageType: CustomImageContainerType.store,
        entityType: EntityType.STORE,
        message: "Found this helpful FAQ: \"${faqItem.question}\" from ${widget.storeName ?? 'this store'} on Swadesic!",
      ),
      context: context,
    );
  }
}
