import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/initial_onboarding_bloc.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/what_is_swadesic_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/store_handle_and_user_name/store_handle_and_user_name.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:swadesic/util/app_constants.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';

class InitialOnboardingScreen extends StatefulWidget {
  final String userReference;
  final String? icon;

  const InitialOnboardingScreen({
    Key? key,
    required this.userReference,
    this.icon,
  }) : super(key: key);

  @override
  _InitialOnboardingScreenState createState() =>
      _InitialOnboardingScreenState();
}

class _InitialOnboardingScreenState extends State<InitialOnboardingScreen> {
  //region Bloc
  late InitialOnboardingBloc initialOnboardingBloc;

  //endregion

  //region Init
  @override
  void initState() {
    initialOnboardingBloc =
        InitialOnboardingBloc(context, widget.userReference, widget.icon);
    initialOnboardingBloc.init();
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        body: SafeArea(
          child: body(),
        ),
      ),
    );
  }

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment:
              CrossAxisAlignment.stretch, // Changed to stretch for full width
          children: [
            const SizedBox(height: 125),
            Center(
              child: Text(
                AppStrings.letsGetToKnowYou,
                style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 40),
            nameTextField(),
            const SizedBox(height: 35),
            userName(),
            const SizedBox(height: 35),
            gender(),
            const SizedBox(height: 35),
            city(),
            const SizedBox(height: 35),
            // inviteCode(),
            const SizedBox(height: 80),
            continueButton(),
            const SizedBox(height: 10),
            termsAndPolicy(),
            AppCommonWidgets.bottomListSpace(context: context),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Name and Text Field
  Widget nameTextField() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppTitleAndOptions(
                title: AppStrings.name,
                option: Column(
                  children: [
                    const SizedBox(height: 10), // Added spacing
                    AppTextFields.onlyStringWithSpaceTextField(
                      context: context,
                      textEditingController: initialOnboardingBloc.nameTextCtrl,
                      hintText: AppStrings.firstName,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  //endregion

  //region User name
  Widget userName() {
    // Wrap the StoreHandleAndUserName in a Column to add spacing
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        StoreHandleAndUserName(
          onSearch: (data) {
            initialOnboardingBloc.isExistUser = data!;
          },
          handleUserNameTextCtrl: initialOnboardingBloc.userNameTextCtrl,
          screenContext: context,
          olderData: initialOnboardingBloc.userNameTextCtrl.text,
          isUserNameCheck: true,
          title: "${AppStrings.userName} (${AppStrings.lowerCaseOnlyAllowed})",
          onChange: (data) {},
        ),
      ],
    );
  }

  //endregion

  //region Gender
  Widget gender() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AppTitleAndOptions(
            title: "Gender",
            titleOption: AppToolTip(
              message: AppStrings.thisHelpUsPersonalize,
              toolTipWidget: Text(
                AppStrings.why,
                style: AppTextStyle.contentText0(
                    textColor: AppColors.writingBlack1),
              ),
            ),
            option: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 15), // Added spacing
                StreamBuilder<String>(
                  stream: initialOnboardingBloc.genderCtrl.stream,
                  initialData: initialOnboardingBloc.gender,
                  builder: (context, snapshot) {
                    return Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              initialOnboardingBloc.onSelectGender("Female");
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              decoration: BoxDecoration(
                                color: snapshot.data == "Female"
                                    ? AppColors.brandBlack
                                    : AppColors.appWhite,
                                borderRadius: const BorderRadius.all(
                                    Radius.circular(100)),
                                border: Border.all(
                                    color: snapshot.data == "Female"
                                        ? AppColors.brandBlack
                                        : AppColors.darkStroke),
                              ),
                              child: Center(
                                child: Text(
                                  "Female",
                                  style: AppTextStyle.contentText0(
                                    textColor: snapshot.data == "Female"
                                        ? AppColors.appWhite
                                        : AppColors.appBlack,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              initialOnboardingBloc.onSelectGender("Male");
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              decoration: BoxDecoration(
                                color: snapshot.data == "Male"
                                    ? AppColors.brandBlack
                                    : AppColors.appWhite,
                                borderRadius: const BorderRadius.all(
                                    Radius.circular(100)),
                                border: Border.all(
                                    color: snapshot.data == "Male"
                                        ? AppColors.brandBlack
                                        : AppColors.darkStroke),
                              ),
                              child: Center(
                                child: Text(
                                  "Male",
                                  style: AppTextStyle.contentText0(
                                    textColor: snapshot.data == "Male"
                                        ? AppColors.appWhite
                                        : AppColors.appBlack,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              initialOnboardingBloc.onSelectGender("Others");
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.all(
                                    Radius.circular(100)),
                                color: snapshot.data == "Others"
                                    ? AppColors.brandBlack
                                    : AppColors.appWhite,
                                border: Border.all(
                                    color: snapshot.data == "Others"
                                        ? AppColors.brandBlack
                                        : AppColors.darkStroke),
                              ),
                              child: Center(
                                child: Text(
                                  "Others",
                                  style: AppTextStyle.contentText0(
                                    textColor: snapshot.data == "Others"
                                        ? AppColors.appWhite
                                        : AppColors.appBlack,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region City
  Widget city() {
    return StreamBuilder<bool>(
      stream: initialOnboardingBloc.refreshCtrl.stream,
      builder: (context, snapshot) {
        return SizedBox(
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              AppTitleAndOptions(
                title: AppStrings.city,
                option: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 15), // Added spacing
                    AppCommonWidgets.dropDownOptions(
                      onTap: () {
                        initialOnboardingBloc.onTapCity();
                      },
                      context: context,
                      hintText: AppStrings.city,
                      value: initialOnboardingBloc.cityTextCtrl.text,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  //endregion

  //region Invite code
  Widget inviteCode() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          AppTitleAndOptions(
            title: AppStrings.inviteCode,
            titleOption: AppToolTip(
              message: "This helps us track who invited you",
              toolTipWidget: Text(
                "does this help?",
                style: AppTextStyle.contentText0(
                    textColor: AppColors.writingBlack1),
              ),
            ),
            option: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 15), // Added spacing
                AppTextFields.allTextField(
                  context: context,
                  maxEntry: 10,
                  textEditingController:
                      initialOnboardingBloc.referralCodeTextCtrl,
                  hintText: AppStrings.inviteCode,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Continue Button
  Widget continueButton() {
    return StreamBuilder<bool>(
      stream: initialOnboardingBloc.loadingCtrl.stream,
      initialData: false,
      builder: (context, snapshot) {
        bool isLoading = snapshot.data ?? false;

        return KeyboardVisibilityBuilder(
          builder: (context, isKeyBoardOpen) {
            return SizedBox(
              width: double.infinity,
              child: CupertinoButton(
                borderRadius: BorderRadius.circular(10),
                padding:
                    const EdgeInsets.symmetric(vertical: 13, horizontal: 15),
                color: AppColors.brandBlack,
                onPressed: isLoading
                    ? null
                    : () async {
                        if (initialOnboardingBloc.validateFields()) {
                          // Set loading state to true
                          initialOnboardingBloc.loadingCtrl.sink.add(true);

                          // Call API to update user details
                          bool success =
                              await initialOnboardingBloc.saveUserData();

                          // Check if widget is still mounted before using context
                          if (!mounted) return;

                          // Set loading state to false
                          initialOnboardingBloc.loadingCtrl.sink.add(false);

                          if (success) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => WhatIsSwadesicScreen(
                                  userReference: widget.userReference,
                                  userData: initialOnboardingBloc.getUserData(),
                                  icon: widget.icon,
                                ),
                              ),
                            );
                          }
                        }
                      },
                                child: isLoading
                    ? SizedBox(
                        width: 24,
                        height: 24,
                        child: AppCommonWidgets.inlineCircularProgress(size: 24),
                      )
                    : Text(
                        "Continue",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyle.access0(
                            textColor: AppColors.appWhite),
                      ),
              ),
            );
          },
        );
      },
    );
  }

  //endregion

  //region Terms and policy
  Widget termsAndPolicy() {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        text: 'By continuing, you agree to our ',
        style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
        children: <TextSpan>[
          TextSpan(
            text: 'Terms of Use',
            style: AppTextStyle.smallText(textColor: AppColors.brandBlack),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                //If web
                if (kIsWeb) {
                  await launchUrl(Uri.parse(
                      AppConstants.appCurrentEnvironment == Environment.dev
                          ? AppConstants.devTermsWebsite
                          : AppConstants.termsWebsite));
                } else {
                  var screen = AppWebView(
                      url: AppConstants.appCurrentEnvironment == Environment.dev
                          ? AppConstants.devTermsWebsite
                          : AppConstants.termsWebsite);
                  var route = CupertinoPageRoute(builder: (context) => screen);
                  Navigator.push(context, route);
                }
              },
          ),
          TextSpan(
            text: ' and ',
            style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
          ),
          TextSpan(
            text: 'Privacy Policy.',
            style: AppTextStyle.smallText(textColor: AppColors.brandBlack),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                //If web
                if (kIsWeb) {
                  await launchUrl(Uri.parse(
                      AppConstants.appCurrentEnvironment == Environment.dev
                          ? AppConstants.devPrivacyPolityWebsite
                          : AppConstants.privacyPolityWebsite));
                } else {
                  var screen = AppWebView(
                      url: AppConstants.appCurrentEnvironment == Environment.dev
                          ? AppConstants.devPrivacyPolityWebsite
                          : AppConstants.privacyPolityWebsite);
                  var route = CupertinoPageRoute(builder: (context) => screen);
                  Navigator.push(context, route);
                }
              },
          ),
          TextSpan(
            text: ' of Swadesic.',
            style: AppTextStyle.smallText(textColor: AppColors.writingBlack1),
          ),
        ],
      ),
    );
  }
  //endregion
}
