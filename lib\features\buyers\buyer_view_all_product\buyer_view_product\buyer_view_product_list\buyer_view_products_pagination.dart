import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_product_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/features/post/post_screen_bloc.dart';
import 'package:swadesic/features/post/recommened_posts/recommended_post_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/app_analytics/app_analytics.dart';
import 'package:swadesic/services/buyer_search_services/buyer_search_services.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum BuyerViewPaginationState { Loading, Done, Empty }

class BuyerViewProductPagination {
  //region Context
  late BuildContext context;
  late BuyerViewProductBloc buyerViewProductBloc;
  int limit = 5;

  // bool isLoadingPaginationData = false;
  BuyerViewPaginationState currentBuyerViewProductsPaginationState = BuyerViewPaginationState.Loading;
  //endregion

  //region Controller
  final buyerViewProductsPaginationStateCtrl = StreamController<BuyerViewPaginationState>.broadcast();

  //endregion

  //region Constructor
  BuyerViewProductPagination(this.context, this.buyerViewProductBloc);
  //endregion

  //region On visible loading
  void onVisibleLoading() async {
    //If Page state is empty then return
    if (currentBuyerViewProductsPaginationState == BuyerViewPaginationState.Empty) {
      return;
    }
    if (buyerViewProductBloc.productList.length >= 2) {
      //Increase offset
      // buyerViewProductBloc.offset = limit + buyerViewProductBloc.offset;
      // Fetch more feed posts when list is scrolled to the bottom
      //If from store
      if (SearchScreenEnum.STORE == buyerViewProductBloc.openingFrom) {
        await getStoreProduct();
      }
      //If from search
      if (SearchScreenEnum.SEARCH_RESULT == buyerViewProductBloc.openingFrom) {
        await getSearchProduct();
      }
      //If from recommended product
      if (SearchScreenEnum.RECOMMENDED_PRODUCTS == buyerViewProductBloc.openingFrom) {
        await getRecommendedProduct();
      }
    }
  }

  //endregion

  //region Get pagination posts
  Future<void> getStoreProduct() async {
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    try {
      //Loading
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Loading);
      // await Future.delayed(Duration(seconds: 2));
      //Api call
      var data = await StoreProductServices().getBuyerStoreProduct(
        storeReference: buyerViewProductBloc.productList.first.storeReference!,
        limit: 10,
        offset: buyerViewProductBloc.productList.length,
      );
      //Add all products into store product list
      buyerViewProductBloc.productList.addAll(data.data!);
      //Add all products in product data model
      productDataModel.addProductIntoList(products: buyerViewProductBloc.productList);
      //If response list is empty
      if (data.data!.isEmpty) {
        currentBuyerViewProductsPaginationState = BuyerViewPaginationState.Empty;
        // isLoadingPaginationData = false;
        return buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Empty);
      }
      // isLoadingPaginationData = false;
      //Done
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Done);
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(error.message.toString(), context) : null;
      //Done
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Done);
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
      //Done
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Done);
    }
  }

//endregion

// region Get recommended product
  Future<void> getRecommendedProduct() async {
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    try {
      //Loading
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Loading);
      // await Future.delayed(Duration(seconds: 2));
      //Api call
      var data = await RecommendedStoreAndUserServices().getRecommendedProducts(limit: 5, offset: buyerViewProductBloc.productList.length,context: context);
      //Add all products into store product list
      buyerViewProductBloc.productList.addAll(data);
      //Add all products in product data model
      productDataModel.addProductIntoList(products: buyerViewProductBloc.productList);
      //If response list is empty
      if (data.isEmpty) {
        currentBuyerViewProductsPaginationState = BuyerViewPaginationState.Empty;
        // isLoadingPaginationData = false;
        return buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Empty);
      }
      // isLoadingPaginationData = false;
      //Done
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Done);
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(error.message.toString(), context) : null;
      //Done
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Done);
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
      //Done
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Done);
    }
  }

//endregion

  //region Get search posts
  Future<void> getSearchProduct() async {
    //Get reference to Product data model
    var productDataModel = Provider.of<ProductDataModel>(context, listen: false);
    try {
      //Loading
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Loading);
      // await Future.delayed(Duration(seconds: 2));
      //Api call
      //  = await StoreProductServices().getBuyerStoreProduct(storeReference: buyerViewProductBloc.productList.first.storeReference!, limit: 5,offset: buyerViewProductBloc.offset,);

      var data = (await BuyerSearchServices().getSearchResult(
        entityType: EntityType.PRODUCT,
        limit: limit,
        offset: buyerViewProductBloc.productList.length,
        pinCode: "110068",
        query: buyerViewProductBloc.searchedText!,
      ))['product']
          .map<Product>((product) => Product.fromJson(product))
          .toList();

      //Add all products into store product list
      buyerViewProductBloc.productList.addAll(data);
      //Add all products in product data model
      productDataModel.addProductIntoList(products: buyerViewProductBloc.productList);
      //If response list is empty
      if (data.isEmpty) {
        currentBuyerViewProductsPaginationState = BuyerViewPaginationState.Empty;
        // isLoadingPaginationData = false;
        return buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Empty);
      }
      // isLoadingPaginationData = false;
      //Done
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Done);
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(error.message.toString(), context) : null;
      //Done
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Done);
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context) : null;
      //Done
      buyerViewProductsPaginationStateCtrl.sink.add(BuyerViewPaginationState.Done);
    }
  }
//endregion
}
