import 'package:flutter/material.dart';
import 'package:swadesic/model/seller_all_order_response/seller_order_details.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class OrderDetailCommonWidgets{


  //region  title
  static Widget title({required PriceDetails priceDetails,required Widget price}){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(priceDetails.orderBreakupItemText!,style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),),
            price,
          ],
        ),

      ],
    );

  }
  //endregion



  //region Sub title
  static Widget subTitle({required PriceDetails priceDetails,bool breakupPriceVisible = false}){
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(priceDetails.orderBreakupItemText!,style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack),),
              Text("₹${priceDetails.orderBreakupItemValue!}",style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack),),
            ],
          ),

          priceDetails.orderBreakupItemSubtext==null?const SizedBox():Visibility(
            visible: breakupPriceVisible,
            child: Container(
              margin: const EdgeInsets.only(top: 3),
                child: Text(CommonMethods.addRupeeSymbol(input: priceDetails.orderBreakupItemSubtext!))),
          )
        ],
      ),
    );

  }
  //endregion

}