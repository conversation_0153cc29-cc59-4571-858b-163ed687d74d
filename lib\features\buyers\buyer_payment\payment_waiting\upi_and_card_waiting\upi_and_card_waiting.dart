import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class UpiAndCardWaiting extends StatefulWidget {
  final bool isUpiWaiting;
  const UpiAndCardWaiting({Key? key, required this.isUpiWaiting}) : super(key: key);

  @override
  State<UpiAndCardWaiting> createState() => _UpiAndCardWaitingState();
}

class _UpiAndCardWaitingState extends State<UpiAndCardWaiting> {
  @override
  Widget build(BuildContext context) {
    return body();
  }


  //region body
Widget body(){
    if(widget.isUpiWaiting){
      return Center(child: info());
    }
    return  const Text("Card");
}
//endregion


//region Icon and info
Widget info(){
    return Padding(
      padding: const EdgeInsets.only(left: 20,right: 20,bottom: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: Container(
              margin: const EdgeInsets.only(top: 43,bottom: 100),
              child: SvgPicture.asset(AppImages.upiWaiting))),
          //Your upi got a notification
          Container(
            margin: const EdgeInsets.only(bottom: 116),
            child: Text(AppStrings.yourUpiAppReceivedANotification,
              textAlign: TextAlign.center,
              style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
          ),
          //Three does
          Container(
              margin: const EdgeInsets.only(bottom: 20),
              child: Lottie.asset(AppImages.threeDotsAnimation,height: 50)),
          //Please don't go back
          Container(
            child: Text(AppStrings.pleaseDoNotGoBack,
              textAlign: TextAlign.center,
              style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack0),),
          ),

        ],
      ),
    );
}
//endregion





}
