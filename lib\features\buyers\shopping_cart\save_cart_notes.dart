import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/cache_storage/storage_keys.dart';

class SaveCartNotes{

  late GetCartDetailsResponse cartDetailsResponse;
  late BuildContext context;

  late CacheStorageService cacheStorageService;
  SaveCartNotes({required this.cartDetailsResponse,required this.context,required this.cacheStorageService});


  //region Save cart details to share pref
void saveCartDetailToSharePref()async{
  //Cart detail object to string
  String cartDetailToString = jsonEncode(cartDetailsResponse);
  //print(cartDetailToString);
  //Save cart detail string to share pref
   await cacheStorageService.saveString(StorageKeys.shoppingCartMessage, cartDetailToString);

}
//endregion


//region Get saved cart detail from share pref
  void getCartDetailToSharePref()async{

    //Get cart detail string from share pref
    var cartDetailToString  = await cacheStorageService.getString(StorageKeys.shoppingCartMessage);

    //Decode json
    Map<String, dynamic> response ;

    response = jsonDecode(cartDetailToString);

    late GetCartDetailsResponse cartDetail;

    cartDetail = GetCartDetailsResponse.fromJson(response);

    ///Adding seller notes
    //Cart detail response
    for (var data in cartDetailsResponse.cartStoreList!){
      //Saved data
      for(var savedData in cartDetail.cartStoreList!){
        //If store refer is equal then save the data
        if(data.storeReference == savedData.storeReference){
          data.sellerNote = savedData.sellerNote;
        }
      }
    }
    ///Adding delivery notes
    cartDetailsResponse.deliveryNotes = cartDetail.deliveryNotes;

 }
//endregion


//region Save address id
void saveAddressId({required String addressId})async{
  //Save cart detail string to share pref
  await cacheStorageService.saveString(StorageKeys.shoppingCartAddress, addressId);
}
//endregion



//region Get address id
  Future<String> getAddressId()async{
    String addressId  = await cacheStorageService.getString(StorageKeys.shoppingCartAddress);

    return addressId;

  }
//endregion

}