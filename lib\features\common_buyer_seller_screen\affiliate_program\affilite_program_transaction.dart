import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_bloc.dart';
import 'package:swadesic/features/widgets/transaction_card/transaction_card.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class AffiliateProgramTransaction extends StatefulWidget {
  final AffiliateProgramBloc affiliateProgramBloc;
  const AffiliateProgramTransaction({super.key, required this.affiliateProgramBloc});

  @override
  State<AffiliateProgramTransaction> createState() => _AffiliateProgramTransactionState();
}

class _AffiliateProgramTransactionState extends State<AffiliateProgramTransaction> {
  @override
  Widget build(BuildContext context) {
    return body();
  }



  //region Body
Widget body(){
    return Column(
      children: [

        Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: AppColors.appBlack.withOpacity(0.1), width: 1.0),
            ),
          ),
          margin: const EdgeInsets.symmetric(vertical: 10),
          padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 15),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(AppStrings.transactions,
                style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
              ),
              // InkWell(
              //     onTap: (){
              //       accountBalanceTransactionBloc.onTapFilter();
              //
              //
              //     },
              //     child: SvgPicture.asset(AppImages.filter))
            ],
          ),
        ),
        transactionList()
      ],

    );
}
//endregion



//region Transaction list
Widget transactionList(){
    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 100),
      shrinkWrap: true,
        itemCount: widget.affiliateProgramBloc.affiliateProgramResponse.userAffiliateRewardHistory!.length,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context,index){
          return InkWell(
              onTap: (){
                //If has rupee
                if(widget.affiliateProgramBloc.affiliateProgramResponse.userAffiliateRewardHistory![index].rewardCategory == "RUPEE"){
                  widget.affiliateProgramBloc.onTapOrder(appTransaction: widget.affiliateProgramBloc.affiliateProgramResponse.userAffiliateRewardHistory![index]);
                }
              },
              child: TransactionCard(appTransaction:widget.affiliateProgramBloc.affiliateProgramResponse.userAffiliateRewardHistory![index] ));

    });
}
//endregion


}

