import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class DeliveryAndReturnPersonLogisticCommonWidgets{


  //region Detail title and sub
  static detailTitleAndSub({required String titleText,required String subTitle,bool isCopyButtonVisible = false,required BuildContext context, bool isCopySubTitle = false}){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

        AppTitleAndOptions(
            title:titleText,
            titlePaddingHorizontal: 15,
            titleOption: SvgPicture.asset(AppImages.exclamation),


            option: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                InkWell(
                  onLongPress: (){
                    isCopySubTitle?CommonMethods.copyText(context, subTitle):null;
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 25),
                    child: Text(subTitle,style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack
                    ),),
                  ),
                ),
                Visibility(
                  visible: isCopyButtonVisible,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 25),
                    child: AppCommonWidgets.copyCallButton(text: AppStrings.copy, onTap: (){
                      CommonMethods.copyText(AppConstants.userStoreCommonBottomNavigationContext, subTitle);
                    }),
                  ),
                ),
              ],
            ),
        )



      ],
    );
  }

//endregion

}