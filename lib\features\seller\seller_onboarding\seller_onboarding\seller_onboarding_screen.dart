import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_common_widget.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding/how_do_you_describe/how_do_you_describe.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding/lets_make_in_bharat/lets_make_in_bharat.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding/seller_onboarding_bloc.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding/seller_onboarding_common_widgets.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Seller On boarding
class SellerOnBoardingScreen extends StatefulWidget {
  final bool isTestStore;
  const SellerOnBoardingScreen({Key? key, required this.isTestStore})
      : super(key: key);

  @override
  _SellerOnBoardingScreenState createState() => _SellerOnBoardingScreenState();
}
// endregion

class _SellerOnBoardingScreenState extends State<SellerOnBoardingScreen> {
  // region Bloc
  late SellerOnBoardingBloc sellerOnBoardingBloc;

  // endregion

  // region Init
  @override
  void initState() {
    sellerOnBoardingBloc = SellerOnBoardingBloc(context, widget.isTestStore);
    sellerOnBoardingBloc.init();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,

        floatingActionButton: indicatorNext(),
        // floatingActionButtonLocation: FloatingActionButtonLocation.,
        backgroundColor: AppColors.appWhite,
        body: SafeArea(child: body()),
      ),
    );
  }

  // endregion

  // region Body
  Widget body() {
    return StreamBuilder<StoreOnBoardingState>(
        stream: sellerOnBoardingBloc.storeOnBoardingCtrl.stream,
        builder: (context, snapshot) {
          if (snapshot.data == StoreOnBoardingState.Loading) {
            return Center(
              child: AppCommonWidgets.appCircularProgress(),
            );
          }
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 33, bottom: 39),
                child: indicator(),
              ),
              Expanded(
                child: PageView(
                  //physics: NeverScrollableScrollPhysics(),
                  controller: sellerOnBoardingBloc.onBoardingPageCtrl,
                  onPageChanged: (index) {
                    sellerOnBoardingBloc.onChangePage(index);
                  },
                  children: [
                    createNationalBrand(),
                    buildTrustAround(),
                    LetsMakeInBharat(
                      sellerOnBoardingBloc: sellerOnBoardingBloc,
                      isTestStore: widget.isTestStore,
                    )
                  ],
                ),
              ),

              //verticalSizedBox(25),
            ],
          );
        });
  }

// endregion

  ///Create a National Brand
  //region Create a nation Screen
  //region Create a National Brand
  Widget createNationalBrand() {
    return ListView(
      shrinkWrap: true,
      // mainAxisSize: MainAxisSize.min,
      // mainAxisAlignment: MainAxisAlignment.center,
      // crossAxisAlignment: CrossAxisAlignment.center,
      // shrinkWrap: true,
      // padding: EdgeInsets.zero,
      children: [
        createNationText(),
        Container(
          margin: const EdgeInsets.only(top: 57, bottom: 40),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              addLogo(),
              verticalSizedBox(10),
              storeCategory(),
            ],
          ),
        ),
        chooseBusinessCategory(),
        verticalSizedBox(30),

        HowDoYouDescribe(
          sellerOnBoardingBloc: sellerOnBoardingBloc,
        ),
        // businessDesc(),
        AppCommonWidgets.bottomListSpace(context: context),
      ],
    );
  }

  //endregion

  //region Create a nation
  Widget createNationText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SellerOnBoardingCommonWidgets.screenTitle(
          value: AppStrings.createNationalBrand),
    );
  }

  //endregion

  //region Add logo
  Widget addLogo() {
    return StreamBuilder<bool>(
        stream: sellerOnBoardingBloc.logoCtrl.stream,
        builder: (context, snapshot) {
          return InkWell(
            onTap: () {
              sellerOnBoardingBloc.goToAddImageScreen();
            },
            child: SizedBox(
              height: 106,
              width: 106,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(CommonMethods()
                    .getBorderRadius(
                        height: 106,
                        imageType: CustomImageContainerType
                            .store)), //region Get border radius
                child: sellerOnBoardingBloc.isImageSelected
                    ? kIsWeb && sellerOnBoardingBloc.webImageBytes != null
                        // Web platform with image selected
                        ? Image.memory(
                            sellerOnBoardingBloc.webImageBytes!,
                            fit: BoxFit.cover,
                            cacheHeight: 300,
                            cacheWidth: 300,
                          )
                        // Mobile platform with image selected
                        : Image.file(
                            File(sellerOnBoardingBloc.files.path),
                            fit: BoxFit.cover,
                            cacheHeight: 300,
                            cacheWidth: 300,
                          )
                    // No image selected
                    : CustomImageContainer(
                        width: 106,
                        height: 106,
                        imageUrl: AppImages.storePlaceHolder,
                        imageType: CustomImageContainerType.store,
                      ),

                // SvgPicture.asset(
                //       AppImages.storePlaceHolder,
                //       fit: BoxFit.cover,
                //       height: 106,
                //       width: 106,
                //     )
              ),
            ),
          );
        });
  }

  //endregion

  //region Store Name and Category
  Widget storeCategory() {
    return StreamBuilder<bool>(
        stream: sellerOnBoardingBloc.onTextChangeCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                sellerOnBoardingBloc.storeNameTextCtrl.text.isEmpty
                    ? "Store Name"
                    : sellerOnBoardingBloc.storeNameTextCtrl.text.trim(),
                textAlign: TextAlign.center,
                style:
                    AppTextStyle.usernameHeading(textColor: AppColors.appBlack),
              ),
              verticalSizedBox(5),
              Text(
                sellerOnBoardingBloc.selectedBusinessCategory.isEmpty
                    ? "Business Category"
                    : sellerOnBoardingBloc.selectedBusinessCategory,
                textAlign: TextAlign.center,
                style: AppTextStyle.contentHeading0(
                    textColor: AppColors.writingBlack1),
              ),
            ],
          );
        });
  }

  //endregion

  //region What is your store name and Text Field
  Widget storeNameField() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: AppStrings.whatIsStoreName,
          option: AppTextFields.allTextField(
            maxEntry: 40,
            onChanged: () {
              sellerOnBoardingBloc.onTextChangeCtrl.sink.add(true);
            },
            context: context,
            textEditingController: sellerOnBoardingBloc.storeNameTextCtrl,
            hintText: AppStrings.enterStoreName,
          ),
        ),
      ],
    );
  }

  //endregion

  //region Choose business category and Text Field
  Widget chooseBusinessCategory() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          storeNameField(),
          verticalSizedBox(30),
          AppTitleAndOptions(
            title: AppStrings.chooseYourBusiness,
            option: AppCommonWidgets.dropDownOptions(
                onTap: () {
                  sellerOnBoardingBloc.onTapBusinessCategory();
                },
                context: context,
                hintText: AppStrings.selectBusinessCategory,
                value: sellerOnBoardingBloc.selectedBusinessCategory),
          ),
        ],
      ),
    );
  }

  //endregion

  //endregion

  ///Brand Build Trust Around
  //region Trust Around Page
  //region Build Trust Around
  Widget buildTrustAround() {
    return ListView(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      children: [
        buildTrust(),
        Container(
          margin: const EdgeInsets.only(top: 57, bottom: 40),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              addLogo(),
              verticalSizedBox(10),
              storeCategory(),
            ],
          ),
        ),
        writeAboutBusiness(),
        verticalSizedBox(30),
        websiteLink(),
        AppCommonWidgets.bottomListSpace(context: context),
      ],
    );
  }

  //endregion

  //region Build Trust
  Widget buildTrust() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SellerOnBoardingCommonWidgets.screenTitle(
          value: AppStrings.buildTrust),
    );

    // return Text(
    //   AppStrings.buildTrust,
    //   textAlign: TextAlign.center,
    //   style: TextStyle(
    //     fontFamily: "LatoRegular",
    //     fontWeight: FontWeight.w400,
    //     fontSize: 24,
    //     color: AppColors.appBlack,
    //   ),
    // );
  }

  //endregion

  //region store
  Widget store() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.all(Radius.circular(5)),
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, 1),
            blurRadius: 5,
            color: AppColors.appBlack.withOpacity(0.2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(alignment: Alignment.center, children: [
            storeImage(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  followers(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      sales(),
                      horizontalSizedBox(10),
                      RotatedBox(
                          quarterTurns: 0,
                          child: SvgPicture.asset(
                            AppImages.newOrderUpArrow,
                            height: 14,
                            color: AppColors.writingColor2,
                          ))
                    ],
                  ),
                ],
              ),
            )
          ]),
          verticalSizedBox(4),
          Center(child: storeNameAndType()),
          // storeName(),
          // verticalSizedBox(4),
          // storeType(),
          verticalSizedBox(5),
          aboutStore(),
          verticalSizedBox(5),
          storeUrl(),
          verticalSizedBox(5),
          followMessageTrust(),
        ],
      ),
    );
  }

  //endregion

  //region Store Image
  Widget storeImage() {
    return SizedBox(
        height: 50,
        child: StreamBuilder<bool>(
            stream: sellerOnBoardingBloc.logoCtrl.stream,
            builder: (context, snapshot) {
              return InkWell(
                onTap: () {
                  AppConstants.multipleSelectedImage.clear();
                  sellerOnBoardingBloc.goToAddImageScreen();
                },
                child: SizedBox(
                  height: 50,
                  width: 50,
                  child: ClipRRect(
                      borderRadius: const BorderRadius.all(Radius.circular(20)),
                      child: sellerOnBoardingBloc.isImageSelected
                          ? kIsWeb && sellerOnBoardingBloc.webImageBytes != null
                              // Web platform with image selected
                              ? Image.memory(
                                  sellerOnBoardingBloc.webImageBytes!,
                                  fit: BoxFit.cover,
                                  cacheHeight: 150,
                                  cacheWidth: 150,
                                )
                              // Mobile platform with image selected
                              : Image.file(
                                  File(sellerOnBoardingBloc.files.path),
                                  fit: BoxFit.cover,
                                  cacheHeight: 150,
                                  cacheWidth: 150,
                                )
                          // No image selected
                          : SvgPicture.asset(
                              AppImages.addYourLogo,
                              fit: BoxFit.cover,
                              height: 150,
                              width: 150,
                            )),
                  // child: ClipRRect(
                  //   borderRadius: BorderRadius.all(Radius.circular(20)),
                  //   child: AppConstants.multipleSelectedImage.isEmpty
                  //       ? SvgPicture.asset(
                  //           AppImages.addYourLogo,
                  //           fit: BoxFit.cover,
                  //           height: 150,
                  //           width: 150,
                  //         )
                  //       : Image.file(
                  //           File(sellerOnBoardingBloc.files!.path),
                  //           fit: BoxFit.cover,
                  //           cacheHeight: 150,
                  //           cacheWidth: 150,
                  //         ),
                  // ),
                ),
              );
            }));
  }

  //endregion

  //region Store Name and Type
  Widget storeNameAndType() {
    return StreamBuilder<bool>(
        stream: sellerOnBoardingBloc.onTextChangeCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                sellerOnBoardingBloc.storeNameTextCtrl.text.isEmpty
                    ? "Store Name"
                    : sellerOnBoardingBloc.storeNameTextCtrl.text.trim(),
                textAlign: TextAlign.center,
                style: const TextStyle(
                    fontSize: 19,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'LatoSemiBold',
                    color: AppColors.appBlack),
              ),
              verticalSizedBox(5),
              Text(
                sellerOnBoardingBloc.selectedBusinessCategory.isEmpty
                    ? "Business Category"
                    : sellerOnBoardingBloc.selectedBusinessCategory,
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'LatoBold',
                    color: AppColors.appBlack.withOpacity(0.5)),
              ),
            ],
          );
        });
  }

  //endregion

  //region Followers
  Widget followers() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text(
          "550",
          style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              fontFamily: 'LatoSemiBold',
              color: AppColors.appBlack),
        ),
        Text(
          AppStrings.supporter,
          style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: 'LatoSemiBold',
              color: AppColors.appBlack),
        ),
      ],
    );
  }

  //endregion

  //region Sales
  Widget sales() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text(
          "410",
          style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              fontFamily: 'LatoSemiBold',
              color: AppColors.appBlack),
        ),
        Text(
          AppStrings.sales,
          style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: 'LatoSemiBold',
              color: AppColors.appBlack),
        ),
      ],
    );
  }

  //endregion

  //region About store
  Widget aboutStore() {
    return StreamBuilder<bool>(
        stream: sellerOnBoardingBloc.onTextChangeCtrl.stream,
        builder: (context, snapshot) {
          var data = sellerOnBoardingBloc.aboutStoreTextCtrl.text.isEmpty
              ? AppStrings.aboutBusinessDummyData
              : sellerOnBoardingBloc.aboutStoreTextCtrl.text;

          return RichText(
            textScaleFactor: MediaQuery.textScaleFactorOf(
                AppConstants.globalNavigator.currentContext!),
            overflow: TextOverflow.ellipsis,
            text: TextSpan(
              style: DefaultTextStyle.of(context).style,
              children: <TextSpan>[
                TextSpan(
                    text: '',
                    children: data.split(" ").map((value) {
                      // children: "Hello world too hh @sushant hello world https://google.com ".split(" ").map((value) {
                      ///Tag user name
                      if (value.startsWith('@') && value.length > 1) {
                        return TextSpan(
                          text: ' $value',
                          style: const TextStyle(
                              color: AppColors.brandBlack,
                              fontFamily: AppConstants.rRegular,
                              fontSize: 14,
                              fontWeight: FontWeight.w400),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              //print(value);
                              //widget.buyerViewStoreBloc.goToProfile();
                            },
                        );
                      }

                      ///Tag url
                      if (Uri.parse(value).isAbsolute) {
                        return TextSpan(
                          text: ' $value',
                          style: const TextStyle(
                              color: AppColors.brandBlack,
                              fontFamily: AppConstants.rRegular,
                              fontSize: 14,
                              fontWeight: FontWeight.w400),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              //print(value);
                              CommonMethods.openAppWebView(
                                  webUrl: value, context: context);
                              //widget.buyerViewStoreBloc.goToProfile();
                            },
                        );
                      }

                      ///Default text
                      return TextSpan(
                        text: ' $value',
                        style: const TextStyle(
                            color: AppColors.appBlack,
                            fontFamily: AppConstants.rRegular,
                            fontSize: 14,
                            fontWeight: FontWeight.w400),
                      );
                    }).toList()),
              ],
            ),
            maxLines: 3,
          );
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text(
                sellerOnBoardingBloc.aboutStoreTextCtrl.text.isEmpty
                    ? AppStrings.aboutBusinessDummyData
                    : sellerOnBoardingBloc.aboutStoreTextCtrl.text,
                textAlign: TextAlign.left,
                overflow: TextOverflow.ellipsis,
                maxLines: 3,
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'LatoRegular',
                    color: AppColors.appBlack)),
          );
        });
  }

  //endregion

  //region Store URL
  Widget storeUrl() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: FittedBox(
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            const Text("www.whitelabel.com/storehandle/links",
                textAlign: TextAlign.left,
                maxLines: 1,
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'LatoBold',
                    color: AppColors.brandBlack)),
            horizontalSizedBox(10),
            CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {},
                child: SvgPicture.asset(
                  AppImages.storeUrlIcon,
                  fit: BoxFit.cover,
                ))
          ],
        ),
      ),
    );
  }

  //endregion

  //region Follow, Message and Trust
  Widget followMessageTrust() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),

      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          //Support Store
          Expanded(
              child: StoreCommonWidgets.storeButtons(
                  textAndIcon: appText(
                    AppStrings.support,
                    color: AppColors.appWhite,
                    fontFamily: AppConstants.rRegular,
                    textAlign: TextAlign.center,
                    fontSize: 15,
                    fontWeight: FontWeight.w700,
                  ),
                  verticalPadding: 10,
                  buttonColor: AppColors.brandBlack,
                  onTapButton: () {
                    // widget.buyerViewStoreBloc.followUnfollow();
                  })),

          horizontalSizedBox(10),
          //Contact
          Expanded(
              child: StoreCommonWidgets.storeButtons(
                  textAndIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      appText(
                        AppStrings.contact,
                        color: AppColors.writingColor2,
                        fontFamily: AppConstants.rRegular,
                        textAlign: TextAlign.center,
                        fontSize: 15,
                        fontWeight: FontWeight.w700,
                      ),
                      horizontalSizedBox(5),
                      SvgPicture.asset(AppImages.downArrow)
                    ],
                  ),
                  verticalPadding: 10,
                  onTapButton: () {
                    // widget.buyerViewStoreBloc.onTapMessage(storeInfo: widget.buyerViewStoreBloc.singleStoreInfoResponse.data!);
                  })),

          horizontalSizedBox(10),
          //Trust
          Expanded(
              child: StoreCommonWidgets.storeButtons(
                  textAndIcon: appText(
                    AppStrings.trust,
                    color: AppColors.writingColor2,
                    fontFamily: AppConstants.rRegular,
                    textAlign: TextAlign.center,
                    fontSize: 15,
                    fontWeight: FontWeight.w700,
                  ),
                  verticalPadding: 10,
                  onTapButton: () {
                    // widget.buyerViewStoreBloc.goToTrustCenter(storeReference: widget.buyerViewStoreBloc.singleStoreInfoResponse.data!.storeReference!);
                  })),
        ],
      ),
      // child: Row(
      //   mainAxisSize: MainAxisSize.max,
      //   mainAxisAlignment: MainAxisAlignment.center,
      //   crossAxisAlignment: CrossAxisAlignment.center,
      //   children: [
      //     Expanded(
      //       child: Container(
      //         padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      //         decoration: const BoxDecoration(color: AppColors.brandGreen, borderRadius: BorderRadius.all(Radius.circular(50))),
      //         child: Center(
      //           child: Text(
      //             AppStrings.support,
      //             style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w700, fontFamily: "LatoBold", color: AppColors.white),
      //           ),
      //         ),
      //       ),
      //     ),
      //     horizontalSizedBox(5),
      //     Expanded(
      //       child: Container(
      //         padding: const EdgeInsets.symmetric(vertical: 8),
      //         decoration:
      //             BoxDecoration(borderRadius: const BorderRadius.all(Radius.circular(50)), border: Border.all(color: AppColors.lightWhite2, width: 1.5)),
      //         child: Center(
      //           child: Row(
      //             mainAxisSize: MainAxisSize.min,
      //             mainAxisAlignment: MainAxisAlignment.center,
      //             crossAxisAlignment: CrossAxisAlignment.center,
      //             children: [
      //               Text(
      //                 AppStrings.contact,
      //                 style: const TextStyle(fontFamily: "LatoBold", fontWeight: FontWeight.w700, fontSize: 15, color: AppColors.writingColor2),
      //               ),
      //               horizontalSizedBox(10),
      //               SvgPicture.asset(
      //                 AppImages.downArrow,
      //                 color: AppColors.darkGray,
      //               ),
      //             ],
      //           ),
      //         ),
      //       ),
      //     ),
      //     horizontalSizedBox(5),
      //     Expanded(
      //       child: Container(
      //         padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      //         decoration:
      //             BoxDecoration(
      //                 color: AppColors.lightGray,
      //                 borderRadius: const BorderRadius.all(Radius.circular(50)), border: Border.all(color: AppColors.lightWhite2, width: 1.5)),
      //         child: Center(
      //           child: Text(
      //             AppStrings.trust,
      //             style: const TextStyle(fontFamily: "LatoBold", fontWeight: FontWeight.w700, fontSize: 15, color: AppColors.writingColor2),
      //           ),
      //         ),
      //       ),
      //     ),
      //   ],
      // ),
    );
  }

  //endregion

  //region Write about business and Text Field
  Widget writeAboutBusiness() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppTitleAndOptions(
            title: AppStrings.writeAboutBusiness,
            option: AppTextFields.allTextField(
                context: context,
                minLines: 5,
                maxLines: 5,
                maxEntry: 500,
                textEditingController: sellerOnBoardingBloc.aboutStoreTextCtrl,
                hintText: AppStrings.howCool,
                textInputAction: TextInputAction.newline,
                keyboardType: TextInputType.multiline),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Website Link
  Widget websiteLink() {
    return StreamBuilder<bool>(
        stream: sellerOnBoardingBloc.addNewLinkCtrl.stream,
        builder: (context, snapshot) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 15),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppTitleAndOptions(
                  title: AppStrings.websiteLink,
                  option: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ListView.builder(
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: sellerOnBoardingBloc.linkText.length,
                          itemBuilder: (BuildContext context, index) {
                            return Padding(
                              padding: EdgeInsets.only(
                                  top: sellerOnBoardingBloc.linkText[index] == 0
                                      ? 0
                                      : 10),
                              //padding: const EdgeInsets.all(8.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child: AppTextFields.websiteTextField(
                                            context: context,
                                            minLines: 1,
                                            maxLines: 1,
                                            textEditingController:
                                                sellerOnBoardingBloc
                                                    .linkText[index],
                                            hintText: AppStrings.yourLinks,
                                            onChanged: () {
                                              sellerOnBoardingBloc
                                                  .checkUrlValidation(
                                                      currentUrlCtrl:
                                                          sellerOnBoardingBloc
                                                              .linkText[index],
                                                      fieldIndex: index);
                                              // List<String> links = sellerOnBoardingBloc.linkText.map((e) => e.text).toList();
                                              // if(sellerOnBoardingBloc.linkText.first.text.isEmpty){
                                              //   null;
                                              // }
                                              // else{
                                              //   for(int i = 0;i<links.length; i++){
                                              //     // bool _validURL = Uri.parse(links[i]).isAbsolute;
                                              //     // //print(_validURL);
                                              //     // if(!_validURL){
                                              //     //   CommonMethods.toastMessage("Please Enter valid URL", context);
                                              //     //   return;
                                              //     // }
                                              //     // else{
                                              //     //   null;
                                              //     // }
                                              //   }
                                              // }
                                            }),
                                        // child: TextFormField(
                                        //   minLines: 1,
                                        //   maxLines: 1,
                                        //   controller:sellerOnBoardingBloc.linkText[index] ,
                                        //   style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack),
                                        //   keyboardType:TextInputType.url ,
                                        //   textCapitalization:TextCapitalization.none ,
                                        //   onEditingComplete: (){
                                        //     List<String> links = sellerOnBoardingBloc.linkText.map((e) => e.text).toList();
                                        //     if(sellerOnBoardingBloc.linkText.first.text.isEmpty){
                                        //       null;
                                        //     }
                                        //     else{
                                        //       for(int i = 0;i<links.length; i++){
                                        //         bool _validURL = Uri.parse(links[i]).isAbsolute;
                                        //         //print(_validURL);
                                        //         if(!_validURL){
                                        //           CommonMethods.toastMessage("Please Enter valid URL", context);
                                        //           return;
                                        //         }
                                        //         else{
                                        //           null;
                                        //         }
                                        //       }
                                        //     }
                                        //   },
                                        //   inputFormatters: [
                                        //     FilteringTextInputFormatter.deny(RegExp(r'\s')),
                                        //   ],
                                        //   decoration: InputDecoration(
                                        //       isDense: true,
                                        //       hintStyle: AppTextStyle.heading2Regular(textColor: AppColors.writingColor3),
                                        //       fillColor: AppColors.lightestGrey, // Specify the desired internal color
                                        //       filled: true,
                                        //       hintText: AppStrings.yourLinks,
                                        //
                                        //       contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 16.0),
                                        //       border:  AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey),
                                        //       focusedBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey),
                                        //       enabledBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey),
                                        //       disabledBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey),
                                        //       focusedErrorBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey),
                                        //       errorBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey)
                                        //
                                        //   ),
                                        // ),
                                      ),
                                      sellerOnBoardingBloc.linkText.length == 1
                                          ? Container()
                                          : CupertinoButton(
                                              padding: EdgeInsets.zero,
                                              onPressed: () {
                                                sellerOnBoardingBloc
                                                    .removeTextField(index);
                                              },
                                              child: const Icon(
                                                Icons.close,
                                                color: AppColors.appBlack,
                                              ))
                                    ],
                                  ),

                                  ///If not-Valid
                                  Visibility(
                                    visible: sellerOnBoardingBloc.isUrlValid !=
                                            null &&
                                        !sellerOnBoardingBloc.isUrlValid! &&
                                        sellerOnBoardingBloc
                                                .currentTextFieldIndex ==
                                            index,
                                    child: AppCommonWidgets.validAndInvalid(
                                        textColor: AppColors.red,
                                        buttonText: AppStrings.invalidUrl,
                                        onTap: () {}),
                                  ),
                                ],
                              ),
                            );
                          }),

                      ///Add a new link
                      Visibility(
                        visible: sellerOnBoardingBloc.isUrlValid != null &&
                            sellerOnBoardingBloc.isUrlValid!,
                        child: AppCommonWidgets.validAndInvalid(
                            textColor: AppColors.brandBlack,
                            buttonText: "add a new link",
                            onTap: () {
                              sellerOnBoardingBloc.increaseTextField();
                            }),
                      )
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  //endregion

  //endregion

  ///Let the world know
  //region Let the world Know

  //region Not use
  //   //region Let the world know
  //   Widget letTheWorldKnow() {
  //     return SingleChildScrollView(
  //       child: Padding(
  //         padding: const EdgeInsets.symmetric(horizontal: 30),
  //         child: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           // shrinkWrap: true,
  //           // padding: EdgeInsets.zero,
  //           children: [
  //             letTheWorldText(),
  //             verticalSizedBox(20),
  //             socialMediaIcons(),
  //             verticalSizedBox(20),
  //             shareLink(),
  //             SizedBox(
  //               height: MediaQuery.of(context).size.height / 8,
  //             ),
  //             chooseStoreHandle(),
  //             SizedBox(
  //               height: MediaQuery.of(context).size.height / 8,
  //             ),
  //             makeInBharat(),
  //             AppCommonWidgets.bottomListSpace(context: context),
  //           ],
  //         ),
  //       ),
  //     );
  //   }
  //
  //   //endregion
  //
  //   //region Let the world Know Text
  //   Widget letTheWorldText() {
  //     return Text(
  //       AppStrings.letTheWorldKnow,
  //       textAlign: TextAlign.center,
  //       style: TextStyle(
  //         fontFamily: "LatoRegular",
  //         fontWeight: FontWeight.w400,
  //         fontSize: 24,
  //         color: AppColors.appBlack,
  //       ),
  //     );
  //   }
  //
  //   //endregion
  //
  //   //region SocialMedia Icons
  //   Widget socialMediaIcons() {
  //     return Image.asset(
  //       AppImages.socialMediaIcons,
  //       fit: BoxFit.cover,
  //       cacheWidth: 900,
  //       cacheHeight: 162,
  //     );
  //   }
  //
  //   //endregion
  //
  //   // region Share your link
  //   Widget shareLink() {
  //     return Column(
  //       mainAxisSize: MainAxisSize.min,
  //       mainAxisAlignment: MainAxisAlignment.center,
  //       crossAxisAlignment: CrossAxisAlignment.center,
  //       children: [
  //         Text(
  //           AppStrings.shareYourLink,
  //           textAlign: TextAlign.center,
  //           style: TextStyle(
  //             fontFamily: "LatoSemiBold",
  //             fontWeight: FontWeight.w600,
  //             fontSize: 16,
  //             color: AppColors.writingColor2,
  //           ),
  //         ),
  //         verticalSizedBox(10),
  //         StreamBuilder<bool>(
  //             stream: sellerOnBoardingBloc.onTextChangeCtrl.stream,
  //             builder: (context, snapshot) {
  //               //print(sellerOnBoardingBloc.storeHandleTextCtrl.text);
  //               return Text(
  //                 "www.whitelabel.com/${sellerOnBoardingBloc.storeHandleTextCtrl.text.isEmpty ? "store_handle" : sellerOnBoardingBloc.storeHandleTextCtrl.text}/",
  //                 maxLines: 2,
  //                 textAlign: TextAlign.center,
  //                 style: TextStyle(
  //                   fontFamily: "LatoBold",
  //                   fontWeight: FontWeight.w700,
  //                   fontSize: 16,
  //                   color: AppColors.brandGreen,
  //                 ),
  //               );
  //             }),
  //       ],
  //     );
  //   }
  //
  //   //endregion
  //
  //   //region Choose a store handle and text field
  //   Widget chooseStoreHandle() {
  //     return Column(
  //       mainAxisSize: MainAxisSize.min,
  //       mainAxisAlignment: MainAxisAlignment.center,
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text.rich(
  //           TextSpan(
  //             //style: DefaultTextStyle.of(context).style,
  //             children: <TextSpan>[
  //               TextSpan(
  //                   text: AppStrings.chooseAStoreHandle,
  //                   style: TextStyle(
  //                     fontFamily: "LatoSemiBold",
  //                     fontWeight: FontWeight.w600,
  //                     fontSize: 15,
  //                     color: AppColors.writingColor2,
  //                   )),
  //               TextSpan(
  //                   text: AppStrings.youCanEdit,
  //                   style: TextStyle(
  //                     fontFamily: "LatoRegular",
  //                     fontWeight: FontWeight.w400,
  //                     fontSize: 12,
  //                     color: AppColors.writingColor2,
  //                   )),
  //             ],
  //           ),
  //         ),
  //         verticalSizedBox(10),
  //
  //         TextFormField(
  //           keyboardType:TextInputType.text ,
  //           textInputAction: TextInputAction.done,
  //           maxLines: 1,
  //           controller: sellerOnBoardingBloc.storeHandleTextCtrl,
  //           onChanged: (value){
  //             sellerOnBoardingBloc.onTextChange();
  //           },
  //           // textCapitalization: TextCapitalization.characters,
  //           inputFormatters: <TextInputFormatter>[
  //
  //             FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9_]')),
  //             LengthLimitingTextInputFormatter(20)
  //           ],
  //           //maxLength: maxLength,
  //           style: const TextStyle(fontFamily: "LatoRegular", fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack),
  //           decoration: InputDecoration(
  //             filled: true,
  //             contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
  //             fillColor: AppColors.lightestGrey,
  //             isDense: true,
  //             hintText: AppStrings.enterStoreHandle,
  //             hintStyle: const TextStyle(
  //               fontSize: 14,
  //               fontWeight: FontWeight.w400,
  //             ),
  //             focusedBorder: OutlineInputBorder(
  //               borderRadius: BorderRadius.circular(10),
  //               borderSide: BorderSide(color:AppColors.lightestGrey, width: 1.5),
  //             ),
  //             enabledBorder: OutlineInputBorder(
  //               borderRadius: BorderRadius.circular(10),
  //               borderSide: BorderSide(color: AppColors.lightestGrey, width: 1.5),
  //             ),
  //           ),
  //         ),
  //
  //         // colorFilledTextField(
  //         //   context: context,
  //         //   textFieldCtrl: sellerOnBoardingBloc.storeHandleTextCtrl,
  //         //   hintText: AppStrings.enterStoreHandle,
  //         //   textFieldMaxLine: 1,
  //         //
  //         //   keyboardType: TextInputType.text,
  //         //   textInputAction: TextInputAction.newline,
  //         //   onChangeText: sellerOnBoardingBloc.onTextChange,
  //         //   regExp: AppConstants.intCapStringNoSpace,
  //         //   textCapitalization: TextCapitalization.words,
  //         //   maxCharacter: 21,
  //         //   hintFontFamily: AppConstants.rRegular
  //         // ),
  //       ],
  //     );
  //   }
  //
  //   //endregion
  //
  //   //region Lets Make in Bharat
  //   Widget makeInBharat() {
  //     return CupertinoButton(
  //       borderRadius: BorderRadius.all(Radius.circular(10)),
  //       color: AppColors.brandGreen,
  //       padding: EdgeInsets.zero,
  //       onPressed: () {
  //         sellerOnBoardingBloc.addStoreApiCall();
  //       },
  //       //onPressed: () => sellerOnBoardingBloc.addWebsiteLinksApiCall(),
  //       //onPressed: () => sellerOnBoardingBloc.addWebsiteLinksApiCall(),
  //       child: Container(
  //         padding: EdgeInsets.symmetric(horizontal: 40, vertical: 10),
  //         child: Text(
  //           AppStrings.makeInBharat,
  //           style: TextStyle(fontSize: 15, fontFamily: "LatoBold", color: Colors.white, fontWeight: FontWeight.w700),
  //         ),
  //       ),
  //     );
  //   }
  //
  //   //endregion
  //endregion
  //endregion

  //region Indicator and Next
  Widget indicatorNext() {
    return FloatingActionButton.extended(
        backgroundColor: Colors.transparent,
        extendedPadding: EdgeInsets.zero,
        isExtended: true,
        elevation: 0,
        tooltip: AppStrings.saveChanges,
        label: StreamBuilder<int>(
            initialData: 0,
            stream: sellerOnBoardingBloc.onChangePageCtrl.stream,
            builder: (context, snapshot) {
              if (snapshot.data! >= 2) {
                return const SizedBox();
              }
              return InkWell(
                onTap: () {
                  sellerOnBoardingBloc.onTapNext();
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 20, bottom: 10, top: 10),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: AppColors.brandBlack,
                  ),
                  child: Text("Next",
                      style:
                          AppTextStyle.access0(textColor: AppColors.appWhite)),
                ),
              );
            }),
        onPressed: null);
  }

  //endregion

  //region Indicator
  Widget indicator() {
    return StreamBuilder<int>(
        stream: sellerOnBoardingBloc.onChangePageCtrl.stream,
        initialData: 0,
        builder: (context, snapshot) {
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 10),
            decoration: BoxDecoration(
                color: AppColors.textFieldFill1,
                borderRadius: BorderRadius.circular(10)),
            height: 10,
            child: ListView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                scrollDirection: Axis.horizontal,
                itemCount: 3,
                itemBuilder: (context, index) {
                  if (sellerOnBoardingBloc.currentPage == 0 && index == 0) {
                    return Container(
                      margin: const EdgeInsets.only(right: 3),
                      // padding: const EdgeInsets.symmetric(vertical: 10),
                      width: 24,
                      decoration: BoxDecoration(
                          color: AppColors.orange,
                          borderRadius: BorderRadius.circular(10)),
                    );
                  }
                  if (sellerOnBoardingBloc.currentPage == 1 && index == 1) {
                    return Container(
                      margin: const EdgeInsets.only(right: 3),
                      // padding: const EdgeInsets.symmetric(vertical: 10),
                      width: 24,
                      decoration: BoxDecoration(
                          color: AppColors.appWhite,
                          borderRadius: BorderRadius.circular(10)),
                    );
                  }
                  if (sellerOnBoardingBloc.currentPage == 2 && index == 2) {
                    return Container(
                      margin: const EdgeInsets.only(right: 3),
                      // padding: const EdgeInsets.symmetric(vertical: 10),
                      width: 24,
                      decoration: BoxDecoration(
                          color: AppColors.green,
                          borderRadius: BorderRadius.circular(10)),
                    );
                  }
                  return Container(
                    margin: const EdgeInsets.only(right: 3),
                    height: 5,
                    width: 5,
                    decoration: BoxDecoration(
                        color: AppColors.appBlack.withOpacity(0.7),
                        shape: BoxShape.circle),
                  );
                }),
          );
        });
  }
//endregion
}
