import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/not_delivered/not_delivered.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/rate_review/rate_review.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/delivered_successfully/return_escalate/return_escalate.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/suggest_improvements/suggest_improvements_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class DeliveredSuccessfullyBloc {
  // region Common Variables
  BuildContext context;
  final Order order;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  late BuyerMyOrderServices buyerMyOrderServices;
  final List<SubOrder> subOrderList;

  List<String> groupNameList = [];


  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  DeliveredSuccessfullyBloc(this.context,this.order,this.buyerSubOrderBloc, this.subOrderList);
  // endregion

  // region Init
  void init() {
    takeOutDisplayPackageNumbers();
  }
// endregion



  //region Take out Display package number
  void takeOutDisplayPackageNumbers(){
    groupNameList.clear();
    for(var data in subOrderList){
      groupNameList.add(data.displayPackageNumber!);
    }
    ///Clear duplicates
    groupNameList=groupNameList.toSet().toList();
    //print("Package name length are ${groupNameList.length}");

  }
  //endregion
// region On tap Rate and review
  Future onTapRateReview({required List<SubOrder> selectedSuborderList}){
    return CommonMethods.appBottomSheet(screen: RateReview(
      order: order,
      subOrderList: selectedSuborderList,
      buyerSubOrderBloc: buyerSubOrderBloc,
    ), context: context, bottomSheetName: AppStrings.rateReview).then((value) {
      //Clear all added rating and images
      for(var data in selectedSuborderList){
        data.ratingValue = 0.0;
        data.reviewImages.clear();
        data.reviewComment = '';
      }
    });

  }
// endregion

// region On Tap not delivered
  Future onTapNotDelivered({required List<SubOrder> selectedSuborderList,required bool isFromGroupHead}){
    return CommonMethods.appBottomSheet(
      context: context,
      screen:NotDelivered(subOrderList:selectedSuborderList, buyerSubOrderBloc: buyerSubOrderBloc,
      order: order,
        isFromGroupHead:isFromGroupHead,

      ),
      bottomSheetName:AppStrings.productNotDelivered,
    ).then((value){
    });

  }
// endregion


  // region On Tap Return escalate
   onTapReturnEscalate({required List<SubOrder> selectedSuborderList}){
    List<SubOrder> eligibleReturnSubOrders =selectedSuborderList.where((element) => element.returnDays!=0).toList();

    if(eligibleReturnSubOrders.isEmpty){
       CommonMethods.toastMessage("Return window is closed or order is not eligible", context);
       return ;
    }
    return CommonMethods.appBottomSheet(
      context: context,
      screen:ReturnEscalate(
        subOrderList:eligibleReturnSubOrders,
        buyerSubOrderBloc: buyerSubOrderBloc,
        order: order,
      ),
      bottomSheetName:AppStrings.productReturnAndEscalate,
    ).then((value){
      //Unselect all
      CommonMethods.subOrderSelectUnSelectAll(isSelectAll: false, subOrderList: subOrderList);

      //Refresh sub order screen.
      buyerSubOrderBloc.buyerSubOrderStateCtrl.sink.add(BuyerSubOrderState.Success);

    });

  }
// endregion

// region On Tap Return
//   Future onTapReturn(List<SubOrder> suborderList,BuyerMyOrdersBloc buyerMyOrdersBloc,SubOrder selectedSuborder){
//     return CommonMethods.sellerAndBuyerOrderBottomSheet(
//       subOrderList: suborderList,
//       context: context,
//       screen:Return(selectedSubOrder: selectedSuborder, store:order , buyerMyOrdersBloc: buyerMyOrdersBloc,),
//       title: AppStrings.returnProduct,
//       subTitle: "",
//     ).then((value){
//       //buyerMyOrdersBloc.getBuyerMyOrders(false);
//     });
//   }
// endregion




// region On Tap Suggest
  Future onTapSuggest(List<SubOrder> suborderList,BuyerMyOrdersBloc buyerMyOrdersBloc){
    return CommonMethods.sellerAndBuyerOrderBottomSheet(
      subOrderList: suborderList,
      context: context,
      screen:BuyerSuggestImprovementsScreen(subOrderList: suborderList,),
      title: AppStrings.helpUs,
      subTitle: "",
    ).then((value){
    });

  }
// endregion



//region Go to product comments
void goToProductComments({required SubOrder subOrder}){
  var screen = SinglePostViewScreen(postReference: subOrder.productReference!,isFromProductScreen: true,);
  var route = CupertinoPageRoute(builder: (context) => screen);
  Navigator.push(context, route);
}
//endregion


}
