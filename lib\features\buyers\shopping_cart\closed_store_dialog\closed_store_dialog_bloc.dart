import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:rich_text_controller/rich_text_controller.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_payment/buyer_payment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/available_to/available_to_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/colsed_and_deleted_store_product.dart';
import 'package:swadesic/features/buyers/shopping_cart/save_cart_notes.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/model/shopping_cart_responses/bank_list_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_items_responses.dart';
import 'package:swadesic/model/shopping_cart_responses/order_create_response.dart';
import 'package:swadesic/model/shopping_cart_responses/payment_create_response.dart';
import 'package:swadesic/model/shopping_cart_responses/sopping_cart_price_response.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/shopping_cart_service/shopping_cart_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/services/user_address_services/user_address_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';


class ClosedStoreDialogBloc {
  // region Common Variables
  BuildContext context;
  final List<CartStore> closedStoreList;
  late List<CartProduct> closedProductList = [];
  final ShoppingCartBloc shoppingCartBloc;
  List<String> closedStoreProductReferenceList = [];
  List<int> closedStoreProductCartIdList = [];

  // endregion

  //region Controller
  //endregion

//region Constructor
  ClosedStoreDialogBloc(this.context, this.closedStoreList, this.shoppingCartBloc);
//endregion


//region Init
void init(){
  getClosedStoreProductReference();
  getClosedStoreProductCartId();
  getClosedProductList();
}
//endregion

  // region Get closed store product reference
  void getClosedStoreProductReference(){
    //Store loop
    for(var store in closedStoreList){
      //Product loop
      for(var product in store.cartProductList!){
        closedStoreProductReferenceList.add(product.productReference!);
      }
    }
  }
//endregion


  // region Get closed store product cart id
  void getClosedStoreProductCartId(){
    //Store loop
    for(var store in closedStoreList){
      //Product loop
      for(var product in store.cartProductList!){
        closedStoreProductCartIdList.add(product.cart!.cartItemId!);
      }
    }
  }
//endregion


//region Get closed product list
void getClosedProductList(){
  for(var store in closedStoreList){
    closedProductList.addAll(store.cartProductList!);
  }
}
//endregion




}
