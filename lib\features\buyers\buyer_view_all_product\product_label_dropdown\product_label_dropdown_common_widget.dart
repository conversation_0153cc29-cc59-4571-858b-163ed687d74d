import 'package:flutter/cupertino.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class ProductLabelInfoCommonWidgets {
  //region Labels
  static Widget label(
      {required String title,
      required String buttonName,
      required BuildContext context,
      required Color borderColor,
      required Color fillColor}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          children: [
            SizedBox(
                width: 60,
                child: Text(
                  title,
                  style: AppTextStyle.smallText(textColor: AppColors.appBlack),
                )),
            Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                child: Text(
                  ":",
                  style: AppTextStyle.smallText(textColor: AppColors.appBlack),
                )),
          ],
        ),
        Container(
          width: CommonMethods.textWidth(
              context: context,
              textStyle: AppTextStyle.smallText(textColor: AppColors.appBlack),
              text: "G Mainly Swadesic Made G"),
          padding: const EdgeInsets.symmetric(vertical: 2.5, horizontal: 6),
          decoration: BoxDecoration(
            color: fillColor,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: borderColor, width: 1.3),
          ),
          child: Text(
            buttonName,
            style: AppTextStyle.smallText(textColor: AppColors.appBlack),
          ),
        ),
      ],
    );
  }
  //endregion
}
