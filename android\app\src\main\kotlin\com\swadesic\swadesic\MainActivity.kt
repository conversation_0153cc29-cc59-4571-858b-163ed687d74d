package com.swadesic.swadesic
import AppUtils
import android.util.Log
import com.android.installreferrer.api.InstallReferrerClient
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel


class MainActivity: FlutterActivity() {

    // region Common Variables
    val appUtils = AppUtils()
    val setupReferral = SetupReferral()
    private lateinit var referrerClient: InstallReferrerClient

    // endregion

    // region static data

    companion object {
        var methodChannel: MethodChannel? = null
    }
    // endregion

    // region configureFlutterEngine
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

//        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, appUtils.channel).setMethodCallHandler { call, result ->
//            listeners(call, result)
//        }

        methodChannel =
            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, appUtils.channel)

        methodChannel?.setMethodCallHandler { call, result ->
            listeners(call, result)
        }
    }

    // endregion


    // region listeners
    private fun listeners(call: MethodCall, result: MethodChannel.Result){

        // setup referral
        if(call.method == appUtils.setupReferral) {
            referrerClient = InstallReferrerClient.newBuilder(this).build()
            setupReferral.setup(referrerClient)
            result.success("OK")
        }
    }
    // endregion



}
