import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_screen.dart';
import 'package:swadesic/model/buyer_payment_options_responses/payment_status_check.dart';


class BuyerPaymentStatusBloc {
  // region Common Methods
  BuildContext context;
  bool drawerVisibility = false;
  final PaymentStatusCheckResponse paymentStatusCheckResponse;





//TXN_FAILURE
  //PENDING

  // endregion
  //region Controller
  final drawerCtrl = StreamController<bool>.broadcast();

  //endregion

  // region | Constructor |
  BuyerPaymentStatusBloc(this.context, this.paymentStatusCheckResponse);
  // endregion

  // region Init
  void init(){
    convertDateTime(date: paymentStatusCheckResponse.data!.body!.txnDate!);

  }
  // endregion




  //region On Tap Drawer
  onTapDrawer(){
    drawerVisibility= !drawerVisibility;
    drawerCtrl.sink.add(drawerVisibility);
  }
//endregion

  //region Go To Buyer My Order
  void goToBuyerMyOrder(){
    var screen =  const BuyerMyOrderScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
    // Navigator.pushReplacement(context, route).then((value) {
    // });
  }
//endregion


//region Convert date time
void convertDateTime({required String date}){
  DateTime dataDateTime = DateFormat("yyyy-MM-dd hh:mm:ss").parse(date);
  String dataDate = DateFormat('dd-MM-yyyy').format(dataDateTime);
  paymentStatusCheckResponse.data!.body!.txnDate = dataDate.toString();
  //print(dataDate.toString());
}
//endregion




}
