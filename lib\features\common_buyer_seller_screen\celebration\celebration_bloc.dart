import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_store_bottom_sheet_screen/share_store_bottom_sheet_screen.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';

class CelebrationBloc{
  //region Common variable
  late BuildContext context;
  final bool isPlay;
  double opacity = 0.0;
  bool isVisibleCongratulation = false;

  //endregion

//region Controller
  final AnimationController animationController;
//region Controller
  final celebrationCtrl = StreamController<bool>.broadcast();

//endregion
  //region Constructor
  CelebrationBloc(this.context, this.isPlay, this.animationController);
  //endregion
//region Init
  void init(){
    playOrNot();
    visibleCongratulation();
  }
//endregion


//region playOrNot
void playOrNot()async{
    //Start animation
  animationController.forward();

  //Check animation status
  if(animationController.status == AnimationStatus.completed){
    // animationController.stop();
  }


    //If is play is true then make play
  // if(isPlay){
  //   confettiController.play();
  // }
  // else{
  //   confettiController.stop();
  // }
}
//endregion


  //region Visible congratulation
  void visibleCongratulation()async{
    await Future.delayed(const Duration(seconds: 4));
    //Opacity
    opacity = 1.0;
    //Visible
    isVisibleCongratulation = true;
    //Refresh
    celebrationCtrl.sink.add(true);

  }
  //endregion

  //region On tap share store
  void onTapShareStore({required StoreInfo storeInfo}) async {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: AppColors.appWhite,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(topRight: Radius.circular(10), topLeft: Radius.circular(10))),
        builder: (context) {
          return  SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: ShareInSocialBottomSheetScreen(storeInfo: storeInfo));
        }).then((value) {
    });
  }
//endregion


  //region Go to find your friend
  void goToFindYourFriend(){
    var screen =  FindYourCustomersScreen(visibleNext: false, title: AppStrings.findYourCustomersOnSwadesic,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

//region Dispose
void dispose(){
  animationController.stop();
  celebrationCtrl.close();
}
//endregion


}