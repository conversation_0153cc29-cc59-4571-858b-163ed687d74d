import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum SellerOrdersPaginationState { Loading, Done, Empty }

class SellerOrdersPagination {
  //region Context
  final BuildContext context;
  final SellerAllOrdersBloc sellerOrdersBloc;
  final int limit = 15; // Matches the limit in the bloc
  bool _disposed = false;
  SellerOrdersPaginationState currentApiCallStatus = SellerOrdersPaginationState.Done;
  SellerOrdersPaginationState currentPaginationState = SellerOrdersPaginationState.Loading;

  //endregion

  //region Controller
  final paginationStateCtrl = StreamController<SellerOrdersPaginationState>.broadcast();
  //endregion

  //region Constructor
  SellerOrdersPagination(this.context, this.sellerOrdersBloc) {
    // Add initial state
    paginationStateCtrl.sink.add(SellerOrdersPaginationState.Done);
    
    // Setup scroll listener
    sellerOrdersBloc.scrollController.addListener(_handleScroll);
    
    // Setup pagination state listener
    paginationStateCtrl.stream.listen(_handlePaginationState);
  }

  void _handleScroll() {
    if (_disposed) return;
    _scrollListener();
  }

  void _handlePaginationState(SellerOrdersPaginationState state) {
    if (_disposed) return;
    _paginationControllerListener(state);
  }

  void dispose() {
    _disposed = true;
    sellerOrdersBloc.scrollController.removeListener(_handleScroll);
    paginationStateCtrl.close();
  }
  //endregion

  //region Pagination Controller Listener
  void _paginationControllerListener(SellerOrdersPaginationState state) {
    currentPaginationState = state;
  }
  //endregion

  //region Scroll Listener
  void _scrollListener() {
    // If no more data to load, return
    if (currentPaginationState == SellerOrdersPaginationState.Empty) {
      return;
    }

    final scrollController = sellerOrdersBloc.scrollController;
    if (!scrollController.hasClients || scrollController.position.maxScrollExtent <= 0) {
      return;
    }

    // Trigger load more when scrolled to 80% of the list
    final scrollPosition = scrollController.offset;
    final maxScroll = scrollController.position.maxScrollExtent;
    
    if (scrollPosition >= maxScroll * 0.8) {
      _loadMoreOrders();
    }
  }
  //endregion

  //region Load More Orders
  Future<void> _loadMoreOrders() async {
    // If already loading or no more data, return
    if (currentApiCallStatus == SellerOrdersPaginationState.Loading || 
        !sellerOrdersBloc.hasMore) {
      return;
    }

    try {
      // Set loading state
      currentApiCallStatus = SellerOrdersPaginationState.Loading;
      paginationStateCtrl.sink.add(SellerOrdersPaginationState.Loading);

      // Load more orders
      await sellerOrdersBloc.getSellerSubOrder(loadMore: true);

      // Update state
      currentApiCallStatus = SellerOrdersPaginationState.Done;
      paginationStateCtrl.sink.add(SellerOrdersPaginationState.Done);
    } on ApiErrorResponseMessage {
      currentApiCallStatus = SellerOrdersPaginationState.Done;
      paginationStateCtrl.sink.add(SellerOrdersPaginationState.Done);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    } catch (error) {
      currentApiCallStatus = SellerOrdersPaginationState.Done;
      paginationStateCtrl.sink.add(SellerOrdersPaginationState.Done);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    }
  }
  //endregion
}
