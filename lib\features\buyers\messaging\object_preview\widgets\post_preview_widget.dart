import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/messaging/object_preview/models/object_preview_models.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'dart:developer' as developer;

/// Widget to display post previews
class PostPreviewWidget extends StatelessWidget {
  final PostPreview post;
  final bool isMe;

  const PostPreviewWidget({
    Key? key,
    required this.post,
    required this.isMe,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get the first image URL if available
    String? imageUrl;
    if (post.postImages.isNotEmpty) {
      final imagePath = post.postImages.first.mediaPath;
        imageUrl = AppConstants.baseUrl + imagePath;
      }

    // Get the creator icon URL
    String creatorIconUrl = '';
    if (post.createdBy.icon != null && post.createdBy.icon!.isNotEmpty) {
        creatorIconUrl = AppConstants.baseUrl + post.createdBy.icon!;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isMe ? AppColors.appRichBlack.withOpacity(0.9) : AppColors.textFieldFill2,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMe ? Colors.grey[800]! : Colors.grey[300]!,
          width: 0.5,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToPostScreen(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with creator info
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    // Creator avatar
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.grey[300],
                      backgroundImage: creatorIconUrl.isNotEmpty
                          ? NetworkImage(creatorIconUrl)
                          : null,
                      child: creatorIconUrl.isEmpty
                          ? Icon(
                              post.createdBy.entityType == 'STORE' ? Icons.store : Icons.person,
                              color: Colors.grey[600],
                              size: 16,
                            )
                          : null,
                    ),
                    const SizedBox(width: 8),
                    // Creator name
                    Expanded(
                      child: Text(
                        post.createdBy.name,
                        style: AppTextStyle.access0(
                          textColor: isMe ? Colors.white : AppColors.appBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Post type indicator
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: isMe ? Colors.white24 : Colors.grey[300],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'POST',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: isMe ? Colors.white : Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Post image if available
              if (imageUrl != null)
                Container(
                  height: 180,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                  ),
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      developer.log(
                        'Error loading post image: ${error.toString()}',
                        name: 'PostPreviewWidget',
                        stackTrace: stackTrace,
                      );
                      return Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.grey[500],
                          size: 40,
                        ),
                      );
                    },
                  ),
                ),

              // Post text
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Text(
                  post.postText,
                  style: AppTextStyle.contentText0(
                    textColor: isMe ? Colors.white : AppColors.appBlack,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Navigate to the post screen
  void _navigateToPostScreen(BuildContext context) {
    // Navigate to the post detail screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SinglePostViewScreen(
          postReference: post.postReference,
          isFromProductScreen: false,
        ),
      ),
    );
  }
}
