import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_on_boarding/select_roles/select_roles_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';

class SelectRoles extends StatefulWidget {
  final Function(List<String>) onChangeData;

  const SelectRoles({super.key, required this.onChangeData});

  @override
  State<SelectRoles> createState() => _SelectRolesState();
}

class _SelectRolesState extends State<SelectRoles> {
  //region Bloc
  late SelectRolesBloc selectRolesBloc;
  //endregion
  //region Init
  @override
  void initState() {
    selectRolesBloc = SelectRolesBloc(context, widget.onChangeData);
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        info(),
      ],
    );
  }

  //region Info
  Widget info() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        options(),
      ],
    );
  }
//endregion

//region Options
  Widget options() {
    return ListView.builder(
        itemCount: selectRolesBloc.roleList.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return card(data: selectRolesBloc.roleList[index]);
        });
  }
//endregion

//region Card
  Widget card({required Map<String, dynamic> data}) {
    return StreamBuilder<bool>(
        stream: selectRolesBloc.selectRoleCtrl.stream,
        builder: (context, snapshot) {
          return Container(
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.textFieldFill1,
            ),
            child: Column(
              children: [
                // Main card content (always visible)
                InkWell(
                  onTap: () {
                    selectRolesBloc.onSelectRole(selectedRole: data);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 20),
                    child: Row(
                      children: [
                        // Checkbox
                        SvgPicture.asset(
                          data['isSelected']
                              ? AppImages.appCheckBoxTrue
                              : AppImages.appCheckBoxFalse,
                          color: AppColors.brandBlack,
                        ),
                        const SizedBox(width: 12),
                        // Title
                        Expanded(
                          child: Text(
                            data['title'],
                            style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack),
                          ),
                        ),
                        // Expand/collapse arrow
                        InkWell(
                          onTap: () {
                            selectRolesBloc.toggleExpandedState(role: data);
                          },
                          child: Icon(
                            data['isExpanded']
                                ? Icons.keyboard_arrow_up
                                : Icons.keyboard_arrow_down,
                            color: AppColors.appBlack,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Expanded content (only visible when expanded)
                if (data['isExpanded'])
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 52, right: 20, bottom: 16),
                    child: Text(
                      data['detail'],
                      style: AppTextStyle.contentText0(
                          textColor: AppColors.writingBlack1),
                    ),
                  ),
              ],
            ),
          );
        });
  }
//endregion
}
