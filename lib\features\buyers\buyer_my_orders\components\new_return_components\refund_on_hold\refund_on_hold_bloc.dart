import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class RefundOnHoldBloc {
  // region Common Variables
  BuildContext context;
  late BuyerMyOrderServices buyerMyOrderServices;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  final List<SubOrder> subOrderList;
  List<String> groupNameList = [];

  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  final checkBoxCtrl = StreamController<bool>.broadcast();

  //endregion

  // region | Constructor |
  RefundOnHoldBloc(this.context, this.buyerSubOrderBloc, this.order, this.subOrderList);

  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    takeOutDisplayPackageNumbers();
  }
  // endregion

  //region Take out display package numbers
  void takeOutDisplayPackageNumbers() {
    //Clear the list
    groupNameList.clear();
    //Add all display package numbers
    for (var element in subOrderList) {
      if (!groupNameList.contains(element.displayPackageNumber)) {
        groupNameList.add(element.displayPackageNumber!);
      }
    }
  }
  //endregion

  // region On Tap need help
  void onTapNeedHelp() {
    CommonMethods.reportAndSuggestion(context: context);
  }
  // endregion
}
