import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_post_store_product_and_people/search_post_store_product_and_people_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/features/post/post_screen_bloc.dart';
import 'package:swadesic/features/post/recommened_posts/recommended_post_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/search_response.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/buyer_search_services/buyer_search_services.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum SearchResultPaginationState { Loading, Done, Empty }

class SearchPostStoreProductAndPeoplePagination {
  //region Context
  late BuildContext context;
  late SearchPostStoreProductAndPeopleBloc searchPostStoreProductAndPeopleBloc;
  // bool isLoadingPaginationData = false;
  SearchResultPaginationState currentApiCallStatus =
      SearchResultPaginationState.Done;

  //endregion

  //region Controller
  final paginationStateCtrl =
      StreamController<SearchResultPaginationState>.broadcast();
  //endregion

//region Constructor
  SearchPostStoreProductAndPeoplePagination(
      this.context, this.searchPostStoreProductAndPeopleBloc);
//endregion

  //region On pagination visible
  void onPaginationVisible() async {
    await getPaginationFeeds();
  }

  //endregion

  //region Get pagination feeds
  Future<void> getPaginationFeeds() async {
    try {
      //If api call status is Loading then return
      if (currentApiCallStatus == SearchResultPaginationState.Loading) {
        return;
      }
      //Loading
      paginationStateCtrl.sink.add(SearchResultPaginationState.Loading);
      //Current api call status is Loading
      currentApiCallStatus = SearchResultPaginationState.Loading;

      ///Post
      if (EntityType.POST == searchPostStoreProductAndPeopleBloc.entityType) {
        //Clear
        // postList.clear();
        List<PostDetail> dataList =
            (await BuyerSearchServices().getSearchResult(
          entityType: searchPostStoreProductAndPeopleBloc.entityType,
          limit: searchPostStoreProductAndPeopleBloc.limit,
          offset: searchPostStoreProductAndPeopleBloc.offset,
          pinCode: "110068",
          query: BuyerSearchBloc.searchTextEditingCtrl.text,
        ))['posts']
                .map<PostDetail>((post) => PostDetail.fromJson(post))
                .toList();
        //Add all new data
        for (var data in dataList) {
          //If post reference contains then ignore
          if (searchPostStoreProductAndPeopleBloc.postList.any(
              (e) => e.postOrCommentReference == data.postOrCommentReference)) {
            continue;
          } else {
            searchPostStoreProductAndPeopleBloc.postList.add(data);
          }
        }
        //Empty
        if (searchPostStoreProductAndPeopleBloc.postList.isEmpty) {
          paginationStateCtrl.sink.add(SearchResultPaginationState.Done);
        }
      }

      ///Store
      if (EntityType.STORE == searchPostStoreProductAndPeopleBloc.entityType) {
        List<StoreInfo> dataList = (await BuyerSearchServices().getSearchResult(
          entityType: searchPostStoreProductAndPeopleBloc.entityType,
          limit: searchPostStoreProductAndPeopleBloc.limit,
          offset: searchPostStoreProductAndPeopleBloc.offset,
          pinCode: "110068",
          query: BuyerSearchBloc.searchTextEditingCtrl.text,
        ))['store']
            .map<StoreInfo>((store) => StoreInfo.fromJson(store))
            .toList();

        //Add all new data
        for (var data in dataList) {
          //If store reference contains then ignore
          if (searchPostStoreProductAndPeopleBloc.storeList
              .any((e) => e.storeReference == data.storeReference)) {
            continue;
          } else {
            searchPostStoreProductAndPeopleBloc.storeList.add(data);
          }
        }
        //Empty
        if (dataList.isEmpty) {
          paginationStateCtrl.sink.add(SearchResultPaginationState.Done);
        }
      }

      ///Product
      if (EntityType.PRODUCT ==
          searchPostStoreProductAndPeopleBloc.entityType) {
        List<Product> dataList = (await BuyerSearchServices().getSearchResult(
          entityType: searchPostStoreProductAndPeopleBloc.entityType,
          limit: searchPostStoreProductAndPeopleBloc.limit,
          offset: searchPostStoreProductAndPeopleBloc.productList.length,
          pinCode: "110068",
          query: BuyerSearchBloc.searchTextEditingCtrl.text,
        ))['product']
            .map<Product>((product) => Product.fromJson(product))
            .toList();

        //Add all new data
        for (var data in dataList) {
          //If store reference contains then ignore
          if (searchPostStoreProductAndPeopleBloc.productList
              .any((e) => e.productReference == data.productReference)) {
            continue;
          } else {
            searchPostStoreProductAndPeopleBloc.productList.add(data);
          }
        }

        //Empty
        if (dataList.isEmpty) {
          paginationStateCtrl.sink.add(SearchResultPaginationState.Done);
        }
      }

      ///User
      if (EntityType.USER == searchPostStoreProductAndPeopleBloc.entityType) {
        List<User> dataList = (await BuyerSearchServices().getSearchResult(
          entityType: searchPostStoreProductAndPeopleBloc.entityType,
          limit: searchPostStoreProductAndPeopleBloc.limit,
          offset: searchPostStoreProductAndPeopleBloc.offset,
          pinCode: "110068",
          query: BuyerSearchBloc.searchTextEditingCtrl.text,
        ))['user']
            .map<User>((user) => User.fromJson(user))
            .toList();

        //Add all new data
        for (var data in dataList) {
          //If store reference contains then ignore
          if (searchPostStoreProductAndPeopleBloc.userList
              .any((e) => e.userReference == data.userReference)) {
            continue;
          } else {
            searchPostStoreProductAndPeopleBloc.userList.add(data);
          }
        }

        //Empty
        if (dataList.isEmpty) {
          paginationStateCtrl.sink.add(SearchResultPaginationState.Done);
        }
      }

      //Done
      paginationStateCtrl.sink.add(SearchResultPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = SearchResultPaginationState.Done;
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Done
      paginationStateCtrl.sink.add(SearchResultPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = SearchResultPaginationState.Done;
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context)
          : null;
      //Done
      paginationStateCtrl.sink.add(SearchResultPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = SearchResultPaginationState.Done;
    }
  }
//endregion
}
