import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class AddingPostProgress extends StatefulWidget {
  const AddingPostProgress({super.key});

  @override
  State<AddingPostProgress> createState() => _AddingPostProgressState();
}

class _AddingPostProgressState extends State<AddingPostProgress> {
  @override
  Widget build(BuildContext context) {
    return body();

  }

  //region Body
Widget body(){
  // Get reference to the PostDataModel
  var postDataModel = Provider.of<PostDataModel>(context, listen: false);
  // return body();
  //Make it visible only if some post is on going
  if(postDataModel.postingStatus ){
    return Container(
      // margin: const EdgeInsets.only(top: kToolbarHeight),
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
          color: AppColors.writingBlack0, boxShadow: AppColors.toastMessageShadow),
      width: double.infinity,
      child: Row(
        children: [
          Expanded(
            child: Text(
              "Posting....",
              textAlign: TextAlign.center,
              style: AppTextStyle.heading4SemiBold(textColor: AppColors.appWhite),
            ),
          ),
        ],
      ),
    );
  }
  return const SizedBox();
}
//endregion

}
