import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/celebration/celebration_bloc.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class Celebration extends StatefulWidget {
  final bool isPlay;
  final String celebrationMessage;
  const Celebration(
      {super.key, this.isPlay = false, required this.celebrationMessage});

  @override
  State<Celebration> createState() => _CelebrationState();
}

class _CelebrationState extends State<Celebration>
    with TickerProviderStateMixin {
  late final AnimationController animationController;
  //region Bloc
  late CelebrationBloc celebrationBloc;
  //endregion

  //region Init
  @override
  void initState() {
    animationController =
        AnimationController(vsync: this, duration: const Duration(seconds: 8));
    celebrationBloc =
        CelebrationBloc(context, widget.isPlay, animationController);
    celebrationBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    celebrationBloc.dispose();
    super.dispose();
  }
  //endregion

  //region Did update
  @override
  void didUpdateWidget(covariant Celebration oldWidget) {
    celebrationBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: SafeArea(child: body()),
    );
  }

  //region Body
  Widget body() {
    return Center(
      child: ListView(
        shrinkWrap: true,
        // mainAxisSize: MainAxisSize.min,
        // mainAxisAlignment: MainAxisAlignment.center,
        // crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          sun(),
          congratulationAndShare(),
          shareStoreLink(),
        ],
      ),
    );
  }
//endregion

  //region Sun
  Widget sun() {
    return Lottie.asset(AppImages.congratulation,
        height: MediaQuery.of(context).size.width,
        width: MediaQuery.of(context).size.width,
        controller: celebrationBloc.animationController);
  }
  //endregion

  //region Congratulation and share
  Widget congratulationAndShare() {
    return StreamBuilder<bool>(
        stream: celebrationBloc.celebrationCtrl.stream,
        builder: (context, snapshot) {
          return AnimatedOpacity(
            duration: const Duration(seconds: 4), // Animation duration
            opacity: celebrationBloc.opacity, // Opacity value to animate to
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [congratulation(), inviteYourFriends()],
            ),
          );
        });
  }
  //endregion

  //region Celebration
  Widget congratulation() {
    return Consumer<SellerOwnStoreInfoDataModel>(
      builder: (BuildContext context, data, Widget? child) {
        return SizedBox(
          height: CommonMethods.textHeight(
                context: context,
                textStyle:
                    AppTextStyle.pageHeading(textColor: AppColors.appBlack),
              ) *
              5,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 50),
                child: Text(
                  "Congratulations,\n@${data.storeInfo!.storehandle} is live now. \nHappy Selling! Happy Branding!",
                  textAlign: TextAlign.center,
                  style:
                      AppTextStyle.pageHeading(textColor: AppColors.appBlack),
                ),
              ),
              Lottie.asset(
                AppImages.celebration,
                // controller:celebrationBloc.animationController
              )
            ],
          ),
        );
      },
    );
  }
  //endregion

//region Invite your friends
  Widget inviteYourFriends() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          celebrationBloc.goToFindYourFriend();
          //storeDashBoardBloc.onTapShareStoreLink(storeHandle: StoreDashBoardBloc.singleStoreInfoResponse.data!.storehandle!);
        },
        child: Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 10,
          ),
          decoration: const BoxDecoration(
              color: AppColors.brandBlack,
              borderRadius: BorderRadius.all(Radius.circular(9))),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                    child: Text(AppStrings.findYourFriendsAndCustomers,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        style: AppTextStyle.access0(
                            textColor: AppColors.appWhite))),
              ],
            ),
          ),
        ),
      ),
    );
  }
//endregion

  //region Share store link

  Widget shareStoreLink() {
    return //Share store and profile
        Consumer<SellerOwnStoreInfoDataModel>(
      builder: (BuildContext context, SellerOwnStoreInfoDataModel data,
          Widget? child) {
        return Container(
          alignment: Alignment.center,
          child: CupertinoButton(
            onPressed: () {
              celebrationBloc.onTapShareStore(storeInfo: data.storeInfo!);
            },
            padding: EdgeInsets.zero,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  AppImages.chainIcon,
                  color: AppColors.brandBlack,
                  height: 16,
                  width: 16,
                ),
                const SizedBox(
                  height: 7,
                ),
                Text(
                  AppStrings.shareStoreLink,
                  maxLines: 2,
                  style: AppTextStyle.subTitle(textColor: AppColors.brandBlack),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  //endregion
}
