import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';

class ReturnPickupScheduledBloc {
  // region Common Variables
  BuildContext context;
  late BuyerMyOrderServices buyerMyOrderServices;
  bool isProductListDropDownVisible = false;


  // endregion


  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  ReturnPickupScheduledBloc(this.context);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();

  }
// endregion



//endregion









}
