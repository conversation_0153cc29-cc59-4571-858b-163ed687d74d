import 'dart:async';
import 'package:flutter/material.dart';



class PreviewBloc {
  // region Common Variables
  BuildContext context;

  bool isFirstWidget = true;
  int durationInSeconds = 6;


  // endregion



  //region Controller
  final GlobalKey<TooltipState> toolTipKey = GlobalKey<TooltipState>();

  final previewRefreshCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  //endregion

  // region | Constructor |
  PreviewBloc(this.context);
  // endregion

  // region Init
  void init() async {
    //timeLoop();
    // countDown();
  }
// endregion

  //region On tap tooltip
  void onTapToolTip(){
    toolTipKey.currentState?.ensureTooltipVisible();
    //Refresh
    previewRefreshCtrl.sink.add(true);
  }
  //endregion


  //region Time loop
  void timeLoop(){
    /*If first widget is true then change teh duration to
     6 seconds else make that to 4.
    */
    if(isFirstWidget){
      durationInSeconds = 6;
    }
    else{
      durationInSeconds = 6;
    }

    //Timer
    Future.delayed( Duration(seconds: durationInSeconds), () {
      //Change the widget
      isFirstWidget = !isFirstWidget;
      //refresh ui
      previewRefreshCtrl.sink.add(true);
      //Call again time loop
      timeLoop();
    });
  }
  //endregion



//region Dispose
  void dispose() {
    imageCache.clear();


  }
//endregion

}
