import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/buyer_search_history_bloc.dart';
import 'package:swadesic/model/buyer_search_response/buyer_search_history_response.dart';



class PeopleHistoryBloc {
  // region Common Variables
  BuildContext context;
  final List<History> peopleHistoryList;
  final BuyerSearchHistoryBloc buyerSearchHistoryBloc;

  int itemCount = 3;
  // endregion


  //region Controller
  final refreshCtrl = StreamController<bool>.broadcast();
  // region | Constructor |
  PeopleHistoryBloc(this.context, this.peopleHistoryList, this.buyerSearchHistoryBloc);

  // endregion


  // region Init
  void init() async {
  }

// endregion



  //region On tap view more
  void onTapViewMore({required bool isIncrease}){
    if(isIncrease){
      itemCount = itemCount+3;
    }
    if(peopleHistoryList.length <= itemCount){
      itemCount = peopleHistoryList.length;
      refreshCtrl.sink.add(true);
      return;
    }
    if(peopleHistoryList.length >= itemCount){
      refreshCtrl.sink.add(true);

    }

  }
  //endregion







  //region On press cross
  void onPressCross({required History history}){
    //Remove history local
    peopleHistoryList.removeWhere((element){
      return element.searchedItemId == history.searchedItemId;
    });
    //Item count check
    onTapViewMore(isIncrease: false);
    //Remove single history api call
    buyerSearchHistoryBloc.removeSingleHistory(history: history);
    //Refresh
    refreshCtrl.sink.add(true);
  }
  //endregion


//region Dispose
  void dispose() {
    imageCache.clear();
    refreshCtrl.close();
  }
//endregion


}
