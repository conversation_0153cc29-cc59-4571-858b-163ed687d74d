import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/messaging/object_preview/models/object_preview_models.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';

/// Widget to display entity (User or Store) previews
class EntityPreviewWidget extends StatelessWidget {
  final EntityPreview entity;
  final bool isMe;

  const EntityPreviewWidget({
    Key? key,
    required this.entity,
    required this.isMe,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine if this is a user or store
    final bool isStore = entity.entityType == 'STORE';
    
    // Get the icon URL
    String iconUrl = '';
    if (entity.icon != null && entity.icon!.isNotEmpty) {
        iconUrl = AppConstants.baseUrl + entity.icon!;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isMe ? AppColors.appRichBlack.withOpacity(0.9) : AppColors.textFieldFill2,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMe ? Colors.grey[800]! : Colors.grey[300]!,
          width: 0.5,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToEntityScreen(context),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Profile image
                CircleAvatar(
                  radius: 24,
                  backgroundColor: Colors.grey[300],
                  backgroundImage: iconUrl.isNotEmpty 
                      ? NetworkImage(iconUrl)
                      : null,
                  child: iconUrl.isEmpty 
                      ? Icon(
                          isStore ? Icons.store : Icons.person,
                          color: Colors.grey[600],
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                // Entity details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        entity.name,
                        style: AppTextStyle.access0(
                          textColor: isMe ? Colors.white : AppColors.appBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Icon(
                            isStore ? Icons.store : Icons.person,
                            size: 14,
                            color: isMe ? Colors.white70 : Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '@${entity.handle}',
                            style: AppTextStyle.smallTextRegular(
                              textColor: isMe ? Colors.white70 : Colors.grey[700]!,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Chevron icon
                Icon(
                  Icons.chevron_right,
                  color: isMe ? Colors.white70 : Colors.grey[600],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Navigate to the appropriate screen based on entity type
  void _navigateToEntityScreen(BuildContext context) {
    if (entity.entityType == 'STORE') {
      // Navigate to store profile
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BuyerViewStoreScreen(
            storeReference: entity.reference,
          ),
        ),
      );
    } else {
      // Navigate to user profile
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(
            userReference: entity.reference,
          ),
        ),
      );
    }
  }
}
