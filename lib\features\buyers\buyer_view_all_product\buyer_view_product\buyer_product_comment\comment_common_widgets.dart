import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class CommentCommonWidgets{


  //region Add and rate buttons
  static Widget addRateButton({required String buttonName,required onPress}){
    return InkWell(
      onTap: (){
        onPress();
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 10),
            decoration: const BoxDecoration(
              color: AppColors.lightGray,
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),

            child: Text(buttonName,
              style: AppTextStyle.access0(textColor: AppColors.appBlack)
              ,),
          ),
        ],
      ),
    );
  }
  //endregion


  //region Post as buttons
  static Widget postAsButton({required String buttonName,required onPress, Color buttonColor = AppColors.appWhite,bool isActive = true }){
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed:(){
        onPress();
      },
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 10),
        decoration:  BoxDecoration(
          color: buttonColor,
          border: Border.all(color: isActive?AppColors.appBlack:AppColors.appBlack.withOpacity(0.3),width: 1),
          borderRadius: const BorderRadius.all(Radius.circular(50)),
        ),

        child: Text(buttonName,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyle.contentText0(textColor:isActive?AppColors.appBlack:AppColors.appBlack.withOpacity(0.3))
          ,),
      ),
    );
  }
//endregion


}