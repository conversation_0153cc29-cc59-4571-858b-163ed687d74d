import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/return_requested/widget/confirm_cancel.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/seller_all_order_service/seller_all_order_service.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ReturnRequestedBloc {
  // region Common Variables
  final BuildContext context;
  late SellerAllOrderServices sellerAllOrderServices;
  late BuyerMyOrderServices buyerMyOrderServices;
  final List<SubOrder> suborderList;
  bool isSelectUnselectVisible = true;
  final Order order;
  final BuyerSubOrderBloc buyerSuborderBloc;
  late Timer timer;
  String countDownValue = "00:00:00";

  // String for estimated return pickup date
  String bottomSheetEstimatedReturnPickupDate = DateFormat("dd-MM-yyyy").format(DateTime.now());

  // endregion

  //region Controller
  final selectUnSelectCtrl = StreamController<bool>.broadcast();
  final confirmCancelRefreshCtrl = StreamController<bool>.broadcast();
  final bottomSheetCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  final returnPersonNameCtrl = TextEditingController();
  final returnPersonContactCtrl = TextEditingController();
  final returnTrackingLinkCtrl = TextEditingController();
  final dateCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  ReturnRequestedBloc(this.context, this.suborderList, this.order, this.buyerSuborderBloc);

  // endregion

  // region Init
  void init() {
    sellerAllOrderServices = SellerAllOrderServices();
    buyerMyOrderServices = BuyerMyOrderServices();
    onSelectChangeDate();
    // No need for auto cancel timer for return requests
  }

// endregion


  //region On save return pickup date
  Future<void> onSaveReturnPickupDate() async {
    //region Try
    try {
      // Get current context before async gap
      final currentContext = context;

      List<String> selectedSubOrders = CommonMethods.sellerSelectedSubOrderNumberList(suborderList);
      //Check Sub-order selected or not
      if (selectedSubOrders.isEmpty) {
        return CommonMethods.toastMessage("Please select orders", currentContext);
      }
      //Api call
      await sellerAllOrderServices.updateEstimatedReturnPickupDate(
        selectedDate: bottomSheetEstimatedReturnPickupDate,
        subOrderNumberList: selectedSubOrders,
        subOrderStatus: "RETURN_CONFIRMED"
      );

      // Check if context is still valid
      if (!currentContext.mounted) return;

      //Message
      CommonMethods.toastMessage(
        "${selectedSubOrders.length==1 ? "${selectedSubOrders.length} order" : "${selectedSubOrders.length} orders"} return pickup date updated",
        currentContext
      );

      //Call the get sub order api
      buyerSuborderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage catch (e) {
      final currentContext = context;
      if (!currentContext.mounted) return;
      return CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
    } catch (error) {
      final currentContext = context;
      if (!currentContext.mounted) return;
      return CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
    }
  }
  //endregion

  //region Accept return
  Future<void> acceptReturn({required List<String> selectedSubOrders}) async {
    //region Try
    try {
      // Get current context before async gap
      final currentContext = context;

      //Check Sub-order selected or not
      if (selectedSubOrders.isEmpty) {
        return CommonMethods.toastMessage("Please select orders", currentContext);
      }

      // First update the return pickup date with the locally selected date
      await updateReturnPickupDate(
        selectedDate: bottomSheetEstimatedReturnPickupDate
      );

      // Then confirm the return
      await sellerAllOrderServices.returnConfirm(
        subOrderNumbers: selectedSubOrders
      );

      // Check if context is still valid
      if (!currentContext.mounted) return;

      //Close bottom sheet
      Navigator.pop(currentContext);

      //Message
      CommonMethods.toastMessage(
        "${selectedSubOrders.length==1 ? "${selectedSubOrders.length} return" : "${selectedSubOrders.length} returns"} accepted",
        currentContext
      );

      //Call the get sub order api
      buyerSuborderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage catch (e) {
      final currentContext = context;
      if (!currentContext.mounted) return;
      return CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
    } catch (error) {
      final currentContext = context;
      if (!currentContext.mounted) return;
      return CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
    }
  }
  //endregion

  //endregion

//region Select suborder
  void onSelectSubOrder(SubOrder subOrder) {
    subOrder.isSelected = !subOrder.isSelected;
    onSelectChangeDate();
    confirmCancelRefreshCtrl.sink.add(true);
    bottomSheetCtrl.sink.add(true);
  }
//endregion

  //region On select change date
  void onSelectChangeDate(){
    //Selected sub orders
    List<SubOrder> selectedSubOrders = suborderList.where((element) => element.isSelected).toList();

    //If suborders are selected then take out minimum date time
    if(selectedSubOrders.isNotEmpty){
      // Use estimatedPickupDate if available, otherwise use current date
      List<String> dateList = [];
      for (var subOrder in selectedSubOrders) {
        if (subOrder.estimatedPickupDate != null && subOrder.estimatedPickupDate!.isNotEmpty) {
          dateList.add(subOrder.estimatedPickupDate!.replaceAll("-", "/"));
        }
      }

      if (dateList.isNotEmpty) {
        bottomSheetEstimatedReturnPickupDate = CommonMethods.minimumAndMaximumDateTime(
          dateList: dateList,
          isMinimum: true
        );
      } else {
        // Default to current date if no pickup dates are available
        bottomSheetEstimatedReturnPickupDate = DateFormat("dd-MM-yyyy").format(DateTime.now());
      }
    }
    //Else use current date
    else {
      bottomSheetEstimatedReturnPickupDate = DateFormat("dd-MM-yyyy").format(DateTime.now());
    }
  }
  //endregion

//region Select unselect dropdown
  void selectUnSelectDropdown() {
    isSelectUnselectVisible = !isSelectUnselectVisible;
    selectUnSelectCtrl.sink.add(true);
    bottomSheetCtrl.sink.add(true);
  }
//endregion

//region Open calender
  void onTapCalender() async {
    // Get current context before async gap
    final currentContext = context;

    String tempSelectedDate = await CommonMethods.openCalender(
      context: currentContext,
      selectedDateTime: bottomSheetEstimatedReturnPickupDate
    );

    // Check if context is still valid
    if (!currentContext.mounted) return;

    //If not same date time then update local variable only
    if(tempSelectedDate != bottomSheetEstimatedReturnPickupDate){
      //Update bottom sheet date time locally without API call
      bottomSheetEstimatedReturnPickupDate = tempSelectedDate;
      //Refresh UI
      bottomSheetCtrl.sink.add(true);
    }
  }
//endregion

  //region Update Return Pickup date
  Future<void> updateReturnPickupDate({required String selectedDate}) async {
    //region Try
    try {
      // Get current context before async gap
      final currentContext = context;

      await sellerAllOrderServices.updateEstimatedReturnPickupDate(
        selectedDate: selectedDate,
        subOrderNumberList: CommonMethods.sellerSelectedSubOrderNumberList(suborderList),
        subOrderStatus: "RETURN_CONFIRMED"
      );

      // Check if context is still valid
      if (!currentContext.mounted) return;

      //Message
      CommonMethods.toastMessage("Return pickup date updated", currentContext);

      //Get suborders
      buyerSuborderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage {
      final currentContext = context;
      if (!currentContext.mounted) return;
      CommonMethods.toastMessage("Unable to update return pickup date", currentContext);
      return;
    } catch (error) {
      final currentContext = context;
      if (!currentContext.mounted) return;
      CommonMethods.toastMessage("Unable to update return pickup date", currentContext);
      return;
    }
  }
  //endregion

  //region Dispose
  void dispose(){
    selectUnSelectCtrl.close();
    confirmCancelRefreshCtrl.close();
    bottomSheetCtrl.close();
  }
  //endregion

  //region Cancel return request
  Future<void> cancelReturnRequest({required SubOrder subOrder}) async {
    //region Try
    try {
      // Get current context before async gap
      final currentContext = context;

      await buyerMyOrderServices.cancelReturnRequest(subOrderNumbers: [subOrder.suborderNumber!]);

      // Check if context is still valid
      if (!currentContext.mounted) return;

      //Message
      CommonMethods.toastMessage("Return cancelled", currentContext);

      //Get sub orders
      buyerSuborderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage {
      final currentContext = context;
      if (!currentContext.mounted) return;
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      return;
    } catch (error) {
      final currentContext = context;
      if (!currentContext.mounted) return;
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, currentContext);
      return;
    }
  }
  //endregion
}
