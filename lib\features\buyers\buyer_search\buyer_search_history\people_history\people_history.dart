import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_history_screen_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/buyer_search_history_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/people_history/people_history_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/buyer_search_response/buyer_search_history_response.dart';
import 'package:swadesic/util/app_images.dart';

class PeopleHistory extends StatefulWidget {
  final List<History> peopleHistoryList;
  final BuyerSearchHistoryBloc buyerSearchHistoryBloc;
  final bool isAllView;


  const PeopleHistory({Key? key, required this.peopleHistoryList, required this.buyerSearchHistoryBloc, required this.isAllView}) : super(key: key);

  @override
  State<PeopleHistory> createState() => _PeopleHistoryState();
}

class _PeopleHistoryState extends State<PeopleHistory> {
  //region Bloc
  late PeopleHistoryBloc peopleHistoryBloc;

  //endregion
  //region Init
  @override
  void initState() {
    peopleHistoryBloc = PeopleHistoryBloc(context, widget.peopleHistoryList,widget.buyerSearchHistoryBloc);
    peopleHistoryBloc.init();
    // TODO: implement initState
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: peopleHistoryBloc.refreshCtrl.stream,
        builder: (context, snapshot) {
          ///If there is no product then
          if(widget.peopleHistoryList.isEmpty){
            return const SizedBox();
          }
          return body();
        }
    );
  }

  //region body
  Widget body() {
    return Column(
      children: [
        BuyerSearchCommonWidgets.title(data: "People"),
        //View all
        Visibility(
          visible: widget.isAllView,
          child: Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 15),
              child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: widget.isAllView?widget.peopleHistoryList.length:widget.peopleHistoryList.length > 3 ? peopleHistoryBloc.itemCount : widget.peopleHistoryList.length,
                  itemBuilder: (context, index) {
                    return BuyerSearchCommonWidgets.searchedDataCard(
                        customImageContainerType: CustomImageContainerType.user,
                        verifiedWidget: VerifiedBadge(
                          width: 15,
                          height: 15,
                          subscriptionType:widget.peopleHistoryList[index].subscriptionType,
                        ),
                        isUser: true,
                        placeHolder: AppImages.userPlaceHolder,
                        context: context,
                        heading: widget.peopleHistoryList[index].name!,
                        imageUrl: widget.peopleHistoryList[index].icon,
                        title:widget.peopleHistoryList[index].handle!,

                        isCrossVisible: true,

                        onPressCross: (){
                          peopleHistoryBloc.onPressCross(history: widget.peopleHistoryList[index]);
                        }, onTapCard: (){
                      widget.buyerSearchHistoryBloc.goToUserProfileScreen(userReference: widget.peopleHistoryList[index].searchItem!);
                    }
                    );
                  }),
            ),
          ),

        ),
        //View all
        Visibility(
          visible: !widget.isAllView,
          child: Padding(
            padding: const EdgeInsets.only(left: 15),
            child: ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: widget.isAllView?widget.peopleHistoryList.length:widget.peopleHistoryList.length > 3 ? peopleHistoryBloc.itemCount : widget.peopleHistoryList.length,
                itemBuilder: (context, index) {
                  return BuyerSearchCommonWidgets.searchedDataCard(
                      customImageContainerType: CustomImageContainerType.user,

                      isUser: true,
                      placeHolder: AppImages.userPlaceHolder,
                      context: context,
                      heading: widget.peopleHistoryList[index].name!,
                      imageUrl: widget.peopleHistoryList[index].icon,
                      title:widget.peopleHistoryList[index].handle!,
                      isCrossVisible: true,

                      onPressCross: (){
                        peopleHistoryBloc.onPressCross(history: widget.peopleHistoryList[index]);
                      }, onTapCard: (){
                    widget.buyerSearchHistoryBloc.goToUserProfileScreen(userReference: widget.peopleHistoryList[index].searchItem!);
                  }
                  );
                }),
          ),

        ),

        InkWell(
          onTap: () {
            peopleHistoryBloc.onTapViewMore(isIncrease: true);
          },
          child: Visibility(
              visible:  widget.isAllView?false:widget.peopleHistoryList.length > peopleHistoryBloc.itemCount,
              child: BuyerSearchCommonWidgets.viewMoreClearAllViewAll(data: "View more people", isUnderline: true)),
        ),
      ],
    );
  }
//endregion
}
